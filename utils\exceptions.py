"""
自定义异常类模块

定义项目中使用的各种自定义异常类。
"""

import time
import traceback
from typing import Optional, Dict, Any, List, Callable
from enum import Enum
from dataclasses import dataclass, field


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorContext:
    """错误上下文信息"""
    function_name: Optional[str] = None
    module_name: Optional[str] = None
    user_action: Optional[str] = None
    system_state: Optional[Dict[str, Any]] = None
    timestamp: float = field(default_factory=time.time)
    retry_count: int = 0


class WeChatMassSenderError(Exception):
    """微信群发助手基础异常类"""

    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[ErrorContext] = None,
        recoverable: bool = True
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.context = context or ErrorContext()
        self.recoverable = recoverable
        self.timestamp = time.time()

    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "message": self.message,
            "error_code": self.error_code,
            "severity": self.severity.value,
            "recoverable": self.recoverable,
            "timestamp": self.timestamp,
            "context": {
                "function_name": self.context.function_name,
                "module_name": self.context.module_name,
                "user_action": self.context.user_action,
                "retry_count": self.context.retry_count,
            } if self.context else None
        }


class ConnectionError(WeChatMassSenderError):
    """连接相关异常"""

    pass


class AuthenticationError(WeChatMassSenderError):
    """认证相关异常"""

    pass


class ValidationError(WeChatMassSenderError):
    """数据验证异常"""

    pass


class ConfigurationError(WeChatMassSenderError):
    """配置相关异常"""

    pass


class SendError(WeChatMassSenderError):
    """发送相关异常"""

    pass


class TemplateError(WeChatMassSenderError):
    """模板相关异常"""

    pass


class FileError(WeChatMassSenderError):
    """文件操作异常"""

    pass


class RiskControlError(WeChatMassSenderError):
    """风控相关异常"""

    pass


class APIError(WeChatMassSenderError):
    """API调用异常"""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[dict] = None,
        error_code: Optional[str] = None,
    ):
        super().__init__(message, error_code)
        self.status_code = status_code
        self.response_data = response_data

    def __str__(self) -> str:
        base_msg = super().__str__()
        if self.status_code:
            base_msg = f"HTTP {self.status_code}: {base_msg}"
        return base_msg


class TimeoutError(WeChatMassSenderError):
    """超时异常"""

    def __init__(self, message: str, timeout_seconds: Optional[float] = None):
        super().__init__(message)
        self.timeout_seconds = timeout_seconds

    def __str__(self) -> str:
        if self.timeout_seconds:
            return f"{self.message} (超时: {self.timeout_seconds}秒)"
        return self.message


class RetryExhaustedError(WeChatMassSenderError):
    """重试次数耗尽异常"""

    def __init__(
        self, message: str, retry_count: int, last_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.retry_count = retry_count
        self.last_error = last_error

    def __str__(self) -> str:
        base_msg = f"{self.message} (重试次数: {self.retry_count})"
        if self.last_error:
            base_msg += f" 最后错误: {self.last_error}"
        return base_msg


def handle_exception(
    exception: Exception, context: str = "", user_friendly: bool = True
) -> str:
    """
    统一异常处理函数

    Args:
        exception: 异常对象
        context: 异常上下文信息
        user_friendly: 是否返回用户友好的错误信息

    Returns:
        格式化的错误信息
    """
    if isinstance(exception, WeChatMassSenderError):
        error_msg = str(exception)
    elif isinstance(exception, ConnectionRefusedError):
        error_msg = "连接被拒绝，请检查服务是否正在运行"
    elif isinstance(exception, FileNotFoundError):
        error_msg = f"文件未找到: {exception.filename}"
    elif isinstance(exception, PermissionError):
        error_msg = "权限不足，请检查文件或目录权限"
    elif isinstance(exception, ValueError):
        error_msg = f"数据格式错误: {exception}"
    elif isinstance(exception, KeyError):
        error_msg = f"缺少必要的配置项: {exception}"
    else:
        if user_friendly:
            error_msg = "发生未知错误，请查看日志获取详细信息"
        else:
            error_msg = str(exception)

    if context:
        error_msg = f"{context}: {error_msg}"

    return error_msg


def create_error_response(
    error: Exception, context: str = "", include_traceback: bool = False
) -> dict:
    """
    创建标准化的错误响应

    Args:
        error: 异常对象
        context: 错误上下文
        include_traceback: 是否包含堆栈跟踪

    Returns:
        标准化的错误响应字典
    """
    import traceback

    response = {
        "success": False,
        "error": {
            "type": type(error).__name__,
            "message": handle_exception(error, context, user_friendly=True),
            "raw_message": str(error),
        },
    }

    if isinstance(error, WeChatMassSenderError):
        response["error"]["error_code"] = error.error_code

    if isinstance(error, APIError):
        response["error"]["status_code"] = error.status_code
        response["error"]["response_data"] = error.response_data

    if include_traceback:
        response["error"]["traceback"] = traceback.format_exc()

    return response


class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self):
        self.recovery_strategies: Dict[type, List[Callable]] = {}
        self.error_history: List[Dict[str, Any]] = []
        self.max_history = 100
        
    def register_recovery_strategy(self, error_type: type, strategy: Callable):
        """注册错误恢复策略"""
        if error_type not in self.recovery_strategies:
            self.recovery_strategies[error_type] = []
        self.recovery_strategies[error_type].append(strategy)
    
    def attempt_recovery(self, error: Exception) -> bool:
        """尝试错误恢复"""
        error_type = type(error)
        
        # 记录错误
        self._record_error(error)
        
        # 查找匹配的恢复策略
        strategies = self.recovery_strategies.get(error_type, [])
        
        # 也检查基类的策略
        for base_class in error_type.__mro__[1:]:
            if base_class in self.recovery_strategies:
                strategies.extend(self.recovery_strategies[base_class])
        
        # 尝试执行恢复策略
        for strategy in strategies:
            try:
                if strategy(error):
                    return True
            except Exception as recovery_error:
                # 恢复策略本身失败，记录但不中断
                self._log_recovery_failure(strategy, recovery_error)
        
        return False
    
    def _record_error(self, error: Exception):
        """记录错误到历史"""
        error_record = {
            "timestamp": time.time(),
            "type": type(error).__name__,
            "message": str(error),
            "traceback": traceback.format_exc()
        }
        
        if isinstance(error, WeChatMassSenderError):
            error_record.update(error.to_dict())
        
        self.error_history.append(error_record)
        
        # 限制历史记录大小
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
    
    def _log_recovery_failure(self, strategy: Callable, error: Exception):
        """记录恢复策略失败"""
        from utils.logger import setup_logger
        logger = setup_logger("error_recovery")
        logger.error(f"恢复策略失败 {strategy.__name__}: {error}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        if not self.error_history:
            return {"total_errors": 0}
        
        # 按错误类型统计
        error_counts = {}
        recent_errors = []
        current_time = time.time()
        
        for error_record in self.error_history:
            error_type = error_record["type"]
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
            
            # 统计最近1小时的错误
            if current_time - error_record["timestamp"] <= 3600:
                recent_errors.append(error_record)
        
        return {
            "total_errors": len(self.error_history),
            "error_types": error_counts,
            "recent_errors_1h": len(recent_errors),
            "most_common_error": max(error_counts.items(), key=lambda x: x[1])[0] if error_counts else None
        }


# 全局错误恢复管理器
error_recovery_manager = ErrorRecoveryManager()


def auto_retry(max_retries: int = 3, delay: float = 1.0, 
               backoff_factor: float = 2.0,
               exceptions: tuple = (Exception,)):
    """自动重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        # 最后一次尝试失败，记录到错误管理器
                        error_recovery_manager._record_error(e)
                        break
                    
                    # 计算延迟时间
                    wait_time = delay * (backoff_factor ** attempt)
                    time.sleep(wait_time)
                    
                    # 记录重试信息
                    from utils.logger import setup_logger
                    logger = setup_logger("auto_retry")
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次重试，{wait_time:.2f}秒后重试")
            
            # 如果是自定义异常，更新重试次数
            if isinstance(last_exception, WeChatMassSenderError):
                if last_exception.context:
                    last_exception.context.retry_count = max_retries
            
            raise last_exception
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return=None, 
                error_callback: Optional[Callable] = None, **kwargs):
    """安全执行函数，捕获并处理异常"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        # 尝试错误恢复
        if error_recovery_manager.attempt_recovery(e):
            try:
                # 恢复成功，重新尝试
                return func(*args, **kwargs)
            except Exception as retry_error:
                e = retry_error
        
        # 调用错误回调
        if error_callback:
            try:
                error_callback(e)
            except Exception:
                pass  # 忽略回调中的错误
        
        # 记录错误
        from utils.logger import setup_logger
        logger = setup_logger("safe_execute")
        logger.error(f"安全执行失败 {func.__name__}: {e}")
        
        return default_return


# 注册一些常见的恢复策略
def _connection_recovery_strategy(error: ConnectionError) -> bool:
    """连接错误恢复策略"""
    try:
        # 简单的重连尝试
        time.sleep(2.0)  # 等待2秒
        return True
    except Exception:
        return False


def _memory_recovery_strategy(error: MemoryError) -> bool:
    """内存错误恢复策略"""
    try:
        import gc
        gc.collect()  # 强制垃圾回收
        return True
    except Exception:
        return False


# 注册默认恢复策略
error_recovery_manager.register_recovery_strategy(ConnectionError, _connection_recovery_strategy)
error_recovery_manager.register_recovery_strategy(MemoryError, _memory_recovery_strategy)
