#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强性能监控器

提供更全面的性能监控、分析和报告功能。
"""

import time
import gc
import sys
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable
from contextlib import contextmanager
import psutil

from PyQt6.QtCore import QObject, QTimer, QThread, pyqtSignal
from utils.logger import setup_logger

logger = setup_logger("enhanced_performance_monitor")


class PerformanceMonitorThread:
    """性能监控线程 - 使用Python线程系统"""

    def __init__(self, monitor, interval=5.0):
        self.monitor = monitor
        self.interval = interval
        self._stop_event = threading.Event()
        self._thread = None

    def start(self):
        """启动监控线程"""
        if self._thread is None or not self._thread.is_alive():
            self._stop_event.clear()
            self._thread = threading.Thread(target=self._run, daemon=True)
            self._thread.start()

    def _run(self):
        """运行监控循环"""
        while not self._stop_event.is_set():
            try:
                self.monitor._collect_system_metrics()
            except Exception as e:
                logger.error(f"收集系统指标失败: {e}")

            # 使用threading.Event.wait()而不是time.sleep()，避免Qt定时器冲突
            self._stop_event.wait(self.interval)

    def stop(self):
        """停止监控"""
        self._stop_event.set()
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=2.0)

    def wait(self, timeout=None):
        """等待线程结束"""
        if self._thread:
            self._thread.join(timeout=timeout)


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    value: float
    timestamp: float
    category: str = "general"
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class PerformanceAlert:
    """性能警报数据类"""
    level: str  # info, warning, error, critical
    message: str
    metric_name: str
    value: float
    threshold: float
    timestamp: float


class PerformanceThresholds:
    """性能阈值配置"""
    
    def __init__(self):
        # 时间阈值 (毫秒)
        self.execution_time = {
            "info": 50,      # 50ms - 信息级别
            "warning": 200,  # 200ms - 警告级别  
            "error": 1000,   # 1s - 错误级别
            "critical": 5000 # 5s - 严重级别
        }
        
        # 内存阈值 (MB) - 调整为更合理的值
        self.memory_usage = {
            "info": 500,     # 500MB (提高info级别阈值)
            "warning": 700,  # 700MB
            "error": 900,    # 900MB
            "critical": 1200 # 1.2GB
        }
        
        # CPU使用率阈值 (%)
        self.cpu_usage = {
            "info": 30,      # 30%
            "warning": 60,   # 60%
            "error": 80,     # 80%
            "critical": 95   # 95%
        }
        
        # 任务队列长度阈值
        self.queue_length = {
            "info": 5,
            "warning": 15,
            "error": 30,
            "critical": 50
        }


class EnhancedPerformanceMonitor(QObject):
    """增强性能监控器"""

    # 信号定义
    metrics_updated = pyqtSignal(dict)  # 指标更新信号
    alert_triggered = pyqtSignal(object)  # 警报触发信号

    def __init__(self, max_metrics_history: int = 1000):
        super().__init__()
        self.max_metrics_history = max_metrics_history
        self.metrics_history: deque = deque(maxlen=max_metrics_history)
        self.alerts_history: deque = deque(maxlen=200)  # 保留最近200个警报
        
        # 分类指标存储
        self.metrics_by_category: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 统计数据
        self.function_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'avg_time': 0.0,
            'last_call': 0.0
        })
        
        # 阈值配置
        self.thresholds = PerformanceThresholds()
        
        # 回调函数
        self.alert_callbacks: List[Callable[[PerformanceAlert], None]] = []
        
        # 监控状态
        self._monitoring_active = False
        self._monitoring_thread: Optional[PerformanceMonitorThread] = None

        logger.info("增强性能监控器初始化完成")

    def start_monitoring(self, interval: float = 5.0):
        """启动后台监控"""
        if self._monitoring_active:
            logger.warning("性能监控已在运行")
            return

        self._monitoring_active = True

        # 使用Qt线程
        self._monitoring_thread = PerformanceMonitorThread(self, interval)
        self._monitoring_thread.start()

        logger.info(f"性能监控已启动，间隔: {interval}秒")

    def stop_monitoring(self):
        """停止后台监控"""
        if not self._monitoring_active:
            return

        if self._monitoring_thread:
            self._monitoring_thread.stop()
            self._monitoring_thread.wait(2000)  # 等待最多2秒

        self._monitoring_active = False
        logger.info("性能监控已停止")

    def _collect_system_metrics(self):
        """收集系统性能指标"""
        try:
            # 内存使用情况
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            self.record_metric(
                "system.memory.rss", 
                memory_mb, 
                category="system",
                tags={"unit": "MB"}
            )
            
            # CPU使用率
            cpu_percent = process.cpu_percent()
            self.record_metric(
                "system.cpu.process", 
                cpu_percent, 
                category="system",
                tags={"unit": "percent"}
            )
            
            # 系统CPU使用率
            system_cpu = psutil.cpu_percent()
            self.record_metric(
                "system.cpu.global", 
                system_cpu, 
                category="system",
                tags={"unit": "percent"}
            )
            
            # 线程数量
            thread_count = threading.active_count()
            self.record_metric(
                "system.threads.count", 
                thread_count, 
                category="system"
            )
            
            # 垃圾回收统计
            gc_stats = gc.get_stats()
            for i, stat in enumerate(gc_stats):
                self.record_metric(
                    f"system.gc.generation_{i}.collections", 
                    stat.get('collections', 0), 
                    category="gc"
                )
            
            # 检查阈值
            self._check_thresholds(memory_mb, cpu_percent, system_cpu)
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")

    def _check_thresholds(self, memory_mb: float, process_cpu: float, system_cpu: float):
        """检查性能阈值"""
        # 检查内存阈值
        for level, threshold in self.thresholds.memory_usage.items():
            if memory_mb > threshold:
                self._emit_alert(level, f"内存使用过高: {memory_mb:.1f}MB", 
                               "system.memory.rss", memory_mb, threshold)
                break
        
        # 检查进程CPU阈值
        for level, threshold in self.thresholds.cpu_usage.items():
            if process_cpu > threshold:
                self._emit_alert(level, f"进程CPU使用率过高: {process_cpu:.1f}%", 
                               "system.cpu.process", process_cpu, threshold)
                break

    def record_metric(self, name: str, value: float, category: str = "general", 
                     tags: Optional[Dict[str, str]] = None):
        """记录性能指标"""
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=time.time(),
            category=category,
            tags=tags or {}
        )
        
        self.metrics_history.append(metric)
        self.metrics_by_category[category].append(metric)

    def record_function_call(self, func_name: str, execution_time: float):
        """记录函数调用性能"""
        stats = self.function_stats[func_name]
        stats['call_count'] += 1
        stats['total_time'] += execution_time
        stats['min_time'] = min(stats['min_time'], execution_time)
        stats['max_time'] = max(stats['max_time'], execution_time)
        stats['avg_time'] = stats['total_time'] / stats['call_count']
        stats['last_call'] = time.time()
        
        # 记录为指标
        self.record_metric(
            f"function.{func_name}.execution_time",
            execution_time * 1000,  # 转换为毫秒
            category="function",
            tags={"function": func_name, "unit": "ms"}
        )
        
        # 检查执行时间阈值
        execution_time_ms = execution_time * 1000
        for level, threshold in self.thresholds.execution_time.items():
            if execution_time_ms > threshold:
                self._emit_alert(level, f"函数执行时间过长: {func_name} {execution_time_ms:.1f}ms", 
                               f"function.{func_name}.execution_time", execution_time_ms, threshold)
                break

    def _emit_alert(self, level: str, message: str, metric_name: str, 
                   value: float, threshold: float):
        """发出性能警报"""
        alert = PerformanceAlert(
            level=level,
            message=message,
            metric_name=metric_name,
            value=value,
            threshold=threshold,
            timestamp=time.time()
        )
        
        self.alerts_history.append(alert)
        
        # 根据级别记录日志
        if level == "critical":
            logger.critical(f"性能警报 [{level.upper()}]: {message}")
        elif level == "error":
            logger.error(f"性能警报 [{level.upper()}]: {message}")
        elif level == "warning":
            logger.warning(f"性能警报 [{level.upper()}]: {message}")
        else:
            logger.info(f"性能警报 [{level.upper()}]: {message}")
        
        # 调用回调函数
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"性能警报回调执行失败: {e}")

    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]):
        """添加警报回调函数"""
        self.alert_callbacks.append(callback)

    def get_metrics_summary(self, category: Optional[str] = None, 
                          since: Optional[float] = None) -> Dict[str, Any]:
        """获取指标摘要"""
        # 筛选指标
        if category:
            metrics = list(self.metrics_by_category.get(category, []))
        else:
            metrics = list(self.metrics_history)
        
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        if not metrics:
            return {"error": "没有找到匹配的指标"}
        
        # 按名称分组统计
        stats_by_name = defaultdict(list)
        for metric in metrics:
            stats_by_name[metric.name].append(metric.value)
        
        summary = {}
        for name, values in stats_by_name.items():
            summary[name] = {
                "count": len(values),
                "min": min(values),
                "max": max(values),
                "avg": sum(values) / len(values),
                "latest": values[-1] if values else None
            }
        
        return summary

    def get_function_stats(self, func_name: Optional[str] = None) -> Dict[str, Any]:
        """获取函数调用统计"""
        if func_name:
            return self.function_stats.get(func_name, {})
        
        # 返回所有函数的统计，按平均时间排序
        sorted_stats = sorted(
            self.function_stats.items(),
            key=lambda x: x[1]['avg_time'],
            reverse=True
        )
        
        return dict(sorted_stats[:20])  # 返回最慢的20个函数

    def get_recent_alerts(self, level: Optional[str] = None, 
                         limit: int = 50) -> List[PerformanceAlert]:
        """获取最近的警报"""
        alerts = list(self.alerts_history)
        
        if level:
            alerts = [a for a in alerts if a.level == level]
        
        # 按时间倒序排列
        alerts.sort(key=lambda x: x.timestamp, reverse=True)
        
        return alerts[:limit]

    @contextmanager
    def measure_execution(self, name: str, category: str = "general"):
        """测量代码块执行时间的上下文管理器"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            yield
        finally:
            execution_time = time.time() - start_time
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_delta = end_memory - start_memory
            
            # 记录执行时间
            self.record_metric(
                f"{name}.execution_time",
                execution_time * 1000,  # 毫秒
                category=category,
                tags={"unit": "ms"}
            )
            
            # 记录内存变化
            if abs(memory_delta) > 0.1:  # 只记录明显的内存变化
                self.record_metric(
                    f"{name}.memory_delta",
                    memory_delta,
                    category=category,
                    tags={"unit": "MB"}
                )

    def cleanup(self):
        """清理资源"""
        self.stop_monitoring()
        logger.info("增强性能监控器清理完成")


# 全局增强性能监控器实例
enhanced_performance_monitor = EnhancedPerformanceMonitor()


def performance_measure(name: str = None, category: str = "function"):
    """性能测量装饰器"""
    def decorator(func):
        func_name = name or f"{func.__module__}.{func.__qualname__}"
        
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                execution_time = time.time() - start_time
                enhanced_performance_monitor.record_function_call(func_name, execution_time)
        
        return wrapper
    return decorator


# 兼容性别名
perf_measure = performance_measure