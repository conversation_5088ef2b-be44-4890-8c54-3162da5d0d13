#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet space 微信群发助手 - 加密单文件EXE构建脚本
创建内容加密但可执行的客户端程序
"""

import os
import sys
import subprocess
import shutil
import secrets
from pathlib import Path

# 项目信息
PROJECT_NAME = "MeetSpaceWeChatSender"
PROJECT_VERSION = "1.0.0"

def generate_encryption_key():
    """生成加密密钥"""
    print("🔐 生成加密密钥...")
    
    # 生成32字节的随机密钥
    key = secrets.token_hex(32)
    print(f"  ✅ 密钥生成完成: {key[:8]}...{key[-8:]}")
    return key

def create_encrypted_spec():
    """创建加密的PyInstaller配置"""
    print("📝 创建加密PyInstaller配置...")
    
    # 生成加密密钥
    encryption_key = generate_encryption_key()
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# Meet space 微信群发助手 - 加密单文件配置

import os
from pathlib import Path

# 项目根目录
project_root = Path(SPECPATH)

# 加密配置 - 使用AES加密
block_cipher = pyi_crypto.PyiBlockCipher(key='{encryption_key}')

# 最小化数据文件
datas = [
    # 应用图标
    ('resources/icons/app_icon.ico', 'resources/icons'),
    # 版本信息
    ('version_info.txt', '.'),
]

# 核心隐藏导入
hiddenimports = [
    # PyQt6核心
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.QtSvg',
    
    # 微信接口
    'wcferry',
    
    # 数据处理
    'pandas',
    'openpyxl',
    'requests',
    'aiohttp',
    
    # 图像处理
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    
    # 加密和安全
    'cryptography',
    'cryptography.fernet',
    'hashlib',
    'base64',
    
    # 配置和工具
    'yaml',
    'json',
    'datetime',
    'pathlib',
    'psutil',
    'tempfile',
    'uuid',
    
    # 业务模块 - 所有核心模块
    'config',
    'config.settings',
    'config.wechat_config',
    'core',
    'core.wechatferry_connector',
    'core.http_api_connector',
    'core.timing_sender',
    'core.loop_sender',
    'core.send_monitor',
    'core.risk_control',
    'core.group_manager',
    'core.message_template',
    'core.config_manager',
    'core.message_sender_core',
    'ui',
    'ui.main_window',
    'ui.timing_send_page',
    'ui.loop_send_page',
    'ui.task_status_page',
    'ui.modern_theme_manager',
    'ui.rich_text_editor',
    'ui.themed_dialog_base',
    'ui.themed_message_box',
    'ui.widgets',
    'ui.widgets.contact_selector',
    'ui.widgets.group_list_widget',
    'ui.widgets.loop_cycle_widget',
    'ui.widgets.message_preview',
    'ui.widgets.send_progress_widget',
    'ui.widgets.task_item_widget',
    'utils',
    'utils.logger',
    'utils.path_manager',
    'utils.performance_optimizer',
    'utils.icon_manager',
]

# 排除不需要的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy.testing',
    'pytest',
    'setuptools',
    'distutils',
    'test',
    'tests',
    'unittest',
    'doctest',
    'pdb',
    'pydoc',
]

# 二进制文件
binaries = []

# 主程序分析
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,  # 启用加密
    noarchive=False,
)

# 过滤和优化
def optimize_toc(toc):
    """优化TOC，移除不必要的文件"""
    optimized = []
    exclude_patterns = [
        'test_', '_test', '.pyc', '__pycache__',
        '.git', 'examples', 'docs', 'tests',
        'LICENSE', 'COPYING', 'AUTHORS',
        '.md', '.rst', '.txt',
    ]
    
    for name, path, typecode in toc:
        should_exclude = False
        name_lower = name.lower()
        path_lower = path.lower()
        
        for pattern in exclude_patterns:
            if pattern in name_lower or pattern in path_lower:
                should_exclude = True
                break
        
        if not should_exclude:
            optimized.append((name, path, typecode))
    
    return optimized

# 应用优化
a.binaries = optimize_toc(a.binaries)
a.datas = optimize_toc(a.datas)

# PYZ归档 - 加密
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 单文件EXE - 完全加密
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{PROJECT_NAME}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 移除调试信息
    upx=True,   # UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='resources/icons/app_icon.ico',
    # 单文件模式
    onefile=True,
)
'''
    
    spec_file = Path(f"{PROJECT_NAME}_Encrypted.spec")
    with open(spec_file, "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print(f"  ✅ 已创建加密配置: {spec_file}")
    return spec_file, encryption_key

def install_crypto_dependencies():
    """安装加密依赖"""
    print("📦 检查加密依赖...")
    
    required_packages = [
        'cryptography',
        'pycryptodome',
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}: 已安装")
        except ImportError:
            print(f"  📥 安装 {package}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"  ✅ {package}: 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"  ❌ {package}: 安装失败 - {e}")
                return False
    
    return True

def build_encrypted_exe():
    """构建加密EXE"""
    print("🔨 构建加密单文件EXE...")
    
    # 1. 安装加密依赖
    if not install_crypto_dependencies():
        return False
    
    # 2. 创建加密spec文件
    spec_file, encryption_key = create_encrypted_spec()
    
    # 3. 清理之前的构建
    print("  🧹 清理之前的构建...")
    for cleanup_dir in ["build", "dist"]:
        if Path(cleanup_dir).exists():
            shutil.rmtree(cleanup_dir)
            print(f"    ✅ 已清理: {cleanup_dir}")
    
    # 4. 运行PyInstaller
    print("  🔐 运行加密构建...")
    cmd = [
        "pyinstaller",
        "--clean",
        "--noconfirm",
        "--key", encryption_key,  # 传递加密密钥
        str(spec_file)
    ]
    
    print(f"  📋 执行加密构建...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("  ✅ 加密构建成功")
        
        # 检查输出文件
        exe_file = Path("dist") / f"{PROJECT_NAME}.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"  ✅ 加密EXE生成成功: {exe_file}")
            print(f"  📊 文件大小: {size_mb:.1f} MB")
            print(f"  🔐 内容已加密，密钥: {encryption_key[:16]}...")
            
            # 保存密钥信息
            key_file = Path("encryption_info.txt")
            with open(key_file, "w", encoding="utf-8") as f:
                f.write(f"# Meet space 微信群发助手 - 加密信息\\n")
                f.write(f"# 生成时间: {datetime.now()}\\n")
                f.write(f"# 加密密钥: {encryption_key}\\n")
                f.write(f"# 文件大小: {size_mb:.1f} MB\\n")
                f.write(f"# 注意: 此文件包含敏感信息，请妥善保管\\n")
            
            print(f"  📄 加密信息已保存: {key_file}")
            return True
        else:
            print("  ❌ 未找到生成的EXE文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"  ❌ 加密构建失败: {e}")
        if e.stdout:
            print(f"  标准输出: {e.stdout}")
        if e.stderr:
            print(f"  错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🔐 Meet space 微信群发助手 - 加密客户端构建")
    print("=" * 60)
    
    # 导入datetime
    from datetime import datetime
    
    # 检查环境
    print("🔍 检查构建环境...")
    
    # 检查PyInstaller
    try:
        result = subprocess.run(["pyinstaller", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"  ✅ PyInstaller: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  ❌ PyInstaller未安装")
        print("  请运行: pip install pyinstaller")
        return False
    
    # 检查必要文件
    required_files = [
        "main.py",
        "version_info.txt",
        "resources/icons/app_icon.ico"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"  ✅ 找到文件: {file_path}")
    
    if missing_files:
        print(f"  ❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 构建加密EXE
    if not build_encrypted_exe():
        return False
    
    print("\\n🎉 加密客户端构建完成！")
    print(f"📁 输出文件: dist/{PROJECT_NAME}.exe")
    print("\\n🔐 安全特性:")
    print("  ✅ 完全加密的单文件客户端")
    print("  ✅ 源代码和资源文件加密保护")
    print("  ✅ 运行时动态解密执行")
    print("  ✅ 防逆向工程保护")
    print("  ✅ 便携式，无需安装")
    print("\\n⚠️  重要提醒:")
    print("  🔑 加密密钥已保存到 encryption_info.txt")
    print("  📄 请妥善保管密钥文件")
    print("  🚫 不要泄露加密信息")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\\n按回车键退出...")
    sys.exit(0 if success else 1)
