"""
循环发送页面

实现循环发送功能的用户界面。
"""

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QDialog,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QListWidget,
    QMessageBox,
    QPushButton,
    QScrollArea,
    QSpinBox,
    QTextEdit,
    QVBoxLayout,
    QWidget,
    QStackedWidget,
)

from core.group_manager import ContactGroup, GroupMember, group_manager
from core.loop_sender import loop_sender
from ui.widgets.group_card import GroupCard, NewGroupCard
from ui.widgets.loop_cycle_widget import LoopCycleWidget
from ui.widgets.button_state_manager import (
    create_loop_button_manager,
    ButtonType,
    map_task_status_to_enum,
)
from ui.group_config_manager import GroupConfigManager
from ui.themed_message_box import ThemedMessageBoxHelper
from ui.themed_dialog_base import ThemedDialogBase
from utils.logger import setup_logger

logger = setup_logger("loop_send_page")


class LoopGroupEditDialog(ThemedDialogBase):
    """循环分组编辑对话框"""

    group_saved = pyqtSignal()

    def __init__(self, group_id: str = None, parent=None):
        super().__init__(parent)
        self.group_id = group_id
        self.group = None
        self.is_edit_mode = group_id is not None

        if self.is_edit_mode:
            self.group = group_manager.get_group(group_id, "loop")

        self.setup_ui()
        self.setWindowTitle("编辑循环分组" if self.is_edit_mode else "新建循环分组")
        self.setModal(True)
        self.resize(400, 300)

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 分组名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("分组名称:"))
        self.name_edit = QLineEdit()
        if self.group:
            self.name_edit.setText(self.group.name)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)

        # 成员管理
        members_group = QGroupBox("成员管理")
        members_layout = QVBoxLayout(members_group)

        # 成员列表
        self.members_list = QListWidget()
        self.members_list.setMaximumHeight(150)
        members_layout.addWidget(self.members_list)

        # 成员操作按钮
        members_btn_layout = QHBoxLayout()
        self.add_member_btn = QPushButton("添加成员")
        self.remove_member_btn = QPushButton("移除成员")
        self.add_member_btn.clicked.connect(self.add_members)
        self.remove_member_btn.clicked.connect(self.remove_selected_members)
        members_btn_layout.addWidget(self.add_member_btn)
        members_btn_layout.addWidget(self.remove_member_btn)
        members_btn_layout.addStretch()
        members_layout.addLayout(members_btn_layout)

        layout.addWidget(members_group)

        # 标签
        tags_layout = QHBoxLayout()
        tags_layout.addWidget(QLabel("标签:"))
        self.tags_edit = QLineEdit()
        if self.group:
            self.tags_edit.setText(", ".join(self.group.tags))
        self.tags_edit.setPlaceholderText("用逗号分隔多个标签")
        tags_layout.addWidget(self.tags_edit)
        layout.addLayout(tags_layout)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.cancel_btn = QPushButton("取消")
        self.save_btn = QPushButton("保存")

        self.cancel_btn.clicked.connect(self.close)
        self.save_btn.clicked.connect(self.save_group)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.save_btn)
        layout.addLayout(button_layout)

        # 加载现有成员
        if self.group:
            self.load_members()

    def load_members(self):
        """加载成员列表"""
        if not self.group:
            return

        self.members_list.clear()
        for member in self.group.members:
            item_text = f"{member.name} ({member.wxid})"
            self.members_list.addItem(item_text)

    def add_members(self):
        """添加成员"""
        try:
            from ui.member_selection_dialog import MemberSelectionDialog

            # 获取连接器
            main_window = self.get_main_window()
            if not main_window or not hasattr(main_window, 'connector') or not main_window.connector:
                from ui.themed_message_box import ThemedMessageBoxHelper
                ThemedMessageBoxHelper.show_warning(self, "警告", "请先连接微信")
                return

            existing_members = self.group.members if self.group else []
            dialog = MemberSelectionDialog(main_window.connector, existing_members, self)
            dialog.members_selected.connect(self.on_members_selected)
            dialog.exec()

        except Exception as e:
            logger.error(f"打开成员选择对话框失败: {e}")
            from ui.themed_message_box import ThemedMessageBoxHelper
            ThemedMessageBoxHelper.show_error(self, "错误", f"打开成员选择失败:\n{e}")

    def get_main_window(self):
        """获取主窗口"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'connector'):
                return parent
            parent = parent.parent()
        return None

    def on_members_selected(self, members):
        """成员选择完成"""
        if not self.group:
            # 创建临时分组对象
            from core.group_manager import ContactGroup
            self.group = ContactGroup("", "loop")

        self.group.members = members
        self.load_members()

    def remove_selected_members(self):
        """移除选中的成员"""
        current_row = self.members_list.currentRow()
        if current_row >= 0 and self.group:
            self.group.members.pop(current_row)
            self.load_members()

    def save_group(self):
        """保存分组"""
        name = self.name_edit.text().strip()
        if not name:
            from ui.themed_message_box import ThemedMessageBoxHelper
            ThemedMessageBoxHelper.show_warning(self, "警告", "请输入分组名称")
            return

        members = self.group.members if self.group else []
        tags = [tag.strip() for tag in self.tags_edit.text().split(",") if tag.strip()]

        try:
            if self.is_edit_mode:
                # 更新现有分组
                self.group.name = name
                self.group.members = members
                self.group.tags = tags
                group_manager.save_groups("loop")
            else:
                # 创建新分组
                group = group_manager.create_group(name, "loop")
                group.members = members
                group.tags = tags
                group_manager.save_groups("loop")

            self.group_saved.emit()
            self.close()

        except Exception as e:
            from ui.themed_message_box import ThemedMessageBoxHelper
            ThemedMessageBoxHelper.show_error(self, "错误", f"保存分组失败: {e}")


class LoopSendPage(QWidget):
    """循环发送页面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_selected_group = None  # 单选模式
        self.group_cards = {}

        # 移除时间规则配置，使用循环间隔设置即可

        # 按钮状态管理器
        self.button_manager = create_loop_button_manager()

        # 获取连接器
        self.connector = None
        try:
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, "connector"):
                self.connector = main_window.connector
                logger.info(
                    f"循环发送页面获取到连接器: {type(self.connector).__name__}"
                )
            else:
                # 尝试从父窗口获取连接器
                parent = self.parent()
                while parent:
                    if hasattr(parent, "connector") and parent.connector:
                        self.connector = parent.connector
                        logger.info(
                            f"从父窗口获取到连接器: {type(self.connector).__name__}"
                        )
                        break
                    parent = parent.parent()

                if not self.connector:
                    logger.warning("无法获取连接器，将在后续设置")
        except Exception as e:
            logger.warning(f"无法获取主窗口连接器: {e}")

        # 初始化配置管理器
        from ui.group_config_manager import GroupConfigManager

        self.config_manager = GroupConfigManager()

        self.setup_ui()
        self.refresh_groups()

        # 连接信号
        loop_sender.task_started.connect(self.on_task_started)
        loop_sender.task_progress.connect(self.on_task_progress)
        loop_sender.task_cycle_completed.connect(self.on_task_cycle_completed)
        loop_sender.task_paused.connect(self.on_task_paused)
        loop_sender.task_stopped.connect(self.on_task_stopped)
        loop_sender.task_error.connect(self.on_task_error)

    def get_main_window(self):
        """获取主窗口"""
        widget = self
        while widget:
            if hasattr(widget, "connector"):
                return widget
            widget = widget.parent()
        return None

    def set_connector(self, connector):
        """设置连接器"""
        self.connector = connector
        logger.info(f"循环发送页面连接器已设置: {type(self.connector).__name__}")

        # 同时设置给循环发送器
        if hasattr(loop_sender, "set_connector"):
            loop_sender.set_connector(connector)

    def setup_ui(self):
        """设置UI"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(10)

        # 左侧：分组管理区域
        left_widget = QWidget()
        left_widget.setMaximumWidth(600)
        left_layout = QVBoxLayout(left_widget)

        # 分组管理标题和批量操作
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("循环发送分组管理"))
        header_layout.addStretch()

        left_layout.addLayout(header_layout)

        # 分组卡片滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.cards_widget = QWidget()
        self.cards_layout = QVBoxLayout(self.cards_widget)
        self.cards_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        scroll_area.setWidget(self.cards_widget)
        left_layout.addWidget(scroll_area)

        main_layout.addWidget(left_widget)

        # 右侧：循环设置区域
        right_widget = QGroupBox("循环发送设置")
        right_layout = QVBoxLayout(right_widget)

        # 当前分组显示
        self.current_group_header = QLabel("📁 请选择一个分组进行配置")
        self.current_group_header.setStyleSheet(
            """
            font-weight: bold;
            font-size: 14px;
            color: #007acc;
            padding: 8px;
            border-bottom: 1px solid #007acc;
            margin-bottom: 8px;
        """
        )
        right_layout.addWidget(self.current_group_header)

        # 配置区域容器
        self.config_container = QWidget()
        config_container_layout = QVBoxLayout(self.config_container)

        # 未选择分组时的提示
        self.no_selection_widget = QWidget()
        no_selection_layout = QVBoxLayout(self.no_selection_widget)
        no_selection_layout.addStretch()

        tip_label = QLabel("👈 请在左侧选择一个分组")
        tip_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tip_label.setStyleSheet(
            """
            font-size: 16px;
            color: #666;
            padding: 20px;
        """
        )
        no_selection_layout.addWidget(tip_label)

        tip_detail = QLabel(
            "选择分组后，您可以配置：\n• 消息模板和内容\n• 循环间隔设置\n• 高级循环设置"
        )
        tip_detail.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tip_detail.setStyleSheet(
            """
            font-size: 11px;
            color: #888;
            line-height: 1.4;
        """
        )
        no_selection_layout.addWidget(tip_detail)
        no_selection_layout.addStretch()

        config_container_layout.addWidget(self.no_selection_widget)

        # 配置表单区域（初始隐藏）
        self.config_form_widget = QWidget()
        self.config_form_widget.setVisible(False)
        config_form_layout = QVBoxLayout(self.config_form_widget)

        # 发送设置
        send_settings_group = QGroupBox("发送设置")
        send_settings_layout = QVBoxLayout(send_settings_group)

        # 发送间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("发送间隔:"))
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setObjectName("loopSendIntervalSpinBox")  # 设置对象名称用于样式
        self.interval_spinbox.setMinimum(1)
        self.interval_spinbox.setMaximum(300)
        self.interval_spinbox.setValue(5)
        self.interval_spinbox.setSuffix(" 秒")
        self.interval_spinbox.valueChanged.connect(self.on_interval_changed)
        interval_layout.addWidget(self.interval_spinbox)
        interval_layout.addStretch()
        send_settings_layout.addLayout(interval_layout)

        # 批量大小
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("批量大小:"))
        self.batch_spinbox = QSpinBox()
        self.batch_spinbox.setObjectName("loopSendBatchSpinBox")  # 设置对象名称用于样式
        self.batch_spinbox.setMinimum(1)
        self.batch_spinbox.setMaximum(100)
        self.batch_spinbox.setValue(10)
        self.batch_spinbox.setSuffix(" 人/批")
        self.batch_spinbox.valueChanged.connect(self.on_batch_size_changed)
        batch_layout.addWidget(self.batch_spinbox)
        batch_layout.addStretch()
        send_settings_layout.addLayout(batch_layout)

        # 风控设置
        risk_layout = QHBoxLayout()
        self.use_system_risk_cb = QCheckBox("使用系统风控设置")
        self.use_system_risk_cb.setChecked(True)
        self.use_system_risk_cb.stateChanged.connect(self.on_risk_control_changed)
        risk_layout.addWidget(self.use_system_risk_cb)
        risk_layout.addStretch()
        send_settings_layout.addLayout(risk_layout)

        config_form_layout.addWidget(send_settings_group)

        # 消息模板选择
        template_layout = QHBoxLayout()
        template_layout.addWidget(QLabel("消息模板:"))
        self.template_combo = QComboBox()
        self.template_combo.addItem("手动输入", "")
        self.load_templates()
        self.template_combo.currentTextChanged.connect(self.on_template_changed)
        template_layout.addWidget(self.template_combo)
        template_layout.addStretch()
        config_form_layout.addLayout(template_layout)

        # 消息类型选择
        message_type_layout = QHBoxLayout()
        message_type_layout.addWidget(QLabel("消息类型:"))
        self.message_type_combo = QComboBox()
        self.message_type_combo.addItems(["富文本消息", "文本消息"])
        self.message_type_combo.currentTextChanged.connect(self.on_message_type_changed)
        message_type_layout.addWidget(self.message_type_combo)
        message_type_layout.addStretch()
        config_form_layout.addLayout(message_type_layout)

        # 消息编辑器堆栈
        self.message_editor_stack = QStackedWidget()

        # 富文本编辑器
        from ui.rich_text_editor import RichTextMessageEditor

        self.rich_text_editor = RichTextMessageEditor()
        self.rich_text_editor.content_changed.connect(self.on_message_content_changed)
        self.message_editor_stack.addWidget(self.rich_text_editor)

        # 传统文本编辑器
        self.message_edit = QTextEdit()
        self.message_edit.setMaximumHeight(150)
        self.message_edit.setPlaceholderText("请输入要循环发送的消息内容...")
        self.message_edit.textChanged.connect(self.on_message_content_changed)
        self.message_editor_stack.addWidget(self.message_edit)

        config_form_layout.addWidget(QLabel("消息内容:"))
        config_form_layout.addWidget(self.message_editor_stack)

        # 任务控制按钮
        buttons_layout = QHBoxLayout()

        # 保存配置按钮
        self.save_config_btn = QPushButton("💾 保存配置")
        self.save_config_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """
        )
        self.save_config_btn.clicked.connect(self.save_loop_config)
        self.save_config_btn.setEnabled(False)
        buttons_layout.addWidget(self.save_config_btn)

        # 创建/启动按钮
        self.create_btn = QPushButton("🔄 创建循环任务")
        self.create_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """
        )
        self.create_btn.clicked.connect(self.create_loop_task)
        self.create_btn.setEnabled(False)
        buttons_layout.addWidget(self.create_btn)

        # 暂停/继续按钮
        self.pause_btn = QPushButton("⏸️ 暂停")
        self.pause_btn.setEnabled(False)
        self.pause_btn.clicked.connect(self.toggle_pause_task)
        buttons_layout.addWidget(self.pause_btn)

        # 停止按钮
        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_task)
        buttons_layout.addWidget(self.stop_btn)

        # 取消按钮
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.clicked.connect(self.cancel_task)
        buttons_layout.addWidget(self.cancel_btn)

        # 注册按钮到状态管理器
        self.button_manager.register_button(ButtonType.PRIMARY, self.create_btn)
        self.button_manager.register_button(ButtonType.PAUSE, self.pause_btn)
        self.button_manager.register_button(ButtonType.STOP, self.stop_btn)
        self.button_manager.register_button(ButtonType.CANCEL, self.cancel_btn)

        buttons_layout.addStretch()

        config_form_layout.addLayout(buttons_layout)

        # 循环间隔设置组件
        self.loop_cycle_widget = LoopCycleWidget("循环间隔设置")
        self.loop_cycle_widget.cycle_enabled_changed.connect(
            self.on_loop_cycle_enabled_changed
        )
        self.loop_cycle_widget.config_changed.connect(self.on_loop_cycle_config_changed)
        config_form_layout.addWidget(self.loop_cycle_widget)

        # 移除时间规则配置，使用循环间隔设置即可
        # 这样避免功能重复和用户混淆

        # 将配置表单添加到容器
        config_container_layout.addWidget(self.config_form_widget)

        # 将容器添加到右侧布局
        right_layout.addWidget(self.config_container)
        right_layout.addStretch()

        main_layout.addWidget(right_widget)

    def refresh_groups(self):
        """刷新分组显示"""
        # 清空现有卡片
        for card in self.group_cards.values():
            card.setParent(None)
        self.group_cards.clear()

        # 清空布局中的所有widget
        while self.cards_layout.count():
            child = self.cards_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

        # 获取排序后的分组
        groups = group_manager.get_sorted_groups("loop")

        # 添加新建分组卡片
        new_card = NewGroupCard()
        new_card.create_group_requested.connect(self.create_new_group)
        self.cards_layout.addWidget(new_card)

        # 添加分组卡片
        for group in groups:
            card = GroupCard(group, "loop")
            card.start_requested.connect(self.start_group_task)
            card.stop_requested.connect(self.stop_group_task)
            card.pause_requested.connect(self.pause_group_task)
            card.delete_requested.connect(self.delete_group)
            card.details_requested.connect(self.show_group_details)
            card.group_chosen.connect(self.choose_group)

            self.group_cards[group.group_id] = card
            self.cards_layout.addWidget(card)

            # 更新按钮状态
            self.update_group_card_button_states(card, group.group_id)

        logger.info(f"刷新循环发送分组，共{len(groups)}个分组")

    def load_templates(self):
        """加载消息模板"""
        try:
            from core.message_template import MessageTemplate

            template_manager = MessageTemplate()
            templates = template_manager.get_all_templates()

            # 清空现有选项（保留"手动输入"）
            while self.template_combo.count() > 1:
                self.template_combo.removeItem(1)

            # 添加模板选项
            for template in templates:
                self.template_combo.addItem(template.name, template.id)

            logger.info(f"加载了 {len(templates)} 个消息模板")

        except Exception as e:
            logger.error(f"加载消息模板失败: {e}")

    def create_new_group(self):
        """创建新分组"""
        dialog = LoopGroupEditDialog(parent=self)
        dialog.group_saved.connect(self.refresh_groups)
        dialog.exec()

    def edit_group(self, group_id: str):
        """编辑分组"""
        dialog = LoopGroupEditDialog(group_id, parent=self)
        dialog.group_saved.connect(self.refresh_groups)
        dialog.exec()

    def delete_group(self, group_id: str):
        """删除分组"""
        group = group_manager.get_group(group_id, "loop")
        if not group:
            return

        # 检查是否有运行中的任务
        tasks = loop_sender.get_tasks_by_group(group_id)
        running_tasks = [t for t in tasks if t.status == "running"]

        if running_tasks:
            ThemedMessageBoxHelper.show_warning(
                self,
                "无法删除",
                f"分组 '{group.name}' 有正在运行的循环任务，请先停止任务再删除分组。",
            )
            return

        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要删除分组 '{group.name}' 吗？\n此操作将同时删除相关的循环任务！",
        )

        if reply:
            # 删除相关任务
            for task in tasks:
                loop_sender.delete_task(task.task_id)

            # 删除分组
            if group_manager.delete_group(group_id, "loop"):
                self.refresh_groups()
                logger.info("分组删除成功")

    def delete_group(self, group_id: str):
        """删除分组"""
        group = group_manager.get_group(group_id, "loop")
        if not group:
            return

        reply = ThemedMessageBoxHelper.show_question(
            self, "确认删除", f"确定要删除分组 '{group.name}' 吗？\n\n此操作不可撤销。"
        )

        if reply:
            if group_manager.delete_group(group_id, "loop"):
                self.refresh_groups()
                logger.info(f"删除分组: {group.name}")
            else:
                logger.error("删除分组失败")

    def show_group_details(self, group_id: str):
        """显示分组详情"""
        try:
            from ui.group_details_dialog import GroupDetailsDialog

            dialog = GroupDetailsDialog(group_id, "loop", None, self)
            dialog.group_updated.connect(self.refresh_groups)
            dialog.exec()
        except Exception as e:
            logger.error(f"显示分组详情失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"显示分组详情失败:\n{e}")

    def choose_group(self, group_id: str):
        """选择分组进行设置（单选模式）"""
        # 如果已经选中了这个分组，不需要重复操作
        if self.current_selected_group == group_id:
            return

        # 更新所有分组卡片的选中状态（单选）
        for card_group_id, card in self.group_cards.items():
            if hasattr(card, "set_selected"):
                card.set_selected(card_group_id == group_id)

        # 使用配置管理器切换分组
        if self.config_manager.switch_group(group_id, "loop"):
            self.current_selected_group = group_id
            group = group_manager.get_group(group_id, "loop")
            if group:
                # 更新标题显示
                self.current_group_header.setText(f"📁 {group.name} 的设置")

                # 显示配置表单，隐藏提示
                self.no_selection_widget.setVisible(False)
                self.config_form_widget.setVisible(True)

                # 设置按钮管理器的分组选择状态
                self.button_manager.set_selected_group(True)

                # 启用保存配置按钮
                self.save_config_btn.setEnabled(True)

                # 加载分组配置到UI
                self.load_group_config_to_ui()

                # 更新按钮状态
                self.update_button_states("idle")

                logger.info(f"选择循环发送分组: {group.name} ({group_id})")

    def start_group_task(self, group_id: str):
        """启动分组任务"""
        try:
            tasks = loop_sender.get_tasks_by_group(group_id)
            if tasks:
                task = tasks[0]
                if task.status == "paused":
                    loop_sender.resume_task(task.task_id)
                else:
                    loop_sender.start_task(task.task_id)
                self.refresh_group_card(group_id)
            else:
                # 如果没有任务，尝试创建任务
                if self.current_selected_group == group_id:
                    self.create_loop_task()
        except Exception as e:
            logger.error(f"启动分组任务失败: {e}")
            self.button_manager.show_error_dialog(self, "错误", f"启动任务失败:\n{e}")

    def pause_group_task(self, group_id: str):
        """暂停分组任务"""
        try:
            tasks = loop_sender.get_tasks_by_group(group_id)
            if tasks:
                loop_sender.pause_task(tasks[0].task_id)
                self.refresh_group_card(group_id)
            else:
                logger.info("没有可暂停的循环任务")
        except Exception as e:
            logger.error(f"暂停分组任务失败: {e}")
            self.button_manager.show_error_dialog(self, "错误", f"暂停任务失败:\n{e}")

    def stop_group_task(self, group_id: str):
        """停止分组任务"""
        try:
            tasks = loop_sender.get_tasks_by_group(group_id)
            if tasks:
                loop_sender.stop_task(tasks[0].task_id)
                self.refresh_group_card(group_id)
            else:
                logger.info("没有运行中的循环任务")
        except Exception as e:
            logger.error(f"停止分组任务失败: {e}")
            self.button_manager.show_error_dialog(self, "错误", f"停止任务失败:\n{e}")

    def refresh_group_card(self, group_id: str):
        """刷新指定分组卡片（包含按钮状态更新）"""
        if group_id in self.group_cards:
            self.group_cards[group_id].refresh()
            # 同时更新按钮状态
            self.update_group_card_button_states(self.group_cards[group_id], group_id)

    def load_group_config_to_ui(self):
        """加载分组配置到UI"""
        if not self.current_selected_group:
            return

        try:
            # 获取当前分组对象，从分组独立配置中加载设置
            from core.group_manager import group_manager
            group = group_manager.get_group(self.current_selected_group, "loop")

            if not group:
                logger.error(f"分组不存在: {self.current_selected_group}")
                return

            # 加载发送设置（从分组独立配置中加载）
            interval = group.get_config("send_settings.interval_seconds", 5)
            self.interval_spinbox.setValue(interval)

            batch_size = group.get_config("send_settings.batch_size", 10)
            self.batch_spinbox.setValue(batch_size)

            use_system_risk = group.get_config("send_settings.use_system_risk_control", True)
            self.use_system_risk_cb.setChecked(use_system_risk)

            # 加载消息设置（从分组独立配置中加载）
            template_id = group.get_config("message_settings.template_id", "")
            if template_id:
                # 设置模板选择
                for i in range(self.template_combo.count()):
                    if self.template_combo.itemData(i) == template_id:
                        self.template_combo.setCurrentIndex(i)
                        break
            else:
                # 没有模板时设置为默认
                self.template_combo.setCurrentIndex(0)

            # 加载消息类型（从分组独立配置中加载）
            declared_type = group.get_config("message_settings.default_type", "text")
            default_content = group.get_config("message_settings.default_content", "")

            # 智能检测消息类型（防止类型和内容不匹配）
            actual_type = self._detect_message_type(default_content, declared_type)

            # 暂时断开信号连接，避免触发保存
            self.rich_text_editor.content_changed.disconnect()
            self.message_edit.textChanged.disconnect()

            if actual_type == "rich_text":
                # 设置为富文本消息
                self.message_type_combo.setCurrentText("富文本消息")
                self.message_editor_stack.setCurrentWidget(self.rich_text_editor)

                # 加载富文本内容（已经获取过了）
                if default_content:
                    try:
                        import json
                        if isinstance(default_content, str):
                            content_data = json.loads(default_content)
                        else:
                            content_data = default_content

                        # 加载富文本内容到编辑器
                        self.rich_text_editor.load_template_content(content_data)
                        logger.info(f"加载分组 {group.name} 的富文本内容")
                    except Exception as e:
                        logger.error(f"加载富文本内容失败: {e}")
                        # 如果解析失败，清空编辑器
                        self.rich_text_editor.clear_content()
                else:
                    # 没有内容时清空编辑器
                    self.rich_text_editor.clear_content()
            else:
                # 设置为文本消息
                self.message_type_combo.setCurrentText("文本消息")
                self.message_editor_stack.setCurrentWidget(self.message_edit)

                # 加载文本内容（已经获取过了）
                if isinstance(default_content, str):
                    self.message_edit.setPlainText(default_content)
                else:
                    # 如果是富文本格式，提取纯文本
                    try:
                        import json
                        if isinstance(default_content, dict):
                            content_data = default_content
                        else:
                            content_data = json.loads(str(default_content))
                        text_content = content_data.get("plain_text", "")
                        self.message_edit.setPlainText(text_content)
                    except:
                        self.message_edit.setPlainText("")

            # 重新连接信号
            self.rich_text_editor.content_changed.connect(self.on_message_content_changed)
            self.message_edit.textChanged.connect(self.on_message_content_changed)

            # 加载循环设置
            loop_cycle_enabled = self.config_manager.get_config(
                "loop_cycle.enabled", False
            )
            self.loop_cycle_widget.set_cycle_enabled(loop_cycle_enabled)

            if loop_cycle_enabled:
                loop_config = self.config_manager.get_config("loop_cycle.config", {})
                if loop_config:
                    self.loop_cycle_widget.set_cycle_config(loop_config)

            # 移除时间规则加载，使用循环间隔设置即可

            logger.info(f"加载循环发送分组配置到UI: {self.current_selected_group}")

        except Exception as e:
            logger.error(f"加载循环发送分组配置失败: {e}")

    def _detect_message_type(self, content, declared_type):
        """
        智能检测消息类型

        Args:
            content: 消息内容
            declared_type: 声明的类型

        Returns:
            实际的消息类型 ("text" 或 "rich_text")
        """
        try:
            # 如果内容为空，使用声明的类型
            if not content:
                return declared_type

            # 如果内容是字符串，尝试解析为JSON
            if isinstance(content, str):
                # 检查是否是JSON格式的富文本内容
                if content.strip().startswith('{"type": "rich_text"'):
                    try:
                        import json
                        json.loads(content)  # 验证JSON格式
                        logger.debug("检测到富文本JSON格式内容")
                        return "rich_text"
                    except json.JSONDecodeError:
                        logger.warning("内容看起来像富文本JSON但解析失败，当作普通文本处理")
                        return "text"
                else:
                    # 普通字符串内容
                    return "text"

            # 如果内容是字典格式
            elif isinstance(content, dict):
                if content.get("type") == "rich_text":
                    logger.debug("检测到富文本字典格式内容")
                    return "rich_text"
                else:
                    return "text"

            # 其他情况使用声明的类型
            else:
                logger.debug(f"未知内容格式，使用声明类型: {declared_type}")
                return declared_type

        except Exception as e:
            logger.error(f"检测消息类型失败: {e}")
            return declared_type

    def create_loop_task(self):
        """创建循环任务"""
        if not self.current_selected_group:
            self.button_manager.show_warning_dialog(self, "警告", "请先选择分组")
            return

        # 检查分组成员
        group = group_manager.get_group(self.current_selected_group, "loop")
        if not group or not group.members:
            self.button_manager.show_warning_dialog(
                self, "警告", "选中的分组没有成员，无法创建循环任务"
            )
            return

        # 根据消息类型获取内容
        message_type = self.message_type_combo.currentText()
        if message_type == "富文本消息":
            message_data = self.rich_text_editor.get_message_content()
            # 获取按顺序的发送部分
            send_parts = self.rich_text_editor.get_send_parts()
            message_data["send_parts"] = send_parts

            # 记录发送顺序信息
            logger.info(f"循环任务富文本消息发送顺序:")
            for i, (text, image) in enumerate(send_parts):
                if text:
                    logger.info(f"  第{i+1}部分: 文字 ({len(text)}字) - {text[:30]}...")
                else:
                    import os

                    image_name = os.path.basename(image) if image else "unknown"
                    logger.info(f"  第{i+1}部分: 图片 ({image_name})")

            # 富文本消息需要将整个数据序列化为JSON
            import json

            message_content = json.dumps(message_data, ensure_ascii=False)
            message_type = "rich_text"

            # 使用用户友好的格式记录日志
            from ui.rich_text_editor import RichTextMessageEditor
            display_content = RichTextMessageEditor.format_rich_text_for_display(message_data)
            logger.info(f"循环任务富文本消息: {display_content}")
            logger.debug(f"循环任务富文本消息内容长度: {len(message_content)}")
        else:
            message_content = self.message_edit.toPlainText().strip()
            message_type = "text"

        # 验证消息内容
        if message_type == "rich_text":
            # 富文本消息验证：检查是否有实际内容
            if not self.rich_text_editor.has_content():
                self.button_manager.show_warning_dialog(self, "警告", "请输入消息内容")
                return
        else:
            # 普通文本消息验证
            if not message_content:
                self.button_manager.show_warning_dialog(self, "警告", "请输入消息内容")
                return

        # 获取循环间隔设置
        if self.loop_cycle_widget.is_cycle_enabled():
            # 验证循环配置
            is_valid, error_msg = self.loop_cycle_widget.validate_config()
            if not is_valid:
                self.button_manager.show_warning_dialog(self, "配置错误", error_msg)
                return

            # 使用配置的循环间隔（小时转分钟）
            cycle_config = self.loop_cycle_widget.get_cycle_config()
            interval_minutes = cycle_config["cycle_interval"] * 60
        else:
            # 使用默认循环间隔（60分钟）
            interval_minutes = 60

        # 检查是否已有任务
        existing_tasks = loop_sender.get_tasks_by_group(self.current_selected_group)
        if existing_tasks:
            if self.button_manager.show_confirmation_dialog(
                self,
                "任务已存在",
                "该分组已有循环任务，是否要删除现有任务并创建新任务？",
            ):
                for task in existing_tasks:
                    loop_sender.delete_task(task.task_id)
            else:
                return

        # 移除创建任务确认对话框，直接创建任务

        # 创建任务（使用循环间隔设置）
        if message_type == "rich_text":
            # 富文本消息需要传递完整的数据
            import json

            full_message_content = json.dumps(message_data, ensure_ascii=False)
            task = loop_sender.create_task(
                self.current_selected_group,
                interval_minutes,
                full_message_content,
                message_type,
            )
        else:
            task = loop_sender.create_task(
                self.current_selected_group, interval_minutes, message_content, "text"
            )

        if task:
            # 更新按钮状态为已创建但未启动
            self.update_button_states("created")

            # 生成任务信息
            task_info = (
                f"循环任务创建成功！\n"
                f"任务ID: {task.task_id}\n"
                f"循环间隔: {interval_minutes}分钟\n"
                f"目标分组: {task.group_name}\n"
            )

            # 如果启用了循环设置，添加详细信息
            if self.loop_cycle_widget.is_cycle_enabled():
                cycle_info = self.loop_cycle_widget.get_cycle_info_text()
                task_info += f"\n{cycle_info}\n"

            task_info += "\n任务已创建，可以使用控制按钮管理任务。"

            logger.info(task_info)

            # 清空输入
            if message_type == "rich_text":
                self.rich_text_editor.clear_content()
            else:
                self.message_edit.clear()
            # 刷新分组显示
            self.refresh_group_card(self.current_selected_group)
        else:
            logger.error("创建循环任务失败")

    def on_task_started(self, task_id: str):
        """任务开始"""
        logger.info(f"循环任务开始: {task_id}")
        task = loop_sender.get_task(task_id)
        if task:
            self.refresh_group_card(task.group_id)

    def on_task_progress(self, task_id: str, sent: int, total: int):
        """任务进度更新"""
        logger.debug(f"循环任务进度: {task_id} - {sent}/{total}")
        task = loop_sender.get_task(task_id)
        if task:
            self.refresh_group_card(task.group_id)

    def on_task_cycle_completed(self, task_id: str):
        """任务周期完成"""
        logger.info(f"循环任务周期完成: {task_id}")
        task = loop_sender.get_task(task_id)
        if task:
            self.refresh_group_card(task.group_id)

    def on_task_paused(self, task_id: str):
        """任务暂停"""
        logger.info(f"循环任务暂停: {task_id}")
        task = loop_sender.get_task(task_id)
        if task:
            self.refresh_group_card(task.group_id)

    def on_task_stopped(self, task_id: str):
        """任务停止"""
        logger.info(f"循环任务停止: {task_id}")
        task = loop_sender.get_task(task_id)
        if task:
            self.refresh_group_card(task.group_id)

    def on_task_error(self, task_id: str, error: str):
        """任务错误"""
        logger.error(f"循环任务错误: {task_id} - {error}")
        task = loop_sender.get_task(task_id)
        if task:
            self.refresh_group_card(task.group_id)

    def on_loop_cycle_enabled_changed(self, enabled: bool):
        """循环间隔功能启用状态改变"""
        if self.current_selected_group:
            self.config_manager.update_config("loop_cycle.enabled", enabled)
        logger.info(f"循环发送页面循环间隔功能: {'启用' if enabled else '禁用'}")

    def on_loop_cycle_config_changed(self):
        """循环间隔配置改变"""
        if self.current_selected_group and self.loop_cycle_widget.is_cycle_enabled():
            config = self.loop_cycle_widget.get_cycle_config()
            self.config_manager.update_config("loop_cycle.config", config)
            logger.debug(f"循环发送页面循环间隔配置: {config}")

    # 移除时间规则相关方法，使用循环间隔设置即可

    def on_interval_changed(self, value):
        """发送间隔更改事件"""
        if self.current_selected_group:
            self.config_manager.update_config("send_settings.interval_seconds", value)

    def on_batch_size_changed(self, value):
        """批量大小更改事件"""
        if self.current_selected_group:
            self.config_manager.update_config("send_settings.batch_size", value)

    def on_risk_control_changed(self, state):
        """风控设置更改事件"""
        if not self.current_selected_group:
            return

        # 避免在加载配置时触发保存
        if hasattr(self, "_loading_config") and self._loading_config:
            return

        try:
            # 获取当前分组对象
            from core.group_manager import group_manager
            group = group_manager.get_group(self.current_selected_group, "loop")

            if group:
                use_system = state == Qt.CheckState.Checked
                # 保存到分组的独立配置中
                group.update_config("send_settings.use_system_risk_control", use_system)
                group_manager.save_groups("loop")

                # 保存循环任务数据
                from core.loop_sender import loop_sender
                loop_sender.save_tasks()

                logger.debug(f"循环发送分组 {group.name} 风控设置已更新: {use_system}")
            else:
                logger.error(f"循环发送分组不存在: {self.current_selected_group}")

        except Exception as e:
            logger.error(f"保存循环发送风控设置失败: {e}")

    def on_message_type_changed(self, message_type: str):
        """消息类型切换处理"""
        if message_type == "富文本消息":
            self.message_editor_stack.setCurrentWidget(self.rich_text_editor)
        else:
            self.message_editor_stack.setCurrentWidget(self.message_edit)

        # 触发内容变化信号
        self.on_message_content_changed()

    def on_message_content_changed(self):
        """消息内容更改事件"""
        if not self.current_selected_group:
            return

        # 避免在加载配置时触发保存
        if hasattr(self, "_loading_config") and self._loading_config:
            return

        try:
            # 获取当前分组对象
            from core.group_manager import group_manager
            group = group_manager.get_group(self.current_selected_group, "loop")

            if not group:
                logger.error(f"分组不存在: {self.current_selected_group}")
                return

            # 根据当前编辑器类型获取内容和类型
            content_data = None  # 初始化变量
            if self.message_type_combo.currentText() == "富文本消息":
                # 富文本消息处理
                content_data = self.rich_text_editor.get_message_content()
                import json
                content = json.dumps(content_data, ensure_ascii=False)
                message_type = "rich_text"
            else:
                # 普通文本消息处理
                content = self.message_edit.toPlainText()
                message_type = "text"

            # 保存到分组的独立配置中
            group.update_config("message_settings.default_type", message_type)
            group.update_config("message_settings.default_content", content)

            # 立即保存分组配置
            group_manager.save_groups("loop")

            # 保存循环任务数据
            from core.loop_sender import loop_sender
            loop_sender.save_tasks()

            # 使用用户友好的格式记录日志
            if message_type == "rich_text" and content_data:
                from ui.rich_text_editor import RichTextMessageEditor
                display_content = RichTextMessageEditor.format_rich_text_for_display(content_data)
                logger.debug(f"分组 {group.name} 循环发送消息内容已更新: {display_content}")
            else:
                logger.debug(f"分组 {group.name} 循环发送消息内容已更新: 类型={message_type}, 长度={len(str(content))}")

        except Exception as e:
            logger.error(f"保存分组循环发送消息内容失败: {e}")
            # 不抛出异常，避免影响用户体验

    def on_template_changed(self, template_name):
        """模板选择更改事件"""
        try:
            template_id = self.template_combo.currentData()

            # 保存模板选择到配置
            if self.current_selected_group:
                self.config_manager.update_config(
                    "message_settings.template_id", template_id or ""
                )

            if not template_id:  # 手动输入
                logger.info("切换到手动输入模式")
                return

            # 如果选择了模板，加载模板内容
            from core.message_template import MessageTemplate

            template_manager = MessageTemplate()
            template = template_manager.get_template(template_id)

            if template:
                # 暂时断开信号连接，避免触发保存
                self.message_edit.textChanged.disconnect()

                if template.type == "text":
                    self.message_edit.setPlainText(template.content)
                elif template.type == "rich_text":
                    # 对于富文本模板，提取纯文本内容
                    if isinstance(template.content, dict):
                        text_content = template.content.get("text", "")
                    else:
                        text_content = str(template.content)
                    self.message_edit.setPlainText(text_content)
                else:
                    self.message_edit.setPlainText(str(template.content))

                # 重新连接信号
                self.message_edit.textChanged.connect(self.on_message_content_changed)

                # 保存模板内容到配置
                if self.current_selected_group:
                    content = self.message_edit.toPlainText()
                    self.config_manager.update_config(
                        "message_settings.default_content", content
                    )

                logger.info(f"应用模板: {template.name}")
            else:
                logger.warning(f"未找到模板: {template_id}")

        except Exception as e:
            logger.error(f"应用模板失败: {e}")
            # 确保信号重新连接
            try:
                self.message_edit.textChanged.connect(self.on_message_content_changed)
            except:
                pass

    def save_loop_config(self):
        """保存循环发送配置"""
        if not self.current_selected_group:
            from ui.themed_message_box import ThemedMessageBoxHelper
            ThemedMessageBoxHelper.show_warning(self, "警告", "请先选择分组")
            return

        try:
            # 获取当前分组对象
            from core.group_manager import group_manager
            group = group_manager.get_group(self.current_selected_group, "loop")

            if not group:
                from ui.themed_message_box import ThemedMessageBoxHelper
                ThemedMessageBoxHelper.show_error(self, "错误", "分组不存在")
                return

            # 保存发送设置到分组独立配置
            group.update_config("send_settings.interval_seconds", self.interval_spinbox.value())
            group.update_config("send_settings.batch_size", self.batch_spinbox.value())
            group.update_config("send_settings.use_system_risk_control", self.use_system_risk_cb.isChecked())

            # 保存消息设置到分组独立配置
            template_id = self.template_combo.currentData()
            group.update_config("message_settings.template_id", template_id or "")

            # 根据消息类型保存内容
            message_type = self.message_type_combo.currentText()
            if message_type == "富文本消息":
                # 富文本消息保存
                if self.rich_text_editor.has_content():
                    message_data = self.rich_text_editor.get_message_content()
                    send_parts = self.rich_text_editor.get_send_parts()
                    message_data["send_parts"] = send_parts

                    import json
                    message_content = json.dumps(message_data, ensure_ascii=False)
                    group.update_config("message_settings.default_content", message_content)
                    group.update_config("message_settings.default_type", "rich_text")
            else:
                # 普通文本消息保存
                message_content = self.message_edit.toPlainText().strip()
                group.update_config("message_settings.default_content", message_content)
                group.update_config("message_settings.default_type", "text")

            # 保存循环设置到分组独立配置
            loop_cycle_enabled = self.loop_cycle_widget.is_cycle_enabled()
            group.update_config("loop_cycle.enabled", loop_cycle_enabled)

            if loop_cycle_enabled:
                # 验证循环配置
                is_valid, error_msg = self.loop_cycle_widget.validate_config()
                if not is_valid:
                    from ui.themed_message_box import ThemedMessageBoxHelper
                    ThemedMessageBoxHelper.show_warning(self, "配置错误", error_msg)
                    return

                # 保存循环配置到分组独立配置
                loop_config = self.loop_cycle_widget.get_cycle_config()
                group.update_config("loop_cycle.config", loop_config)

            # 保存到文件
            from core.group_manager import group_manager
            group_manager.save_groups("loop")

            # 保存循环任务数据
            from core.loop_sender import loop_sender
            loop_sender.save_tasks()

            logger.info(f"保存循环发送配置成功: 分组={self.current_selected_group}")

            from ui.themed_message_box import ThemedMessageBoxHelper
            ThemedMessageBoxHelper.show_success(self, "成功", "分组配置已保存")

        except Exception as e:
            logger.error(f"保存循环发送配置失败: {e}")
            from ui.themed_message_box import ThemedMessageBoxHelper
            ThemedMessageBoxHelper.show_error(self, "错误", f"保存配置失败:\n{e}")

    def toggle_pause_task(self):
        """暂停/继续任务"""
        if not self.current_selected_group:
            return

        # 获取当前分组的任务
        tasks = loop_sender.get_tasks_by_group(self.current_selected_group)
        if not tasks:
            logger.info("当前分组没有运行中的任务")
            return

        # 找到运行中的任务
        running_task = None
        for task in tasks:
            if task.status in ["running", "paused"]:
                running_task = task
                break

        if not running_task:
            logger.info("当前分组没有可暂停的任务")
            return

        try:
            if self.pause_btn.text() == "⏸️ 暂停":
                # 暂停任务
                success = loop_sender.pause_task(running_task.task_id)
                if success:
                    self.pause_btn.setText("▶️ 继续")
                    logger.info(f"暂停循环任务: {running_task.task_id}")
                else:
                    logger.error("暂停任务失败")
            else:
                # 继续任务，使用当前的消息内容
                current_content = self.get_current_message_content()
                current_type = self.get_current_message_type()
                success = loop_sender.resume_task(running_task.task_id, current_content, current_type)
                if success:
                    self.pause_btn.setText("⏸️ 暂停")
                    logger.info(f"继续循环任务: {running_task.task_id}，已更新内容")
                else:
                    logger.error("继续任务失败")
        except Exception as e:
            logger.error(f"切换任务状态失败: {e}")

    def get_current_message_content(self) -> str:
        """获取当前的消息内容"""
        message_type = self.message_type_combo.currentText()
        if message_type == "富文本消息":
            if self.rich_text_editor.has_content():
                message_data = self.rich_text_editor.get_message_content()
                send_parts = self.rich_text_editor.get_send_parts()
                message_data["send_parts"] = send_parts
                import json
                return json.dumps(message_data, ensure_ascii=False)
            else:
                return ""
        else:
            return self.message_edit.toPlainText().strip()

    def get_current_message_type(self) -> str:
        """获取当前的消息类型"""
        message_type = self.message_type_combo.currentText()
        if message_type == "富文本消息":
            return "rich_text"
        else:
            return "text"

    def stop_task(self):
        """停止任务"""
        if not self.current_selected_group:
            return

        # 移除停止任务确认对话框，直接停止任务

        try:
            # 获取当前分组的所有任务
            tasks = loop_sender.get_tasks_by_group(self.current_selected_group)
            stopped_count = 0

            for task in tasks:
                if task.status in ["running", "paused"]:
                    success = loop_sender.stop_task(task.task_id)
                    if success:
                        stopped_count += 1

            if stopped_count > 0:
                self.update_button_states("stopped")
                logger.info(f"停止了 {stopped_count} 个循环任务")
            else:
                logger.info("没有需要停止的任务")

        except Exception as e:
            logger.error(f"停止任务失败: {e}")

    def cancel_task(self):
        """取消任务"""
        if not self.current_selected_group:
            return

        # 移除取消任务确认对话框，直接取消任务

        try:
            # 获取当前分组的所有任务
            tasks = loop_sender.get_tasks_by_group(self.current_selected_group)
            cancelled_count = 0

            for task in tasks:
                success = loop_sender.cancel_task(task.task_id)
                if success:
                    cancelled_count += 1

            if cancelled_count > 0:
                self.update_button_states("cancelled")
                logger.info(f"取消了 {cancelled_count} 个循环任务")
            else:
                logger.info("没有需要取消的任务")

        except Exception as e:
            logger.error(f"取消任务失败: {e}")

    def update_button_states(self, task_status: str):
        """更新按钮状态（使用统一的按钮状态管理器）"""
        # 将字符串状态映射到枚举
        status_enum = map_task_status_to_enum(task_status)

        # 使用按钮状态管理器更新按钮
        self.button_manager.set_task_status(status_enum)

        # 同时更新分组卡片按钮状态
        if self.current_selected_group:
            self.update_group_card_button_states(None, self.current_selected_group)

    def update_group_card_button_states(self, card, group_id: str):
        """更新分组卡片按钮状态（与定时发送页面保持一致）"""
        try:
            # 如果没有传入card，尝试从group_cards中获取
            if card is None and group_id in self.group_cards:
                card = self.group_cards[group_id]

            # 如果仍然没有card，跳过更新
            if card is None:
                logger.debug(f"未找到分组卡片: {group_id}")
                return

            from core.loop_sender import loop_sender

            # 获取该分组的任务
            tasks = loop_sender.get_tasks_by_group(group_id)

            # 检查是否有运行中的任务
            has_running_task = any(task.status == "running" for task in tasks)
            has_paused_task = any(task.status == "paused" for task in tasks)

            # 更新卡片按钮状态
            card.update_button_states(has_running_task, has_paused_task)

        except Exception as e:
            logger.error(f"更新分组卡片按钮状态失败: {e}")
