#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet Space 微信群发助手 - 生产环境打包脚本

支持完整的生产环境打包，包含：
- 依赖检查
- 环境验证
- 资源打包
- 版本信息
- 部署文档
- 质量检查
"""

import os
import sys
import subprocess
import shutil
import json
import time
from pathlib import Path
from datetime import datetime

# 项目配置
PROJECT_ROOT = Path(__file__).parent
DIST_DIR = PROJECT_ROOT / 'dist'
BUILD_DIR = PROJECT_ROOT / 'build'
SPEC_FILE = PROJECT_ROOT / 'MeetSpaceWeChatSender_Simple.spec'

# 生产环境配置
PRODUCTION_CONFIG = {
    'app_name': 'MeetSpaceWeChatSender',
    'version': '1.0.0',
    'build_type': 'production',
    'target_platform': 'win32',
    'architecture': 'x64',
    'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
}

def print_banner():
    """打印构建横幅"""
    print("=" * 80)
    print("🚀 Meet Space 微信群发助手 - 生产环境打包")
    print("=" * 80)
    print(f"📦 应用名称: {PRODUCTION_CONFIG['app_name']}")
    print(f"🏷️  版本号: {PRODUCTION_CONFIG['version']}")
    print(f"🏗️  构建类型: {PRODUCTION_CONFIG['build_type']}")
    print(f"💻 目标平台: {PRODUCTION_CONFIG['target_platform']}")
    print(f"🔧 Python版本: {PRODUCTION_CONFIG['python_version']}")
    print(f"📅 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

def check_dependencies():
    """检查构建依赖"""
    print("\n🔍 检查构建依赖...")
    
    required_packages = [
        'PyInstaller',
        'PyQt6',
        'requests',
        'psutil',
        'wcferry',
        'Pillow',
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PyInstaller':
                import PyInstaller
            elif package == 'PyQt6':
                import PyQt6
            elif package == 'requests':
                import requests
            elif package == 'psutil':
                import psutil
            elif package == 'wcferry':
                import wcferry
            elif package == 'Pillow':
                import PIL
            else:
                __import__(package.lower().replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def check_resources():
    """检查资源文件"""
    print("\n🔍 检查资源文件...")
    
    required_resources = [
        'tools/Injector.exe',
        'tools/x64/Injector.exe',
        'wxhelper_files/wxhelper.dll',
        'resources/icons/app_icon.ico',
        'resources/themes',
        'config/system_config.json',
        'version_info.txt',
        'LICENSE.txt',
        'README.md',
    ]
    
    missing_resources = []
    
    for resource in required_resources:
        resource_path = PROJECT_ROOT / resource
        if resource_path.exists():
            print(f"✅ {resource}")
        else:
            print(f"❌ {resource} - 缺失")
            missing_resources.append(resource)
    
    if missing_resources:
        print(f"\n❌ 缺少资源文件: {', '.join(missing_resources)}")
        return False
    
    print("✅ 所有资源文件检查通过")
    return True

def clean_build_dirs():
    """清理构建目录"""
    print("\n🧹 清理构建目录...")
    
    dirs_to_clean = [BUILD_DIR, DIST_DIR]
    
    for dir_path in dirs_to_clean:
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"✅ 清理: {dir_path}")
            except Exception as e:
                print(f"⚠️  清理失败: {dir_path} - {e}")
        else:
            print(f"ℹ️  目录不存在: {dir_path}")
    
    print("✅ 构建目录清理完成")
    return True

def update_version_info():
    """更新版本信息"""
    print("\n📝 更新版本信息...")
    
    try:
        version_info = f"""# UTF-8
#
# 版本信息文件
#
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({PRODUCTION_CONFIG['version'].replace('.', ', ')}, 0),
    prodvers=({PRODUCTION_CONFIG['version'].replace('.', ', ')}, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'Meet Space'),
            StringStruct(u'FileDescription', u'安全、高效的微信群发工具'),
            StringStruct(u'FileVersion', u'{PRODUCTION_CONFIG['version']}.0'),
            StringStruct(u'InternalName', u'{PRODUCTION_CONFIG['app_name']}'),
            StringStruct(u'LegalCopyright', u'© 2025 Meet Space. All rights reserved.'),
            StringStruct(u'OriginalFilename', u'{PRODUCTION_CONFIG['app_name']}.exe'),
            StringStruct(u'ProductName', u'Meet Space 微信群发助手'),
            StringStruct(u'ProductVersion', u'{PRODUCTION_CONFIG['version']}.0'),
            StringStruct(u'Comments', u'生产环境版本 - {datetime.now().strftime("%Y-%m-%d")}'),
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)"""
        
        with open(PROJECT_ROOT / 'version_info.txt', 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        print("✅ 版本信息更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 版本信息更新失败: {e}")
        return False

def run_pyinstaller():
    """运行PyInstaller打包"""
    print("\n🔨 开始PyInstaller打包...")
    
    try:
        # 构建命令
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',  # 清理缓存
            '--noconfirm',  # 不确认覆盖
            str(SPEC_FILE)
        ]
        
        print(f"📋 执行命令: {' '.join(cmd)}")
        
        # 执行打包
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=PROJECT_ROOT)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ PyInstaller打包成功 (耗时: {end_time - start_time:.1f}秒)")
            return True
        else:
            print(f"❌ PyInstaller打包失败")
            print(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ PyInstaller执行异常: {e}")
        return False

def verify_build():
    """验证构建结果"""
    print("\n🔍 验证构建结果...")
    
    exe_path = DIST_DIR / f"{PRODUCTION_CONFIG['app_name']}.exe"
    
    if not exe_path.exists():
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 检查文件大小
    file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
    print(f"📊 可执行文件大小: {file_size:.1f} MB")
    
    if file_size < 10:
        print("⚠️  文件大小异常小，可能打包不完整")
        return False
    
    if file_size > 500:
        print("⚠️  文件大小异常大，可能包含不必要的依赖")
    
    # 检查必要文件
    required_files = [
        'deployment_info.json',
        'README.txt',
    ]
    
    for file_name in required_files:
        file_path = DIST_DIR / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} - 缺失")
            return False
    
    print("✅ 构建结果验证通过")
    return True

def create_deployment_package():
    """创建部署包"""
    print("\n📦 创建部署包...")
    
    try:
        # 创建部署目录
        deploy_dir = PROJECT_ROOT / 'deploy'
        if deploy_dir.exists():
            shutil.rmtree(deploy_dir)
        deploy_dir.mkdir()
        
        # 复制可执行文件
        exe_src = DIST_DIR / f"{PRODUCTION_CONFIG['app_name']}.exe"
        exe_dst = deploy_dir / f"{PRODUCTION_CONFIG['app_name']}.exe"
        shutil.copy2(exe_src, exe_dst)
        print(f"✅ 复制可执行文件: {exe_dst.name}")
        
        # 复制说明文件
        doc_files = ['README.txt', 'deployment_info.json']
        for doc_file in doc_files:
            src = DIST_DIR / doc_file
            dst = deploy_dir / doc_file
            if src.exists():
                shutil.copy2(src, dst)
                print(f"✅ 复制文档: {doc_file}")
        
        # 创建安装脚本
        install_script = f"""@echo off
echo 正在安装 {PRODUCTION_CONFIG['app_name']} v{PRODUCTION_CONFIG['version']}...
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限检查通过
) else (
    echo ❌ 需要管理员权限，请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 启动程序
echo 🚀 启动程序...
start "" "{PRODUCTION_CONFIG['app_name']}.exe"

echo ✅ 安装完成！
echo 📖 使用说明请查看 README.txt
pause
"""
        
        with open(deploy_dir / 'install.bat', 'w', encoding='gbk') as f:
            f.write(install_script)
        print("✅ 创建安装脚本: install.bat")
        
        # 创建部署信息
        deploy_info = {
            'package_name': f"{PRODUCTION_CONFIG['app_name']}_v{PRODUCTION_CONFIG['version']}_Production",
            'created_time': datetime.now().isoformat(),
            'files': [f.name for f in deploy_dir.iterdir()],
            'total_size_mb': sum(f.stat().st_size for f in deploy_dir.iterdir()) / (1024 * 1024),
            'installation': {
                'requirements': ['Windows 10/11', '管理员权限', '微信PC版'],
                'steps': [
                    '1. 右键点击 install.bat',
                    '2. 选择"以管理员身份运行"',
                    '3. 按照提示完成安装',
                    '4. 首次运行会自动配置环境'
                ]
            }
        }
        
        with open(deploy_dir / 'deploy_info.json', 'w', encoding='utf-8') as f:
            json.dump(deploy_info, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 部署包创建完成: {deploy_dir}")
        print(f"📊 包含文件: {len(deploy_info['files'])} 个")
        print(f"📏 总大小: {deploy_info['total_size_mb']:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建部署包失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查步骤
    steps = [
        ("检查构建依赖", check_dependencies),
        ("检查资源文件", check_resources),
        ("清理构建目录", clean_build_dirs),
        ("更新版本信息", update_version_info),
        ("执行PyInstaller打包", run_pyinstaller),
        ("验证构建结果", verify_build),
        ("创建部署包", create_deployment_package),
    ]
    
    start_time = time.time()
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        
        try:
            if not step_func():
                print(f"\n❌ {step_name} 失败，构建中止")
                return False
        except Exception as e:
            print(f"\n❌ {step_name} 异常: {e}")
            return False
    
    end_time = time.time()
    
    # 构建成功
    print("\n" + "=" * 80)
    print("🎉 生产环境打包完成！")
    print("=" * 80)
    print(f"⏱️  总耗时: {end_time - start_time:.1f} 秒")
    print(f"📁 输出目录: {DIST_DIR}")
    print(f"📦 部署包: {PROJECT_ROOT / 'deploy'}")
    print(f"🚀 可执行文件: {PRODUCTION_CONFIG['app_name']}.exe")
    
    print("\n📋 部署说明:")
    print("1. 将 deploy 目录复制到目标机器")
    print("2. 右键点击 install.bat，选择'以管理员身份运行'")
    print("3. 按照提示完成安装")
    print("4. 程序将自动配置运行环境")
    
    print("\n✨ 生产环境特性:")
    print("- ✅ 完整的依赖打包")
    print("- ✅ 管理员权限支持")
    print("- ✅ 路径自适应")
    print("- ✅ 资源文件完整")
    print("- ✅ 版本信息完整")
    print("- ✅ 部署文档齐全")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断构建")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 构建过程异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
