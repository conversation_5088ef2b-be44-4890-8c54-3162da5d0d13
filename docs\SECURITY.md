# 安全指南

本文档描述了微信无感群发助手的安全特性和最佳实践。

## 安全特性

### 1. 数据加密

#### 配置文件加密

应用程序支持对配置文件进行加密存储：

```python
from config.wechat_config import WeChatConfig

# 启用配置加密
config = WeChatConfig()
config.encrypt_config = True
config.save_to_file()
```

#### 敏感数据加密

使用内置的安全管理器加密敏感数据：

```python
from utils.security import security_manager

# 加密数据
encrypted_data = security_manager.encrypt_data("敏感信息")

# 解密数据
decrypted_data = security_manager.decrypt_data(encrypted_data)
```

### 2. 输入验证和清理

#### 字符串清理

```python
from utils.security import input_sanitizer

# 清理用户输入
clean_input = input_sanitizer.sanitize_string(user_input, max_length=500)

# 清理文件名
safe_filename = input_sanitizer.sanitize_filename(filename)

# 验证微信ID
is_valid = input_sanitizer.validate_wxid(wxid)
```

#### 文件类型验证

```python
from utils.security import validate_file_type

# 验证文件类型
allowed_types = ['.jpg', '.png', '.gif', '.txt', '.pdf']
is_safe = validate_file_type(file_path, allowed_types)
```

### 3. 速率限制

防止滥用和过度请求：

```python
from utils.security import rate_limiter

# 检查是否允许请求
if rate_limiter.is_allowed(user_id):
    # 处理请求
    process_request()
else:
    # 拒绝请求
    reject_request("请求过于频繁")
```

### 4. 安全审计

记录和监控安全事件：

```python
from utils.security import security_auditor

# 记录安全事件
security_auditor.log_security_event(
    event_type="LOGIN_ATTEMPT",
    description="用户尝试登录",
    severity="INFO",
    metadata={"user_id": "user123", "ip": "***********"}
)

# 获取安全摘要
summary = security_auditor.get_security_summary(hours=24)
```

### 5. 密码安全

#### 密码哈希

```python
from utils.security import security_manager

# 哈希密码
password_hash, salt = security_manager.hash_password("user_password")

# 验证密码
is_valid = security_manager.verify_password("user_password", password_hash, salt)
```

#### 安全令牌

```python
from utils.security import generate_token

# 生成安全令牌
token = generate_token(32)  # 32字节的随机令牌
```

## 安全配置

### 1. 基本安全设置

在配置文件中启用安全特性：

```json
{
  "encrypt_config": true,
  "enable_risk_control": true,
  "simulate_human": true,
  "random_delay": true,
  "send_interval_min": 3,
  "send_interval_max": 8,
  "daily_send_limit": 200,
  "hourly_send_limit": 50
}
```

### 2. 环境变量

使用环境变量存储敏感信息：

```bash
# 设置主密钥
export WX_MASTER_KEY="your-secure-master-key"

# 设置API密钥
export WX_API_KEY="your-api-key"
```

### 3. 文件权限

确保配置文件和日志文件的权限设置正确：

```bash
# 限制配置文件权限
chmod 600 config/wechat_config.json

# 限制日志目录权限
chmod 700 logs/
```

## 安全最佳实践

### 1. 网络安全

#### HTTPS通信

确保所有HTTP API通信使用HTTPS：

```python
# 使用HTTPS端点
connector = HTTPAPIConnector(
    api_type="wxhelper",
    base_url="https://localhost:19088"  # 使用HTTPS
)
```

#### 证书验证

验证SSL证书：

```python
import ssl
import aiohttp

# 创建安全的SSL上下文
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = True
ssl_context.verify_mode = ssl.CERT_REQUIRED

# 在HTTP连接中使用
connector = aiohttp.TCPConnector(ssl=ssl_context)
```

### 2. 访问控制

#### 用户权限

实现基于角色的访问控制：

```python
class UserRole:
    ADMIN = "admin"
    USER = "user"
    READONLY = "readonly"

def check_permission(user_role: str, required_permission: str) -> bool:
    """检查用户权限"""
    permissions = {
        UserRole.ADMIN: ["read", "write", "delete", "admin"],
        UserRole.USER: ["read", "write"],
        UserRole.READONLY: ["read"]
    }
    
    return required_permission in permissions.get(user_role, [])
```

#### API密钥管理

```python
from utils.security import secure_compare

def validate_api_key(provided_key: str, stored_key: str) -> bool:
    """安全地验证API密钥"""
    return secure_compare(provided_key, stored_key)
```

### 3. 数据保护

#### 敏感数据处理

```python
import os
from utils.security import security_manager

class SecureConfig:
    def __init__(self):
        self.api_key = None
        self.encrypted_data = {}
    
    def set_api_key(self, api_key: str):
        """安全地设置API密钥"""
        self.api_key = security_manager.encrypt_data(api_key)
    
    def get_api_key(self) -> str:
        """安全地获取API密钥"""
        if self.api_key:
            return security_manager.decrypt_data(self.api_key)
        return ""
    
    def clear_sensitive_data(self):
        """清理敏感数据"""
        self.api_key = None
        self.encrypted_data.clear()
```

#### 内存安全

```python
import gc
from utils.performance import memory_manager

def secure_cleanup():
    """安全清理内存"""
    # 清理敏感变量
    sensitive_data = None
    
    # 强制垃圾回收
    memory_manager.force_gc()
    
    # 清理Python垃圾回收器
    gc.collect()
```

### 4. 日志安全

#### 安全日志记录

```python
from utils.logger import setup_logger
from utils.security import input_sanitizer

logger = setup_logger("security")

def secure_log(message: str, user_data: dict = None):
    """安全地记录日志"""
    # 清理用户数据
    if user_data:
        cleaned_data = {
            key: input_sanitizer.sanitize_string(str(value))
            for key, value in user_data.items()
        }
    else:
        cleaned_data = {}
    
    # 记录日志（不包含敏感信息）
    logger.info(f"{message} | 数据: {cleaned_data}")
```

#### 日志轮转

```python
import logging.handlers

def setup_secure_logging():
    """设置安全的日志记录"""
    handler = logging.handlers.RotatingFileHandler(
        'logs/security.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    
    # 设置安全的文件权限
    os.chmod('logs/security.log', 0o600)
    
    return handler
```

## 安全检查清单

### 部署前检查

- [ ] 配置文件加密已启用
- [ ] 敏感信息不在代码中硬编码
- [ ] 使用环境变量存储密钥
- [ ] 文件权限设置正确
- [ ] 日志不包含敏感信息
- [ ] 输入验证已实现
- [ ] 速率限制已配置
- [ ] SSL/TLS证书有效
- [ ] 依赖包无已知漏洞

### 运行时监控

- [ ] 监控异常登录尝试
- [ ] 监控API调用频率
- [ ] 监控文件访问
- [ ] 监控内存使用
- [ ] 监控网络连接
- [ ] 定期检查日志
- [ ] 定期更新依赖

### 定期维护

- [ ] 更新密钥和证书
- [ ] 审查访问日志
- [ ] 更新安全策略
- [ ] 备份重要数据
- [ ] 测试恢复程序
- [ ] 培训用户安全意识

## 漏洞报告

如果发现安全漏洞，请通过以下方式报告：

1. **邮箱**: <EMAIL>
2. **加密通信**: 使用PGP密钥加密敏感信息
3. **负责任披露**: 给我们合理时间修复漏洞

### 报告内容

请在报告中包含：

- 漏洞详细描述
- 重现步骤
- 影响评估
- 建议修复方案
- 您的联系方式

## 安全更新

定期检查和应用安全更新：

```bash
# 检查依赖漏洞
pip-audit

# 更新依赖
pip install --upgrade -r requirements.txt

# 运行安全测试
python run_tests.py --security
```

## 应急响应

### 安全事件响应流程

1. **检测**: 通过监控系统发现异常
2. **评估**: 确定事件严重程度
3. **隔离**: 隔离受影响的系统
4. **调查**: 分析事件原因和影响
5. **修复**: 实施修复措施
6. **恢复**: 恢复正常服务
7. **总结**: 编写事件报告和改进措施

### 紧急联系方式

- **技术支持**: <EMAIL>
- **安全团队**: <EMAIL>
- **24小时热线**: +86-xxx-xxxx-xxxx

---

**注意**: 安全是一个持续的过程，需要定期评估和改进。请确保团队成员了解并遵循这些安全指南。
