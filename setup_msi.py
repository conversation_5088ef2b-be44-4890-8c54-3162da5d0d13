#!/usr/bin/env python3
"""
Setup script for creating MSI installer
"""

from cx_Freeze import setup, Executable
import sys
from pathlib import Path

# 项目信息
APP_NAME = "Meet space 微信群发助手"
APP_VERSION = "1.0.0"
DESCRIPTION = "一个安全、高效的微信群发工具"
AUTHOR = "Meet space 会客创意空间"

# 构建选项
build_exe_options = {
    "packages": [
        "PyQt6", "PyQt6.QtCore", "PyQt6.QtGui", "PyQt6.QtWidgets",
        "requests", "aiohttp", "PIL", "psutil", "yaml", "dateutil",
        "cryptography", "sqlite3", "json", "xml", "email",
        "asyncio", "concurrent", "threading", "multiprocessing"
    ],
    "excludes": [
        "tkinter", "matplotlib", "scipy", "jupyter", "IPython", 
        "notebook", "pytest", "unittest", "test", "tests"
    ],
    "include_files": [
        ("resources", "resources"),
        ("config", "config"),
        ("tools", "tools"),
        ("wxhelper_files", "wxhelper_files"),
        ("README.md", "README.md"),
        ("LICENSE.txt", "LICENSE.txt"),
    ],
    "include_msvcrt": True,
    "optimize": 2,
}

# MSI构建选项
bdist_msi_options = {
    "upgrade_code": "{12345678-1234-1234-1234-123456789012}",
    "add_to_path": False,
    "initial_target_dir": r"[ProgramFilesFolder]\MeetSpaceWeChatSender",
    "install_icon": "resources/icons/app.ico" if Path("resources/icons/app.ico").exists() else None,
}

# 可执行文件配置
executables = [
    Executable(
        "main.py",
        base="Win32GUI",  # 无控制台窗口
        target_name="MeetSpaceWeChatSender.exe",
        icon="resources/icons/app.ico" if Path("resources/icons/app.ico").exists() else None,
        shortcut_name=APP_NAME,
        shortcut_dir="ProgramMenuFolder",
    )
]

setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=DESCRIPTION,
    author=AUTHOR,
    options={
        "build_exe": build_exe_options,
        "bdist_msi": bdist_msi_options,
    },
    executables=executables,
)
