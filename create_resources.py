#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建项目资源文件
包括图标、图片等资源
"""

import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def create_directories():
    """创建资源目录结构"""
    print("📁 创建资源目录...")
    
    directories = [
        "resources",
        "resources/icons",
        "resources/images",
        "resources/themes",
        "installer",
        "tools",
        "wxhelper_files",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  ✅ {directory}")

def create_app_icon():
    """创建应用程序图标"""
    print("🎨 创建应用程序图标...")
    
    # 创建一个简单的图标
    sizes = [16, 32, 48, 64, 128, 256]
    
    for size in sizes:
        # 创建图像
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制背景圆形
        margin = size // 8
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(0, 123, 255, 255), outline=(0, 100, 200, 255), width=2)
        
        # 绘制字母 "M"
        try:
            font_size = size // 3
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        
        text = "M"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - bbox[1]
        
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        # 保存PNG格式
        png_path = f"resources/icons/app_icon_{size}x{size}.png"
        img.save(png_path, "PNG")
        print(f"  ✅ {png_path}")
    
    # 创建ICO文件（Windows图标）
    try:
        # 使用最大尺寸创建ICO
        img_256 = Image.open("resources/icons/app_icon_256x256.png")
        ico_path = "resources/icons/app_icon.ico"
        img_256.save(ico_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print(f"  ✅ {ico_path}")
    except Exception as e:
        print(f"  ⚠️  ICO创建失败: {e}")

def create_installer_images():
    """创建安装程序图片"""
    print("🖼️  创建安装程序图片...")
    
    # 创建对话框背景图 (493x312)
    dialog_img = Image.new('RGB', (493, 312), (240, 240, 240))
    draw = ImageDraw.Draw(dialog_img)
    
    # 绘制渐变背景
    for y in range(312):
        color_value = int(240 - (y / 312) * 40)
        draw.line([(0, y), (493, y)], fill=(color_value, color_value, color_value))
    
    # 添加标题
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    title = "Meet space 微信群发助手"
    bbox = draw.textbbox((0, 0), title, font=font)
    title_width = bbox[2] - bbox[0]
    x = (493 - title_width) // 2
    y = 50
    
    draw.text((x, y), title, fill=(0, 50, 100), font=font)
    
    # 添加版本信息
    try:
        font_small = ImageFont.truetype("arial.ttf", 14)
    except:
        font_small = ImageFont.load_default()
    
    version = "版本 1.0.0"
    bbox = draw.textbbox((0, 0), version, font=font_small)
    version_width = bbox[2] - bbox[0]
    x = (493 - version_width) // 2
    y = 90
    
    draw.text((x, y), version, fill=(100, 100, 100), font=font_small)
    
    dialog_img.save("installer/dialog.bmp", "BMP")
    print("  ✅ installer/dialog.bmp")
    
    # 创建横幅图 (493x58)
    banner_img = Image.new('RGB', (493, 58), (0, 123, 255))
    draw = ImageDraw.Draw(banner_img)
    
    # 绘制渐变
    for x in range(493):
        color_value = int(255 - (x / 493) * 100)
        draw.line([(x, 0), (x, 58)], fill=(0, 123, color_value))
    
    # 添加文字
    banner_text = "Meet space 微信群发助手 - 安装向导"
    try:
        font_banner = ImageFont.truetype("arial.ttf", 16)
    except:
        font_banner = ImageFont.load_default()
    
    draw.text((20, 20), banner_text, fill=(255, 255, 255), font=font_banner)
    
    banner_img.save("installer/banner.bmp", "BMP")
    print("  ✅ installer/banner.bmp")

def create_readme_files():
    """创建说明文件"""
    print("📝 创建说明文件...")
    
    # 创建工具目录说明
    tools_readme = """# 工具目录

此目录包含项目构建和维护所需的工具文件。

## 文件说明

- `build_exe.py` - 可执行文件构建脚本
- `build_msi.py` - MSI安装包构建脚本  
- `build_all.py` - 一键构建脚本
- `create_resources.py` - 资源文件创建脚本

## 使用方法

```bash
# 创建资源文件
python create_resources.py

# 构建可执行文件
python build_exe.py

# 构建MSI安装包
python build_msi.py

# 一键构建所有
python build_all.py
```
"""
    
    with open("tools/README.md", "w", encoding="utf-8") as f:
        f.write(tools_readme)
    print("  ✅ tools/README.md")
    
    # 创建微信助手文件说明
    wxhelper_readme = """# 微信助手文件目录

此目录用于存放微信相关的辅助文件。

## 目录说明

- 微信接口配置文件
- 微信自动化脚本
- 微信连接器相关资源

## 注意事项

- 请确保微信PC版已正确安装
- 相关文件会在程序运行时自动创建
- 不要手动修改此目录中的文件
"""
    
    with open("wxhelper_files/README.md", "w", encoding="utf-8") as f:
        f.write(wxhelper_readme)
    print("  ✅ wxhelper_files/README.md")

def main():
    """主函数"""
    print("🎨 创建项目资源文件")
    print("=" * 40)
    
    try:
        # 1. 创建目录结构
        create_directories()
        
        # 2. 创建应用图标
        create_app_icon()
        
        # 3. 创建安装程序图片
        create_installer_images()
        
        # 4. 创建说明文件
        create_readme_files()
        
        print("\n🎉 资源文件创建完成！")
        print("\n📁 创建的资源:")
        print("  - 应用程序图标 (多种尺寸)")
        print("  - 安装程序图片")
        print("  - 目录结构")
        print("  - 说明文档")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 创建资源文件时出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
