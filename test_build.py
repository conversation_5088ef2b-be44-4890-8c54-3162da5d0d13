#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试构建脚本
"""

import subprocess
import sys
from pathlib import Path

def test_simple_build():
    """测试最简单的构建"""
    print("🧪 测试简单构建...")
    
    # 最简单的命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--name=TestApp",
        "main_encrypted.py"
    ]
    
    print(f"执行: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        # 检查输出文件
        exe_file = Path("dist") / "TestApp.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"✅ 构建成功: {exe_file} ({size_mb:.1f} MB)")
            return True
        else:
            print("❌ 未找到输出文件")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

if __name__ == "__main__":
    test_simple_build()
