#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联系人选择对话框

用于在分组详情页面中选择要添加的联系人。
"""

import sys
from typing import List, Optional, Callable
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QTabWidget,
    QWidget,
    QListWidget,
    QListWidgetItem,
    QDialogButtonBox,
    QMessageBox,
    QSplitter,
    QGroupBox,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QCheckBox,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

from core.wechatferry_connector import Contact
from ui.themed_dialog_base import ThemedDialogBase
from ui.themed_message_box import ThemedMessageBoxHelper
from utils.logger import setup_logger

logger = setup_logger("contact_selector_dialog")


class ContactSelectorDialog(ThemedDialogBase):
    """联系人选择对话框"""

    # 信号定义
    contacts_selected = pyqtSignal(list)  # 选中的联系人列表

    def __init__(self, parent=None, existing_contacts: List[Contact] = None):
        super().__init__(parent)
        self.existing_contacts = existing_contacts or []
        self.selected_contacts = []
        self.all_contacts = []  # 所有可用联系人

        # 主题支持已由 ThemedDialogBase 自动设置

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("选择联系人")
        self.setMinimumSize(800, 600)
        self.setModal(True)

        # 主布局
        main_layout = QVBoxLayout(self)

        # 搜索区域
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索联系人:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入姓名、微信ID或备注进行模糊搜索...")
        self.clear_search_btn = QPushButton("清空搜索")

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.clear_search_btn)
        main_layout.addLayout(search_layout)

        # 联系人显示区域
        contacts_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：好友列表
        friends_group = QGroupBox("好友列表")
        friends_layout = QVBoxLayout(friends_group)

        # 好友搜索
        friends_search_layout = QHBoxLayout()
        friends_search_label = QLabel("搜索好友:")
        self.friends_search_input = QLineEdit()
        self.friends_search_input.setPlaceholderText("搜索好友...")
        friends_search_layout.addWidget(friends_search_label)
        friends_search_layout.addWidget(self.friends_search_input)
        friends_layout.addLayout(friends_search_layout)

        # 好友列表
        self.friends_table = QTableWidget(0, 4)
        self.friends_table.setHorizontalHeaderLabels(["选择", "名称", "微信ID", "备注"])
        self.friends_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        self.friends_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        friends_layout.addWidget(self.friends_table)

        # 好友操作按钮
        friends_btn_layout = QHBoxLayout()
        self.select_all_friends_btn = QPushButton("全选好友")
        self.deselect_all_friends_btn = QPushButton("取消全选好友")
        self.add_selected_friends_btn = QPushButton("添加选中好友")

        friends_btn_layout.addWidget(self.select_all_friends_btn)
        friends_btn_layout.addWidget(self.deselect_all_friends_btn)
        friends_btn_layout.addWidget(self.add_selected_friends_btn)
        friends_layout.addLayout(friends_btn_layout)

        self.friends_count_label = QLabel("好友: 0 个")
        friends_layout.addWidget(self.friends_count_label)

        contacts_splitter.addWidget(friends_group)

        # 右侧：群聊列表
        groups_group = QGroupBox("群聊列表")
        groups_layout = QVBoxLayout(groups_group)

        # 群聊搜索
        groups_search_layout = QHBoxLayout()
        groups_search_label = QLabel("搜索群聊:")
        self.groups_search_input = QLineEdit()
        self.groups_search_input.setPlaceholderText("搜索群聊...")
        groups_search_layout.addWidget(groups_search_label)
        groups_search_layout.addWidget(self.groups_search_input)
        groups_layout.addLayout(groups_search_layout)

        # 群聊列表
        self.groups_table = QTableWidget(0, 4)
        self.groups_table.setHorizontalHeaderLabels(["选择", "群名称", "群ID", "备注"])
        self.groups_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        self.groups_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        groups_layout.addWidget(self.groups_table)

        # 群聊操作按钮
        groups_btn_layout = QHBoxLayout()
        self.select_all_groups_btn = QPushButton("全选群聊")
        self.deselect_all_groups_btn = QPushButton("取消全选群聊")
        self.add_selected_groups_btn = QPushButton("添加选中群聊")

        groups_btn_layout.addWidget(self.select_all_groups_btn)
        groups_btn_layout.addWidget(self.deselect_all_groups_btn)
        groups_btn_layout.addWidget(self.add_selected_groups_btn)
        groups_layout.addLayout(groups_btn_layout)

        self.groups_count_label = QLabel("群聊: 0 个")
        groups_layout.addWidget(self.groups_count_label)

        contacts_splitter.addWidget(groups_group)

        # 设置分割器比例
        contacts_splitter.setSizes([400, 400])
        main_layout.addWidget(contacts_splitter)

        # 已选联系人区域
        selected_group = QGroupBox("已选联系人")
        selected_layout = QVBoxLayout(selected_group)

        self.selected_table = QTableWidget(0, 4)
        self.selected_table.setHorizontalHeaderLabels(
            ["类型", "名称", "微信ID", "备注"]
        )
        self.selected_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        selected_layout.addWidget(self.selected_table)

        # 已选联系人操作按钮
        selected_btn_layout = QHBoxLayout()
        self.remove_selected_btn = QPushButton("移除选中")
        self.clear_all_selected_btn = QPushButton("清空已选")

        selected_btn_layout.addWidget(self.remove_selected_btn)
        selected_btn_layout.addWidget(self.clear_all_selected_btn)
        selected_btn_layout.addStretch()
        selected_layout.addLayout(selected_btn_layout)

        self.selected_count_label = QLabel("已选择: 0 个联系人")
        selected_layout.addWidget(self.selected_count_label)

        main_layout.addWidget(selected_group)

        # 底部按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

        # 设置搜索延迟
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

    def setup_connections(self):
        """设置信号连接"""
        # 搜索功能
        self.search_input.textChanged.connect(self.on_search_changed)
        self.friends_search_input.textChanged.connect(self.on_friends_search_changed)
        self.groups_search_input.textChanged.connect(self.on_groups_search_changed)
        self.clear_search_btn.clicked.connect(self.clear_search)

        # 好友操作
        self.select_all_friends_btn.clicked.connect(self.select_all_friends)
        self.deselect_all_friends_btn.clicked.connect(self.deselect_all_friends)
        self.add_selected_friends_btn.clicked.connect(self.add_selected_friends)

        # 群聊操作
        self.select_all_groups_btn.clicked.connect(self.select_all_groups)
        self.deselect_all_groups_btn.clicked.connect(self.deselect_all_groups)
        self.add_selected_groups_btn.clicked.connect(self.add_selected_groups)

        # 已选联系人操作
        self.remove_selected_btn.clicked.connect(self.remove_selected_contacts)
        self.clear_all_selected_btn.clicked.connect(self.clear_all_selected)

    def set_contacts(self, contacts: List[Contact]):
        """设置联系人数据"""
        self.all_contacts = contacts
        self.update_contacts_display()

    def update_contacts_display(self):
        """更新联系人显示"""
        # 分类联系人
        friends = []
        groups = []

        for contact in self.all_contacts:
            if contact.type == "group" or contact.wxid.endswith("@chatroom"):
                groups.append(contact)
            else:
                friends.append(contact)

        # 更新好友表格
        self.update_friends_table(friends)

        # 更新群聊表格
        self.update_groups_table(groups)

        # 更新统计
        self.friends_count_label.setText(f"好友: {len(friends)} 个")
        self.groups_count_label.setText(f"群聊: {len(groups)} 个")

    def update_friends_table(self, friends: List[Contact]):
        """更新好友表格"""
        self.friends_table.setRowCount(len(friends))

        for row, contact in enumerate(friends):
            # 选择框
            checkbox = QCheckBox()
            self.friends_table.setCellWidget(row, 0, checkbox)

            # 名称
            name_item = QTableWidgetItem(contact.name)
            name_item.setData(Qt.ItemDataRole.UserRole, contact)
            self.friends_table.setItem(row, 1, name_item)

            # 微信ID
            wxid_item = QTableWidgetItem(contact.wxid)
            self.friends_table.setItem(row, 2, wxid_item)

            # 备注
            remark_item = QTableWidgetItem(contact.remark)
            self.friends_table.setItem(row, 3, remark_item)

    def update_groups_table(self, groups: List[Contact]):
        """更新群聊表格"""
        self.groups_table.setRowCount(len(groups))

        for row, contact in enumerate(groups):
            # 选择框
            checkbox = QCheckBox()
            self.groups_table.setCellWidget(row, 0, checkbox)

            # 群名称
            name_item = QTableWidgetItem(contact.name)
            name_item.setData(Qt.ItemDataRole.UserRole, contact)
            self.groups_table.setItem(row, 1, name_item)

            # 群ID
            wxid_item = QTableWidgetItem(contact.wxid)
            self.groups_table.setItem(row, 2, wxid_item)

            # 备注
            remark_item = QTableWidgetItem(contact.remark)
            self.groups_table.setItem(row, 3, remark_item)

    def on_search_changed(self):
        """搜索文本变化"""
        self.search_timer.start(300)  # 300ms延迟

    def on_friends_search_changed(self):
        """好友搜索文本变化"""
        self.search_timer.start(300)

    def on_groups_search_changed(self):
        """群聊搜索文本变化"""
        self.search_timer.start(300)

    def perform_search(self):
        """执行搜索"""
        search_text = self.search_input.text().lower()
        friends_search_text = self.friends_search_input.text().lower()
        groups_search_text = self.groups_search_input.text().lower()

        # 过滤好友
        filtered_friends = []
        for contact in self.all_contacts:
            if contact.type != "group" and not contact.wxid.endswith("@chatroom"):
                if self._contact_matches_search(
                    contact, search_text
                ) and self._contact_matches_search(contact, friends_search_text):
                    filtered_friends.append(contact)

        # 过滤群聊
        filtered_groups = []
        for contact in self.all_contacts:
            if contact.type == "group" or contact.wxid.endswith("@chatroom"):
                if self._contact_matches_search(
                    contact, search_text
                ) and self._contact_matches_search(contact, groups_search_text):
                    filtered_groups.append(contact)

        # 更新显示
        self.update_friends_table(filtered_friends)
        self.update_groups_table(filtered_groups)

        # 更新统计
        self.friends_count_label.setText(f"好友: {len(filtered_friends)} 个")
        self.groups_count_label.setText(f"群聊: {len(filtered_groups)} 个")

    def _contact_matches_search(self, contact: Contact, search_text: str) -> bool:
        """检查联系人是否匹配搜索条件"""
        if not search_text:
            return True

        search_text = search_text.lower()
        return (
            search_text in contact.name.lower()
            or search_text in contact.wxid.lower()
            or search_text in contact.remark.lower()
        )

    def clear_search(self):
        """清空搜索"""
        self.search_input.clear()
        self.friends_search_input.clear()
        self.groups_search_input.clear()
        self.update_contacts_display()

    def select_all_friends(self):
        """全选好友"""
        for row in range(self.friends_table.rowCount()):
            checkbox = self.friends_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_friends(self):
        """取消全选好友"""
        for row in range(self.friends_table.rowCount()):
            checkbox = self.friends_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def select_all_groups(self):
        """全选群聊"""
        for row in range(self.groups_table.rowCount()):
            checkbox = self.groups_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_groups(self):
        """取消全选群聊"""
        for row in range(self.groups_table.rowCount()):
            checkbox = self.groups_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def add_selected_friends(self):
        """添加选中的好友"""
        selected_contacts = []
        for row in range(self.friends_table.rowCount()):
            checkbox = self.friends_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                name_item = self.friends_table.item(row, 1)
                if name_item:
                    contact = name_item.data(Qt.ItemDataRole.UserRole)
                    if contact:
                        selected_contacts.append(contact)

        self.add_contacts_to_selected(selected_contacts)

    def add_selected_groups(self):
        """添加选中的群聊"""
        selected_contacts = []
        for row in range(self.groups_table.rowCount()):
            checkbox = self.groups_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                name_item = self.groups_table.item(row, 1)
                if name_item:
                    contact = name_item.data(Qt.ItemDataRole.UserRole)
                    if contact:
                        selected_contacts.append(contact)

        self.add_contacts_to_selected(selected_contacts)

    def add_contacts_to_selected(self, contacts: List[Contact]):
        """添加联系人到已选列表"""
        for contact in contacts:
            # 检查是否已存在
            if not any(c.wxid == contact.wxid for c in self.selected_contacts):
                self.selected_contacts.append(contact)

        self.update_selected_table()

    def update_selected_table(self):
        """更新已选联系人表格"""
        self.selected_table.setRowCount(len(self.selected_contacts))

        for row, contact in enumerate(self.selected_contacts):
            # 类型
            type_item = QTableWidgetItem("好友" if contact.type == "friend" else "群聊")
            self.selected_table.setItem(row, 0, type_item)

            # 名称
            name_item = QTableWidgetItem(contact.name)
            self.selected_table.setItem(row, 1, name_item)

            # 微信ID
            wxid_item = QTableWidgetItem(contact.wxid)
            self.selected_table.setItem(row, 2, wxid_item)

            # 备注
            remark_item = QTableWidgetItem(contact.remark)
            self.selected_table.setItem(row, 3, remark_item)

        self.selected_count_label.setText(
            f"已选择: {len(self.selected_contacts)} 个联系人"
        )

    def remove_selected_contacts(self):
        """移除选中的已选联系人"""
        selected_rows = set()
        for item in self.selected_table.selectedItems():
            selected_rows.add(item.row())

        # 从后往前删除，避免索引变化
        for row in sorted(selected_rows, reverse=True):
            if row < len(self.selected_contacts):
                self.selected_contacts.pop(row)

        self.update_selected_table()

    def clear_all_selected(self):
        """清空所有已选联系人"""
        self.selected_contacts.clear()
        self.update_selected_table()

    def accept(self):
        """确认选择"""
        if not self.selected_contacts:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请至少选择一个联系人")
            return

        # 发送选中的联系人
        self.contacts_selected.emit(self.selected_contacts)
        super().accept()

    def get_selected_contacts(self) -> List[Contact]:
        """获取选中的联系人"""
        return self.selected_contacts.copy()


if __name__ == "__main__":
    # 测试代码
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # 创建测试联系人
    # 演示代码已移除
    dialog = ContactSelectorDialog()
    dialog.show()

    sys.exit(app.exec())
