#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版构建脚本 - 解决临时目录问题
包含注入工具和DLL文件
"""

import os
import sys
import subprocess
import shutil
import tempfile
import time
from pathlib import Path

def cleanup_temp_dirs():
    """清理PyInstaller临时目录"""
    print("🧹 清理临时目录...")
    
    temp_dir = Path(tempfile.gettempdir())
    mei_dirs = list(temp_dir.glob("_MEI*"))
    
    for mei_dir in mei_dirs:
        try:
            if mei_dir.exists():
                shutil.rmtree(mei_dir, ignore_errors=True)
                print(f"  ✅ 已清理: {mei_dir.name}")
        except Exception as e:
            print(f"  ⚠️  清理失败: {mei_dir.name} - {e}")
    
    # 清理项目构建目录
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            try:
                shutil.rmtree(dir_name)
                print(f"  ✅ 已清理: {dir_name}")
            except Exception as e:
                print(f"  ⚠️  清理失败: {dir_name} - {e}")

def setup_environment():
    """设置构建环境"""
    print("🔧 设置构建环境...")
    
    # 设置环境变量
    env_vars = {
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONUTF8': '1',
        'PYTHONLEGACYWINDOWSSTDIO': '1',
        'TEMP': str(Path.cwd() / "temp_build"),  # 使用项目内的临时目录
        'TMP': str(Path.cwd() / "temp_build"),
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # 创建临时目录
    temp_build = Path("temp_build")
    temp_build.mkdir(exist_ok=True)
    
    print("  ✅ 环境已设置")

def verify_tools():
    """验证工具文件"""
    print("🔍 验证工具文件...")
    
    tools = {
        "注入工具": "tools/Injector.exe",
        "微信DLL": "wxhelper_files/wxhelper.dll",
        "备用DLL": "wxhelper_files/wxhelper_latest.dll",
        "主程序": "main.py",
        "应用图标": "resources/icons/app_icon.ico"
    }
    
    missing = []
    for name, path in tools.items():
        if Path(path).exists():
            size = Path(path).stat().st_size
            print(f"  ✅ {name}: {path} ({size} bytes)")
        else:
            missing.append(f"{name} ({path})")
            print(f"  ❌ {name}: {path}")
    
    if missing:
        print(f"⚠️  缺少文件: {missing}")
        return False
    
    return True

def build_project():
    """构建项目"""
    print("🔨 构建项目...")
    
    # 简化的构建命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MeetSpaceWeChatSender",
        
        # 图标
        "--icon=resources/icons/app_icon.ico",
        
        # 关键工具文件
        "--add-data=tools/Injector.exe;tools",
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        
        # 基础资源
        "--add-data=resources/icons;resources/icons",
        "--add-data=version_info.txt;.",
        
        # 核心隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=ctypes",
        "--hidden-import=subprocess",
        
        # 项目模块
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=ui",
        "--hidden-import=utils",
        
        # 排除模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        
        # 构建选项
        "--clean",
        "--noconfirm",
        "--noupx",  # 禁用UPX压缩，避免问题
        
        "main.py"
    ]
    
    print("📋 执行构建命令...")
    
    try:
        # 运行构建
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=300,  # 5分钟超时
            cwd=str(Path.cwd())
        )
        
        print("✅ 构建完成")
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 构建超时（5分钟）")
        return False
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败，返回码: {e.returncode}")
        
        if e.stdout:
            print("标准输出:")
            print(e.stdout[-1000:])
        
        if e.stderr:
            print("错误输出:")
            print(e.stderr[-1000:])
        
        return False
        
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def test_executable():
    """测试可执行文件"""
    print("🧪 测试可执行文件...")
    
    exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
    
    if not exe_file.exists():
        print("❌ 可执行文件不存在")
        return False
    
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"✅ 文件存在: {exe_file}")
    print(f"📊 文件大小: {size_mb:.1f} MB")
    
    # 简单测试启动
    try:
        print("  🚀 测试启动...")
        process = subprocess.Popen(
            [str(exe_file)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待2秒
        time.sleep(2)
        
        if process.poll() is None:
            print("  ✅ 程序启动成功")
            process.terminate()
            process.wait(timeout=5)
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"  ❌ 程序启动失败")
            if stderr:
                print(f"  错误: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"  ⚠️  测试异常: {e}")
        return True  # 测试失败不影响构建成功

def cleanup_build_temp():
    """清理构建临时文件"""
    print("🧹 清理构建临时文件...")
    
    temp_dirs = ["temp_build", "build"]
    for temp_dir in temp_dirs:
        if Path(temp_dir).exists():
            try:
                shutil.rmtree(temp_dir)
                print(f"  ✅ 已清理: {temp_dir}")
            except Exception as e:
                print(f"  ⚠️  清理失败: {temp_dir} - {e}")

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 修复版构建")
    print("=" * 60)
    print("解决临时目录问题，包含注入工具和DLL文件")
    print("=" * 60)
    
    try:
        # 1. 清理临时目录
        cleanup_temp_dirs()
        
        # 2. 设置环境
        setup_environment()
        
        # 3. 验证工具文件
        if not verify_tools():
            print("❌ 工具文件验证失败")
            return False
        
        # 4. 构建项目
        if not build_project():
            print("❌ 项目构建失败")
            return False
        
        # 5. 测试可执行文件
        test_executable()
        
        # 6. 清理临时文件
        cleanup_build_temp()
        
        print("\n🎉 构建成功完成!")
        print("📁 输出文件: dist/MeetSpaceWeChatSender.exe")
        print("\n✨ 包含功能:")
        print("  🔧 注入工具 (Injector.exe)")
        print("  📚 微信DLL文件 (wxhelper.dll)")
        print("  🚀 完整项目功能")
        print("  ⚙️  运行时配置生成")
        print("\n📋 使用说明:")
        print("  1. 双击运行 MeetSpaceWeChatSender.exe")
        print("  2. 程序会自动处理微信注入")
        print("  3. 首次运行会生成配置文件")
        print("  4. 享受完整的群发功能")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  构建被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 构建异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 确保清理临时文件
        cleanup_build_temp()

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
