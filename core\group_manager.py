"""
分组管理模块

管理定时发送和循环发送的分组功能。
"""

import json
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from utils.logger import setup_logger

logger = setup_logger("group_manager")


class GroupMember:
    """分组成员类"""

    def __init__(self, wxid: str, name: str, member_type: str, remark: str = ""):
        self.wxid = wxid
        self.name = name
        self.member_type = member_type  # "contact" 或 "group"
        self.remark = remark  # 备注信息

    def to_dict(self) -> dict:
        return {
            "wxid": self.wxid,
            "name": self.name,
            "type": self.member_type,
            "remark": self.remark,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "GroupMember":
        return cls(
            wxid=data["wxid"],
            name=data["name"],
            member_type=data["type"],
            remark=data.get("remark", ""),
        )


class ContactGroup:
    """联系人分组类"""

    def __init__(self, group_id: str, name: str, group_type: str):
        self.group_id = group_id
        self.name = name
        self.group_type = group_type  # "timing" 或 "loop"
        self.members: List[GroupMember] = []
        self.created_time = datetime.now()
        self.updated_time = datetime.now()
        self.use_count = 0
        self.last_used = None
        self.tags: List[str] = []
        self.sort_score = 0.0
        self.last_send_time: Optional[datetime] = None

        # 新增：分组独立配置
        self.config = self._init_default_config()

    def _init_default_config(self) -> dict:
        """初始化默认配置"""
        if self.group_type == "timing":
            # 获取当前日期和时间作为默认值
            from datetime import datetime
            now = datetime.now()
            current_date = now.strftime("%Y-%m-%d")
            current_time = now.strftime("%H:%M")

            return {
                "send_settings": {
                    "interval_seconds": 5,  # 发送间隔（秒）
                    "batch_size": 10,  # 批量大小
                    "retry_count": 3,  # 重试次数
                    "timeout": 30,  # 超时时间
                    "use_system_risk_control": True,  # 默认使用系统风控设置
                    "custom_risk_control": {  # 自定义风控参数
                        "max_per_hour": 100,
                        "detection_mode": "smart",
                    },
                },
                "message_settings": {
                    "template_id": "",  # 默认模板ID（手动输入）
                    "default_content": "",  # 默认消息内容（空的）
                    "default_type": "rich_text",  # 默认消息类型（富文本消息）
                    "variables": {},  # 模板变量
                    "personalization": True,  # 个性化设置
                },
                "schedule_settings": {
                    "default_date": current_date,  # 默认日期（当前日期）
                    "default_time": current_time,  # 默认时间（当前时间）
                    "timezone": "Asia/Shanghai",  # 时区
                },
            }
        else:  # loop
            return {
                "send_settings": {
                    "interval_seconds": 5,  # 发送间隔（秒）
                    "batch_size": 10,  # 批量大小
                    "retry_count": 3,  # 重试次数
                    "timeout": 30,  # 超时时间
                    "use_system_risk_control": True,  # 是否使用系统风控
                    "custom_risk_control": {  # 自定义风控参数
                        "max_per_hour": 100,
                        "detection_mode": "smart",
                    },
                },
                "message_settings": {
                    "template_id": "",  # 默认模板ID
                    "default_content": "",  # 默认消息内容
                    "variables": {},  # 模板变量
                    "personalization": True,  # 个性化设置
                },
                "schedule_settings": {
                    "default_interval": 60,  # 默认循环间隔（分钟）
                    "auto_start": False,  # 是否自动启动
                    "timezone": "Asia/Shanghai",  # 时区
                },
            }

    def _merge_config_with_defaults(self, loaded_config: dict) -> dict:
        """
        将加载的配置与默认配置合并，确保加载的配置优先

        Args:
            loaded_config: 从文件加载的配置

        Returns:
            合并后的配置
        """
        def deep_merge(default_dict: dict, loaded_dict: dict) -> dict:
            """深度合并两个字典，loaded_dict的值优先"""
            result = default_dict.copy()

            for key, value in loaded_dict.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    # 递归合并嵌套字典
                    result[key] = deep_merge(result[key], value)
                else:
                    # 直接覆盖，包括False、None等值
                    result[key] = value

            return result

        # 获取默认配置
        default_config = self._init_default_config()

        # 深度合并，确保加载的配置（包括False值）优先
        merged_config = deep_merge(default_config, loaded_config)

        return merged_config

    def add_member(self, member: GroupMember) -> bool:
        """添加成员到分组"""
        # 检查是否已存在（允许重复）
        self.members.append(member)
        self.updated_time = datetime.now()
        logger.info(f"添加成员 {member.name} 到分组 {self.name}")
        return True

    def remove_member(self, wxid: str) -> bool:
        """从分组中移除成员"""
        original_count = len(self.members)
        self.members = [m for m in self.members if m.wxid != wxid]
        removed_count = original_count - len(self.members)

        if removed_count > 0:
            self.updated_time = datetime.now()
            logger.info(f"从分组 {self.name} 移除了 {removed_count} 个成员")
            return True
        return False

    def get_member_count(self) -> int:
        """获取成员数量"""
        return len(self.members)

    def update_usage(self):
        """更新使用统计"""
        self.use_count += 1
        self.last_used = datetime.now()
        self.updated_time = datetime.now()

    def calculate_sort_score(self, progress_percent: float = 0.0) -> float:
        """计算排序分数"""
        # 使用频率权重 (40%)
        days_since_created = (datetime.now() - self.created_time).days + 1
        frequency_weight = min(self.use_count / days_since_created, 1.0) * 0.4

        # 完成进度权重 (35%)
        progress_weight = progress_percent * 0.35

        # 创建时间权重 (25%) - 越新权重越高
        max_days = 30  # 最大考虑30天
        days_old = min(days_since_created, max_days)
        time_weight = (max_days - days_old) / max_days * 0.25

        self.sort_score = frequency_weight + progress_weight + time_weight
        return self.sort_score

    def update_config(self, config_path: str, value: Any) -> bool:
        """更新配置项"""
        try:
            keys = config_path.split(".")
            current = self.config

            # 导航到目标位置
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]

            # 设置值
            current[keys[-1]] = value
            self.updated_time = datetime.now()
            return True

        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False

    def get_config(self, config_path: str, default=None):
        """获取配置项"""
        try:
            keys = config_path.split(".")
            current = self.config

            for key in keys:
                if key not in current:
                    return default
                current = current[key]

            return current

        except Exception:
            return default

    def get_merged_send_settings(self, system_settings: dict = None):
        """获取合并后的发送设置（系统设置 + 分组设置）"""
        merged = {}

        # 先应用系统设置
        if system_settings:
            merged.update(system_settings)

        # 分组设置覆盖系统设置
        group_settings = self.config.get("send_settings", {})

        # 如果使用系统风控，则不覆盖风控设置
        if group_settings.get("use_system_risk_control", True):
            # 保留系统风控设置，只覆盖其他设置
            risk_control = merged.get("risk_control", {})
            merged.update(
                {
                    k: v
                    for k, v in group_settings.items()
                    if k not in ["use_system_risk_control", "custom_risk_control"]
                }
            )
            if risk_control:
                merged["risk_control"] = risk_control
        else:
            # 使用自定义风控设置
            merged.update(group_settings)
            merged["risk_control"] = group_settings.get("custom_risk_control", {})

        return merged

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "group_id": self.group_id,
            "name": self.name,
            "group_type": self.group_type,
            "members": [member.to_dict() for member in self.members],
            "created_time": self.created_time.isoformat(),
            "updated_time": self.updated_time.isoformat(),
            "use_count": self.use_count,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "tags": self.tags,
            "sort_score": self.sort_score,
            "config": self.config,  # 新增：保存配置
        }

    @classmethod
    def from_dict(cls, data: dict) -> "ContactGroup":
        """从字典创建分组"""
        group = cls(data["group_id"], data["name"], data["group_type"])
        group.members = [GroupMember.from_dict(m) for m in data.get("members", [])]
        group.created_time = datetime.fromisoformat(data["created_time"])
        group.updated_time = datetime.fromisoformat(data["updated_time"])
        group.use_count = data.get("use_count", 0)
        if data.get("last_used"):
            group.last_used = datetime.fromisoformat(data["last_used"])
        group.tags = data.get("tags", [])
        group.sort_score = data.get("sort_score", 0.0)

        # 加载配置，如果没有则使用默认配置
        if "config" in data:
            # 深度合并配置，确保保存的配置覆盖默认配置
            group.config = group._merge_config_with_defaults(data["config"])
        # 如果是旧数据，保持默认配置

        return group


class GroupManager:
    """分组管理器"""

    def __init__(self):
        self.data_dir = Path("data/groups")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        self.timing_groups_file = self.data_dir / "timing_groups.json"
        self.loop_groups_file = self.data_dir / "loop_groups.json"
        self.progress_file = self.data_dir / "progress.json"

        self.timing_groups: Dict[str, ContactGroup] = {}
        self.loop_groups: Dict[str, ContactGroup] = {}
        self.progress_data: Dict[str, dict] = {}

        self.load_all_data()

    def generate_group_id(self, group_type: str) -> str:
        """生成分组ID"""
        timestamp = int(time.time())
        return f"{group_type}_{timestamp}"

    def create_group(self, name: str, group_type: str) -> ContactGroup:
        """创建新分组"""
        group_id = self.generate_group_id(group_type)
        group = ContactGroup(group_id, name, group_type)

        if group_type == "timing":
            self.timing_groups[group_id] = group
        elif group_type == "loop":
            self.loop_groups[group_id] = group

        self.save_groups(group_type)
        logger.info(f"创建新分组: {name} ({group_type})")
        return group

    def delete_group(self, group_id: str, group_type: str) -> bool:
        """删除分组"""
        if group_type == "timing" and group_id in self.timing_groups:
            group_name = self.timing_groups[group_id].name
            del self.timing_groups[group_id]
            self.save_groups(group_type)
            logger.info(f"删除定时分组: {group_name}")
            return True
        elif group_type == "loop" and group_id in self.loop_groups:
            group_name = self.loop_groups[group_id].name
            del self.loop_groups[group_id]
            self.save_groups(group_type)
            logger.info(f"删除循环分组: {group_name}")
            return True
        return False

    def update_group(self, group_id: str, group_type: str, **kwargs) -> bool:
        """更新分组信息"""
        group = self.get_group(group_id, group_type)
        if not group:
            return False

        if "name" in kwargs:
            group.name = kwargs["name"]
        if "tags" in kwargs:
            group.tags = kwargs["tags"]

        group.updated_time = datetime.now()
        self.save_groups(group_type)
        return True

    def get_group(self, group_id: str, group_type: str) -> Optional[ContactGroup]:
        """获取分组"""
        if group_type == "timing":
            return self.timing_groups.get(group_id)
        elif group_type == "loop":
            return self.loop_groups.get(group_id)
        return None

    def get_all_groups(self, group_type: str) -> List[ContactGroup]:
        """获取所有分组"""
        if group_type == "timing":
            return list(self.timing_groups.values())
        elif group_type == "loop":
            return list(self.loop_groups.values())
        return []

    def get_sorted_groups(self, group_type: str) -> List[ContactGroup]:
        """获取排序后的分组列表"""
        groups = self.get_all_groups(group_type)

        # 更新排序分数
        for group in groups:
            progress = self.get_group_progress(group.group_id)
            group.calculate_sort_score(progress)

        # 按分数排序
        return sorted(groups, key=lambda g: g.sort_score, reverse=True)

    def add_member_to_group(
        self, group_id: str, group_type: str, member: GroupMember
    ) -> bool:
        """添加成员到分组"""
        group = self.get_group(group_id, group_type)
        if group:
            result = group.add_member(member)
            if result:
                self.save_groups(group_type)
            return result
        return False

    def remove_member_from_group(
        self, group_id: str, group_type: str, wxid: str
    ) -> bool:
        """从分组移除成员"""
        group = self.get_group(group_id, group_type)
        if group:
            result = group.remove_member(wxid)
            if result:
                self.save_groups(group_type)
            return result
        return False

    def get_member_groups(self, wxid: str, group_type: str) -> List[ContactGroup]:
        """获取成员所在的分组"""
        groups = []
        for group in self.get_all_groups(group_type):
            for member in group.members:
                if member.wxid == wxid:
                    groups.append(group)
                    break
        return groups

    def update_group_config(
        self, group_id: str, group_type: str, config_path: str, value: Any
    ) -> bool:
        """更新分组配置"""
        group = self.get_group(group_id, group_type)
        if group:
            success = group.update_config(config_path, value)
            if success:
                self.save_groups(group_type)
                logger.info(f"更新分组配置: {group.name} - {config_path} = {value}")
            return success
        return False

    def get_group_config(
        self, group_id: str, group_type: str, config_path: str, default=None
    ):
        """获取分组配置"""
        group = self.get_group(group_id, group_type)
        if group:
            return group.get_config(config_path, default)
        return default

    def get_group_send_settings(
        self, group_id: str, group_type: str, system_settings: dict = None
    ) -> dict:
        """获取分组的合并发送设置"""
        group = self.get_group(group_id, group_type)
        if group:
            return group.get_merged_send_settings(system_settings)
        return system_settings or {}

    def save_group_config_immediately(self, group_id: str, group_type: str) -> bool:
        """立即保存分组配置"""
        try:
            self.save_groups(group_type)
            logger.info(f"立即保存分组配置: {group_id}")
            return True
        except Exception as e:
            logger.error(f"立即保存分组配置失败: {e}")
            return False

    def update_group_progress(self, group_id: str, sent_count: int, total_count: int):
        """更新分组进度"""
        today = datetime.now().strftime("%Y-%m-%d")
        if today not in self.progress_data:
            self.progress_data[today] = {}

        progress_percent = (sent_count / total_count * 100) if total_count > 0 else 0
        self.progress_data[today][group_id] = {
            "sent_count": sent_count,
            "total_count": total_count,
            "progress_percent": progress_percent,
            "updated_time": datetime.now().isoformat(),
        }

        self.save_progress()

    def get_group_progress(self, group_id: str) -> float:
        """获取分组今日进度百分比"""
        today = datetime.now().strftime("%Y-%m-%d")
        if today in self.progress_data and group_id in self.progress_data[today]:
            return self.progress_data[today][group_id]["progress_percent"]
        return 0.0

    def get_group_progress_detail(self, group_id: str) -> Tuple[int, int, float]:
        """获取分组详细进度信息"""
        today = datetime.now().strftime("%Y-%m-%d")
        if today in self.progress_data and group_id in self.progress_data[today]:
            data = self.progress_data[today][group_id]
            return data["sent_count"], data["total_count"], data["progress_percent"]
        return 0, 0, 0.0

    def save_groups(self, group_type: str):
        """保存分组数据"""
        try:
            if group_type == "timing":
                data = {
                    gid: group.to_dict() for gid, group in self.timing_groups.items()
                }
                with open(self.timing_groups_file, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            elif group_type == "loop":
                data = {gid: group.to_dict() for gid, group in self.loop_groups.items()}
                with open(self.loop_groups_file, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            logger.debug(f"保存{group_type}分组数据成功")
        except Exception as e:
            logger.error(f"保存{group_type}分组数据失败: {e}")

    def save_progress(self):
        """保存进度数据"""
        try:
            with open(self.progress_file, "w", encoding="utf-8") as f:
                json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
            logger.debug("保存进度数据成功")
        except Exception as e:
            logger.error(f"保存进度数据失败: {e}")

    def load_all_data(self):
        """加载所有数据"""
        self.load_groups("timing")
        self.load_groups("loop")
        self.load_progress()

    def load_groups(self, group_type: str):
        """加载分组数据"""
        try:
            file_path = (
                self.timing_groups_file
                if group_type == "timing"
                else self.loop_groups_file
            )
            if file_path.exists():
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)

                groups_dict = (
                    self.timing_groups if group_type == "timing" else self.loop_groups
                )
                groups_dict.clear()

                for group_id, group_data in data.items():
                    group = ContactGroup.from_dict(group_data)
                    groups_dict[group_id] = group

                logger.info(f"加载{group_type}分组数据成功，共{len(groups_dict)}个分组")
        except Exception as e:
            logger.error(f"加载{group_type}分组数据失败: {e}")

    def load_progress(self):
        """加载进度数据"""
        try:
            if self.progress_file.exists():
                with open(self.progress_file, "r", encoding="utf-8") as f:
                    self.progress_data = json.load(f)
                logger.info("加载进度数据成功")
        except Exception as e:
            logger.error(f"加载进度数据失败: {e}")
            self.progress_data = {}


# 全局分组管理器实例
group_manager = GroupManager()
