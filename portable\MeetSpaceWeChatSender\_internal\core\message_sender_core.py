"""
统一的消息发送核心模块

提供定时发送和循环发送共享的核心逻辑，包括：
- 统一的消息发送接口
- 错误处理和重试机制
- 风控检查
- 日志记录
- 进度监控
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from utils.logger import setup_logger

logger = setup_logger("message_sender_core")


class MessageType(Enum):
    """消息类型枚举"""

    TEXT = "text"
    RICH_TEXT = "rich_text"
    IMAGE = "image"
    FILE = "file"


class SendResult(Enum):
    """发送结果枚举"""

    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class SendProgress:
    """发送进度数据"""

    task_id: str
    current: int
    total: int
    success_count: int
    failed_count: int
    skipped_count: int
    start_time: datetime
    current_time: datetime

    @property
    def progress_percent(self) -> float:
        """进度百分比"""
        return (self.current / self.total * 100) if self.total > 0 else 0

    @property
    def elapsed_time(self) -> timedelta:
        """已用时间"""
        return self.current_time - self.start_time

    @property
    def estimated_remaining(self) -> Optional[timedelta]:
        """预估剩余时间"""
        if self.current == 0:
            return None
        avg_time_per_item = self.elapsed_time.total_seconds() / self.current
        remaining_items = self.total - self.current
        return timedelta(seconds=avg_time_per_item * remaining_items)


@dataclass
class SendMember:
    """发送成员数据"""

    wxid: str
    name: str

    def __str__(self):
        return f"{self.name}({self.wxid})"


@dataclass
class SendTask:
    """发送任务数据"""

    task_id: str
    message_content: str
    message_type: MessageType
    members: List[SendMember]

    # 可选配置
    send_interval: float = 1.0  # 发送间隔（秒）
    enable_risk_control: bool = True  # 是否启用风控
    max_retries: int = 0  # 最大重试次数

    def __post_init__(self):
        """初始化后处理"""
        if isinstance(self.message_type, str):
            self.message_type = MessageType(self.message_type)


class MessageSenderCore:
    """统一的消息发送核心"""

    def __init__(self, connector=None):
        self.connector = connector
        self._progress_callbacks: List[Callable[[SendProgress], None]] = []
        self._member_callbacks: List[
            Callable[[str, SendMember, SendResult, str], None]
        ] = []

    def set_connector(self, connector):
        """设置连接器"""
        self.connector = connector
        logger.info("消息发送核心已设置连接器")

    def add_progress_callback(self, callback: Callable[[SendProgress], None]):
        """添加进度回调"""
        self._progress_callbacks.append(callback)

    def add_member_callback(
        self, callback: Callable[[str, SendMember, SendResult, str], None]
    ):
        """添加成员发送结果回调"""
        self._member_callbacks.append(callback)

    async def send_task(self, task: SendTask) -> SendProgress:
        """
        执行发送任务

        Args:
            task: 发送任务

        Returns:
            最终的发送进度
        """
        logger.info(f"开始执行发送任务: {task.task_id}")
        logger.info(
            f"任务详情: 消息类型={task.message_type.value}, 成员数={len(task.members)}"
        )

        # 初始化进度
        progress = SendProgress(
            task_id=task.task_id,
            current=0,
            total=len(task.members),
            success_count=0,
            failed_count=0,
            skipped_count=0,
            start_time=datetime.now(),
            current_time=datetime.now(),
        )

        # 发送进度回调
        self._notify_progress(progress)

        # 逐个发送
        for i, member in enumerate(task.members):
            try:
                # 更新当前进度
                progress.current = i + 1
                progress.current_time = datetime.now()

                # 检查是否应该跳过
                if task.enable_risk_control and not self._check_risk_control(
                    task, member
                ):
                    result = SendResult.SKIPPED
                    error_msg = "风控检查未通过"
                    progress.skipped_count += 1
                    logger.warning(f"跳过发送: {member.name} - {error_msg}")
                else:
                    # 执行发送
                    result, error_msg = await self._send_to_member(task, member)
                    if result == SendResult.SUCCESS:
                        progress.success_count += 1
                    else:
                        progress.failed_count += 1

                # 通知成员发送结果
                self._notify_member_result(task.task_id, member, result, error_msg)

                # 通知进度更新
                self._notify_progress(progress)

                # 发送间隔
                if i < len(task.members) - 1:  # 不是最后一个
                    await asyncio.sleep(task.send_interval)

            except Exception as e:
                logger.error(f"发送到 {member.name} 时发生异常: {e}")
                progress.failed_count += 1
                self._notify_member_result(
                    task.task_id, member, SendResult.ERROR, str(e)
                )

        # 最终进度
        progress.current_time = datetime.now()
        self._notify_progress(progress)

        logger.info(f"发送任务完成: {task.task_id}")
        logger.info(
            f"结果统计: 成功={progress.success_count}, 失败={progress.failed_count}, 跳过={progress.skipped_count}"
        )

        return progress

    async def _send_to_member(
        self, task: SendTask, member: SendMember
    ) -> tuple[SendResult, str]:
        """
        发送消息到指定成员

        Returns:
            (发送结果, 错误信息)
        """
        try:
            if not self.connector:
                return SendResult.ERROR, "连接器未设置"

            logger.debug(f"发送消息到 {member.name}: {task.message_type.value}")

            # 根据消息类型选择发送方法
            if task.message_type == MessageType.RICH_TEXT:
                success = await self._send_rich_text_message(
                    member.wxid, task.message_content
                )
            elif task.message_type == MessageType.TEXT:
                success = await self._send_text_message(
                    member.wxid, task.message_content
                )
            elif task.message_type == MessageType.IMAGE:
                success = await self._send_image_message(
                    member.wxid, task.message_content
                )
            elif task.message_type == MessageType.FILE:
                success = await self._send_file_message(
                    member.wxid, task.message_content
                )
            else:
                return SendResult.ERROR, f"不支持的消息类型: {task.message_type}"

            if success:
                logger.debug(f"发送成功: {member.name}")
                return SendResult.SUCCESS, ""
            else:
                logger.warning(f"发送失败: {member.name}")
                return SendResult.FAILED, "发送方法返回失败"

        except Exception as e:
            logger.error(f"发送到 {member.name} 异常: {e}")
            return SendResult.ERROR, str(e)

    async def _send_rich_text_message(self, wxid: str, content: str) -> bool:
        """发送富文本消息"""
        try:
            message_data = json.loads(content)
            if hasattr(self.connector, "send_itchat_style_rich_message"):
                return await self.connector.send_itchat_style_rich_message(
                    wxid, message_data
                )
            else:
                # 回退到普通文本
                plain_text = message_data.get("plain_text", "")
                if plain_text:
                    return await self._send_text_message(wxid, plain_text)
                return False
        except (json.JSONDecodeError, AttributeError) as e:
            logger.warning(f"富文本发送失败，回退到普通文本: {e}")
            return await self._send_text_message(wxid, content)

    async def _send_text_message(self, wxid: str, content: str) -> bool:
        """发送文本消息"""
        if hasattr(self.connector, "send_text_message"):
            return await self.connector.send_text_message(wxid, content)
        return False

    async def _send_image_message(self, wxid: str, image_path: str) -> bool:
        """发送图片消息"""
        if hasattr(self.connector, "send_image_message"):
            return await self.connector.send_image_message(wxid, image_path)
        return False

    async def _send_file_message(self, wxid: str, file_path: str) -> bool:
        """发送文件消息"""
        if hasattr(self.connector, "send_file_message"):
            return await self.connector.send_file_message(wxid, file_path)
        return False

    def _check_risk_control(self, task: SendTask, member: SendMember) -> bool:
        """检查风控"""
        try:
            if not task.enable_risk_control:
                return True

            # 基础风控检查
            from core.risk_control import RiskController
            from config.wechat_config import WeChatConfig

            wechat_config = WeChatConfig()
            risk_controller = RiskController(wechat_config)
            return risk_controller.check_risk_control()

        except Exception as e:
            logger.error(f"风控检查异常: {e}")
            return False

    def _notify_progress(self, progress: SendProgress):
        """通知进度更新"""
        for callback in self._progress_callbacks:
            try:
                callback(progress)
            except Exception as e:
                logger.error(f"进度回调异常: {e}")

    def _notify_member_result(
        self, task_id: str, member: SendMember, result: SendResult, error_msg: str
    ):
        """通知成员发送结果"""
        for callback in self._member_callbacks:
            try:
                callback(task_id, member, result, error_msg)
            except Exception as e:
                logger.error(f"成员结果回调异常: {e}")


# 全局实例
message_sender_core = MessageSenderCore()
