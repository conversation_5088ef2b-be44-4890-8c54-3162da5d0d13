#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清空日志文件后的日志恢复功能
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_log_clearing_and_recovery():
    """测试日志清空和恢复功能"""
    print("🧹 测试日志清空和恢复功能...")
    
    try:
        # 1. 初始化日志系统
        from utils.logger import setup_logger
        from utils.path_manager import path_manager
        
        logs_dir = path_manager.app_data_dir / "logs"
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"日志目录: {logs_dir}")
        
        # 2. 创建多个日志器并写入日志
        test_loggers = [
            "main",
            "main_window", 
            "config_manager",
            "injector_tool",
            "test_logger"
        ]
        
        print("\n📝 创建日志器并写入测试日志...")
        loggers = {}
        for logger_name in test_loggers:
            logger = setup_logger(logger_name)
            loggers[logger_name] = logger
            logger.info(f"这是来自 {logger_name} 的测试日志")
            logger.warning(f"这是来自 {logger_name} 的警告日志")
            print(f"   创建日志器: {logger_name}")
        
        # 3. 检查日志文件是否创建
        print("\n📁 检查日志文件...")
        log_files_before = []
        total_size_before = 0
        
        for file_path in logs_dir.glob("*.log"):
            if file_path.is_file():
                size = file_path.stat().st_size
                log_files_before.append(file_path.name)
                total_size_before += size
                print(f"   {file_path.name}: {size} 字节")
        
        print(f"总计: {len(log_files_before)} 个文件, {total_size_before} 字节")
        
        # 4. 模拟清空日志文件操作
        print("\n🧹 执行清空日志文件操作...")
        
        # 关闭所有日志处理器
        all_loggers = [logging.getLogger(name) for name in logging.root.manager.loggerDict]
        all_loggers.append(logging.getLogger())  # 添加根日志器
        
        closed_handlers = 0
        for logger in all_loggers:
            for handler in logger.handlers[:]:
                if isinstance(handler, (logging.FileHandler, logging.handlers.RotatingFileHandler)):
                    try:
                        handler.close()
                        logger.removeHandler(handler)
                        closed_handlers += 1
                    except Exception:
                        pass
        
        print(f"   关闭了 {closed_handlers} 个文件处理器")
        
        # 等待文件句柄释放
        time.sleep(0.5)
        
        # 清空所有日志文件
        cleared_files = 0
        for file_path in logs_dir.glob("*.log"):
            if file_path.is_file():
                try:
                    with open(file_path, "r+", encoding="utf-8") as f:
                        f.truncate(0)
                    cleared_files += 1
                    print(f"   清空: {file_path.name}")
                except Exception as e:
                    print(f"   失败: {file_path.name} - {e}")
        
        print(f"   清空了 {cleared_files} 个文件")
        
        # 5. 重新初始化日志处理器
        print("\n🔄 重新初始化日志处理器...")
        
        # 获取所有已存在的日志器名称
        existing_loggers = list(logging.root.manager.loggerDict.keys())
        all_logger_names = set(test_loggers + existing_loggers)
        
        # 重新初始化所有日志器
        success_count = 0
        for logger_name in all_logger_names:
            try:
                # 清除旧的处理器
                logger_obj = logging.getLogger(logger_name)
                logger_obj.handlers.clear()
                
                # 重新设置
                setup_logger(logger_name)
                success_count += 1
            except Exception:
                pass
        
        print(f"   重新初始化了 {success_count} 个日志器")
        
        # 6. 测试日志恢复
        print("\n📝 测试日志恢复...")
        
        # 写入新的测试日志
        for logger_name in test_loggers:
            try:
                logger = logging.getLogger(logger_name)
                logger.info(f"清空后的测试日志 - {logger_name}")
                logger.warning(f"清空后的警告日志 - {logger_name}")
                print(f"   ✅ {logger_name}: 日志写入成功")
            except Exception as e:
                print(f"   ❌ {logger_name}: 日志写入失败 - {e}")
        
        # 7. 检查恢复后的日志文件
        print("\n📁 检查恢复后的日志文件...")
        time.sleep(1)  # 等待日志写入
        
        log_files_after = []
        total_size_after = 0
        
        for file_path in logs_dir.glob("*.log"):
            if file_path.is_file():
                size = file_path.stat().st_size
                log_files_after.append(file_path.name)
                total_size_after += size
                print(f"   {file_path.name}: {size} 字节")
        
        print(f"总计: {len(log_files_after)} 个文件, {total_size_after} 字节")
        
        # 8. 验证日志内容
        print("\n🔍 验证日志内容...")
        content_found = False
        
        for file_path in logs_dir.glob("*.log"):
            if file_path.is_file() and file_path.stat().st_size > 0:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if "清空后的测试日志" in content:
                            content_found = True
                            print(f"   ✅ {file_path.name}: 包含恢复后的日志内容")
                        else:
                            print(f"   ⚠️  {file_path.name}: 不包含预期内容")
                except Exception as e:
                    print(f"   ❌ {file_path.name}: 读取失败 - {e}")
        
        # 9. 清理测试文件
        print("\n🧹 清理测试文件...")
        for file_path in logs_dir.glob("*.log"):
            if file_path.name.startswith("test_"):
                try:
                    file_path.unlink()
                    print(f"   删除: {file_path.name}")
                except Exception:
                    pass
        
        # 10. 结果评估
        success = (
            cleared_files > 0 and  # 成功清空了文件
            success_count > 0 and  # 成功重新初始化了日志器
            total_size_after > 0 and  # 恢复后有新的日志内容
            content_found  # 找到了预期的日志内容
        )
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 日志清空恢复测试")
    print("=" * 60)
    
    success = test_log_clearing_and_recovery()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 日志清空和恢复测试通过！")
        print("\n✨ 验证内容:")
        print("  🧹 日志文件成功清空")
        print("  🔄 日志处理器成功重新初始化")
        print("  📝 新日志内容成功写入")
        print("  ✅ 日志系统完全恢复正常")
        
        print("\n📋 现在清空日志文件后，后续日志会正常产生！")
    else:
        print("❌ 日志清空和恢复测试失败！")
        print("\n🔧 可能的问题:")
        print("  1. 日志处理器没有正确重新初始化")
        print("  2. 文件权限问题")
        print("  3. 日志器配置问题")
        print("  4. 路径管理器问题")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
