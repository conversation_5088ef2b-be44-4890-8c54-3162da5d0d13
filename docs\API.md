# API 文档

本文档描述了微信无感群发助手的核心API接口。

## 核心模块

### 1. 连接器模块 (Connectors)

#### WeChatFerryConnector

基于WeChatFerry的微信连接器。

```python
from core.wechatferry_connector import WeChatFerryConnector

connector = WeChatFerryConnector()
```

**主要方法：**

- `async connect() -> Tuple[bool, str]`: 连接微信客户端
- `async disconnect() -> bool`: 断开连接
- `async get_contacts() -> List[Contact]`: 获取联系人列表
- `async send_text_message(wxid: str, content: str) -> bool`: 发送文本消息
- `async send_image_message(wxid: str, image_path: str) -> bool`: 发送图片消息
- `async send_file_message(wxid: str, file_path: str) -> bool`: 发送文件消息

#### HTTPAPIConnector

基于HTTP API的微信连接器。

```python
from core.http_api_connector import HTTPAPIConnector

connector = HTTPAPIConnector(
    api_type="wxhelper",
    base_url="http://localhost:19088"
)
```

**主要方法：**

与WeChatFerryConnector相同的接口，但通过HTTP API实现。

### 2. 消息模板模块 (MessageTemplate)

管理消息模板的创建、编辑和渲染。

```python
from core.message_template import MessageTemplate

template = MessageTemplate()
```

**主要方法：**

- `create_template(name: str, template_data: dict) -> bool`: 创建模板
- `get_template(template_id: str) -> Optional[Template]`: 获取模板
- `get_all_templates() -> List[Template]`: 获取所有模板
- `render_template(template_id: str, variables: dict) -> str`: 渲染模板
- `delete_template(template_id: str) -> bool`: 删除模板

**模板变量：**

- `{{name}}`: 联系人名称
- `{{wxid}}`: 联系人微信ID
- `{{current_time}}`: 当前时间
- `{{current_date}}`: 当前日期
- `{{random_emoji}}`: 随机表情

### 3. 发送监控模块 (SendMonitor)

监控和管理消息发送任务。

```python
from core.send_monitor import SendMonitor, SendTask, SendStatus

monitor = SendMonitor()
```

**主要方法：**

- `add_task(task: SendTask) -> str`: 添加发送任务
- `get_task(task_id: str) -> Optional[SendTask]`: 获取任务
- `update_task_status(task_id: str, status: SendStatus) -> bool`: 更新任务状态
- `get_statistics() -> dict`: 获取发送统计
- `clear_completed_tasks() -> int`: 清理已完成任务

**发送状态：**

- `PENDING`: 等待发送
- `SENDING`: 正在发送
- `SUCCESS`: 发送成功
- `FAILED`: 发送失败
- `CANCELLED`: 已取消

### 4. 风控管理模块 (RiskController)

实现发送频率控制和风险检测。

```python
from core.risk_control import RiskController
from config.wechat_config import WeChatConfig

config = WeChatConfig()
risk_controller = RiskController(config)
```

**主要方法：**

- `can_send_message(wxid: str) -> Tuple[bool, str]`: 检查是否可以发送
- `get_send_delay() -> float`: 获取发送延迟
- `record_send_result(wxid: str, success: bool) -> None`: 记录发送结果
- `get_statistics() -> dict`: 获取风控统计

### 5. 配置管理模块 (WeChatConfig)

管理应用程序配置。

```python
from config.wechat_config import WeChatConfig

config = WeChatConfig.load_from_file()
```

**主要方法：**

- `load_from_file(config_file: str) -> WeChatConfig`: 从文件加载配置
- `save_to_file(config_file: str) -> bool`: 保存配置到文件
- `validate() -> bool`: 验证配置有效性
- `update(**kwargs) -> None`: 更新配置参数

**配置参数：**

- `send_interval_min`: 最小发送间隔（秒）
- `send_interval_max`: 最大发送间隔（秒）
- `batch_size`: 批次大小
- `batch_interval`: 批次间隔（秒）
- `daily_send_limit`: 每日发送限制
- `hourly_send_limit`: 每小时发送限制
- `enable_risk_control`: 是否启用风控
- `simulate_human`: 是否模拟人工操作
- `random_delay`: 是否启用随机延迟

## 工具模块

### 1. 数据验证 (Validators)

```python
from utils.validators import (
    validate_wxid,
    validate_contact,
    validate_message,
    validate_file_path
)

# 验证微信ID
valid, error = validate_wxid("test_user")

# 验证联系人
valid, error = validate_contact("test_user", "测试用户")

# 验证消息
valid, error = validate_message("Hello", "text")

# 验证文件路径
valid, error = validate_file_path("/path/to/file.jpg")
```

### 2. 异常处理 (Exceptions)

```python
from utils.exceptions import (
    WeChatMassSenderError,
    ConnectionError,
    ValidationError,
    handle_exception
)

try:
    # 一些操作
    pass
except Exception as e:
    error_msg = handle_exception(e, "操作上下文")
    print(error_msg)
```

### 3. 性能监控 (Performance)

```python
from utils.performance import (
    timing_decorator,
    async_timing_decorator,
    performance_context,
    get_performance_report
)

# 使用装饰器
@timing_decorator("my_function")
def my_function():
    pass

@async_timing_decorator("my_async_function")
async def my_async_function():
    pass

# 使用上下文管理器
with performance_context("operation_name"):
    # 一些操作
    pass

# 获取性能报告
report = get_performance_report()
```

## 信号和事件

### 连接器信号

```python
# 连接成功
connector.login_success.connect(on_login_success)

# 连接失败
connector.login_failed.connect(on_login_failed)

# 联系人更新
connector.contact_updated.connect(on_contact_updated)

# 连接丢失
connector.connection_lost.connect(on_connection_lost)
```

### 发送监控信号

```python
# 任务状态变化
monitor.task_status_changed.connect(on_task_status_changed)

# 进度更新
monitor.progress_updated.connect(on_progress_updated)

# 发送完成
monitor.send_completed.connect(on_send_completed)
```

## 使用示例

### 基本发送流程

```python
import asyncio
from core.wechatferry_connector import WeChatFerryConnector
from core.message_template import MessageTemplate
from core.send_monitor import SendMonitor, SendTask

async def send_message_example():
    # 1. 创建连接器
    connector = WeChatFerryConnector()
    
    # 2. 连接微信
    success, message = await connector.connect()
    if not success:
        print(f"连接失败: {message}")
        return
    
    # 3. 获取联系人
    contacts = await connector.get_contacts()
    
    # 4. 创建模板
    template = MessageTemplate()
    template.create_template("greeting", {
        "type": "text",
        "content": "你好 {{name}}，今天是 {{current_date}}！"
    })
    
    # 5. 渲染消息
    for contact in contacts[:5]:  # 只发送给前5个联系人
        message_content = template.render_template("greeting", {
            "name": contact.name
        })
        
        # 6. 发送消息
        success = await connector.send_text_message(
            contact.wxid, 
            message_content
        )
        
        if success:
            print(f"发送成功: {contact.name}")
        else:
            print(f"发送失败: {contact.name}")
    
    # 7. 断开连接
    await connector.disconnect()

# 运行示例
asyncio.run(send_message_example())
```

### 批量发送示例

```python
from core.send_monitor import SendMonitor, SendTask, SendStatus
from core.risk_control import RiskController

async def batch_send_example():
    connector = WeChatFerryConnector()
    monitor = SendMonitor()
    risk_controller = RiskController(config)
    
    # 连接微信
    await connector.connect()
    
    # 获取联系人
    contacts = await connector.get_contacts()
    
    # 创建发送任务
    for contact in contacts:
        task = SendTask(
            wxid=contact.wxid,
            name=contact.name,
            message_type="text",
            content="批量发送测试消息"
        )
        monitor.add_task(task)
    
    # 执行发送
    for task_id, task in monitor.tasks.items():
        # 检查风控
        can_send, reason = risk_controller.can_send_message(task.wxid)
        if not can_send:
            print(f"风控阻止: {reason}")
            continue
        
        # 发送消息
        monitor.update_task_status(task_id, SendStatus.SENDING)
        
        success = await connector.send_text_message(
            task.wxid, 
            task.content
        )
        
        if success:
            monitor.update_task_status(task_id, SendStatus.SUCCESS)
            risk_controller.record_send_result(task.wxid, True)
        else:
            monitor.update_task_status(task_id, SendStatus.FAILED)
            risk_controller.record_send_result(task.wxid, False)
        
        # 等待延迟
        delay = risk_controller.get_send_delay()
        await asyncio.sleep(delay)
    
    await connector.disconnect()

asyncio.run(batch_send_example())
```
