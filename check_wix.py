#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WiX Toolset 安装检查工具
检查WiX Toolset是否正确安装并可用
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_wix_in_path():
    """检查WiX工具是否在PATH中"""
    print("🔍 检查WiX工具是否在PATH中...")
    
    wix_tools = ["candle.exe", "light.exe", "heat.exe"]
    found_tools = {}
    
    for tool in wix_tools:
        tool_path = shutil.which(tool)
        if tool_path:
            found_tools[tool] = tool_path
            print(f"  ✅ {tool}: {tool_path}")
        else:
            found_tools[tool] = None
            print(f"  ❌ {tool}: 未找到")
    
    return found_tools

def check_wix_installation():
    """检查WiX Toolset安装"""
    print("🔍 检查WiX Toolset安装...")
    
    # 常见的WiX安装路径
    possible_paths = [
        r"C:\Program Files (x86)\WiX Toolset v3.11\bin",
        r"C:\Program Files\WiX Toolset v3.11\bin",
        r"C:\Program Files (x86)\WiX Toolset v3.14\bin",
        r"C:\Program Files\WiX Toolset v3.14\bin",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Microsoft\WiX\v3.x",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\WiX\v3.x",
    ]
    
    found_installations = []
    
    for path in possible_paths:
        if os.path.exists(path):
            candle_path = os.path.join(path, "candle.exe")
            light_path = os.path.join(path, "light.exe")
            
            if os.path.exists(candle_path) and os.path.exists(light_path):
                found_installations.append(path)
                print(f"  ✅ 找到WiX安装: {path}")
    
    if not found_installations:
        print("  ❌ 未找到WiX Toolset安装")
        
        # 检查是否通过winget安装
        print("\n🔍 检查winget安装的WiX...")
        try:
            result = subprocess.run(["winget", "list", "WiX.Toolset"], 
                                  capture_output=True, text=True, timeout=10)
            if "WiX.Toolset" in result.stdout:
                print("  ✅ 通过winget安装的WiX Toolset")
            else:
                print("  ❌ 未通过winget安装WiX Toolset")
        except Exception as e:
            print(f"  ⚠️  无法检查winget: {e}")
    
    return found_installations

def test_wix_tools():
    """测试WiX工具功能"""
    print("🧪 测试WiX工具功能...")
    
    # 测试candle
    print("  测试candle...")
    try:
        result = subprocess.run(["candle", "-?"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("    ✅ candle工具正常")
            # 提取版本信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if "version" in line.lower() or "Microsoft" in line:
                    print(f"    📋 {line.strip()}")
                    break
        else:
            print(f"    ❌ candle工具异常: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("    ❌ candle工具超时")
        return False
    except FileNotFoundError:
        print("    ❌ candle工具未找到")
        return False
    except Exception as e:
        print(f"    ❌ candle工具错误: {e}")
        return False
    
    # 测试light
    print("  测试light...")
    try:
        result = subprocess.run(["light", "-?"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("    ✅ light工具正常")
        else:
            print(f"    ❌ light工具异常: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("    ❌ light工具超时")
        return False
    except FileNotFoundError:
        print("    ❌ light工具未找到")
        return False
    except Exception as e:
        print(f"    ❌ light工具错误: {e}")
        return False
    
    return True

def check_environment():
    """检查环境变量"""
    print("🔍 检查环境变量...")
    
    path_env = os.environ.get("PATH", "")
    wix_paths = [p for p in path_env.split(os.pathsep) if "wix" in p.lower()]
    
    if wix_paths:
        print("  ✅ 找到WiX相关PATH:")
        for path in wix_paths:
            print(f"    - {path}")
    else:
        print("  ❌ PATH中未找到WiX相关路径")
    
    # 检查WiX相关环境变量
    wix_vars = ["WIX", "WixToolPath", "WIX_ROOT"]
    found_vars = {}
    
    for var in wix_vars:
        value = os.environ.get(var)
        if value:
            found_vars[var] = value
            print(f"  ✅ {var}: {value}")
        else:
            print(f"  ❌ {var}: 未设置")
    
    return found_vars

def provide_solutions():
    """提供解决方案"""
    print("\n💡 解决方案:")
    
    print("\n1. 如果WiX Toolset未安装:")
    print("   winget install WiX.Toolset")
    print("   或从官网下载: https://wixtoolset.org/releases/")
    
    print("\n2. 如果WiX已安装但PATH中找不到:")
    print("   手动添加WiX bin目录到系统PATH")
    print("   例如: C:\\Program Files (x86)\\WiX Toolset v3.11\\bin")
    
    print("\n3. 重启命令提示符或PowerShell")
    print("   安装后需要重启终端以刷新PATH")
    
    print("\n4. 验证安装:")
    print("   candle -?")
    print("   light -?")

def main():
    """主函数"""
    print("🔧 WiX Toolset 安装检查工具")
    print("=" * 50)
    
    # 1. 检查PATH中的WiX工具
    path_tools = check_wix_in_path()
    
    # 2. 检查WiX安装
    installations = check_wix_installation()
    
    # 3. 检查环境变量
    env_vars = check_environment()
    
    # 4. 测试工具功能
    tools_working = False
    if any(path_tools.values()):
        tools_working = test_wix_tools()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    
    if tools_working:
        print("✅ WiX Toolset 安装正常，可以使用！")
        print("\n🎉 您可以运行以下命令开始构建:")
        print("   python build_msi.py")
        print("   或")
        print("   python build_all.py")
        return True
    else:
        print("❌ WiX Toolset 安装有问题")
        
        if installations:
            print(f"\n⚠️  找到WiX安装但无法在PATH中访问:")
            for install in installations:
                print(f"   {install}")
            print("\n💡 请将WiX bin目录添加到系统PATH")
        else:
            print("\n❌ 未找到WiX Toolset安装")
        
        provide_solutions()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    if success:
        print("🎉 WiX检查完成 - 一切正常！")
    else:
        print("⚠️  WiX检查完成 - 需要修复问题")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
