"""
时间规则配置模块

为循环发送提供时间控制功能，包括：
- 工作时间限制
- 日期范围控制
- 工作日/周末配置
- 时间段配置
"""

from datetime import datetime, time, date, timedelta
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum

from utils.logger import setup_logger

logger = setup_logger("time_rules")


class WeekdayType(Enum):
    """工作日类型"""

    WEEKDAYS = "weekdays"  # 工作日（周一到周五）
    WEEKENDS = "weekends"  # 周末（周六、周日）
    ALL_DAYS = "all_days"  # 所有日期
    CUSTOM = "custom"  # 自定义


@dataclass
class TimeRange:
    """时间范围"""

    start_time: time
    end_time: time

    def contains(self, check_time: time) -> bool:
        """检查时间是否在范围内"""
        if self.start_time <= self.end_time:
            # 正常情况：09:00 - 18:00
            return self.start_time <= check_time <= self.end_time
        else:
            # 跨天情况：22:00 - 06:00
            return check_time >= self.start_time or check_time <= self.end_time

    def __str__(self):
        return (
            f"{self.start_time.strftime('%H:%M')} - {self.end_time.strftime('%H:%M')}"
        )


@dataclass
class DateRange:
    """日期范围"""

    start_date: Optional[date] = None
    end_date: Optional[date] = None

    def contains(self, check_date: date) -> bool:
        """检查日期是否在范围内"""
        if self.start_date and check_date < self.start_date:
            return False
        if self.end_date and check_date > self.end_date:
            return False
        return True

    def is_unlimited(self) -> bool:
        """是否无限制"""
        return self.start_date is None and self.end_date is None

    def __str__(self):
        if self.is_unlimited():
            return "无限制"
        start_str = (
            self.start_date.strftime("%Y-%m-%d") if self.start_date else "无限制"
        )
        end_str = self.end_date.strftime("%Y-%m-%d") if self.end_date else "无限制"
        return f"{start_str} 至 {end_str}"


@dataclass
class TimeRules:
    """时间规则配置"""

    # 基础配置
    enabled: bool = False

    # 日期范围
    date_range: DateRange = None

    # 工作日配置
    weekday_type: WeekdayType = WeekdayType.ALL_DAYS
    custom_weekdays: List[int] = None  # 自定义工作日（0=周一, 6=周日）

    # 时间段配置
    time_ranges: List[TimeRange] = None

    # 节假日配置
    exclude_holidays: bool = False
    custom_holidays: List[date] = None

    def __post_init__(self):
        """初始化后处理"""
        if self.date_range is None:
            self.date_range = DateRange()
        if self.custom_weekdays is None:
            self.custom_weekdays = []
        if self.time_ranges is None:
            self.time_ranges = []
        if self.custom_holidays is None:
            self.custom_holidays = []

    def is_valid_datetime(self, check_datetime: datetime) -> bool:
        """检查指定时间是否符合规则"""
        if not self.enabled:
            return True

        check_date = check_datetime.date()
        check_time = check_datetime.time()
        check_weekday = check_datetime.weekday()  # 0=周一, 6=周日

        # 检查日期范围
        if not self.date_range.contains(check_date):
            logger.debug(f"日期 {check_date} 不在允许范围内: {self.date_range}")
            return False

        # 检查工作日
        if not self._is_valid_weekday(check_weekday):
            logger.debug(f"工作日 {check_weekday} 不在允许范围内")
            return False

        # 检查节假日
        if self.exclude_holidays and check_date in self.custom_holidays:
            logger.debug(f"日期 {check_date} 是节假日，被排除")
            return False

        # 检查时间段
        if self.time_ranges and not self._is_valid_time(check_time):
            logger.debug(f"时间 {check_time} 不在允许的时间段内")
            return False

        return True

    def _is_valid_weekday(self, weekday: int) -> bool:
        """检查工作日是否有效"""
        if self.weekday_type == WeekdayType.ALL_DAYS:
            return True
        elif self.weekday_type == WeekdayType.WEEKDAYS:
            return weekday < 5  # 周一到周五
        elif self.weekday_type == WeekdayType.WEEKENDS:
            return weekday >= 5  # 周六、周日
        elif self.weekday_type == WeekdayType.CUSTOM:
            return weekday in self.custom_weekdays
        return True

    def _is_valid_time(self, check_time: time) -> bool:
        """检查时间是否在允许的时间段内"""
        if not self.time_ranges:
            return True

        for time_range in self.time_ranges:
            if time_range.contains(check_time):
                return True

        return False

    def get_next_valid_datetime(
        self, from_datetime: datetime, max_days_ahead: int = 30
    ) -> Optional[datetime]:
        """
        获取下一个有效的执行时间

        Args:
            from_datetime: 起始时间
            max_days_ahead: 最多向前查找的天数

        Returns:
            下一个有效时间，如果找不到则返回None
        """
        if not self.enabled:
            return from_datetime

        current = from_datetime
        end_search = from_datetime + timedelta(days=max_days_ahead)

        while current <= end_search:
            if self.is_valid_datetime(current):
                return current

            # 如果有时间段限制，尝试当天的下一个时间段
            if self.time_ranges:
                next_time = self._get_next_valid_time_today(current)
                if next_time:
                    next_datetime = datetime.combine(current.date(), next_time)
                    if next_datetime > current and self.is_valid_datetime(
                        next_datetime
                    ):
                        return next_datetime

            # 移动到下一天的开始
            current = datetime.combine(current.date() + timedelta(days=1), time.min)

            # 如果有时间段限制，移动到第一个时间段的开始
            if self.time_ranges:
                first_time = min(tr.start_time for tr in self.time_ranges)
                current = datetime.combine(current.date(), first_time)

        logger.warning(f"在 {max_days_ahead} 天内找不到有效的执行时间")
        return None

    def _get_next_valid_time_today(self, current_datetime: datetime) -> Optional[time]:
        """获取当天下一个有效的时间"""
        current_time = current_datetime.time()

        for time_range in sorted(self.time_ranges, key=lambda tr: tr.start_time):
            if time_range.start_time > current_time:
                return time_range.start_time

        return None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)

        # 处理特殊类型
        if self.date_range:
            data["date_range"] = {
                "start_date": (
                    self.date_range.start_date.isoformat()
                    if self.date_range.start_date
                    else None
                ),
                "end_date": (
                    self.date_range.end_date.isoformat()
                    if self.date_range.end_date
                    else None
                ),
            }

        data["weekday_type"] = self.weekday_type.value

        data["time_ranges"] = [
            {
                "start_time": tr.start_time.strftime("%H:%M"),
                "end_time": tr.end_time.strftime("%H:%M"),
            }
            for tr in self.time_ranges
        ]

        data["custom_holidays"] = [d.isoformat() for d in self.custom_holidays]

        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TimeRules":
        """从字典创建"""
        # 处理日期范围
        date_range_data = data.get("date_range", {})
        date_range = DateRange(
            start_date=(
                date.fromisoformat(date_range_data["start_date"])
                if date_range_data.get("start_date")
                else None
            ),
            end_date=(
                date.fromisoformat(date_range_data["end_date"])
                if date_range_data.get("end_date")
                else None
            ),
        )

        # 处理工作日类型
        weekday_type = WeekdayType(data.get("weekday_type", WeekdayType.ALL_DAYS.value))

        # 处理时间段
        time_ranges = []
        for tr_data in data.get("time_ranges", []):
            start_time = time.fromisoformat(tr_data["start_time"])
            end_time = time.fromisoformat(tr_data["end_time"])
            time_ranges.append(TimeRange(start_time, end_time))

        # 处理节假日
        custom_holidays = [
            date.fromisoformat(d) for d in data.get("custom_holidays", [])
        ]

        return cls(
            enabled=data.get("enabled", False),
            date_range=date_range,
            weekday_type=weekday_type,
            custom_weekdays=data.get("custom_weekdays", []),
            time_ranges=time_ranges,
            exclude_holidays=data.get("exclude_holidays", False),
            custom_holidays=custom_holidays,
        )

    def __str__(self):
        if not self.enabled:
            return "时间规则: 未启用"

        parts = ["时间规则: 已启用"]

        if not self.date_range.is_unlimited():
            parts.append(f"日期范围: {self.date_range}")

        if self.weekday_type != WeekdayType.ALL_DAYS:
            if self.weekday_type == WeekdayType.WEEKDAYS:
                parts.append("工作日: 周一至周五")
            elif self.weekday_type == WeekdayType.WEEKENDS:
                parts.append("工作日: 周末")
            elif self.weekday_type == WeekdayType.CUSTOM:
                weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
                custom_names = [weekday_names[i] for i in self.custom_weekdays]
                parts.append(f"工作日: {', '.join(custom_names)}")

        if self.time_ranges:
            time_strs = [str(tr) for tr in self.time_ranges]
            parts.append(f"时间段: {', '.join(time_strs)}")

        if self.exclude_holidays and self.custom_holidays:
            parts.append(f"排除节假日: {len(self.custom_holidays)}个")

        return "\n".join(parts)


def create_work_hours_rule(start_hour: int = 9, end_hour: int = 18) -> TimeRules:
    """创建工作时间规则（工作日 9:00-18:00）"""
    return TimeRules(
        enabled=True,
        weekday_type=WeekdayType.WEEKDAYS,
        time_ranges=[TimeRange(time(start_hour, 0), time(end_hour, 0))],
    )


def create_unlimited_rule() -> TimeRules:
    """创建无限制规则"""
    return TimeRules(enabled=False)


def create_custom_rule(weekdays: List[int], time_ranges: List[tuple]) -> TimeRules:
    """
    创建自定义规则

    Args:
        weekdays: 工作日列表（0=周一, 6=周日）
        time_ranges: 时间段列表 [(start_hour, start_min, end_hour, end_min), ...]
    """
    tr_list = []
    for start_h, start_m, end_h, end_m in time_ranges:
        tr_list.append(TimeRange(time(start_h, start_m), time(end_h, end_m)))

    return TimeRules(
        enabled=True,
        weekday_type=WeekdayType.CUSTOM,
        custom_weekdays=weekdays,
        time_ranges=tr_list,
    )
