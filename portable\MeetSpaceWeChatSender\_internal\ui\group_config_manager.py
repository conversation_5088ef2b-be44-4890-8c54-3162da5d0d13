"""
分组配置管理器

处理分组配置的UI状态管理和数据同步。
"""

from ui.themed_message_box import ThemedMessageBoxHelper
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QMessageBox

from core.group_manager import group_manager
from utils.logger import setup_logger

logger = setup_logger("group_config_manager")


class GroupConfigManager(QObject):
    """分组配置管理器"""

    # 信号定义
    config_changed = pyqtSignal(
        str, str, str, object
    )  # group_id, group_type, config_path, value
    group_switched = pyqtSignal(str, str)  # group_id, group_type
    config_saved = pyqtSignal(str, str)  # group_id, group_type

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_group_id = None
        self.current_group_type = None
        self.unsaved_changes = False
        self.config_cache = {}  # 配置缓存
        self.parent_widget = parent

    def switch_group(self, group_id: str, group_type: str, force: bool = False) -> bool:
        """切换分组"""
        # 如果是同一个分组，不需要切换
        if self.current_group_id == group_id and self.current_group_type == group_type:
            return True

        # 检查是否有未保存的更改
        if self.unsaved_changes and not force:
            if not self._confirm_save_changes():
                return False

        # 保存当前配置
        if self.current_group_id and self.unsaved_changes:
            self.save_current_config()

        # 切换到新分组
        old_group_id = self.current_group_id
        old_group_type = self.current_group_type

        self.current_group_id = group_id
        self.current_group_type = group_type
        self.unsaved_changes = False

        # 加载新分组配置到缓存
        self._load_group_config_to_cache()

        # 发送切换信号
        self.group_switched.emit(group_id, group_type)

        logger.info(f"切换分组: {old_group_id} -> {group_id}")
        return True

    def update_config(
        self, config_path: str, value: any, save_immediately: bool = True
    ) -> bool:
        """更新配置"""
        if not self.current_group_id:
            return False

        try:
            # 更新缓存
            self._update_cache(config_path, value)

            # 标记有未保存的更改
            self.unsaved_changes = True

            # 立即保存（如果需要）
            if save_immediately:
                success = group_manager.update_group_config(
                    self.current_group_id, self.current_group_type, config_path, value
                )
                if success:
                    self.unsaved_changes = False
                    self.config_saved.emit(
                        self.current_group_id, self.current_group_type
                    )

            # 发送配置更改信号
            self.config_changed.emit(
                self.current_group_id, self.current_group_type, config_path, value
            )

            return True

        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False

    def get_config(self, config_path: str, default=None):
        """获取配置"""
        if not self.current_group_id:
            return default

        # 先从缓存获取
        cache_key = f"{self.current_group_id}_{self.current_group_type}"
        if cache_key in self.config_cache:
            try:
                keys = config_path.split(".")
                current = self.config_cache[cache_key]
                for key in keys:
                    if key not in current:
                        return default
                    current = current[key]
                return current
            except:
                pass

        # 从数据库获取
        return group_manager.get_group_config(
            self.current_group_id, self.current_group_type, config_path, default
        )

    def save_current_config(self) -> bool:
        """保存当前配置"""
        if not self.current_group_id or not self.unsaved_changes:
            return True

        try:
            success = group_manager.save_group_config_immediately(
                self.current_group_id, self.current_group_type
            )
            if success:
                self.unsaved_changes = False
                self.config_saved.emit(self.current_group_id, self.current_group_type)
                logger.info(f"保存分组配置成功: {self.current_group_id}")
            return success

        except Exception as e:
            logger.error(f"保存分组配置失败: {e}")
            return False

    def has_unsaved_changes(self) -> bool:
        """是否有未保存的更改"""
        return self.unsaved_changes

    def get_current_group_info(self) -> tuple:
        """获取当前分组信息"""
        return self.current_group_id, self.current_group_type

    def reset_unsaved_changes(self):
        """重置未保存更改标记"""
        self.unsaved_changes = False

    def _confirm_save_changes(self) -> bool:
        """确认是否保存更改"""
        if not self.parent_widget:
            return True

        reply = ThemedMessageBoxHelper.show_question(
            self.parent_widget, "保存更改", "当前分组有未保存的配置更改，是否保存？"
        )

        if reply:
            return self.save_current_config()
        else:
            self.unsaved_changes = False
            return True

    def _load_group_config_to_cache(self):
        """加载分组配置到缓存"""
        if not self.current_group_id:
            return

        group = group_manager.get_group(self.current_group_id, self.current_group_type)
        if group:
            cache_key = f"{self.current_group_id}_{self.current_group_type}"
            self.config_cache[cache_key] = group.config.copy()

    def _update_cache(self, config_path: str, value: any):
        """更新缓存"""
        if not self.current_group_id:
            return

        cache_key = f"{self.current_group_id}_{self.current_group_type}"
        if cache_key not in self.config_cache:
            self._load_group_config_to_cache()

        try:
            keys = config_path.split(".")
            current = self.config_cache[cache_key]

            # 导航到目标位置
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]

            # 设置值
            current[keys[-1]] = value

        except Exception as e:
            logger.error(f"更新缓存失败: {e}")

    def clear_cache(self):
        """清空缓存"""
        self.config_cache.clear()

    def get_merged_send_settings(self, system_settings: dict = None) -> dict:
        """获取合并后的发送设置"""
        if not self.current_group_id:
            return system_settings or {}

        return group_manager.get_group_send_settings(
            self.current_group_id, self.current_group_type, system_settings
        )
