#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet space 微信群发助手 - MSI安装包构建脚本
使用WiX Toolset创建MSI安装包
"""

import os
import sys
import shutil
import subprocess
import uuid
from pathlib import Path
from datetime import datetime

# 项目信息
PROJECT_NAME = "MeetSpaceWeChatSender"
PROJECT_VERSION = "1.0.0"
COMPANY_NAME = "Meet space 会客创意空间"
PRODUCT_NAME = "Meet space 微信群发助手"

def check_wix_toolset():
    """检查WiX Toolset是否已安装"""
    print("🔍 检查WiX Toolset...")

    # 检查WiX v6.0 (新版本)
    try:
        result = subprocess.run(["wix", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version_info = result.stdout.strip()
            print(f"  ✅ WiX Toolset已安装: {version_info}")
            return "v6"
    except FileNotFoundError:
        pass

    # 检查WiX v3.x (旧版本)
    try:
        result = subprocess.run(["candle", "-?"], capture_output=True, text=True)
        if result.returncode == 0:
            print("  ✅ WiX Toolset v3.x已安装")
            return "v3"
    except FileNotFoundError:
        pass

    print("  ❌ 未找到WiX Toolset")
    print("  📥 请从以下地址下载并安装WiX Toolset:")
    print("  https://wixtoolset.org/releases/")
    print("  或使用命令: winget install WiX.Toolset")
    return False

def generate_guids():
    """生成GUID"""
    return {
        'product': str(uuid.uuid4()).upper(),
        'upgrade': str(uuid.uuid4()).upper(),
        'main_component': str(uuid.uuid4()).upper(),
        'menu_component': str(uuid.uuid4()).upper(),
        'desktop_component': str(uuid.uuid4()).upper(),
    }

def collect_files(source_dir):
    """收集要打包的文件"""
    print(f"📁 收集文件: {source_dir}")
    
    source_path = Path(source_dir)
    if not source_path.exists():
        print(f"  ❌ 源目录不存在: {source_dir}")
        return []
    
    files = []
    for file_path in source_path.rglob("*"):
        if file_path.is_file():
            relative_path = file_path.relative_to(source_path)
            files.append({
                'source': str(file_path),
                'relative': str(relative_path),
                'id': f"File_{len(files)}",
                'component_id': f"Component_{len(files)}"
            })
    
    print(f"  ✅ 找到 {len(files)} 个文件")
    return files

def create_wix_config_v6(files, guids):
    """创建WiX v6.0配置文件"""
    print("📝 创建WiX v6.0配置文件...")

    # 创建installer目录
    installer_dir = Path("installer")
    installer_dir.mkdir(exist_ok=True)

    # WiX v6.0的简化配置
    wix_content = f'''<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  <Package Name="{PRODUCT_NAME}"
           Version="{PROJECT_VERSION}"
           Manufacturer="{COMPANY_NAME}"
           UpgradeCode="{{{guids['upgrade']}}}"
           Language="2052">

    <SummaryInformation Description="{PRODUCT_NAME} 安装程序"
                        Comments="功能强大的微信群发工具，支持定时发送、循环发送和多主题界面"
                        Manufacturer="{COMPANY_NAME}" />

    <MajorUpgrade DowngradeErrorMessage="已安装更新版本的 {PRODUCT_NAME}。安装程序将退出。" />

    <Feature Id="ProductFeature" Title="{PRODUCT_NAME}" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>

    <StandardDirectory Id="ProgramFilesFolder">
      <Directory Id="INSTALLFOLDER" Name="{PROJECT_NAME}" />
    </StandardDirectory>

    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="MainExecutable">
        <File Source="dist\\{PROJECT_NAME}\\{PROJECT_NAME}.exe" />
      </Component>
    </ComponentGroup>

  </Package>
</Wix>
'''

    wix_file = installer_dir / "setup.wxs"
    with open(wix_file, "w", encoding="utf-8") as f:
        f.write(wix_content)

    print(f"  ✅ 已创建: {wix_file}")
    return wix_file

def create_wix_config_v3(files, guids):
    """创建WiX v3.x配置文件"""
    print("📝 创建WiX v3.x配置文件...")

    # 创建installer目录
    installer_dir = Path("installer")
    installer_dir.mkdir(exist_ok=True)
    
    # 生成文件组件
    file_components = []
    component_refs = []
    
    for i, file_info in enumerate(files):
        # 创建目录结构
        dir_parts = Path(file_info['relative']).parent.parts
        dir_structure = ""
        current_dir = "INSTALLFOLDER"
        
        for part in dir_parts:
            if part != ".":
                dir_id = f"Dir_{part}_{i}"
                dir_structure += f'<Directory Id="{dir_id}" Name="{part}">'
                current_dir = dir_id
        
        # 文件组件
        component = f'''
      <Component Id="{file_info['component_id']}" Directory="{current_dir}" Guid="{{{str(uuid.uuid4()).upper()}}}">
        <File Id="{file_info['id']}" Source="{file_info['source']}" KeyPath="yes" />
      </Component>'''
        
        file_components.append(component)
        component_refs.append(f'<ComponentRef Id="{file_info['component_id']}" />')
        
        # 关闭目录结构
        for _ in dir_parts:
            if _ != ".":
                dir_structure += "</Directory>"
    
    wix_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="{{{guids['product']}}}" 
           Name="{PRODUCT_NAME}" 
           Language="2052" 
           Version="{PROJECT_VERSION}" 
           Manufacturer="{COMPANY_NAME}" 
           UpgradeCode="{{{guids['upgrade']}}}">
    
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine" 
             Description="{PRODUCT_NAME} 安装程序"
             Comments="功能强大的微信群发工具，支持定时发送、循环发送和多主题界面"
             Manufacturer="{COMPANY_NAME}"
             InstallerVersion="200"
             Languages="2052"
             SummaryCodepage="936" />
    
    <MajorUpgrade DowngradeErrorMessage="已安装更新版本的 {PRODUCT_NAME}。安装程序将退出。" />
    <MediaTemplate EmbedCab="yes" />
    
    <!-- 属性 -->
    <Property Id="ARPPRODUCTICON" Value="AppIcon.exe" />
    <Property Id="ARPHELPLINK" Value="https://meetspace.cn" />
    <Property Id="ARPURLINFOABOUT" Value="https://meetspace.cn" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />
    
    <!-- 功能定义 -->
    <Feature Id="ProductFeature" Title="{PRODUCT_NAME}" Level="1" Description="主程序文件">
      <ComponentRef Id="MainExecutable" />
      <ComponentRef Id="ProgramMenuShortcut" />
      <ComponentRef Id="DesktopShortcut" />
      {''.join(component_refs)}
    </Feature>
    
    <!-- 目录结构 -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="{PROJECT_NAME}">
          <!-- 动态目录结构将在这里生成 -->
        </Directory>
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="{PRODUCT_NAME}" />
      </Directory>
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>
    
    <!-- 主要组件 -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <!-- 主程序 -->
      <Component Id="MainExecutable" Guid="{{{guids['main_component']}}}">
        <File Id="MainExe" 
              Source="dist\\{PROJECT_NAME}\\{PROJECT_NAME}.exe" 
              KeyPath="yes">
          <Shortcut Id="StartMenuShortcut"
                    Directory="ApplicationProgramsFolder"
                    Name="{PRODUCT_NAME}"
                    Description="功能强大的微信群发工具"
                    WorkingDirectory="INSTALLFOLDER"
                    Icon="AppIcon.exe"
                    IconIndex="0" />
        </File>
        
        <!-- 注册表项 -->
        <RegistryKey Root="HKLM" Key="Software\\{COMPANY_NAME}\\{PROJECT_NAME}">
          <RegistryValue Name="InstallPath" Type="string" Value="[INSTALLFOLDER]" />
          <RegistryValue Name="Version" Type="string" Value="{PROJECT_VERSION}" />
          <RegistryValue Name="InstallDate" Type="string" Value="[Date]" />
        </RegistryKey>
      </Component>
      
      <!-- 程序菜单快捷方式 -->
      <Component Id="ProgramMenuShortcut" Directory="ApplicationProgramsFolder" Guid="{{{guids['menu_component']}}}">
        <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
        <RegistryValue Root="HKCU" 
                       Key="Software\\{COMPANY_NAME}\\{PROJECT_NAME}" 
                       Name="MenuShortcut" 
                       Type="integer" 
                       Value="1" 
                       KeyPath="yes" />
      </Component>
      
      <!-- 桌面快捷方式 -->
      <Component Id="DesktopShortcut" Directory="DesktopFolder" Guid="{{{guids['desktop_component']}}}">
        <Shortcut Id="DesktopShortcut"
                  Name="{PRODUCT_NAME}"
                  Description="功能强大的微信群发工具"
                  Target="[INSTALLFOLDER]{PROJECT_NAME}.exe"
                  WorkingDirectory="INSTALLFOLDER"
                  Icon="AppIcon.exe"
                  IconIndex="0" />
        <RegistryValue Root="HKCU" 
                       Key="Software\\{COMPANY_NAME}\\{PROJECT_NAME}" 
                       Name="DesktopShortcut" 
                       Type="integer" 
                       Value="1" 
                       KeyPath="yes" />
      </Component>
      
      <!-- 文件组件 -->
      {''.join(file_components)}
    </ComponentGroup>
    
    <!-- 图标 -->
    <Icon Id="AppIcon.exe" SourceFile="dist\\{PROJECT_NAME}\\{PROJECT_NAME}.exe" />
    
    <!-- UI配置 -->
    <UI>
      <UIRef Id="WixUI_InstallDir" />
      <Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="InstallDirDlg" Order="2">1</Publish>
      <Publish Dialog="InstallDirDlg" Control="Back" Event="NewDialog" Value="WelcomeDlg" Order="2">1</Publish>
    </UI>
    
    <!-- 许可协议 -->
    <WixVariable Id="WixUILicenseRtf" Value="installer\\license.rtf" />
    <WixVariable Id="WixUIDialogBmp" Value="installer\\dialog.bmp" />
    <WixVariable Id="WixUIBannerBmp" Value="installer\\banner.bmp" />
    
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />
    
  </Product>
</Wix>
'''
    
    wix_file = installer_dir / "setup.wxs"
    with open(wix_file, "w", encoding="utf-8") as f:
        f.write(wix_content)
    
    print(f"  ✅ 已创建: {wix_file}")
    return wix_file

def create_license_file():
    """创建许可协议文件"""
    print("📄 创建许可协议文件...")
    
    license_content = f'''{{\\rtf1\\ansi\\deff0 {{\\fonttbl {{\\f0 Times New Roman;}}}}
\\f0\\fs24
{PRODUCT_NAME} 软件许可协议\\par
\\par
版权所有 © 2024 {COMPANY_NAME}\\par
\\par
本软件受版权法保护。使用本软件即表示您同意以下条款：\\par
\\par
1. 许可范围：本许可证授予您使用本软件的非独占权利。\\par
\\par
2. 使用限制：您不得复制、分发、修改或逆向工程本软件。\\par
\\par
3. 免责声明：本软件按"现状"提供，不提供任何明示或暗示的保证。\\par
\\par
4. 责任限制：在任何情况下，软件提供商均不对任何损害承担责任。\\par
\\par
5. 终止：违反本协议的任何条款将导致许可证自动终止。\\par
\\par
如有疑问，请联系：{COMPANY_NAME}\\par
}}'''
    
    license_file = Path("installer") / "license.rtf"
    with open(license_file, "w", encoding="utf-8") as f:
        f.write(license_content)
    
    print(f"  ✅ 已创建: {license_file}")

def build_msi_v6(wix_file):
    """使用WiX v6.0构建MSI安装包"""
    print("🔨 构建MSI安装包 (WiX v6.0)...")

    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)

    msi_file = output_dir / f"{PROJECT_NAME}_v{PROJECT_VERSION}.msi"

    # WiX v6.0使用单一的build命令
    wix_cmd = [
        "wix",
        "build",
        str(wix_file),
        "-o", str(msi_file),
        "-ext", "WixToolset.UI.wixext"
    ]

    print(f"  📋 执行命令: {' '.join(wix_cmd)}")

    try:
        result = subprocess.run(wix_cmd, check=True, capture_output=True, text=True)
        print(f"    ✅ MSI生成成功: {msi_file}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"    ❌ MSI生成失败: {e}")
        print(f"    错误输出: {e.stderr}")
        if e.stdout:
            print(f"    标准输出: {e.stdout}")
        return False

def build_msi_v3(wix_file):
    """使用WiX v3.x构建MSI安装包"""
    print("🔨 构建MSI安装包 (WiX v3.x)...")

    installer_dir = Path("installer")
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)

    # 编译WiX文件
    print("  📝 编译WiX文件...")
    wixobj_file = installer_dir / "setup.wixobj"

    candle_cmd = [
        "candle",
        "-out", str(wixobj_file),
        str(wix_file)
    ]

    try:
        result = subprocess.run(candle_cmd, check=True, capture_output=True, text=True)
        print("    ✅ WiX编译成功")
    except subprocess.CalledProcessError as e:
        print(f"    ❌ WiX编译失败: {e}")
        print(f"    错误输出: {e.stderr}")
        return False

    # 链接生成MSI
    print("  🔗 链接生成MSI...")
    msi_file = output_dir / f"{PROJECT_NAME}_v{PROJECT_VERSION}.msi"

    light_cmd = [
        "light",
        "-ext", "WixUIExtension",
        "-out", str(msi_file),
        str(wixobj_file)
    ]

    try:
        result = subprocess.run(light_cmd, check=True, capture_output=True, text=True)
        print(f"    ✅ MSI生成成功: {msi_file}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"    ❌ MSI生成失败: {e}")
        print(f"    错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("📦 Meet space 微信群发助手 - MSI安装包构建")
    print("=" * 60)

    # 检查WiX Toolset
    wix_version = check_wix_toolset()
    if not wix_version:
        return False

    # 检查dist目录
    dist_dir = Path("dist") / PROJECT_NAME
    if not dist_dir.exists():
        print(f"❌ 未找到可执行文件目录: {dist_dir}")
        print("请先运行: python build_exe.py")
        return False

    try:
        # 1. 生成GUID
        guids = generate_guids()
        print(f"🔑 生成GUID: {len(guids)} 个")

        # 2. 收集文件
        files = collect_files(dist_dir)
        if not files:
            return False

        # 3. 根据WiX版本创建配置
        if wix_version == "v6":
            wix_file = create_wix_config_v6(files, guids)
        else:  # v3
            wix_file = create_wix_config_v3(files, guids)

        # 4. 创建许可协议
        create_license_file()

        # 5. 根据WiX版本构建MSI
        if wix_version == "v6":
            success = build_msi_v6(wix_file)
        else:  # v3
            success = build_msi_v3(wix_file)

        if not success:
            return False

        print("\n🎉 MSI安装包构建完成！")
        print(f"📁 输出目录: output/")
        print(f"📦 安装包: {PROJECT_NAME}_v{PROJECT_VERSION}.msi")
        print("\n📋 安装包信息:")
        print(f"  产品名称: {PRODUCT_NAME}")
        print(f"  版本号: {PROJECT_VERSION}")
        print(f"  制造商: {COMPANY_NAME}")
        print(f"  WiX版本: {wix_version}")

        return True

    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
