#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试注入 - 跳过GUI对话框
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_direct_injection():
    """直接测试注入，跳过权限提升对话框"""
    print("💉 直接测试注入（跳过权限提升对话框）...")
    
    try:
        from core.injector_tool import InjectorTool
        
        # 创建注入器（禁用自动权限提升，直接注入）
        injector = InjectorTool(auto_elevate=False)
        
        print(f"注入器路径: {injector.injector_path}")
        print(f"DLL路径: {injector.dll_path}")
        print(f"自动权限提升: {'✅ 启用' if injector.auto_elevate else '❌ 禁用'}")
        
        # 检查微信进程
        wechat_process = injector.find_wechat_process()
        if not wechat_process:
            print("❌ 未找到微信进程，请先启动微信")
            return False
        
        print(f"✅ 找到微信进程: {wechat_process.info['name']} (PID: {wechat_process.pid})")
        
        # 直接尝试注入（不弹出对话框）
        print("\n开始直接注入...")
        success, message = injector.inject_dll()
        
        if success:
            print(f"✅ 注入成功: {message}")
            
            # 验证API服务
            if injector.check_api_service():
                print("✅ API服务正常响应")
            else:
                print("⚠️  API服务未响应，但注入可能成功")
            
            return True
        else:
            print(f"❌ 注入失败: {message}")
            
            # 如果失败，尝试强制权限提升
            if "Could not create thread in remote process" in message:
                print("\n🔧 检测到权限问题，尝试强制权限提升...")
                return test_force_elevation()
            
            return False
            
    except Exception as e:
        print(f"❌ 注入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_force_elevation():
    """测试强制权限提升"""
    print("\n🚀 测试强制权限提升...")
    
    try:
        from utils.simple_admin import force_admin_privileges
        
        print("⚠️  即将弹出UAC对话框...")
        print("如果您看到UAC对话框，说明修复成功！")
        
        # 调用强制权限提升
        success = force_admin_privileges()
        
        # 如果到达这里，说明没有弹出UAC对话框
        print("❌ 没有弹出UAC对话框，强制权限提升失败")
        return False
        
    except SystemExit:
        # 这是正常的，说明UAC对话框弹出了，程序退出等待新进程启动
        print("✅ UAC对话框已弹出，程序正常退出")
        return True
    except Exception as e:
        print(f"❌ 强制权限提升异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 直接注入测试")
    print("=" * 60)
    print("测试注入功能，如果失败则尝试强制权限提升")
    print("=" * 60)
    
    # 先尝试直接注入
    success = test_direct_injection()
    
    if success:
        print("\n🎉 注入成功！问题已解决！")
        print("✨ 现在注入功能应该正常工作了")
    else:
        print("\n⚠️  直接注入失败")
        print("这可能是权限问题，需要强制权限提升")
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    if success:
        print("✅ 注入功能正常")
        print("✅ 不需要额外的权限提升")
        print("✅ 问题已完全解决")
    else:
        print("❌ 注入功能异常")
        print("🔧 需要强制权限提升")
        print("💡 建议：在主程序中点击注入时会弹出UAC对话框")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
