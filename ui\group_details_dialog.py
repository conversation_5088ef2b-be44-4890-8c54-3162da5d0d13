"""
分组详情对话框

显示分组的详细信息，包括发送历史、成员信息、统计数据等。
"""

from datetime import datetime

from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QTabWidget,
    QWidget,
    QLabel,
    QTableWidget,
    QTableWidgetItem,
    QTextEdit,
    QGroupBox,
    QPushButton,
    QHeaderView,
    QScrollArea,
    QGridLayout,
    QProgressBar,
    QMessageBox,
    QCheckBox,
    QMenu,
    QAbstractItemView,
)
from PyQt6.QtCore import pyqtSignal

from core.group_manager import group_manager
from core.timing_sender import timing_sender
from core.loop_sender import loop_sender
from ui.modern_theme_manager import theme_manager as modern_theme_manager
from ui.themed_dialog_base import ThemedDialogBase
from ui.themed_message_box import ThemedMessageBoxHelper
from utils.logger import setup_logger

logger = setup_logger("group_details_dialog")


class GroupDetailsDialog(ThemedDialogBase):
    """分组详情对话框"""

    # 信号定义
    group_updated = pyqtSignal()  # 分组更新信号

    def __init__(self, group_id: str, group_type: str, connector=None, parent=None):
        super().__init__(parent)
        self.group_id = group_id
        self.group_type = group_type
        self.connector = connector
        self.group = group_manager.get_group(group_id, group_type)

        if not self.group:
            raise ValueError(f"分组不存在: {group_id}")

        self.setup_ui()
        self.load_data()

        # 主题支持已由 ThemedDialogBase 自动设置

        self.setWindowTitle(f"分组详情 - {self.group.name}")
        self.setModal(True)
        self.resize(900, 700)

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel(f"📁 {self.group.name}")
        title_label.setObjectName("groupDialogTitle")
        layout.addWidget(title_label)

        # 标签页
        self.tab_widget = QTabWidget()

        # 基本信息标签页
        self.create_basic_info_tab()

        # 成员信息标签页
        self.create_members_tab()

        # 发送历史标签页
        self.create_history_tab()

        # 配置详情标签页
        self.create_config_tab()

        # 统计信息标签页
        self.create_statistics_tab()

        layout.addWidget(self.tab_widget)

        # 按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.refresh_data)
        buttons_layout.addWidget(refresh_btn)

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

    def create_basic_info_tab(self):
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基本信息
        info_group = QGroupBox("基本信息")
        info_layout = QGridLayout(info_group)

        info_layout.addWidget(QLabel("分组ID:"), 0, 0)
        info_layout.addWidget(QLabel(self.group.group_id), 0, 1)

        info_layout.addWidget(QLabel("分组类型:"), 1, 0)
        type_text = "定时发送" if self.group.group_type == "timing" else "循环发送"
        info_layout.addWidget(QLabel(type_text), 1, 1)

        info_layout.addWidget(QLabel("成员数量:"), 2, 0)
        info_layout.addWidget(QLabel(f"{self.group.get_member_count()} 人"), 2, 1)

        info_layout.addWidget(QLabel("创建时间:"), 3, 0)
        info_layout.addWidget(
            QLabel(self.group.created_time.strftime("%Y-%m-%d %H:%M:%S")), 3, 1
        )

        info_layout.addWidget(QLabel("最后更新:"), 4, 0)
        info_layout.addWidget(
            QLabel(self.group.updated_time.strftime("%Y-%m-%d %H:%M:%S")), 4, 1
        )

        info_layout.addWidget(QLabel("使用次数:"), 5, 0)
        info_layout.addWidget(QLabel(f"{self.group.use_count} 次"), 5, 1)

        info_layout.addWidget(QLabel("最后使用:"), 6, 0)
        last_used = (
            self.group.last_used.strftime("%Y-%m-%d %H:%M:%S")
            if self.group.last_used
            else "从未使用"
        )
        info_layout.addWidget(QLabel(last_used), 6, 1)

        layout.addWidget(info_group)

        # 标签信息
        if self.group.tags:
            tags_group = QGroupBox("标签")
            tags_layout = QHBoxLayout(tags_group)
            tags_text = ", ".join(self.group.tags)
            tags_layout.addWidget(QLabel(tags_text))
            layout.addWidget(tags_group)

        layout.addStretch()
        self.tab_widget.addTab(widget, "📋 基本信息")

    def create_members_tab(self):
        """创建成员信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 成员管理工具栏
        toolbar_layout = QHBoxLayout()

        # 成员数量标签
        self.member_count_label = QLabel(
            f"成员列表 (共 {self.group.get_member_count()} 人)"
        )
        self.member_count_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        toolbar_layout.addWidget(self.member_count_label)

        toolbar_layout.addStretch()

        # 添加成员按钮
        self.add_member_btn = QPushButton("➕ 添加成员")
        self.add_member_btn.clicked.connect(self.add_members)
        toolbar_layout.addWidget(self.add_member_btn)

        # 删除选中成员按钮
        self.delete_selected_btn = QPushButton("🗑️ 删除选中")
        self.delete_selected_btn.clicked.connect(self.delete_selected_members)
        self.delete_selected_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_selected_btn)

        # 刷新成员列表按钮
        self.refresh_members_btn = QPushButton("🔄 刷新")
        self.refresh_members_btn.clicked.connect(self.refresh_members)
        toolbar_layout.addWidget(self.refresh_members_btn)

        layout.addLayout(toolbar_layout)

        # 成员列表表格
        self.members_table = QTableWidget()
        self.members_table.setColumnCount(4)
        self.members_table.setHorizontalHeaderLabels(["选择", "类型", "名称", "微信ID"])

        # 设置列宽
        header = self.members_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)

        # 设置选择模式
        self.members_table.setSelectionBehavior(
            QAbstractItemView.SelectionBehavior.SelectRows
        )

        # 设置右键菜单
        self.members_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.members_table.customContextMenuRequested.connect(
            self.show_member_context_menu
        )

        layout.addWidget(self.members_table)

        self.tab_widget.addTab(widget, "👥 成员信息")

    def create_history_tab(self):
        """创建发送历史标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 发送历史表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(5)
        self.history_table.setHorizontalHeaderLabels(
            ["时间", "类型", "状态", "发送数量", "消息内容"]
        )

        # 设置列宽
        header = self.history_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)

        layout.addWidget(QLabel("发送历史记录"))
        layout.addWidget(self.history_table)

        self.tab_widget.addTab(widget, "📜 发送历史")

    def create_config_tab(self):
        """创建配置详情标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 配置显示区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)

        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)

        # 发送设置
        send_group = QGroupBox("发送设置")
        send_layout = QGridLayout(send_group)

        config = self.group.config.get("send_settings", {})

        send_layout.addWidget(QLabel("发送间隔:"), 0, 0)
        send_layout.addWidget(QLabel(f"{config.get('interval_seconds', 5)} 秒"), 0, 1)

        send_layout.addWidget(QLabel("批量大小:"), 1, 0)
        send_layout.addWidget(QLabel(f"{config.get('batch_size', 10)} 个"), 1, 1)

        send_layout.addWidget(QLabel("重试次数:"), 2, 0)
        send_layout.addWidget(QLabel(f"{config.get('retry_count', 3)} 次"), 2, 1)

        send_layout.addWidget(QLabel("超时时间:"), 3, 0)
        send_layout.addWidget(QLabel(f"{config.get('timeout', 30)} 秒"), 3, 1)

        send_layout.addWidget(QLabel("使用系统风控:"), 4, 0)
        use_system = "是" if config.get("use_system_risk_control", True) else "否"
        send_layout.addWidget(QLabel(use_system), 4, 1)

        config_layout.addWidget(send_group)

        # 消息设置
        msg_group = QGroupBox("消息设置")
        msg_layout = QGridLayout(msg_group)

        msg_config = self.group.config.get("message_settings", {})

        msg_layout.addWidget(QLabel("默认模板:"), 0, 0)
        template_id = msg_config.get("template_id", "")
        template_text = template_id if template_id else "无"
        msg_layout.addWidget(QLabel(template_text), 0, 1)

        msg_layout.addWidget(QLabel("个性化设置:"), 1, 0)
        personalization = "启用" if msg_config.get("personalization", True) else "禁用"
        msg_layout.addWidget(QLabel(personalization), 1, 1)

        config_layout.addWidget(msg_group)

        # 时间设置
        schedule_group = QGroupBox("时间设置")
        schedule_layout = QGridLayout(schedule_group)

        schedule_config = self.group.config.get("schedule_settings", {})

        if self.group.group_type == "timing":
            schedule_layout.addWidget(QLabel("默认时间:"), 0, 0)
            default_time = schedule_config.get("default_time", "09:00")
            schedule_layout.addWidget(QLabel(default_time), 0, 1)
        else:
            schedule_layout.addWidget(QLabel("默认间隔:"), 0, 0)
            default_interval = schedule_config.get("default_interval", 60)
            schedule_layout.addWidget(QLabel(f"{default_interval} 分钟"), 0, 1)

            schedule_layout.addWidget(QLabel("自动启动:"), 1, 0)
            auto_start = "是" if schedule_config.get("auto_start", False) else "否"
            schedule_layout.addWidget(QLabel(auto_start), 1, 1)

        schedule_layout.addWidget(QLabel("时区:"), 2, 0)
        timezone = schedule_config.get("timezone", "Asia/Shanghai")
        schedule_layout.addWidget(QLabel(timezone), 2, 1)

        config_layout.addWidget(schedule_group)
        config_layout.addStretch()

        scroll_area.setWidget(config_widget)
        layout.addWidget(scroll_area)

        self.tab_widget.addTab(widget, "⚙️ 配置详情")

    def create_statistics_tab(self):
        """创建统计信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_layout = QGridLayout(stats_group)

        # 获取进度信息
        sent_count, total_count, progress_percent = (
            group_manager.get_group_progress_detail(self.group.group_id)
        )

        stats_layout.addWidget(QLabel("今日进度:"), 0, 0)
        progress_bar = QProgressBar()
        progress_bar.setValue(int(progress_percent))
        progress_bar.setFormat(f"{progress_percent:.1f}% ({sent_count}/{total_count})")
        stats_layout.addWidget(progress_bar, 0, 1)

        stats_layout.addWidget(QLabel("排序分数:"), 1, 0)
        stats_layout.addWidget(QLabel(f"{self.group.sort_score:.2f}"), 1, 1)

        # 任务统计
        if self.group.group_type == "timing":
            tasks = timing_sender.get_tasks_by_group(self.group.group_id)
            stats_layout.addWidget(QLabel("定时任务数:"), 2, 0)
            stats_layout.addWidget(QLabel(f"{len(tasks)} 个"), 2, 1)

            completed_tasks = len([t for t in tasks if t.status == "completed"])
            stats_layout.addWidget(QLabel("已完成任务:"), 3, 0)
            stats_layout.addWidget(QLabel(f"{completed_tasks} 个"), 3, 1)
        else:
            tasks = loop_sender.get_tasks_by_group(self.group.group_id)
            stats_layout.addWidget(QLabel("循环任务数:"), 2, 0)
            stats_layout.addWidget(QLabel(f"{len(tasks)} 个"), 2, 1)

            running_tasks = len([t for t in tasks if t.status == "running"])
            stats_layout.addWidget(QLabel("运行中任务:"), 3, 0)
            stats_layout.addWidget(QLabel(f"{running_tasks} 个"), 3, 1)

        layout.addWidget(stats_group)
        layout.addStretch()

        self.tab_widget.addTab(widget, "📊 统计信息")

    def load_data(self):
        """加载数据"""
        self.load_members_data()
        self.load_history_data()

    def load_members_data(self):
        """加载成员数据"""
        self.members_table.setRowCount(len(self.group.members))

        for row, member in enumerate(self.group.members):
            # 复选框
            checkbox = QCheckBox()
            checkbox.stateChanged.connect(self.on_member_selection_changed)
            self.members_table.setCellWidget(row, 0, checkbox)

            # 类型
            type_icon = "👤" if member.member_type == "contact" else "👥"
            type_text = "好友" if member.member_type == "contact" else "群聊"
            self.members_table.setItem(
                row, 1, QTableWidgetItem(f"{type_icon} {type_text}")
            )

            # 名称
            self.members_table.setItem(row, 2, QTableWidgetItem(member.name))

            # 微信ID
            self.members_table.setItem(row, 3, QTableWidgetItem(member.wxid))

        # 更新成员数量显示
        self.update_member_count_display()

    def load_history_data(self):
        """加载历史数据"""
        # 获取相关任务
        if self.group.group_type == "timing":
            tasks = timing_sender.get_tasks_by_group(self.group.group_id)
        else:
            tasks = loop_sender.get_tasks_by_group(self.group.group_id)

        self.history_table.setRowCount(len(tasks))

        for row, task in enumerate(tasks):
            # 时间
            if hasattr(task, "executed_time") and task.executed_time:
                time_str = task.executed_time.strftime("%Y-%m-%d %H:%M:%S")
            elif hasattr(task, "created_time"):
                time_str = task.created_time.strftime("%Y-%m-%d %H:%M:%S")
            else:
                time_str = "未知"
            self.history_table.setItem(row, 0, QTableWidgetItem(time_str))

            # 类型
            task_type = "定时发送" if self.group.group_type == "timing" else "循环发送"
            self.history_table.setItem(row, 1, QTableWidgetItem(task_type))

            # 状态
            status_map = {
                "pending": "待执行",
                "executing": "执行中",
                "completed": "已完成",
                "failed": "失败",
                "cancelled": "已取消",
                "running": "运行中",
                "paused": "已暂停",
                "stopped": "已停止",
            }
            status_text = status_map.get(task.status, task.status)
            self.history_table.setItem(row, 2, QTableWidgetItem(status_text))

            # 发送数量
            if hasattr(task, "sent_count") and hasattr(task, "total_count"):
                count_text = f"{task.sent_count}/{task.total_count}"
            else:
                count_text = "0/0"
            self.history_table.setItem(row, 3, QTableWidgetItem(count_text))

            # 消息内容 - 使用用户友好的格式
            try:
                # 检查是否是富文本消息
                if hasattr(task, 'message_type') and task.message_type == 'rich_text':
                    from ui.rich_text_editor import RichTextMessageEditor
                    content = RichTextMessageEditor.format_rich_text_for_display(task.message_content)
                else:
                    # 普通文本消息
                    content = (
                        task.message_content[:50] + "..."
                        if len(task.message_content) > 50
                        else task.message_content
                    )
            except Exception as e:
                logger.error(f"格式化消息内容显示失败: {e}")
                content = "消息内容"

            self.history_table.setItem(row, 4, QTableWidgetItem(content))

    def update_member_count_display(self):
        """更新成员数量显示"""
        count = self.group.get_member_count()
        self.member_count_label.setText(f"成员列表 (共 {count} 人)")

    def on_member_selection_changed(self):
        """成员选择状态变化"""
        selected_count = 0
        for row in range(self.members_table.rowCount()):
            checkbox = self.members_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_count += 1

        self.delete_selected_btn.setEnabled(selected_count > 0)

    def add_members(self):
        """添加成员"""
        try:
            # 获取主窗口实例
            main_window = self.get_main_window()
            if not main_window:
                ThemedMessageBoxHelper.show_warning(
                    self,
                    "无法获取联系人数据",
                    "无法找到主窗口实例。\n\n请确保：\n"
                    "1. 程序正常启动\n"
                    "2. 主窗口已完全加载\n"
                    "3. 尝试关闭此对话框后重新打开"
                )
                return

            # 检查主窗口是否有联系人数据
            if not hasattr(main_window, "contacts") or not main_window.contacts:
                contacts_count = len(main_window.contacts) if hasattr(main_window, "contacts") and main_window.contacts else 0
                ThemedMessageBoxHelper.show_warning(
                    self,
                    "没有联系人数据",
                    f"当前联系人数据为空（{contacts_count} 个联系人）。\n\n"
                    "请按以下步骤操作：\n"
                    "1. 确保微信已登录并正常运行\n"
                    "2. 在主界面点击'联系人管理'标签页\n"
                    "3. 点击'获取联系人'按钮\n"
                    "4. 等待联系人数据加载完成后重试"
                )
                return

            logger.info(f"找到联系人数据: {len(main_window.contacts)} 个")

            from ui.contact_selector_dialog import ContactSelectorDialog
            from core.wechatferry_connector import Contact

            # 将现有成员转换为Contact对象
            existing_contacts = []
            for member in self.group.members:
                contact = Contact(
                    wxid=member.wxid,
                    name=member.name,
                    type="friend" if member.member_type == "contact" else "group",
                    remark=member.remark,
                )
                existing_contacts.append(contact)

            # 创建联系人选择对话框
            dialog = ContactSelectorDialog(self, existing_contacts)
            dialog.set_contacts(main_window.contacts)

            # 连接信号
            dialog.contacts_selected.connect(self.on_contacts_selected)
            dialog.exec()

        except Exception as e:
            logger.error(f"打开成员选择对话框失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "错误", f"打开成员选择对话框失败:\n{e}"
            )

    def get_main_window(self):
        """获取主窗口实例"""
        try:
            # 方法1: 通过父窗口链查找主窗口
            parent = self.parent()
            while parent:
                if hasattr(parent, "contacts") and parent.contacts:
                    logger.debug(f"通过父窗口链找到主窗口: {parent.__class__.__name__}")
                    return parent
                parent = parent.parent()

            # 方法2: 通过应用程序查找主窗口
            from PyQt6.QtWidgets import QApplication

            app = QApplication.instance()
            if app:
                for widget in app.topLevelWidgets():
                    if (hasattr(widget, "contacts") and
                        widget.contacts and
                        widget.__class__.__name__ == "MainWindow"):
                        logger.debug(f"通过应用程序找到主窗口: {widget.__class__.__name__}")
                        return widget

            # 方法3: 尝试通过全局变量或单例模式查找
            try:
                import sys
                for name, obj in sys.modules.items():
                    if hasattr(obj, 'main_window_instance'):
                        main_window = getattr(obj, 'main_window_instance')
                        if (hasattr(main_window, "contacts") and
                            main_window.contacts):
                            logger.debug(f"通过模块找到主窗口: {name}")
                            return main_window
            except Exception:
                pass

            logger.warning("无法找到主窗口实例")
            return None
        except Exception as e:
            logger.error(f"获取主窗口失败: {e}")
            return None

    def on_contacts_selected(self, contacts):
        """联系人选择回调"""
        try:
            from core.group_manager import GroupMember

            added_count = 0
            for contact in contacts:
                # 检查是否已存在
                if not any(
                    member.wxid == contact.wxid for member in self.group.members
                ):
                    # 创建分组成员
                    member = GroupMember(
                        wxid=contact.wxid,
                        name=contact.name,
                        member_type="contact" if contact.type == "friend" else "group",
                        remark=contact.remark,
                    )

                    # 添加到分组
                    success = group_manager.add_member_to_group(
                        self.group_id, self.group_type, member
                    )
                    if success:
                        added_count += 1

            if added_count > 0:
                # 重新加载数据
                self.group = group_manager.get_group(self.group_id, self.group_type)
                self.load_members_data()
                self.group_updated.emit()

                ThemedMessageBoxHelper.show_information(
                    self, "成功", f"成功添加 {added_count} 个成员到分组"
                )
                logger.info(f"添加 {added_count} 个成员到分组 {self.group.name}")
            else:
                ThemedMessageBoxHelper.show_information(
                    self, "提示", "没有新成员被添加"
                )

        except Exception as e:
            logger.error(f"添加成员失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"添加成员失败:\n{e}")

    def on_members_added(self, new_members):
        """处理添加的成员"""
        try:
            added_count = 0
            for member in new_members:
                # 检查是否已存在（避免重复）
                existing = any(m.wxid == member.wxid for m in self.group.members)
                if not existing:
                    success = group_manager.add_member_to_group(
                        self.group_id, self.group_type, member
                    )
                    if success:
                        added_count += 1

            if added_count > 0:
                # 重新加载数据
                self.group = group_manager.get_group(self.group_id, self.group_type)
                self.load_members_data()
                self.group_updated.emit()

                ThemedMessageBoxHelper.show_information(
                    self, "成功", f"成功添加 {added_count} 个成员到分组"
                )
                logger.info(f"添加 {added_count} 个成员到分组 {self.group.name}")
            else:
                ThemedMessageBoxHelper.show_information(
                    self, "提示", "没有新成员被添加"
                )

        except Exception as e:
            logger.error(f"添加成员失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"添加成员失败:\n{e}")

    def delete_selected_members(self):
        """删除选中的成员"""
        selected_members = []

        # 获取选中的成员
        for row in range(self.members_table.rowCount()):
            checkbox = self.members_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                wxid_item = self.members_table.item(row, 3)
                name_item = self.members_table.item(row, 2)
                if wxid_item and name_item:
                    selected_members.append(
                        {"wxid": wxid_item.text(), "name": name_item.text()}
                    )

        if not selected_members:
            ThemedMessageBoxHelper.show_information(
                self, "提示", "请先选择要删除的成员"
            )
            return

        # 确认删除
        member_names = [m["name"] for m in selected_members]
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要删除以下 {len(selected_members)} 个成员吗？\n\n"
            + "\n".join(member_names[:5])
            + ("..." if len(member_names) > 5 else ""),
        )

        if reply:
            self.perform_delete_members(selected_members)

    def perform_delete_members(self, members_to_delete):
        """执行删除成员操作"""
        try:
            deleted_count = 0
            for member in members_to_delete:
                success = group_manager.remove_member_from_group(
                    self.group_id, self.group_type, member["wxid"]
                )
                if success:
                    deleted_count += 1

            if deleted_count > 0:
                # 重新加载数据
                self.group = group_manager.get_group(self.group_id, self.group_type)
                self.load_members_data()
                self.group_updated.emit()

                ThemedMessageBoxHelper.show_information(
                    self, "成功", f"成功删除 {deleted_count} 个成员"
                )
                logger.info(f"从分组 {self.group.name} 删除了 {deleted_count} 个成员")
            else:
                ThemedMessageBoxHelper.show_warning(self, "警告", "没有成员被删除")

        except Exception as e:
            logger.error(f"删除成员失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"删除成员失败:\n{e}")

    def show_member_context_menu(self, position):
        """显示成员右键菜单"""
        item = self.members_table.itemAt(position)
        if not item:
            return

        row = item.row()
        wxid_item = self.members_table.item(row, 3)
        name_item = self.members_table.item(row, 2)

        if not wxid_item or not name_item:
            return

        menu = QMenu(self)

        # 删除成员动作
        delete_action = menu.addAction("🗑️ 删除此成员")
        delete_action.triggered.connect(
            lambda: self.delete_single_member(wxid_item.text(), name_item.text())
        )

        menu.exec(self.members_table.mapToGlobal(position))

    def delete_single_member(self, wxid, name):
        """删除单个成员"""
        reply = ThemedMessageBoxHelper.show_question(
            self, "确认删除", f"确定要删除成员 '{name}' 吗？"
        )

        if reply:
            self.perform_delete_members([{"wxid": wxid, "name": name}])

    def refresh_members(self):
        """刷新成员列表"""
        try:
            # 重新获取分组数据
            self.group = group_manager.get_group(self.group_id, self.group_type)
            if self.group:
                self.load_members_data()
                logger.info(f"刷新成员列表: {self.group.name}")
            else:
                logger.warning(f"分组不存在，无法刷新: {self.group_id}")
                ThemedMessageBoxHelper.show_warning(
                    self, "警告", "分组不存在，无法刷新"
                )
        except Exception as e:
            logger.error(f"刷新成员列表失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"刷新成员列表失败:\n{e}")

    def refresh_data(self):
        """刷新数据"""
        # 重新获取分组数据
        self.group = group_manager.get_group(self.group_id, self.group_type)
        if self.group:
            self.load_data()
            logger.info(f"刷新分组详情数据: {self.group.name}")
        else:
            logger.warning(f"分组不存在，无法刷新: {self.group_id}")
            self.close()

    # 主题处理已由 ThemedDialogBase 自动处理
