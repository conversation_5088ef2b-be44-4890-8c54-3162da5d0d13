#!/usr/bin/env python3
"""
主题变化检查工具
检查科技主题是否正确应用了修复
"""

import sys
from pathlib import Path

def check_theme_changes():
    """检查主题变化"""
    print("🔍 检查科技主题修复状态...")
    
    theme_file = Path(__file__).parent / "ui" / "modern_theme_manager.py"
    
    if not theme_file.exists():
        print("❌ 主题文件不存在")
        return False
    
    try:
        content = theme_file.read_text(encoding='utf-8')
        
        # 查找科技主题部分
        tech_theme_start = content.find("def _get_tech_theme(self)")
        if tech_theme_start == -1:
            print("❌ 未找到科技主题定义")
            return False
        
        # 查找科技主题结束位置（下一个方法定义）
        remaining_content = content[tech_theme_start:]
        next_method = remaining_content.find("\n    def ", 1)  # 查找下一个方法
        if next_method == -1:
            tech_theme_content = remaining_content
        else:
            tech_theme_content = remaining_content[:next_method]
        
        print(f"\n📊 科技主题内容分析:")
        print(f"  主题定义长度: {len(tech_theme_content)} 字符")
        
        # 详细检查各种属性
        checks = {
            "尺寸属性": {
                "width:": tech_theme_content.count("width:"),
                "height:": tech_theme_content.count("height:"),
                "min-width:": tech_theme_content.count("min-width:"),
                "max-width:": tech_theme_content.count("max-width:"),
                "padding:": tech_theme_content.count("padding:"),
                "margin:": tech_theme_content.count("margin:"),
                "border-radius:": tech_theme_content.count("border-radius:"),
            },
            "颜色属性": {
                "background:": tech_theme_content.count("background:"),
                "background-color:": tech_theme_content.count("background-color:"),
                "color:": tech_theme_content.count("color:"),
                "border: 1px solid": tech_theme_content.count("border: 1px solid"),
            },
            "渐变效果": {
                "qlineargradient": tech_theme_content.count("qlineargradient"),
                "stop:": tech_theme_content.count("stop:"),
            }
        }
        
        for category, items in checks.items():
            print(f"\n  {category}:")
            for prop, count in items.items():
                status = "✅" if (category == "尺寸属性" and count == 0) or (category != "尺寸属性" and count > 0) else "❌"
                print(f"    {status} {prop} {count}个")
        
        # 总体评估
        size_issues = sum(checks["尺寸属性"].values())
        color_count = sum(checks["颜色属性"].values())
        gradient_count = sum(checks["渐变效果"].values())
        
        print(f"\n📈 总体评估:")
        if size_issues == 0:
            print("  ✅ 尺寸属性已完全清理")
        else:
            print(f"  ❌ 仍有 {size_issues} 个尺寸属性")
        
        if color_count > 0:
            print(f"  ✅ 颜色属性正常 ({color_count}个)")
        else:
            print("  ❌ 缺少颜色属性")
        
        if gradient_count > 0:
            print(f"  ✅ 渐变效果正常 ({gradient_count}个)")
        else:
            print("  ⚠️  缺少渐变效果")
        
        # 检查特定控件
        print(f"\n🔍 特定控件检查:")
        controls = ["QSpinBox", "QComboBox", "QCheckBox", "QRadioButton", "QPushButton"]
        for control in controls:
            count = tech_theme_content.count(control)
            print(f"  {control}: {count}个定义")
        
        return size_issues == 0 and color_count > 0
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_theme_application():
    """检查主题应用逻辑"""
    print("\n🔍 检查主题应用逻辑...")
    
    theme_file = Path(__file__).parent / "ui" / "modern_theme_manager.py"
    
    try:
        content = theme_file.read_text(encoding='utf-8')
        
        # 检查主题列表
        if '"科技主题"' in content:
            print("  ✅ 科技主题在可用主题列表中")
        else:
            print("  ❌ 科技主题不在可用主题列表中")
        
        # 检查主题映射
        if '"科技主题": self._get_tech_theme()' in content:
            print("  ✅ 科技主题映射正确")
        else:
            print("  ❌ 科技主题映射错误")
        
        # 检查主题设置逻辑
        if 'def set_theme' in content:
            print("  ✅ 主题设置方法存在")
        else:
            print("  ❌ 主题设置方法缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 科技主题变化检查工具")
    print("=" * 50)
    
    print("📋 检查内容:")
    print("  1. 科技主题样式定义")
    print("  2. 尺寸属性清理状态")
    print("  3. 颜色属性保留状态")
    print("  4. 主题应用逻辑")
    
    # 检查主题定义
    theme_ok = check_theme_changes()
    
    # 检查应用逻辑
    app_ok = check_theme_application()
    
    print(f"\n🎯 检查结果:")
    if theme_ok and app_ok:
        print("✅ 科技主题修复完成且正确配置")
        print("\n📋 修复确认:")
        print("  - 所有尺寸属性已移除")
        print("  - 颜色和渐变效果保留")
        print("  - 主题应用逻辑正常")
        print("  - 科技主题现在使用默认尺寸")
        
        print("\n🧪 测试建议:")
        print("1. 启动程序: python main.py")
        print("2. 进入系统设置页面")
        print("3. 切换到科技主题")
        print("4. 观察控件尺寸是否与默认主题相同")
        print("5. 如果尺寸仍然不同，可能需要重启程序")
        
        print("\n💡 注意事项:")
        print("  - 主题切换后可能需要重启程序才能完全生效")
        print("  - 某些已打开的对话框可能需要重新打开")
        print("  - 如果问题仍然存在，请提供具体的控件和页面信息")
        
    else:
        print("❌ 科技主题修复存在问题")
        if not theme_ok:
            print("  - 主题定义有问题")
        if not app_ok:
            print("  - 主题应用逻辑有问题")
    
    return 0 if (theme_ok and app_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
