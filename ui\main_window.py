import os
import asyncio
import time
from datetime import datetime
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QPushButton,
    QLabel,
    QTabWidget,
    QTextEdit,
    QListWidget,
    QListWidgetItem,
    QComboBox,
    QSpinBox,
    QCheckBox,
    QFileDialog,
    QMessageBox,
    QProgressBar,
    QGroupBox,
    QSplitter,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QLineEdit,
    QDialog,
    QRadioButton,
    QButtonGroup,
    QMenu,
    QApplication,
    QStackedWidget,
)
from PyQt6.QtCore import (
    Qt,
    pyqtSlot,
    QSize,
    QTimer,
    QThread,
    pyqtSignal,
    QMetaObject,
    Q_ARG,
)
from PyQt6.QtGui import QIcon, QFont, QTextCursor, QTextDocument

from core.wechatferry_connector import WeChatFerryConnector, Contact
from core.message_template import MessageTemplate
from core.send_monitor import Send<PERSON>oni<PERSON>, SendTask, SendStatus
from core.risk_control import RiskController
from core.timing_sender import timing_sender
from core.loop_sender import loop_sender
from config.wechat_config import WeChatConfig
from ui.rich_text_editor import RichTextMessageEditor, MessagePreviewDialog
from ui.themed_message_box import ThemedMessageBoxHelper

# 移除旧的主题管理器导入

from ui.ui_optimizer import (
    ui_optimizer,
    ui_performance_monitor,
    batch_ui_update,
    safe_ui_operation,
)
from ui.custom_main_window import CustomMainWindow
from ui.backup_manager_dialog import BackupManagerDialog
from ui.modern_theme_manager import theme_manager as modern_theme_manager
from ui.animated_connect_button import AnimatedConnectButton
from core.optimized_sender import OptimizedSender
from utils.logger import setup_logger
from utils.icon_manager import apply_window_icon
from utils.performance import (
    async_timing_decorator,
    memory_manager,
    performance_context,
    timing_decorator,
)
from utils.performance_optimizer import (
    performance_optimizer,
    performance_monitor,
    async_task,
    perf_config,
)

logger = setup_logger("ui")


class AsyncTaskRunner(QThread):
    """异步任务运行器"""

    # 信号定义
    task_finished = pyqtSignal(object)  # 任务完成信号
    task_error = pyqtSignal(str)  # 任务错误信号
    task_progress = pyqtSignal(str)  # 任务进度信号

    def __init__(self, coro, task_name: str = None, parent=None):
        super().__init__()  # 不设置parent，避免线程问题
        self.coro = coro
        self.task_name = task_name or f"async_task_{id(self)}"
        self.result = None
        self.error = None
        self._start_time = time.time()
        self._cancelled = False

    def run(self):
        """运行异步任务"""
        loop = None
        try:
            logger.debug(f"启动异步任务: {self.task_name}")
            
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 设置任务超时（30分钟）
            timeout = 30 * 60  # 30分钟
            
            # 运行协程
            self.result = loop.run_until_complete(
                asyncio.wait_for(self.coro, timeout=timeout)
            )

            # 如果没有被取消，发送完成信号
            if not self._cancelled:
                elapsed_time = time.time() - self._start_time
                logger.debug(f"异步任务完成: {self.task_name}, 耗时: {elapsed_time:.2f}秒")
                self.task_finished.emit(self.result)

        except asyncio.TimeoutError:
            self.error = f"任务 {self.task_name} 超时"
            logger.error(self.error)
            if not self._cancelled:
                self.task_error.emit(self.error)
                
        except asyncio.CancelledError:
            self.error = f"任务 {self.task_name} 被取消"
            logger.info(self.error)
            # 取消不算错误，不发送错误信号
            
        except Exception as e:
            self.error = str(e)
            logger.error(f"异步任务失败: {self.task_name}, 错误: {self.error}")
            if not self._cancelled:
                self.task_error.emit(self.error)
                
        finally:
            # 安全清理事件循环
            self._cleanup_event_loop(loop)

    def _cleanup_event_loop(self, loop):
        """安全清理事件循环"""
        if loop is None:
            return
            
        try:
            # 取消所有未完成的任务
            pending_tasks = []
            try:
                pending_tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
            except RuntimeError:
                # 事件循环可能已经关闭
                pass

            if pending_tasks:
                logger.debug(f"取消 {len(pending_tasks)} 个未完成的任务")
                for task in pending_tasks:
                    task.cancel()

                # 等待所有任务完成或被取消
                try:
                    loop.run_until_complete(
                        asyncio.gather(*pending_tasks, return_exceptions=True)
                    )
                except Exception as e:
                    logger.debug(f"清理任务时出错: {e}")

        except Exception as e:
            logger.warning(f"清理事件循环中的任务失败: {e}")

        finally:
            # 关闭事件循环
            try:
                if not loop.is_closed():
                    loop.close()
                logger.debug(f"事件循环已关闭: {self.task_name}")
            except Exception as e:
                logger.warning(f"关闭事件循环失败: {e}")

    def cancel_task(self):
        """取消任务"""
        self._cancelled = True
        if self.isRunning():
            self.requestInterruption()
            logger.debug(f"请求中断任务: {self.task_name}")

    def get_elapsed_time(self) -> float:
        """获取已运行时间"""
        return time.time() - self._start_time


class MainWindow(CustomMainWindow):
    """主窗口（自定义标题栏版）"""

    def __init__(
        self,
        connector: WeChatFerryConnector,
        template: MessageTemplate,
        monitor: SendMonitor,
        risk_controller: RiskController,
        config: WeChatConfig,
    ):
        # 调用自定义主窗口的初始化
        super().__init__("Meet space 微信群发助手")

        # 设置窗口图标
        apply_window_icon(self)

        # 性能优化：启动优化
        performance_optimizer.optimize_startup()

        self.connector = connector
        self.template = template
        self.monitor = monitor
        self.risk_controller = risk_controller
        self.config = config

        # 初始化基本属性
        self.contacts = []
        self.selected_contacts = []
        self.message_content = ""
        self.message_type = "text"
        self.file_path = ""

        # 异步任务管理 - 使用性能优化器的任务管理器
        self.async_task_manager = performance_optimizer.async_task_manager
        self.async_tasks = []  # 保持向后兼容

        # 初始化优化发送器
        self.optimized_sender = OptimizedSender(connector, monitor, config)

        # 延迟初始化重型组件
        self._init_heavy_components()

        # 快速初始化UI
        self.init_ui()
        self.connect_signals()

        # 优化UI组件
        self._optimize_ui_components()

        # 注册主窗口到现代主题管理器
        modern_theme_manager.register_widget(self, "main")

        # 注册所有复选框到主题管理器
        self.register_checkboxes_to_theme()

        # 连接主题变更信号
        modern_theme_manager.theme_changed.connect(self.on_theme_manager_changed)
        modern_theme_manager.theme_switching_started.connect(
            self.on_theme_switching_started
        )
        modern_theme_manager.theme_switching_finished.connect(
            self.on_theme_switching_finished
        )

        # 应用保存的主题
        self.apply_saved_theme()

        # 使用配置的定时器频率
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.update_status_optimized)
        self.status_timer.start(perf_config.status_update_interval)

        # 添加状态更新计数器，避免频繁更新
        self.status_update_counter = 0

    def _init_heavy_components(self):
        """延迟初始化重型组件"""
        # 使用异步任务初始化定时发送器
        self.async_task_manager.submit_task(
            "init_timed_sender", self._create_timed_sender
        )
        self.async_task_manager.task_completed.connect(self._on_timed_sender_ready)

    def _create_timed_sender(self):
        """创建定时发送器"""
        # 使用timing_sender替代TimedSender
        return timing_sender

    def _on_timed_sender_ready(self, task_id: str, timed_sender):
        """定时发送器准备完成"""
        if task_id == "init_timed_sender":
            self.timed_sender = timed_sender
            # 连接TimingSender的实际信号
            self.timed_sender.task_started.connect(self.on_timed_task_started)
            self.timed_sender.task_progress.connect(self.on_timed_task_progress)
            self.timed_sender.task_completed.connect(self.on_timed_task_completed)
            self.timed_sender.task_failed.connect(self.on_timed_task_failed)

            # 设置连接器到发送器
            self._setup_senders_connector()

            # 使用QTimer.singleShot确保在主线程中初始化定时器
            QTimer.singleShot(0, timing_sender.init_timer)
            QTimer.singleShot(0, loop_sender.init_timer)

            logger.info("定时发送器初始化完成")

    def _setup_senders_connector(self):
        """设置连接器到各个发送器"""
        try:
            if hasattr(self, "connector") and self.connector:
                # 设置连接器到定时发送器
                timing_sender.set_connector(self.connector)

                # 设置连接器到循环发送器
                loop_sender.set_connector(self.connector)

                logger.info("已设置连接器到定时和循环发送器")
            else:
                logger.warning("连接器未初始化，无法设置到发送器")

        except Exception as e:
            logger.error(f"设置发送器连接器失败: {e}")

    def _optimize_ui_components(self):
        """优化UI组件"""
        try:
            # 连接UI优化器信号
            ui_optimizer.ui_freeze_detected.connect(self._on_ui_freeze_detected)
            ui_optimizer.performance_warning.connect(self._on_performance_warning)

            # 延迟优化表格组件（等UI创建完成）
            QTimer.singleShot(1000, self._optimize_tables)

            logger.info("UI组件优化设置完成")

        except Exception as e:
            logger.error(f"UI组件优化失败: {e}")

    def _optimize_tables(self):
        """优化表格组件"""
        try:
            # 优化联系人表格
            if hasattr(self, "friends_table"):
                ui_optimizer.optimize_table_widget(self.friends_table)
            if hasattr(self, "groups_table"):
                ui_optimizer.optimize_table_widget(self.groups_table)
            if hasattr(self, "status_table"):
                ui_optimizer.optimize_table_widget(self.status_table)

            # 优化列表组件
            if hasattr(self, "friends_list"):
                ui_optimizer.optimize_list_widget(self.friends_list)
            if hasattr(self, "groups_list"):
                ui_optimizer.optimize_list_widget(self.groups_list)

            logger.info("表格组件优化完成")

        except Exception as e:
            logger.error(f"表格组件优化失败: {e}")

    def _on_ui_freeze_detected(self, operation: str, duration: float):
        """UI冻结检测处理"""
        logger.warning(f"检测到UI冻结: {operation} 耗时 {duration:.1f}ms")

        # 如果冻结时间过长，显示警告
        if duration > 500:  # 超过500ms
            self.statusBar().showMessage(f"操作响应较慢: {operation}", 3000)

    def _on_performance_warning(self, warning_type: str, data: dict):
        """性能警告处理"""
        if warning_type == "memory_high":
            memory_mb = data.get("memory_mb", 0)
            logger.warning(f"内存使用过高: {memory_mb:.1f}MB")
            self.statusBar().showMessage(f"内存使用: {memory_mb:.1f}MB", 2000)

        elif warning_type == "cpu_high":
            cpu_percent = data.get("cpu_percent", 0)
            logger.warning(f"CPU使用过高: {cpu_percent:.1f}%")
            self.statusBar().showMessage(f"CPU使用: {cpu_percent:.1f}%", 2000)

    @performance_monitor
    def update_status_optimized(self):
        """优化的状态更新"""
        try:
            # 检查是否需要更新（避免无意义的更新）
            if not self.isVisible():
                return

            # 增加更新计数器
            self.status_update_counter += 1

            # 检查是否有其他高优先级任务在执行
            if (
                hasattr(self, "async_task_manager")
                and self.async_task_manager.has_running_tasks()
            ):
                # 如果有任务在执行，跳过这次更新
                logger.debug(
                    f"跳过状态更新，有 {self.async_task_manager.get_running_task_count()} 个任务在执行"
                )
                return

            # 使用配置的跳过比例
            if self.status_update_counter % perf_config.status_update_skip_ratio != 0:
                return

            # 简化的状态更新，避免调用复杂的事件处理
            self._update_status_simple()

        except Exception as e:
            logger.error(f"优化状态更新失败: {e}")

    def _update_status_simple(self):
        """简化的状态更新，避免复杂操作"""
        try:
            # 只更新最基本的连接状态
            if hasattr(self, "connector") and self.connector:
                if self.connector.is_connected:
                    if self.connector.is_logged_in:
                        self.status_label.setText("已连接并登录")
                    else:
                        self.status_label.setText("已连接，等待登录")
                else:
                    self.status_label.setText("未连接")
            else:
                self.status_label.setText("初始化中...")

        except Exception as e:
            logger.error(f"简化状态更新失败: {e}")
            self.status_label.setText("状态更新失败")

    def apply_saved_theme(self):
        """应用保存的主题设置"""
        try:
            # 获取保存的主题和字体大小
            saved_theme = getattr(self.config, "theme", "默认主题")
            saved_font_size = getattr(self.config, "font_size", "中")
            logger.info(f"应用保存的主题: {saved_theme}, 字体大小: {saved_font_size}")

            # 先设置字体大小
            modern_theme_manager.setup_dynamic_font(
                QApplication.instance(), saved_font_size
            )

            # 然后应用主题
            modern_theme_manager.set_theme(QApplication.instance(), saved_theme)

            # 更新主题选择器显示（如果已创建）
            if hasattr(self, "theme_combo"):
                # 临时断开信号连接，避免触发on_theme_changed
                self.theme_combo.currentTextChanged.disconnect()
                self.theme_combo.setCurrentText(saved_theme)
                # 重新连接信号
                self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
                logger.info(f"已更新主题选择器显示: {saved_theme}")

        except Exception as e:
            logger.error(f"应用保存的主题失败: {e}")
            # 如果失败，应用默认主题
            modern_theme_manager.setup_dynamic_font(QApplication.instance(), "中")
            modern_theme_manager.set_theme(QApplication.instance(), "默认主题")

    def on_theme_switching_started(self, theme_name: str):
        """主题切换开始处理"""
        try:
            self.statusBar().showMessage(f"正在切换到 {theme_name}...", 0)
            # 设置鼠标为等待状态
            QApplication.setOverrideCursor(Qt.CursorShape.WaitCursor)
            logger.debug(f"主题切换开始: {theme_name}")
        except Exception as e:
            logger.error(f"主题切换开始处理失败: {e}")

    def on_theme_switching_progress(self, current: int, total: int):
        """主题切换进度处理"""
        try:
            if total > 0:
                progress = int((current / total) * 100)
                self.statusBar().showMessage(
                    f"正在应用主题... {progress}% ({current}/{total})", 0
                )
        except Exception as e:
            logger.error(f"主题切换进度处理失败: {e}")

    def on_theme_switching_finished(self, theme_name: str):
        """主题切换完成处理"""
        try:
            # 恢复鼠标状态
            QApplication.restoreOverrideCursor()
            self.statusBar().showMessage(f"主题已切换到 {theme_name}", 2000)
            logger.debug(f"主题切换完成: {theme_name}")
        except Exception as e:
            logger.error(f"主题切换完成处理失败: {e}")

    def on_theme_manager_changed(self, theme_name: str):
        """主题管理器主题变更处理"""
        try:
            logger.info(f"主题管理器通知主题变更: {theme_name}")

            # 更新连接微信按钮
            self.update_connect_button_for_theme(theme_name)

            # 通知所有子页面更新主题
            self.notify_theme_changed(theme_name)

            # 更新状态栏
            self.statusBar().showMessage(f"已切换到{theme_name}")

        except Exception as e:
            logger.error(f"处理主题管理器变更失败: {e}")

    def update_connect_button_for_theme(self, theme_name: str):
        """根据主题更新连接微信按钮"""
        try:
            # 获取当前按钮的父布局
            parent_layout = self.connect_btn.parent().layout()
            if not parent_layout:
                return

            # 获取按钮在布局中的位置
            button_index = -1
            for i in range(parent_layout.count()):
                if parent_layout.itemAt(i).widget() == self.connect_btn:
                    button_index = i
                    break

            if button_index == -1:
                return

            # 保存按钮的连接状态
            old_button = self.connect_btn
            was_connected = (
                hasattr(old_button, "clicked")
                and old_button.receivers(old_button.clicked) > 0
            )

            # 停止旧按钮的动画（如果是动画按钮）
            if isinstance(old_button, AnimatedConnectButton):
                old_button.stop_gradient_animation()

            # 移除旧按钮
            parent_layout.removeWidget(old_button)
            old_button.deleteLater()

            # 创建新按钮
            if theme_name == "科技主题":
                self.connect_btn = AnimatedConnectButton("连接微信")
            else:
                self.connect_btn = QPushButton("连接微信")
                self.connect_btn.setProperty("class", "connect-wechat")

            # 重新连接信号
            self.connect_btn.clicked.connect(self.toggle_connection)

            # 将新按钮插入到原位置
            parent_layout.insertWidget(button_index, self.connect_btn)

            logger.info(f"连接微信按钮已更新为{theme_name}样式")

        except Exception as e:
            logger.error(f"更新连接微信按钮失败: {e}")

    @timing_decorator("ui_async_task")
    def run_async_task(self, coro, on_finished=None, on_error=None, task_name=None):
        """
        运行异步任务

        Args:
            coro: 协程对象
            on_finished: 完成回调函数
            on_error: 错误回调函数
            task_name: 任务名称（用于调试）
        """
        # 检查任务数量，避免过多任务
        max_concurrent_tasks = 15  # 增加最大并发任务数
        if len(self.async_tasks) > max_concurrent_tasks:
            logger.warning(f"异步任务数量过多: {len(self.async_tasks)}")
            self._cleanup_finished_tasks()
            
            # 如果清理后仍然过多，取消最旧的任务
            if len(self.async_tasks) > max_concurrent_tasks:
                self._cancel_oldest_tasks(max_concurrent_tasks // 2)

        # 生成任务名称
        if not task_name:
            task_name = f"ui_task_{len(self.async_tasks)}_{int(time.time())}"

        # 创建任务运行器
        task_runner = AsyncTaskRunner(coro, task_name)

        # 注册到内存管理器
        memory_manager.register_object(task_runner)

        # 连接信号 - 使用Qt.QueuedConnection确保线程安全
        if on_finished:
            task_runner.task_finished.connect(
                on_finished, Qt.ConnectionType.QueuedConnection
            )
        if on_error:
            task_runner.task_error.connect(on_error, Qt.ConnectionType.QueuedConnection)

        # 清理完成的任务
        def cleanup():
            try:
                if task_runner in self.async_tasks:
                    self.async_tasks.remove(task_runner)
                logger.debug(f"异步任务清理完成: {task_name}")
            except Exception as e:
                logger.warning(f"清理异步任务失败: {e}")

        task_runner.task_finished.connect(cleanup)
        task_runner.task_error.connect(cleanup)

        # 启动任务
        self.async_tasks.append(task_runner)
        task_runner.start()
        
        logger.debug(f"启动异步任务: {task_name}, 当前任务数: {len(self.async_tasks)}")
        return task_runner

    def _cleanup_finished_tasks(self):
        """清理已完成的任务"""
        before_count = len(self.async_tasks)
        self.async_tasks = [task for task in self.async_tasks if task.isRunning()]
        cleaned_count = before_count - len(self.async_tasks)
        if cleaned_count > 0:
            logger.debug(f"清理了 {cleaned_count} 个已完成的异步任务")

    def _cancel_oldest_tasks(self, keep_count: int):
        """取消最旧的任务，保留指定数量的最新任务"""
        if len(self.async_tasks) <= keep_count:
            return
            
        # 按创建时间排序（最旧的在前）
        tasks_by_time = sorted(self.async_tasks, key=lambda t: t._start_time)
        
        # 取消最旧的任务
        tasks_to_cancel = tasks_by_time[:-keep_count] if keep_count > 0 else tasks_by_time
        
        for task in tasks_to_cancel:
            try:
                task.cancel_task()
                if task in self.async_tasks:
                    self.async_tasks.remove(task)
                logger.debug(f"取消最旧任务: {task.task_name}")
            except Exception as e:
                logger.warning(f"取消任务失败: {e}")
                
        if tasks_to_cancel:
            logger.info(f"取消了 {len(tasks_to_cancel)} 个最旧的异步任务")

    def init_ui(self):
        """初始化UI"""
        # 设置窗口标题（通过自定义标题栏）
        self.set_title("Meet Space -by SWD")
        self.setMinimumSize(900, 600)

        # 使用自定义主窗口的内容布局
        main_layout = self.get_content_layout()

        # 设置统一的布局边距和间距（符合技术规范）
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # 顶部状态区域
        status_layout = QHBoxLayout()
        status_layout.setSpacing(8)  # 统一间距
        self.status_label = QLabel("未连接")
        # 使用动画连接微信按钮（仅在科技主题下启用动画）
        if modern_theme_manager.get_current_theme() == "科技主题":
            self.connect_btn = AnimatedConnectButton("连接微信")
        else:
            self.connect_btn = QPushButton("连接微信")
            self.connect_btn.setProperty("class", "connect-wechat")

        status_layout.addWidget(QLabel("状态:"))
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        status_layout.addWidget(self.connect_btn)
        main_layout.addLayout(status_layout)

        # 创建选项卡
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)

        # 创建各个选项卡
        self.create_send_tab(self.tabs)
        self.create_timing_send_tab(self.tabs)
        self.create_loop_send_tab(self.tabs)
        self.create_task_status_tab(self.tabs)
        self.create_contacts_tab(self.tabs)
        self.create_templates_tab(self.tabs)
        self.create_logs_tab(self.tabs)
        self.create_settings_tab(self.tabs)

        # 底部状态栏
        self.statusBar().showMessage("就绪")

    def create_send_tab(self, tabs):
        """创建发送选项卡"""
        send_tab = QWidget()
        tabs.addTab(send_tab, "消息发送")

        layout = QVBoxLayout(send_tab)
        # 设置统一的布局边距和间距（符合技术规范）
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # 上半部分：联系人选择和消息编辑
        upper_layout = QHBoxLayout()

        # 左侧：联系人选择
        contacts_group = QGroupBox("待接收人")
        contacts_layout = QVBoxLayout(contacts_group)

        # 搜索框
        search_layout = QHBoxLayout()
        self.contact_search = QLineEdit()
        self.contact_search.setPlaceholderText("搜索联系人...")
        search_layout.addWidget(self.contact_search)
        contacts_layout.addLayout(search_layout)

        # 分类标签页
        self.contact_tabs = QTabWidget()

        # 好友列表
        friends_widget = QWidget()
        friends_layout = QVBoxLayout(friends_widget)
        self.friends_list = QListWidget()
        self.friends_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        friends_layout.addWidget(self.friends_list)
        self.contact_tabs.addTab(friends_widget, "好友")

        # 群聊列表
        groups_widget = QWidget()
        groups_layout = QVBoxLayout(groups_widget)
        self.groups_list = QListWidget()
        self.groups_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        groups_layout.addWidget(self.groups_list)
        self.contact_tabs.addTab(groups_widget, "群聊")

        contacts_layout.addWidget(self.contact_tabs)

        # 选择按钮
        select_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        # 修复：设置为次要按钮
        self.select_all_btn.setProperty("class", "secondary")

        self.deselect_all_btn = QPushButton("取消全选")
        # 修复：设置为次要按钮
        self.deselect_all_btn.setProperty("class", "secondary")

        self.invert_select_btn = QPushButton("反选")
        # 修复：设置为小型按钮
        self.invert_select_btn.setProperty("class", "small")

        select_layout.addWidget(self.select_all_btn)
        select_layout.addWidget(self.deselect_all_btn)
        select_layout.addWidget(self.invert_select_btn)
        contacts_layout.addLayout(select_layout)

        # 已选计数
        self.selected_count_label = QLabel("已选择: 0")
        contacts_layout.addWidget(self.selected_count_label)

        # 同步和清空按钮
        import_layout = QHBoxLayout()
        self.sync_from_send_list_btn = QPushButton("同步列表")
        self.clear_recipients_btn = QPushButton("清空待接收人")
        import_layout.addWidget(self.sync_from_send_list_btn)
        import_layout.addWidget(self.clear_recipients_btn)
        contacts_layout.addLayout(import_layout)

        upper_layout.addWidget(contacts_group)

        # 右侧：消息编辑
        message_group = QGroupBox("编辑消息")
        message_layout = QVBoxLayout(message_group)

        # 消息类型选择
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("消息类型:"))
        self.msg_type_combo = QComboBox()
        self.msg_type_combo.addItems(["富文本消息", "文本消息", "图片消息", "文件消息"])
        type_layout.addWidget(self.msg_type_combo)
        type_layout.addStretch()
        message_layout.addLayout(type_layout)

        # 消息编辑器堆栈
        self.editor_stack = QStackedWidget()

        # 富文本编辑器
        self.rich_text_editor = RichTextMessageEditor()
        self.rich_text_editor.content_changed.connect(self.update_content_summary)
        self.editor_stack.addWidget(self.rich_text_editor)

        # 传统编辑器容器
        traditional_widget = QWidget()
        traditional_layout = QVBoxLayout(traditional_widget)

        # 文本编辑区
        self.message_edit = QTextEdit()
        self.message_edit.setPlaceholderText(
            "在此输入消息内容...\n支持变量: {{name}} - 联系人名称, {{current_time}} - 当前时间"
        )
        self.message_edit.textChanged.connect(self.update_traditional_text_count)
        traditional_layout.addWidget(self.message_edit)

        # 文件选择区域
        file_layout = QHBoxLayout()
        self.file_path_label = QLabel("未选择文件")
        self.select_file_btn = QPushButton("选择文件")
        file_layout.addWidget(self.file_path_label)
        file_layout.addWidget(self.select_file_btn)
        traditional_layout.addLayout(file_layout)

        self.editor_stack.addWidget(traditional_widget)
        message_layout.addWidget(self.editor_stack)

        # 富文本编辑器工具栏
        editor_toolbar = QHBoxLayout()

        self.insert_image_btn = QPushButton("插入图片")
        self.insert_image_btn.clicked.connect(self.insert_image_file)
        editor_toolbar.addWidget(self.insert_image_btn)

        self.paste_image_btn = QPushButton("粘贴图片")
        self.paste_image_btn.clicked.connect(self.paste_image_from_clipboard)
        editor_toolbar.addWidget(self.paste_image_btn)

        self.clear_content_btn = QPushButton("清空内容")
        self.clear_content_btn.clicked.connect(self.clear_message_content)
        editor_toolbar.addWidget(self.clear_content_btn)

        editor_toolbar.addStretch()

        # 内容摘要标签
        self.content_summary_label = QLabel("内容: 空消息")
        editor_toolbar.addWidget(self.content_summary_label)

        message_layout.addLayout(editor_toolbar)

        # 模板选择
        template_layout = QHBoxLayout()
        template_layout.addWidget(QLabel("选择模板:"))
        self.template_combo = QComboBox()
        self.template_combo.addItem("无")
        template_layout.addWidget(self.template_combo)
        message_layout.addLayout(template_layout)

        upper_layout.addWidget(message_group)
        layout.addLayout(upper_layout)

        # 中间部分：发送选项和定时设置
        options_group = QGroupBox("发送选项")
        options_layout = QVBoxLayout(options_group)

        # 第一行：基础发送设置
        basic_layout = QHBoxLayout()

        # 延迟设置
        delay_layout = QVBoxLayout()
        delay_layout.addWidget(QLabel("发送间隔(秒):"))
        delay_h_layout = QHBoxLayout()
        self.min_delay_spin = QSpinBox()
        self.min_delay_spin.setRange(1, 60)
        self.min_delay_spin.setValue(self.config.send_interval_min)
        self.max_delay_spin = QSpinBox()
        self.max_delay_spin.setRange(1, 60)
        self.max_delay_spin.setValue(self.config.send_interval_max)
        delay_h_layout.addWidget(self.min_delay_spin)
        delay_h_layout.addWidget(QLabel("-"))
        delay_h_layout.addWidget(self.max_delay_spin)
        delay_layout.addLayout(delay_h_layout)
        basic_layout.addLayout(delay_layout)

        # 批次设置
        batch_layout = QVBoxLayout()
        batch_layout.addWidget(QLabel("批次设置:"))
        batch_h_layout = QHBoxLayout()
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 100)
        self.batch_size_spin.setValue(self.config.batch_size)
        batch_h_layout.addWidget(QLabel("每批"))
        batch_h_layout.addWidget(self.batch_size_spin)
        batch_h_layout.addWidget(QLabel("个"))
        batch_layout.addLayout(batch_h_layout)
        basic_layout.addLayout(batch_layout)

        # 安全选项
        safety_layout = QVBoxLayout()
        self.simulate_human_check = QCheckBox("模拟人工操作")
        self.simulate_human_check.setChecked(self.config.simulate_human)
        self.random_delay_check = QCheckBox("随机延迟")
        self.random_delay_check.setChecked(self.config.random_delay)
        safety_layout.addWidget(self.simulate_human_check)
        safety_layout.addWidget(self.random_delay_check)
        basic_layout.addLayout(safety_layout)

        options_layout.addLayout(basic_layout)

        # 第二行：定时循环发送设置
        timing_group = QGroupBox("定时循环发送")
        timing_layout = QVBoxLayout(timing_group)

        # 启用定时发送
        self.enable_timing_check = QCheckBox("启用定时循环发送")
        self.enable_timing_check.stateChanged.connect(self.toggle_timing_options)
        timing_layout.addWidget(self.enable_timing_check)

        # 定时设置容器
        self.timing_options_widget = QWidget()
        timing_options_layout = QHBoxLayout(self.timing_options_widget)

        # 循环间隔设置
        interval_layout = QVBoxLayout()
        interval_layout.addWidget(QLabel("循环间隔:"))
        self.cycle_interval_spin = QSpinBox()
        self.cycle_interval_spin.setRange(1, 24)
        self.cycle_interval_spin.setValue(2)  # 默认2小时
        self.cycle_interval_spin.setSuffix(" 小时")
        interval_layout.addWidget(self.cycle_interval_spin)
        timing_options_layout.addLayout(interval_layout)

        # 星期选择
        weekday_layout = QVBoxLayout()
        weekday_layout.addWidget(QLabel("执行星期:"))
        weekday_group_layout = QHBoxLayout()
        self.weekday_checks = []
        weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        for i, day in enumerate(weekdays):
            check = QCheckBox(day)
            if i < 5:  # 默认选中工作日
                check.setChecked(True)
            self.weekday_checks.append(check)
            weekday_group_layout.addWidget(check)
        weekday_layout.addLayout(weekday_group_layout)
        timing_options_layout.addLayout(weekday_layout)

        # 时间范围设置
        time_range_layout = QVBoxLayout()
        time_range_layout.addWidget(QLabel("有效时间:"))
        time_h_layout = QHBoxLayout()
        self.start_time_spin = QSpinBox()
        self.start_time_spin.setRange(0, 23)
        self.start_time_spin.setValue(9)  # 默认9点开始
        self.start_time_spin.setSuffix(":00")
        self.end_time_spin = QSpinBox()
        self.end_time_spin.setRange(0, 23)
        self.end_time_spin.setValue(18)  # 默认18点结束
        self.end_time_spin.setSuffix(":00")
        time_h_layout.addWidget(self.start_time_spin)
        time_h_layout.addWidget(QLabel("-"))
        time_h_layout.addWidget(self.end_time_spin)
        time_range_layout.addLayout(time_h_layout)
        timing_options_layout.addLayout(time_range_layout)

        timing_layout.addWidget(self.timing_options_widget)

        # 默认禁用定时选项
        self.timing_options_widget.setEnabled(False)

        options_layout.addWidget(timing_group)
        layout.addWidget(options_group)

        # 下半部分：发送状态和控制按钮
        status_group = QGroupBox("发送状态")
        status_layout = QVBoxLayout(status_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        status_layout.addWidget(self.progress_bar)

        # 状态表格
        self.status_table = QTableWidget(0, 4)
        self.status_table.setHorizontalHeaderLabels(
            ["接收人", "状态", "发送时间", "错误信息"]
        )
        self.status_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        status_layout.addWidget(self.status_table)

        layout.addWidget(status_group)

        # 底部按钮
        button_layout = QHBoxLayout()
        self.preview_btn = QPushButton("预览")
        self.send_btn = QPushButton("开始发送")
        # 修复：设置为成功按钮，表示积极操作
        self.send_btn.setProperty("class", "success")

        self.pause_btn = QPushButton("暂停发送")
        # 修复：设置为次要按钮
        self.pause_btn.setProperty("class", "secondary")
        self.pause_btn.setEnabled(False)

        self.stop_btn = QPushButton("停止")
        # 修复：设置为危险按钮，表示停止操作
        self.stop_btn.setProperty("class", "danger")
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.preview_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.send_btn)
        button_layout.addWidget(self.pause_btn)
        button_layout.addWidget(self.stop_btn)
        layout.addLayout(button_layout)

    def create_timing_send_tab(self, tabs):
        """创建定时发送选项卡"""
        from ui.timing_send_page import TimingSendPage

        timing_send_page = TimingSendPage()
        tabs.addTab(timing_send_page, "定时发送")

        # 保存引用
        self.timing_send_page = timing_send_page

        # 设置连接器
        if hasattr(timing_send_page, "set_connector"):
            timing_send_page.set_connector(self.connector)

        # 重新注册复选框到主题
        self.register_checkboxes_to_theme()

    def create_loop_send_tab(self, tabs):
        """创建循环发送选项卡"""
        from ui.loop_send_page import LoopSendPage

        loop_send_page = LoopSendPage()
        tabs.addTab(loop_send_page, "循环发送")

        # 保存引用
        self.loop_send_page = loop_send_page

        # 设置连接器
        if hasattr(loop_send_page, "set_connector"):
            loop_send_page.set_connector(self.connector)

        # 重新注册复选框到主题
        self.register_checkboxes_to_theme()

    def create_task_status_tab(self, tabs):
        """创建任务状态管理选项卡"""
        from ui.task_status_page import TaskStatusPage

        task_status_page = TaskStatusPage(self)
        tabs.addTab(task_status_page, "📊 任务状态")

        # 保存引用
        self.task_status_page = task_status_page

        # 连接信号，实现状态同步
        task_status_page.group_status_changed.connect(self.on_group_status_changed)
        task_status_page.task_deleted.connect(self.on_task_deleted)

    def create_contacts_tab(self, tabs):
        """创建联系人选项卡"""
        contacts_tab = QWidget()
        tabs.addTab(contacts_tab, "联系人管理")

        layout = QVBoxLayout(contacts_tab)

        # 搜索功能区域
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索联系人:")
        self.contacts_search_input = QLineEdit()
        self.contacts_search_input.setPlaceholderText(
            "输入姓名、微信ID或备注进行模糊搜索..."
        )
        self.contacts_search_input.textChanged.connect(self.on_contacts_search_changed)

        self.clear_search_btn = QPushButton("清空搜索")
        self.clear_search_btn.clicked.connect(self.clear_contacts_search)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.contacts_search_input)
        search_layout.addWidget(self.clear_search_btn)
        layout.addLayout(search_layout)

        # 顶部按钮
        button_layout = QHBoxLayout()
        self.refresh_contacts_btn = QPushButton("获取联系人")
        self.export_contacts_btn = QPushButton("导出联系人")
        self.import_contacts_btn = QPushButton("导入联系人")
        self.delete_contacts_btn = QPushButton("删除选中")
        self.clear_all_contacts_btn = QPushButton("清空全部")

        button_layout.addWidget(self.refresh_contacts_btn)
        button_layout.addWidget(self.export_contacts_btn)
        button_layout.addWidget(self.import_contacts_btn)
        button_layout.addWidget(self.delete_contacts_btn)
        button_layout.addWidget(self.clear_all_contacts_btn)
        layout.addLayout(button_layout)

        # 分类显示联系人 - 使用水平分割器
        contacts_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：好友列表
        friends_group = QGroupBox("好友列表")
        friends_layout = QVBoxLayout(friends_group)

        self.friends_table = QTableWidget(0, 3)
        self.friends_table.setHorizontalHeaderLabels(["名称", "微信ID", "备注"])
        self.friends_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        self.friends_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.friends_table.setSelectionMode(
            QTableWidget.SelectionMode.ExtendedSelection
        )
        self.friends_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.friends_table.customContextMenuRequested.connect(
            self.show_friends_context_menu
        )
        friends_layout.addWidget(self.friends_table)

        # 好友操作按钮
        friends_btn_layout = QHBoxLayout()
        self.select_all_friends_btn = QPushButton("全选")
        self.deselect_all_friends_btn = QPushButton("取消全选")
        self.invert_friends_btn = QPushButton("反选")
        self.add_friends_to_send_btn = QPushButton("添加到待发")

        self.select_all_friends_btn.clicked.connect(self.select_all_friends)
        self.deselect_all_friends_btn.clicked.connect(self.deselect_all_friends)
        self.invert_friends_btn.clicked.connect(self.invert_friends_selection)
        self.add_friends_to_send_btn.clicked.connect(self.add_friends_to_send_list)

        friends_btn_layout.addWidget(self.select_all_friends_btn)
        friends_btn_layout.addWidget(self.deselect_all_friends_btn)
        friends_btn_layout.addWidget(self.invert_friends_btn)
        friends_btn_layout.addWidget(self.add_friends_to_send_btn)
        friends_layout.addLayout(friends_btn_layout)

        self.friends_count_label = QLabel("好友: 0 个")
        friends_layout.addWidget(self.friends_count_label)

        contacts_splitter.addWidget(friends_group)

        # 右侧：群聊列表
        groups_group = QGroupBox("群聊列表")
        groups_layout = QVBoxLayout(groups_group)

        self.groups_table = QTableWidget(0, 3)
        self.groups_table.setHorizontalHeaderLabels(["群名称", "群ID", "备注"])
        self.groups_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        self.groups_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.groups_table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)
        self.groups_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.groups_table.customContextMenuRequested.connect(
            self.show_groups_context_menu
        )
        groups_layout.addWidget(self.groups_table)

        # 群聊操作按钮
        groups_btn_layout = QHBoxLayout()
        self.select_all_groups_btn = QPushButton("全选")
        self.deselect_all_groups_btn = QPushButton("取消全选")
        self.invert_groups_btn = QPushButton("反选")
        self.add_groups_to_send_btn = QPushButton("添加到待发")

        self.select_all_groups_btn.clicked.connect(self.select_all_groups)
        self.deselect_all_groups_btn.clicked.connect(self.deselect_all_groups)
        self.invert_groups_btn.clicked.connect(self.invert_groups_selection)
        self.add_groups_to_send_btn.clicked.connect(self.add_groups_to_send_list)

        groups_btn_layout.addWidget(self.select_all_groups_btn)
        groups_btn_layout.addWidget(self.deselect_all_groups_btn)
        groups_btn_layout.addWidget(self.invert_groups_btn)
        groups_btn_layout.addWidget(self.add_groups_to_send_btn)
        groups_layout.addLayout(groups_btn_layout)

        self.groups_count_label = QLabel("群聊: 0 个")
        groups_layout.addWidget(self.groups_count_label)

        contacts_splitter.addWidget(groups_group)

        # 设置分割器比例
        contacts_splitter.setSizes([400, 400])

        # 创建主分割器，将联系人列表和待发列表分开
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        main_splitter.addWidget(contacts_splitter)

        # 待发联系人区域
        send_list_group = QGroupBox("待发联系人列表")
        send_list_layout = QVBoxLayout(send_list_group)

        # 待发联系人表格
        self.send_list_table = QTableWidget(0, 4)
        self.send_list_table.setHorizontalHeaderLabels(
            ["名称", "微信ID", "类型", "备注"]
        )
        self.send_list_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        self.send_list_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.send_list_table.setSelectionMode(
            QTableWidget.SelectionMode.ExtendedSelection
        )
        # 移除固定高度限制，让表格能够随窗口大小自适应
        self.send_list_table.setMinimumHeight(100)  # 设置最小高度
        send_list_layout.addWidget(self.send_list_table)

        # 待发联系人操作按钮
        send_list_btn_layout = QHBoxLayout()
        self.clear_send_list_btn = QPushButton("清空列表")
        self.remove_selected_send_btn = QPushButton("移除选中")
        self.apply_to_send_tab_btn = QPushButton("应用到发送页面")

        self.clear_send_list_btn.clicked.connect(self.clear_send_list)
        self.remove_selected_send_btn.clicked.connect(
            self.remove_selected_from_send_list
        )
        self.apply_to_send_tab_btn.clicked.connect(self.apply_to_send_tab)

        send_list_btn_layout.addWidget(self.clear_send_list_btn)
        send_list_btn_layout.addWidget(self.remove_selected_send_btn)
        send_list_btn_layout.addWidget(self.apply_to_send_tab_btn)
        send_list_btn_layout.addStretch()
        send_list_layout.addLayout(send_list_btn_layout)

        self.send_list_count_label = QLabel("待发: 0 个联系人")
        send_list_layout.addWidget(self.send_list_count_label)

        # 将待发联系人区域添加到主分割器
        main_splitter.addWidget(send_list_group)

        # 设置主分割器的比例 (联系人列表:待发列表 = 3:1)
        main_splitter.setSizes([600, 200])
        main_splitter.setStretchFactor(0, 3)  # 联系人列表区域拉伸因子
        main_splitter.setStretchFactor(1, 1)  # 待发列表区域拉伸因子

        layout.addWidget(main_splitter)

        # 底部总计状态
        self.contacts_count_label = QLabel("共 0 个联系人 (好友: 0, 群聊: 0)")
        layout.addWidget(self.contacts_count_label)

        # 初始化待发联系人列表
        self.send_contacts = []

    def create_templates_tab(self, tabs):
        """创建模板选项卡"""
        templates_tab = QWidget()
        tabs.addTab(templates_tab, "消息模板")

        layout = QVBoxLayout(templates_tab)

        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：模板列表
        templates_list_widget = QWidget()
        templates_list_layout = QVBoxLayout(templates_list_widget)

        templates_list_layout.addWidget(QLabel("模板列表"))

        self.templates_list = QListWidget()
        templates_list_layout.addWidget(self.templates_list)

        templates_btn_layout = QHBoxLayout()
        self.add_template_btn = QPushButton("新建")
        self.delete_template_btn = QPushButton("删除")
        templates_btn_layout.addWidget(self.add_template_btn)
        templates_btn_layout.addWidget(self.delete_template_btn)
        templates_list_layout.addLayout(templates_btn_layout)

        splitter.addWidget(templates_list_widget)

        # 右侧：模板编辑
        template_edit_widget = QWidget()
        template_edit_layout = QVBoxLayout(template_edit_widget)

        # 模板名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("模板名称:"))
        self.template_name_edit = QLineEdit()
        name_layout.addWidget(self.template_name_edit)
        template_edit_layout.addLayout(name_layout)

        # 模板类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("模板类型:"))
        self.template_type_group = QButtonGroup(self)
        self.template_type_rich_text = QRadioButton("富文本")
        self.template_type_text = QRadioButton("文本")
        self.template_type_image = QRadioButton("图片")
        self.template_type_file = QRadioButton("文件")
        self.template_type_group.addButton(self.template_type_rich_text, 0)
        self.template_type_group.addButton(self.template_type_text, 1)
        self.template_type_group.addButton(self.template_type_image, 2)
        self.template_type_group.addButton(self.template_type_file, 3)
        self.template_type_rich_text.setChecked(True)
        type_layout.addWidget(self.template_type_rich_text)
        type_layout.addWidget(self.template_type_text)
        type_layout.addWidget(self.template_type_image)
        type_layout.addWidget(self.template_type_file)
        template_edit_layout.addLayout(type_layout)

        # 模板内容编辑器堆栈
        self.template_editor_stack = QStackedWidget()

        # 富文本模板编辑器
        self.template_rich_text_editor = RichTextMessageEditor()
        self.template_editor_stack.addWidget(self.template_rich_text_editor)

        # 传统模板编辑器
        template_traditional_widget = QWidget()
        template_traditional_layout = QVBoxLayout(template_traditional_widget)
        template_traditional_layout.addWidget(QLabel("模板内容:"))
        self.template_content_edit = QTextEdit()
        template_traditional_layout.addWidget(self.template_content_edit)
        self.template_editor_stack.addWidget(template_traditional_widget)

        template_edit_layout.addWidget(self.template_editor_stack)

        # 文件路径
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("文件路径:"))
        self.template_file_path = QLineEdit()
        self.template_file_path.setReadOnly(True)
        self.template_select_file_btn = QPushButton("选择文件")
        file_layout.addWidget(self.template_file_path)
        file_layout.addWidget(self.template_select_file_btn)
        template_edit_layout.addLayout(file_layout)

        # 变量说明
        template_edit_layout.addWidget(QLabel("支持的变量:"))
        variables_text = """
        {{name}} - 联系人名称
        {{wxid}} - 联系人微信ID
        {{current_time}} - 当前时间
        {{current_date}} - 当前日期
        {{random_emoji}} - 随机表情
        """
        variables_label = QLabel(variables_text)
        template_edit_layout.addWidget(variables_label)

        # 保存按钮
        self.save_template_btn = QPushButton("保存模板")
        template_edit_layout.addWidget(self.save_template_btn)

        splitter.addWidget(template_edit_widget)

        layout.addWidget(splitter)

    def create_logs_tab(self, tabs):
        """创建日志选项卡"""
        logs_tab = QWidget()
        tabs.addTab(logs_tab, "运行日志")

        layout = QVBoxLayout(logs_tab)

        # 顶部工具栏
        toolbar_layout = QHBoxLayout()

        # 左侧过滤器
        filter_group = QGroupBox("日志过滤")
        filter_layout = QHBoxLayout(filter_group)

        filter_layout.addWidget(QLabel("级别:"))
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["全部", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        self.log_level_combo.currentTextChanged.connect(self.filter_logs)
        filter_layout.addWidget(self.log_level_combo)

        filter_layout.addWidget(QLabel("模块:"))
        self.log_module_combo = QComboBox()
        self.log_module_combo.addItems(
            [
                "全部",
                # 主程序和启动
                "main",
                "startup",
                # 连接器（唯一支持）
                "http_api_connector",
                # 任务管理
                "timing_sender",
                "loop_sender",
                "timed_sender",
                "send_monitor",
                "optimized_sender",
                # 注入器（唯一支持）
                "auto_injector",
                "injector_tool",
                # UI相关
                "ui",
                "timing_send_page",
                "loop_send_page",
                "task_status_page",
                "rich_text_editor",
                "custom_main_window",
                "custom_title_bar",
                "theme_manager",
                "ui_optimizer",
                # 管理器
                "group_manager",
                "message_template",
                "config_manager",
                "group_config_manager",
                "version_manager",
                # 安全和权限
                "risk_control",
                "security",
                "admin_privileges",
                "simple_admin",
                # 性能和优化
                "performance",
                "performance_optimizer",
                # 对话框和组件
                "group_card",
                "group_detail_dialog",
                "group_details_dialog",
                "contact_selector_dialog",
                "member_selection_dialog",
                "loop_cycle_widget",
                "timing_cycle_widget",
                "performance_panel",
                # 工具和验证
                "validators",
                "file_handler",
                "dll_downloader",
                "wxhelper_downloader",
                # 系统和错误
                "error",
    
                "console_error",
                "console_output",
                # 测试
    
    
    
    
            ]
        )
        self.log_module_combo.currentTextChanged.connect(self.filter_logs)
        filter_layout.addWidget(self.log_module_combo)

        filter_layout.addWidget(QLabel("搜索:"))
        self.log_search_input = QLineEdit()
        self.log_search_input.setPlaceholderText("输入关键词搜索日志内容...")
        self.log_search_input.textChanged.connect(self.filter_logs)
        filter_layout.addWidget(self.log_search_input)

        toolbar_layout.addWidget(filter_group)

        # 右侧控制选项
        control_group = QGroupBox("显示控制")
        control_layout = QHBoxLayout(control_group)

        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        control_layout.addWidget(self.auto_scroll_check)

        self.show_timestamp_check = QCheckBox("显示时间戳")
        self.show_timestamp_check.setChecked(True)
        control_layout.addWidget(self.show_timestamp_check)

        self.show_module_check = QCheckBox("显示模块")
        self.show_module_check.setChecked(True)
        control_layout.addWidget(self.show_module_check)

        self.highlight_errors_check = QCheckBox("高亮错误")
        self.highlight_errors_check.setChecked(True)
        control_layout.addWidget(self.highlight_errors_check)

        toolbar_layout.addWidget(control_group)
        layout.addLayout(toolbar_layout)

        # 日志显示区域
        log_display_layout = QHBoxLayout()

        # 左侧日志文本
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)
        self.log_text.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.log_text.customContextMenuRequested.connect(self.show_log_context_menu)
        log_display_layout.addWidget(self.log_text, 2)

        # 右侧统计面板
        stats_panel = QWidget()
        stats_layout = QVBoxLayout(stats_panel)

        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_group_layout = QVBoxLayout(stats_group)

        self.log_stats_label = QLabel("总记录: 0 条")
        stats_group_layout.addWidget(self.log_stats_label)

        self.log_error_count_label = QLabel("错误: 0 条")
        stats_group_layout.addWidget(self.log_error_count_label)

        self.log_warning_count_label = QLabel("警告: 0 条")
        stats_group_layout.addWidget(self.log_warning_count_label)

        self.log_info_count_label = QLabel("信息: 0 条")
        stats_group_layout.addWidget(self.log_info_count_label)


        stats_layout.addWidget(stats_group)

        # 快速操作
        quick_actions_group = QGroupBox("快速操作")
        quick_actions_layout = QVBoxLayout(quick_actions_group)

        self.refresh_log_btn = QPushButton("🔄 刷新日志")
        self.refresh_log_btn.clicked.connect(self.refresh_log)
        quick_actions_layout.addWidget(self.refresh_log_btn)

        self.clear_log_btn = QPushButton("🗑️ 清空显示")
        self.clear_log_btn.clicked.connect(self.clear_log)
        quick_actions_layout.addWidget(self.clear_log_btn)

        self.clear_log_files_btn = QPushButton("🗂️ 清空日志文件")
        # 注意：事件连接在connect_signals方法中统一处理，避免重复连接
        # 修复：使用主题管理器的样式类而不是内联样式
        self.clear_log_files_btn.setProperty("class", "danger")
        quick_actions_layout.addWidget(self.clear_log_files_btn)

        self.export_log_btn = QPushButton("📤 导出日志")
        self.export_log_btn.clicked.connect(self.export_log)
        quick_actions_layout.addWidget(self.export_log_btn)

        self.copy_selected_btn = QPushButton("📋 复制选中")
        self.copy_selected_btn.clicked.connect(self.copy_selected_log)
        quick_actions_layout.addWidget(self.copy_selected_btn)

        self.find_next_btn = QPushButton("🔍 查找下一个")
        self.find_next_btn.clicked.connect(self.find_next_log)
        quick_actions_layout.addWidget(self.find_next_btn)

        self.goto_top_btn = QPushButton("⬆️ 回到顶部")
        self.goto_top_btn.clicked.connect(self.goto_log_top)
        quick_actions_layout.addWidget(self.goto_top_btn)

        self.goto_bottom_btn = QPushButton("⬇️ 跳到底部")
        self.goto_bottom_btn.clicked.connect(self.goto_log_bottom)
        quick_actions_layout.addWidget(self.goto_bottom_btn)

        stats_layout.addWidget(quick_actions_group)

        # 设置固定宽度
        stats_panel.setFixedWidth(200)
        log_display_layout.addWidget(stats_panel)

        layout.addLayout(log_display_layout)

        # 底部状态栏
        status_layout = QHBoxLayout()

        self.log_status_label = QLabel("就绪")
        status_layout.addWidget(self.log_status_label)

        status_layout.addStretch()

        self.log_file_label = QLabel("日志文件: logs/app.log")
        status_layout.addWidget(self.log_file_label)

        layout.addLayout(status_layout)

        # 存储所有日志记录
        self.all_log_records = []
        self.filtered_log_records = []
        self.current_search_index = -1

    def create_settings_tab(self, tabs):
        """创建设置选项卡"""
        settings_tab = QWidget()
        tabs.addTab(settings_tab, "系统设置")

        layout = QVBoxLayout(settings_tab)
        # 设置统一的布局边距和间距（符合技术规范）
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # 主题切换区域 - 放在最顶部
        theme_group = QGroupBox("界面主题")
        theme_layout = QHBoxLayout(theme_group)
        theme_layout.setSpacing(8)

        theme_layout.addWidget(QLabel("选择主题:"))
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(modern_theme_manager.get_available_themes())
        self.theme_combo.setCurrentText(getattr(self.config, "theme", "默认主题"))
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        theme_layout.addWidget(self.theme_combo)
        theme_layout.addStretch()

        layout.addWidget(theme_group)

        # 连接设置
        connection_group = QGroupBox("连接设置")
        connection_layout = QVBoxLayout(connection_group)
        connection_layout.setSpacing(8)  # 统一间距

        self.auto_start_check = QCheckBox("启动时自动连接微信")
        self.auto_start_check.setChecked(self.config.auto_start)
        connection_layout.addWidget(self.auto_start_check)

        startup_delay_layout = QHBoxLayout()
        startup_delay_layout.addWidget(QLabel("启动延迟(秒):"))
        self.startup_delay_spin = QSpinBox()
        self.startup_delay_spin.setRange(1, 30)
        self.startup_delay_spin.setValue(self.config.startup_delay)
        startup_delay_layout.addWidget(self.startup_delay_spin)
        startup_delay_layout.addStretch()
        connection_layout.addLayout(startup_delay_layout)

        login_timeout_layout = QHBoxLayout()
        login_timeout_layout.addWidget(QLabel("登录超时(秒):"))
        self.login_timeout_spin = QSpinBox()
        self.login_timeout_spin.setRange(10, 300)
        self.login_timeout_spin.setValue(self.config.login_timeout)
        login_timeout_layout.addWidget(self.login_timeout_spin)
        login_timeout_layout.addStretch()
        connection_layout.addLayout(login_timeout_layout)

        layout.addWidget(connection_group)

        # 发送设置
        send_group = QGroupBox("发送设置")
        send_layout = QVBoxLayout(send_group)
        send_layout.setSpacing(8)  # 统一间距

        daily_limit_layout = QHBoxLayout()
        daily_limit_layout.setSpacing(8)  # 统一间距
        daily_limit_layout.addWidget(QLabel("每日发送限制:"))
        self.daily_limit_spin = QSpinBox()
        self.daily_limit_spin.setRange(1, 1000)
        self.daily_limit_spin.setValue(self.config.daily_send_limit)
        daily_limit_layout.addWidget(self.daily_limit_spin)
        daily_limit_layout.addStretch()
        send_layout.addLayout(daily_limit_layout)

        hourly_limit_layout = QHBoxLayout()
        hourly_limit_layout.addWidget(QLabel("每小时发送限制:"))
        self.hourly_limit_spin = QSpinBox()
        self.hourly_limit_spin.setRange(1, 200)
        self.hourly_limit_spin.setValue(self.config.hourly_send_limit)
        hourly_limit_layout.addWidget(self.hourly_limit_spin)
        hourly_limit_layout.addStretch()
        send_layout.addLayout(hourly_limit_layout)

        self.enable_risk_control_check = QCheckBox("启用风控保护")
        self.enable_risk_control_check.setChecked(self.config.enable_risk_control)
        send_layout.addWidget(self.enable_risk_control_check)

        layout.addWidget(send_group)

        # 重试设置
        retry_group = QGroupBox("重试设置")
        retry_layout = QVBoxLayout(retry_group)

        max_retries_layout = QHBoxLayout()
        max_retries_layout.addWidget(QLabel("最大重试次数:"))
        self.max_retries_spin = QSpinBox()
        self.max_retries_spin.setRange(0, 10)
        self.max_retries_spin.setValue(self.config.max_retries)
        max_retries_layout.addWidget(self.max_retries_spin)
        max_retries_layout.addStretch()
        retry_layout.addLayout(max_retries_layout)

        retry_delay_layout = QHBoxLayout()
        retry_delay_layout.addWidget(QLabel("重试延迟(秒):"))
        self.retry_delay_spin = QSpinBox()
        self.retry_delay_spin.setRange(1, 60)
        self.retry_delay_spin.setValue(self.config.retry_delay)
        retry_delay_layout.addWidget(self.retry_delay_spin)
        retry_delay_layout.addStretch()
        retry_layout.addLayout(retry_delay_layout)

        layout.addWidget(retry_group)

        # 界面设置
        ui_group = QGroupBox("界面设置")
        ui_layout = QVBoxLayout(ui_group)

        # 主题设置已移除 - 使用PyQt6原生样式

        # 字体大小设置
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        self.font_size_combo = QComboBox()
        self.font_size_combo.addItems(["小", "中", "大", "特大"])
        self.font_size_combo.setCurrentText(getattr(self.config, "font_size", "中"))
        self.font_size_combo.currentTextChanged.connect(self.on_font_size_changed)
        font_size_layout.addWidget(self.font_size_combo)
        font_size_layout.addStretch()
        ui_layout.addLayout(font_size_layout)

        # 启动设置
        self.minimize_to_tray_check = QCheckBox("最小化到系统托盘")
        self.minimize_to_tray_check.setChecked(
            getattr(self.config, "minimize_to_tray", False)
        )
        ui_layout.addWidget(self.minimize_to_tray_check)

        self.remember_window_state_check = QCheckBox("记住窗口状态")
        self.remember_window_state_check.setChecked(
            getattr(self.config, "remember_window_state", True)
        )
        ui_layout.addWidget(self.remember_window_state_check)

        layout.addWidget(ui_group)

        # 高级设置
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QVBoxLayout(advanced_group)

        # 移除调试模式功能

        # 自动备份
        self.auto_backup_check = QCheckBox("自动备份配置")
        self.auto_backup_check.setChecked(getattr(self.config, "auto_backup", True))
        advanced_layout.addWidget(self.auto_backup_check)

        # 备份管理按钮
        backup_layout = QHBoxLayout()
        self.backup_manager_btn = QPushButton("📁 备份管理")
        self.backup_manager_btn.clicked.connect(self.open_backup_manager)
        # 修复：使用主题管理器的样式类而不是内联样式
        self.backup_manager_btn.setProperty("class", "secondary")
        backup_layout.addWidget(self.backup_manager_btn)
        backup_layout.addStretch()
        advanced_layout.addLayout(backup_layout)

        # 检查更新
        self.check_updates_check = QCheckBox("启动时检查更新")
        self.check_updates_check.setChecked(getattr(self.config, "check_updates", True))
        advanced_layout.addWidget(self.check_updates_check)

        layout.addWidget(advanced_group)

        layout.addStretch()

        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)  # 统一间距

        self.reset_settings_btn = QPushButton("重置设置")
        self.reset_settings_btn.setProperty("class", "secondary")  # 次要按钮样式

        self.save_settings_btn = QPushButton("保存设置")
        self.save_settings_btn.setProperty("class", "success")  # 成功按钮样式

        button_layout.addWidget(self.reset_settings_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.save_settings_btn)
        layout.addLayout(button_layout)

        layout.addStretch()

    def open_backup_manager(self):
        """打开备份管理对话框"""
        try:
            dialog = BackupManagerDialog(self)
            dialog.backup_restored.connect(self.on_backup_restored)
            dialog.exec()
        except Exception as e:
            logger.error(f"打开备份管理对话框失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"打开备份管理失败:\n{e}")

    def on_backup_restored(self):
        """备份恢复完成处理"""
        ThemedMessageBoxHelper.show_information(
            self, "备份恢复", "配置已恢复完成！\n\n建议重启程序以确保所有配置生效。"
        )

    def register_checkboxes_to_theme(self):
        """为所有复选框注册主题"""
        try:
            # 收集所有复选框，检查是否存在
            checkboxes = []

            # 基础复选框
            checkbox_attrs = [
                "simulate_human_check",
                "random_delay_check",
                "enable_timing_check",
                "auto_scroll_check",
                "auto_start_check",
                "enable_risk_control_check",
                "minimize_to_tray_check",
                "remember_window_state_check",
                "auto_backup_check",
                "check_updates_check",
            ]

            for attr_name in checkbox_attrs:
                if hasattr(self, attr_name):
                    checkbox = getattr(self, attr_name)
                    if checkbox:
                        checkboxes.append(checkbox)

            # 添加星期复选框
            if hasattr(self, "weekday_checks"):
                checkboxes.extend(self.weekday_checks)

            # 添加定时发送页面的复选框
            if hasattr(self, "timing_send_page") and self.timing_send_page:
                if (
                    hasattr(self.timing_send_page, "use_system_risk_cb")
                    and self.timing_send_page.use_system_risk_cb
                ):
                    checkboxes.append(self.timing_send_page.use_system_risk_cb)

            # 添加循环发送页面的复选框
            if hasattr(self, "loop_send_page") and self.loop_send_page:
                if (
                    hasattr(self.loop_send_page, "use_system_risk_cb")
                    and self.loop_send_page.use_system_risk_cb
                ):
                    checkboxes.append(self.loop_send_page.use_system_risk_cb)

            # 为每个复选框注册主题
            for checkbox in checkboxes:
                if checkbox:
                    modern_theme_manager.register_widget(checkbox, "checkbox")

            logger.debug(f"已为 {len(checkboxes)} 个复选框注册主题")

        except Exception as e:
            logger.error(f"注册复选框主题失败: {e}")

    def connect_signals(self):
        """连接信号"""
        # 连接按钮
        self.connect_btn.clicked.connect(self.toggle_connection)

        # 发送选项卡
        self.contact_search.textChanged.connect(self.filter_contacts)
        self.friends_list.itemSelectionChanged.connect(self.update_selected_count)
        self.groups_list.itemSelectionChanged.connect(self.update_selected_count)
        self.select_all_btn.clicked.connect(self.select_all_contacts)
        self.deselect_all_btn.clicked.connect(self.deselect_all_contacts)
        self.invert_select_btn.clicked.connect(self.invert_selection)
        self.sync_from_send_list_btn.clicked.connect(self.sync_from_send_list)
        self.clear_recipients_btn.clicked.connect(self.clear_recipients)
        self.msg_type_combo.currentTextChanged.connect(self.on_message_type_changed)
        self.select_file_btn.clicked.connect(self.select_file)
        self.template_combo.currentIndexChanged.connect(self.load_template)
        self.preview_btn.clicked.connect(self.preview_message)
        self.send_btn.clicked.connect(self.start_sending)
        self.pause_btn.clicked.connect(self.pause_sending)
        self.stop_btn.clicked.connect(self.stop_sending)

        # 联系人选项卡
        self.refresh_contacts_btn.clicked.connect(self.refresh_contacts)
        self.export_contacts_btn.clicked.connect(self.export_contacts_from_page)
        self.import_contacts_btn.clicked.connect(self.import_contacts_to_page)
        self.delete_contacts_btn.clicked.connect(
            self.delete_selected_contacts_from_page
        )
        self.clear_all_contacts_btn.clicked.connect(self.clear_all_contacts_from_page)

        # 模板选项卡
        self.templates_list.itemClicked.connect(self.load_template_from_list)
        self.add_template_btn.clicked.connect(self.add_new_template)
        self.delete_template_btn.clicked.connect(self.delete_template)
        self.template_type_group.buttonClicked.connect(self.on_template_type_changed)
        self.template_select_file_btn.clicked.connect(self.select_template_file)
        self.save_template_btn.clicked.connect(self.save_template)

        # 日志选项卡
        self.refresh_log_btn.clicked.connect(self.refresh_log)
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.clear_log_files_btn.clicked.connect(self.clear_log_files)
        self.export_log_btn.clicked.connect(self.export_log)

        # 设置选项卡
        self.reset_settings_btn.clicked.connect(self.reset_settings)
        self.save_settings_btn.clicked.connect(self.save_settings)

        # 连接器信号
        self.connector.login_success.connect(self.on_login_success)
        self.connector.login_failed.connect(self.on_login_failed)
        self.connector.logout.connect(self.on_logout)
        self.connector.contact_updated.connect(self.on_contacts_updated)
        self.connector.connection_lost.connect(self.on_connection_lost)

        # 监控器信号
        self.monitor.task_status_changed.connect(self.on_task_status_changed)
        self.monitor.progress_updated.connect(self.on_progress_updated)
        self.monitor.send_completed.connect(self.on_send_completed)

        # 初始化模板列表
        self.refresh_template_list()
        self.refresh_send_page_template_list()

        # 初始化日志显示
        self.refresh_log()

    @pyqtSlot()
    def toggle_connection(self):
        """切换连接状态"""
        if not self.connector.is_connected:
            self.connect_btn.setEnabled(False)
            self.statusBar().showMessage("正在连接微信...")
            self.run_async_task(
                self.connect_wechat(),
                on_finished=self._on_connect_finished,
                on_error=self._on_connect_error,
            )
        else:
            self.connect_btn.setEnabled(False)
            self.statusBar().showMessage("正在断开连接...")
            self.run_async_task(
                self.disconnect_wechat(),
                on_finished=self._on_disconnect_finished,
                on_error=self._on_disconnect_error,
            )

    async def connect_wechat(self):
        """连接微信 - 不包含GUI操作的纯异步版本"""
        try:
            # 新的注入器适配器返回3个值，旧的连接器返回2个值
            result = await self.connector.connect()

            if len(result) == 3:
                # 新的注入器适配器
                success, message, user_info = result
                return success, message, user_info
            elif len(result) == 2:
                # 旧的连接器
                success, message = result
                if success:
                    # 连接成功后自动获取用户信息（模拟登录）
                    user_info = await self.connector.get_self_info()
                    if user_info:
                        logger.info("自动登录成功")
                        return success, message, user_info
                    else:
                        logger.warning("自动登录失败")
                        return success, message, None
                else:
                    return success, message, None
            else:
                logger.error(f"连接方法返回了意外的结果格式: {result}")
                return False, "连接方法返回格式错误", None

        except Exception as e:
            logger.error(f"连接微信时出错: {e}")
            return False, str(e), None

    async def disconnect_wechat(self):
        """断开微信连接 - 不包含GUI操作的纯异步版本"""
        success, message = await self.connector.disconnect()
        return success, message

    def _on_connect_finished(self, result):
        """连接完成回调"""
        self.connect_btn.setEnabled(True)

        if len(result) == 3:  # 来自connect_wechat的结果
            success, message, user_info = result
            if success:
                self.status_label.setText("已连接，等待登录")
                self.connect_btn.setText("断开连接")
                self.statusBar().showMessage(message)

                # 连接成功后设置连接器到发送器
                self._setup_senders_connector()

                if user_info:
                    logger.info("自动登录成功")
                else:
                    logger.warning("自动登录失败")
            else:
                self.status_label.setText("连接失败")
                self.statusBar().showMessage(f"连接失败: {message}")
                ThemedMessageBoxHelper.show_warning(
                    self, "连接失败", f"无法连接到微信: {message}"
                )
        else:
            logger.warning(f"未知的连接结果格式: {result}")

    def _on_connect_error(self, error):
        """连接错误回调"""
        self.connect_btn.setEnabled(True)
        logger.error(f"连接异步任务错误: {error}")

    def _on_disconnect_finished(self, result):
        """断开连接完成回调"""
        self.connect_btn.setEnabled(True)

        if len(result) == 2:  # 来自disconnect_wechat的结果
            success, message = result
            if success:
                self.status_label.setText("未连接")
                self.connect_btn.setText("连接微信")
                self.statusBar().showMessage("已断开连接")
                self.clear_contacts()
            else:
                self.statusBar().showMessage(f"断开连接失败: {message}")
                ThemedMessageBoxHelper.show_warning(
                    self, "断开失败", f"无法断开连接: {message}"
                )
        else:
            logger.warning(f"未知的断开连接结果格式: {result}")

        logger.info(f"断开连接异步任务完成: {result}")

    def _on_disconnect_error(self, error):
        """断开连接错误回调"""
        self.connect_btn.setEnabled(True)
        logger.error(f"断开连接异步任务错误: {error}")

    async def auto_connect(self):
        """自动连接微信 - 不包含GUI操作的纯异步版本"""
        await asyncio.sleep(self.config.startup_delay)
        if not self.connector.is_connected:
            return await self.connect_wechat()
        return True, "已连接", None

    @pyqtSlot(dict)
    def on_login_success(self, user_info):
        """登录成功回调"""
        self.status_label.setText(f"已登录: {user_info.get('name', '未知用户')}")
        self.statusBar().showMessage("微信登录成功")
        logger.info(f"微信登录成功: {user_info}")

        # 移除自动获取联系人，改为按需获取
        # 用户需要点击"获取联系人"按钮来获取联系人列表

    @pyqtSlot(str)
    def on_login_failed(self, error_message):
        """登录失败回调"""
        self.status_label.setText("登录失败")
        self.statusBar().showMessage(f"登录失败: {error_message}")
        logger.error(f"微信登录失败: {error_message}")
        ThemedMessageBoxHelper.show_warning(
            self, "登录失败", f"微信登录失败:\n{error_message}"
        )

    @pyqtSlot()
    def on_logout(self):
        """登出回调"""
        self.status_label.setText("未连接")
        self.statusBar().showMessage("已登出")
        self.clear_contacts()
        logger.info("微信已登出")

    @pyqtSlot(list)
    def on_contacts_updated(self, contacts):
        """联系人更新回调"""
        # 联系人更新
        self.contacts = contacts

        # 调试信息：显示前几个联系人
        for i, contact in enumerate(contacts[:3]):
            print(f"  联系人 {i+1}: {contact.name} ({contact.wxid}) [{contact.type}]")

        # 更新联系人列表
        self.update_contact_list()

        # 更新联系人表格
        self.update_contacts_table()

        # 联系人更新完成
        logger.info(f"联系人列表已更新，共 {len(contacts)} 个联系人")

    @pyqtSlot()
    def on_connection_lost(self):
        """连接丢失回调"""
        self.status_label.setText("连接丢失")
        self.statusBar().showMessage("与微信的连接已丢失")
        logger.warning("与微信的连接已丢失")
        ThemedMessageBoxHelper.show_warning(
            self, "连接丢失", "与微信的连接已丢失，请重新连接"
        )

    @pyqtSlot(str, str)
    def on_task_status_changed(self, task_id, status):
        """任务状态变更回调"""
        # 更新状态表格
        self.update_status_table()

    @pyqtSlot(int, int)
    def on_progress_updated(self, completed, total):
        """进度更新回调"""
        if total > 0:
            progress = int(completed / total * 100)
            self.progress_bar.setValue(progress)
            self.statusBar().showMessage(f"发送进度: {completed}/{total} ({progress}%)")

    @pyqtSlot(dict)
    def on_send_completed(self, statistics):
        """发送完成回调"""
        # 发送完成统计

        self.progress_bar.setValue(100)
        self.send_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        # 最后更新一次状态表格
        self.update_status_table()

        success_rate = statistics.get("success_rate", 0)
        message = f"发送完成！成功率: {success_rate:.1f}%"
        self.statusBar().showMessage(message)

        ThemedMessageBoxHelper.show_information(
            self,
            "发送完成",
            f"批量发送已完成！\n\n"
            f"总数: {statistics.get('total', 0)}\n"
            f"成功: {statistics.get('success', 0)}\n"
            f"失败: {statistics.get('failed', 0)}\n"
            f"取消: {statistics.get('cancelled', 0)}\n"
            f"成功率: {success_rate:.1f}%",
        )

    @performance_monitor
    def update_status(self):
        """更新状态显示"""
        try:
            # 检查连接状态
            if hasattr(self, "connector") and self.connector:
                if self.connector.is_connected:
                    if self.connector.is_logged_in:
                        self.status_label.setText("已连接并登录")
                    else:
                        self.status_label.setText("已连接，等待登录")
                else:
                    self.status_label.setText("未连接")
            else:
                self.status_label.setText("初始化中...")

        except Exception as e:
            logger.error(f"更新状态失败: {e}")
            self.status_label.setText("状态更新失败")

    def clear_contacts(self):
        """清空联系人列表"""
        self.contacts = []
        self.friends_list.clear()
        self.groups_list.clear()
        self.friends_table.setRowCount(0)
        self.groups_table.setRowCount(0)
        self.friends_count_label.setText("好友: 0 个")
        self.groups_count_label.setText("群聊: 0 个")
        self.contacts_count_label.setText("共 0 个联系人 (好友: 0, 群聊: 0)")

    def update_contact_list(self):
        """更新联系人列表"""
        # 更新联系人列表开始

        # 检查UI组件是否存在
        if not hasattr(self, "friends_list") or self.friends_list is None:
            # friends_list 不存在
            return
        if not hasattr(self, "groups_list") or self.groups_list is None:
            # groups_list 不存在
            return

        self.friends_list.clear()
        self.groups_list.clear()

        friends_count = 0
        groups_count = 0

        for contact in self.contacts:
            print(f"  处理联系人: {contact.name} ({contact.wxid}) [{contact.type}]")
            item = QListWidgetItem(f"{contact.name}")
            item.setData(Qt.ItemDataRole.UserRole, contact)

            # 根据类型分类显示
            if contact.type == "group" or contact.wxid.endswith("@chatroom"):
                self.groups_list.addItem(item)
                groups_count += 1
                print(f"    添加到群聊列表: {contact.name}")
            else:
                self.friends_list.addItem(item)
                friends_count += 1
                print(f"    添加到好友列表: {contact.name}")

        # 更新标签页标题显示数量
        if hasattr(self, "contact_tabs") and self.contact_tabs is not None:
            self.contact_tabs.setTabText(0, f"好友 ({friends_count})")
            self.contact_tabs.setTabText(1, f"群聊 ({groups_count})")

        print(
            f"联系人列表更新完成，好友: {friends_count}, 群聊: {groups_count}"
        )
        # 好友列表数量
        # 群聊列表数量

    @performance_monitor
    def update_contacts_table(self):
        """更新联系人表格（性能优化版）"""
        try:
            if not self.contacts:
                # 没有联系人数据
                return

            logger.debug(f"开始更新联系人表格，总数: {len(self.contacts)}")

            # 使用异步任务处理大量联系人
            if len(self.contacts) > 100:
                self._update_contacts_table_async()
            else:
                self._update_contacts_table_sync()

        except Exception as e:
            logger.error(f"更新联系人表格失败: {e}")

    def _update_contacts_table_async(self):
        """异步更新联系人表格"""
        self.async_task_manager.submit_task(
            "update_contacts_table", self._process_contacts_data
        )
        self.async_task_manager.task_completed.connect(self._on_contacts_data_ready)

    def _process_contacts_data(self):
        """处理联系人数据"""
        friends = []
        groups = []

        for contact in self.contacts:
            if contact.type == "group" or contact.wxid.endswith("@chatroom"):
                groups.append(contact)
            else:
                friends.append(contact)

        return {"friends": friends, "groups": groups}

    def _on_contacts_data_ready(self, task_id: str, data):
        """联系人数据准备完成"""
        if task_id == "update_contacts_table" and data:
            self._update_tables_with_data(data["friends"], data["groups"])

    def _update_contacts_table_sync(self):
        """同步更新联系人表格"""
        # 分类联系人
        friends = []
        groups = []

        for contact in self.contacts:
            if contact.type == "group" or contact.wxid.endswith("@chatroom"):
                groups.append(contact)
            else:
                friends.append(contact)

        self._update_tables_with_data(friends, groups)

    def _update_tables_with_data(self, friends, groups):
        """使用数据更新表格"""
        try:
            # 检查表格组件是否存在
            if not hasattr(self, "friends_table") or self.friends_table is None:
                logger.error("friends_table 不存在!")
                return
            if not hasattr(self, "groups_table") or self.groups_table is None:
                logger.error("groups_table 不存在!")
                return

            # 更新表格开始

            # 批量更新好友表格
            self._update_friends_table_batch(friends)

            # 批量更新群聊表格
            self._update_groups_table_batch(groups)

            # 更新统计标签
            self._update_contact_statistics(friends, groups)

            logger.debug(
                f"联系人表格更新完成 - 好友: {len(friends)}, 群聊: {len(groups)}"
            )

        except Exception as e:
            logger.error(f"更新表格数据失败: {e}")

    @ui_performance_monitor(200.0)  # 200ms阈值
    def _update_friends_table_batch(self, friends):
        """批量更新好友表格（UI优化版）"""
        try:
            # 更新好友表格

            # 设置表格行数
            self.friends_table.setRowCount(len(friends))

            def update_friend_row(table, row, contact):
                table.setItem(row, 0, QTableWidgetItem(contact.name))
                table.setItem(row, 1, QTableWidgetItem(contact.wxid))
                table.setItem(row, 2, QTableWidgetItem(contact.remark))

            # 使用UI优化器的批量更新
            ui_optimizer.batch_update_table(
                self.friends_table, friends, update_friend_row
            )

            print(
                f"好友表格更新完成，行数: {self.friends_table.rowCount()}"
            )

        except Exception as e:
            logger.error(f"批量更新好友表格失败: {e}")

    @ui_performance_monitor(200.0)  # 200ms阈值
    def _update_groups_table_batch(self, groups):
        """批量更新群聊表格（UI优化版）"""
        try:
            # 更新群聊表格

            # 设置表格行数
            self.groups_table.setRowCount(len(groups))

            def update_group_row(table, row, contact):
                table.setItem(row, 0, QTableWidgetItem(contact.name))
                table.setItem(row, 1, QTableWidgetItem(contact.wxid))
                table.setItem(row, 2, QTableWidgetItem(contact.remark))

            # 使用UI优化器的批量更新
            ui_optimizer.batch_update_table(self.groups_table, groups, update_group_row)

            print(
                f"群聊表格更新完成，行数: {self.groups_table.rowCount()}"
            )

        except Exception as e:
            logger.error(f"批量更新群聊表格失败: {e}")

    def _update_contact_statistics(self, friends, groups):
        """更新联系人统计信息"""
        try:
            # 更新计数标签
            if hasattr(self, "friends_count_label"):
                self.friends_count_label.setText(f"好友: {len(friends)} 个")

            if hasattr(self, "groups_count_label"):
                self.groups_count_label.setText(f"群聊: {len(groups)} 个")

            if hasattr(self, "contacts_count_label"):
                self.contacts_count_label.setText(
                    f"共 {len(self.contacts)} 个联系人 (好友: {len(friends)}, 群聊: {len(groups)})"
                )

            logger.debug(
                f"联系人统计更新完成 - 好友: {len(friends)}, 群聊: {len(groups)}"
            )

        except Exception as e:
            logger.error(f"更新联系人统计失败: {e}")

    def update_status_table(self):
        """更新发送状态表格"""
        tasks = self.monitor.get_all_tasks()
        self.status_table.setRowCount(len(tasks))

        # 更新状态表格

        for i, task in enumerate(tasks):
            print(
                f"  任务 {i+1}: {task.name} - 状态: {task.status.value} - 错误: {task.error_message}"
            )

            self.status_table.setItem(i, 0, QTableWidgetItem(task.name))

            # 状态显示优化
            status_text = task.status.value
            if task.status.value == "success":
                status_text = "成功"
            elif task.status.value == "failed":
                status_text = "失败"
            elif task.status.value == "sending":
                status_text = "发送中"
            elif task.status.value == "pending":
                status_text = "等待"
            elif task.status.value == "cancelled":
                status_text = "已取消"

            self.status_table.setItem(i, 1, QTableWidgetItem(status_text))
            sent_time = task.sent_at.strftime("%H:%M:%S") if task.sent_at else ""
            self.status_table.setItem(i, 2, QTableWidgetItem(sent_time))
            self.status_table.setItem(i, 3, QTableWidgetItem(task.error_message or ""))

    # 添加其他缺失的方法的基本实现
    def filter_contacts(self):
        """过滤联系人"""
        search_text = self.contact_search.text().strip().lower()

        # 过滤好友列表
        for i in range(self.friends_list.count()):
            item = self.friends_list.item(i)
            contact = item.data(Qt.ItemDataRole.UserRole)

            if not search_text:
                item.setHidden(False)
            else:
                # 检查名称、微信ID、备注是否包含搜索文本
                match = (
                    search_text in contact.name.lower()
                    or search_text in contact.wxid.lower()
                    or search_text in contact.remark.lower()
                )
                item.setHidden(not match)

        # 过滤群聊列表
        for i in range(self.groups_list.count()):
            item = self.groups_list.item(i)
            contact = item.data(Qt.ItemDataRole.UserRole)

            if not search_text:
                item.setHidden(False)
            else:
                # 检查名称、微信ID、备注是否包含搜索文本
                match = (
                    search_text in contact.name.lower()
                    or search_text in contact.wxid.lower()
                    or search_text in contact.remark.lower()
                )
                item.setHidden(not match)

    def update_selected_count(self):
        """更新选中计数"""
        friends_selected = len(self.friends_list.selectedItems())
        groups_selected = len(self.groups_list.selectedItems())
        total_selected = friends_selected + groups_selected
        self.selected_count_label.setText(
            f"已选择: {total_selected} (好友:{friends_selected}, 群聊:{groups_selected})"
        )

    def select_all_contacts(self):
        """全选联系人"""
        # 获取当前活动的标签页
        current_tab = self.contact_tabs.currentIndex()
        if current_tab == 0:  # 好友标签页
            self.friends_list.selectAll()
        elif current_tab == 1:  # 群聊标签页
            self.groups_list.selectAll()

    def deselect_all_contacts(self):
        """取消全选联系人"""
        self.friends_list.clearSelection()
        self.groups_list.clearSelection()

    def invert_selection(self):
        """反选联系人"""
        # 获取当前活动的标签页
        current_tab = self.contact_tabs.currentIndex()
        if current_tab == 0:  # 好友标签页
            for i in range(self.friends_list.count()):
                item = self.friends_list.item(i)
                if not item.isHidden():  # 只处理可见的联系人
                    item.setSelected(not item.isSelected())
        elif current_tab == 1:  # 群聊标签页
            for i in range(self.groups_list.count()):
                item = self.groups_list.item(i)
                if not item.isHidden():  # 只处理可见的联系人
                    item.setSelected(not item.isSelected())

    def update_message_type(self):
        """更新消息类型"""
        message_type_index = self.msg_type_combo.currentIndex()

        # 根据消息类型显示/隐藏文件选择区域
        if message_type_index == 0:  # 文本消息
            self.file_path_label.setVisible(False)
            self.select_file_btn.setVisible(False)
            self.message_edit.setPlaceholderText(
                "在此输入消息内容...\n支持变量: {{name}} - 联系人名称, {{current_time}} - 当前时间"
            )
        else:  # 图片或文件消息
            self.file_path_label.setVisible(True)
            self.select_file_btn.setVisible(True)
            if message_type_index == 1:  # 图片消息
                self.file_path_label.setText("未选择图片")
                self.message_edit.setPlaceholderText("可选：输入图片说明文字...")
            else:  # 文件消息
                self.file_path_label.setText("未选择文件")
                self.message_edit.setPlaceholderText("可选：输入文件说明文字...")

    def select_file(self):
        """选择文件"""
        # 根据消息类型选择文件过滤器
        message_type_index = self.msg_type_combo.currentIndex()

        if message_type_index == 1:  # 图片消息
            file_filter = "图片文件 (*.jpg *.jpeg *.png *.gif *.bmp);;所有文件 (*)"
            dialog_title = "选择图片文件"
        elif message_type_index == 2:  # 文件消息
            file_filter = "所有文件 (*);;文档文件 (*.txt *.doc *.docx *.pdf);;表格文件 (*.xls *.xlsx)"
            dialog_title = "选择文件"
        else:
            ThemedMessageBoxHelper.show_information(
                self, "提示", "文本消息不需要选择文件"
            )
            return

        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(self, dialog_title, "", file_filter)

        if file_path:
            # 验证文件
            from utils.validators import validate_file_path

            valid, error = validate_file_path(file_path)

            if valid:
                self.file_path_label.setText(file_path)
                self.statusBar().showMessage(f"已选择文件: {file_path}")
                logger.info(f"选择文件: {file_path}")
            else:
                ThemedMessageBoxHelper.show_warning(
                    self, "文件验证失败", f"选择的文件不符合要求：\n{error}"
                )
                self.file_path_label.setText("未选择文件")

    def on_message_type_changed(self, message_type):
        """消息类型改变时的处理"""
        if message_type == "富文本消息":
            self.editor_stack.setCurrentIndex(0)  # 显示富文本编辑器
            self.insert_image_btn.setVisible(True)
            self.paste_image_btn.setVisible(True)
            self.clear_content_btn.setVisible(True)
            self.content_summary_label.setVisible(True)
            # 更新富文本统计
            self.update_rich_text_stats()
        else:
            self.editor_stack.setCurrentIndex(1)  # 显示传统编辑器
            self.insert_image_btn.setVisible(False)
            self.paste_image_btn.setVisible(False)
            self.clear_content_btn.setVisible(False)
            self.content_summary_label.setVisible(False)
            # 更新传统编辑器统计
            self.update_traditional_text_count()

        # 更新传统消息类型
        if message_type != "富文本消息":
            self.update_message_type()

    def update_content_summary(self):
        """更新内容摘要和统计信息"""
        try:
            if (
                hasattr(self, "rich_text_editor")
                and hasattr(self, "content_summary_label")
                and hasattr(self.rich_text_editor, "get_content_summary")
            ):

                summary = self.rich_text_editor.get_content_summary()
                self.content_summary_label.setText(f"内容: {summary}")

                # 更新富文本编辑器的统计信息
                self.update_rich_text_stats()
            elif hasattr(self, "content_summary_label"):
                # 如果富文本编辑器未初始化，设置默认值
                self.content_summary_label.setText("内容: 空消息")

        except Exception as e:
            logger.error(f"更新内容摘要失败: {e}")
            if hasattr(self, "content_summary_label"):
                self.content_summary_label.setText("内容: 空消息")

    def update_rich_text_stats(self):
        """更新富文本编辑器的统计信息"""
        # 统计标签已移除，此方法保留用于兼容性
        pass

    def update_traditional_text_count(self):
        """更新传统编辑器的文字统计"""
        # 统计标签已移除，此方法保留用于兼容性
        pass

    def insert_image_file(self):
        """插入图片文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择图片文件",
            "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp *.webp *.tiff);;所有文件 (*)",
        )

        if file_path:
            self.rich_text_editor.insert_image_file(file_path)

    def paste_image_from_clipboard(self):
        """从剪贴板粘贴图片"""
        self.rich_text_editor.insert_image_from_clipboard()

    def clear_message_content(self):
        """清空消息内容"""
        self.rich_text_editor.clear_content()
        self.update_content_summary()

    def load_template(self):
        """加载模板"""
        try:
            current_index = self.template_combo.currentIndex()
            if current_index <= 0:  # "无" 选项
                return

            template_name = self.template_combo.currentText()
            if not template_name or template_name == "无":
                return

            # 从模板管理器加载模板
            template_data = self.template.load_template(template_name)

            if template_data:
                template_type = template_data.get("type", "text")
                content = template_data.get("content", "")

                if template_type == "rich_text":
                    # 加载到富文本编辑器
                    if hasattr(self, "rich_text_editor"):
                        # 解析富文本内容
                        if isinstance(content, dict):
                            self.rich_text_editor.load_template_content(content)
                        else:
                            # 如果是字符串，设置为HTML
                            self.rich_text_editor.setHtml(content)

                        # 切换到富文本模式
                        self.msg_type_combo.setCurrentText("富文本")
                        self.on_message_type_changed("富文本")

                elif template_type in ["text", "file", "image"]:
                    # 加载到传统编辑器
                    if hasattr(self, "message_edit"):
                        self.message_edit.setPlainText(content)

                        # 切换到对应的消息类型
                        if template_type == "text":
                            self.msg_type_combo.setCurrentText("文字")
                        elif template_type == "image":
                            self.msg_type_combo.setCurrentText("图片")
                        elif template_type == "file":
                            self.msg_type_combo.setCurrentText("文件")

                        self.on_message_type_changed(self.msg_type_combo.currentText())

                # 更新内容摘要
                self.update_content_summary()

                logger.info(f"已加载模板: {template_name} (类型: {template_type})")
                self.statusBar().showMessage(f"已加载模板: {template_name}")

            else:
                ThemedMessageBoxHelper.show_warning(
                    self, "错误", f"无法加载模板: {template_name}"
                )

        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"加载模板时出错:\n{e}")

    def preview_message(self):
        """预览消息"""
        # 获取选中的联系人
        selected_contacts = []
        # 从好友列表获取选中的联系人
        for item in self.friends_list.selectedItems():
            contact = item.data(Qt.ItemDataRole.UserRole)
            if contact:
                selected_contacts.append(contact)
        # 从群聊列表获取选中的联系人
        for item in self.groups_list.selectedItems():
            contact = item.data(Qt.ItemDataRole.UserRole)
            if contact:
                selected_contacts.append(contact)

        if not selected_contacts:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要预览的联系人")
            return

        # 获取消息内容
        message_type = self.msg_type_combo.currentText()

        if message_type == "富文本消息":
            if not self.rich_text_editor.has_content():
                ThemedMessageBoxHelper.show_warning(self, "提示", "请输入消息内容")
                return

            # 显示富文本预览
            content = self.rich_text_editor.get_message_content()
            preview_dialog = MessagePreviewDialog(content, self)
            preview_dialog.exec()
            return
        else:
            message_content = self.message_edit.toPlainText().strip()
            if not message_content:
                ThemedMessageBoxHelper.show_warning(self, "提示", "请输入消息内容")
                return

        # 预览前几个联系人的消息
        preview_count = min(3, len(selected_contacts))
        preview_text = f"消息预览（显示前{preview_count}个联系人）：\n\n"

        for i, contact in enumerate(selected_contacts[:preview_count]):
            # 渲染消息内容
            rendered_content = self.template.render_content(
                message_content, {"name": contact.name, "wxid": contact.wxid}
            )

            preview_text += f"发送给: {contact.name} ({contact.wxid})\n"
            preview_text += f"内容: {rendered_content}\n"
            preview_text += "-" * 50 + "\n"

        if len(selected_contacts) > preview_count:
            preview_text += (
                f"\n还有 {len(selected_contacts) - preview_count} 个联系人..."
            )

        # 显示预览对话框
        ThemedMessageBoxHelper.show_information(self, "消息预览", preview_text)

    def start_sending(self):
        """开始发送"""
        # 检查是否是继续发送（从暂停状态恢复）
        if self.send_btn.text() == "继续发送":
            self.monitor.resume()
            self.send_btn.setText("开始发送")
            self.send_btn.setEnabled(False)
            self.pause_btn.setEnabled(True)
            logger.info("继续普通发送任务")
            self.statusBar().showMessage("继续发送任务")
            return
        elif self.send_btn.text() == "继续定时发送":
            self.timed_sender.resume_timed_sending()
            self.send_btn.setText("开始定时发送")
            self.send_btn.setEnabled(False)
            self.pause_btn.setEnabled(True)
            logger.info("继续定时发送任务")
            self.statusBar().showMessage("继续定时发送任务")
            return

        # 检查连接状态
        if not self.connector.is_connected:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先连接微信")
            return

        if not self.connector.is_logged_in:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先登录微信")
            return

        # 获取选中的联系人
        selected_contacts = []
        # 从好友列表获取选中的联系人
        for item in self.friends_list.selectedItems():
            contact = item.data(Qt.ItemDataRole.UserRole)
            if contact:
                selected_contacts.append(contact)
        # 从群聊列表获取选中的联系人
        for item in self.groups_list.selectedItems():
            contact = item.data(Qt.ItemDataRole.UserRole)
            if contact:
                selected_contacts.append(contact)

        if not selected_contacts:
            ThemedMessageBoxHelper.show_warning(
                self, "提示", "请先选择要发送消息的联系人"
            )
            return

        # 获取消息内容和类型
        message_type_text = self.msg_type_combo.currentText()

        if message_type_text == "富文本消息":
            # 富文本消息处理
            if not self.rich_text_editor.has_content():
                ThemedMessageBoxHelper.show_warning(self, "提示", "请输入消息内容")
                return

            # 创建富文本发送任务
            tasks = self.create_rich_text_tasks(selected_contacts)
        else:
            # 传统消息处理
            message_content = self.message_edit.toPlainText().strip()
            if not message_content:
                ThemedMessageBoxHelper.show_warning(self, "提示", "请输入消息内容")
                return

            # 获取消息类型
            message_type_index = (
                self.msg_type_combo.currentIndex() - 1
            )  # 减1因为第一个是富文本消息
            message_types = ["text", "image", "file"]
            message_type = (
                message_types[message_type_index]
                if 0 <= message_type_index < len(message_types)
                else "text"
            )

            # 获取文件路径（如果是文件消息）
            file_path = ""
            if message_type in ["image", "file"]:
                file_path = self.file_path_label.text()
                if file_path == "未选择文件" or not file_path:
                    ThemedMessageBoxHelper.show_warning(
                        self, "提示", f"请选择要发送的{message_type}文件"
                    )
                    return

            # 验证消息内容
            from utils.validators import validate_message

            valid, error = validate_message(message_content, message_type, file_path)
            if not valid:
                ThemedMessageBoxHelper.show_warning(
                    self, "消息验证失败", f"消息内容不符合要求：\n{error}"
                )
                return

            # 创建传统发送任务
            tasks = self.create_traditional_tasks(
                selected_contacts, message_content, message_type, file_path
            )

        # 检查是否启用定时发送
        timing_enabled = self.enable_timing_check.isChecked()
        timing_info = ""

        if timing_enabled:
            # 获取定时设置
            cycle_interval = self.cycle_interval_spin.value()
            selected_weekdays = [
                i for i, check in enumerate(self.weekday_checks) if check.isChecked()
            ]
            start_time = self.start_time_spin.value()
            end_time = self.end_time_spin.value()

            if not selected_weekdays:
                ThemedMessageBoxHelper.show_warning(
                    self, "提示", "请至少选择一个执行星期"
                )
                return

            if start_time >= end_time:
                ThemedMessageBoxHelper.show_warning(
                    self, "提示", "开始时间必须早于结束时间"
                )
                return

            weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
            selected_weekday_names = [weekday_names[i] for i in selected_weekdays]

            timing_info = (
                f"\n\n定时设置:\n"
                f"• 循环间隔: 每 {cycle_interval} 小时\n"
                f"• 执行星期: {', '.join(selected_weekday_names)}\n"
                f"• 有效时间: {start_time:02d}:00 - {end_time:02d}:00"
            )

        # 确认发送
        if message_type_text == "富文本消息":
            content_summary = self.rich_text_editor.get_content_summary()
            task_count = len(tasks)
            confirm_text = (
                f"确定要向 {len(selected_contacts)} 个联系人发送消息吗？\n\n"
                f"消息类型: 富文本消息\n"
                f"内容摘要: {content_summary}\n"
                f"发送任务数: {task_count} 个"
            )
            if timing_enabled:
                confirm_text += timing_info

            reply = ThemedMessageBoxHelper.show_question(self, "确认发送", confirm_text)
        else:
            confirm_text = (
                f"确定要向 {len(selected_contacts)} 个联系人发送消息吗？\n\n"
                f"消息类型: {message_type_text}\n"
                f"消息内容: {message_content[:50]}{'...' if len(message_content) > 50 else ''}"
            )
            if timing_enabled:
                confirm_text += timing_info

            reply = ThemedMessageBoxHelper.show_question(self, "确认发送", confirm_text)

        if not reply:
            return

        # 开始发送
        if timing_enabled:
            # 定时发送
            timing_config = {
                "cycle_interval": cycle_interval,
                "selected_weekdays": selected_weekdays,
                "start_time": start_time,
                "end_time": end_time,
            }

            if message_type_text == "富文本消息":
                self.execute_timed_rich_text_sending(tasks, timing_config)
            else:
                if tasks:
                    first_task = tasks[0]
                    self.execute_timed_sending(
                        selected_contacts,
                        first_task.content,
                        first_task.message_type,
                        getattr(first_task, "file_path", ""),
                        timing_config,
                    )
        else:
            # 立即发送
            if message_type_text == "富文本消息":
                self.execute_rich_text_sending(tasks)
            else:
                # 从tasks中获取第一个任务的信息用于传统发送
                if tasks:
                    first_task = tasks[0]
                    self.execute_sending(
                        selected_contacts,
                        first_task.content,
                        first_task.message_type,
                        getattr(first_task, "file_path", ""),
                    )

    def create_rich_text_tasks(self, selected_contacts):
        """创建富文本发送任务（按顺序发送）"""
        tasks = []
        content = self.rich_text_editor.get_message_content()

        # 获取按顺序的发送部分
        send_parts = self.rich_text_editor.get_send_parts()

        # 将发送部分添加到内容中
        content["send_parts"] = send_parts

        # 获取文字内容（去除图片占位符）
        text_content = content.get("plain_text", "").replace("\ufffc", "").strip()

        # 获取图片路径列表
        images = content.get("images", [])
        image_paths = []
        for image_info in images:
            if isinstance(image_info, dict):
                path = image_info.get("path", "")
            else:
                path = str(image_info)
            if path:
                image_paths.append(path)

        # 检查是否有内容
        if not text_content and not image_paths:
            logger.warning("没有可发送的内容")
            return tasks

        # 记录发送顺序信息
        logger.info(f"富文本消息发送顺序:")
        for i, (text, image) in enumerate(send_parts):
            if text:
                logger.info(f"  第{i+1}部分: 文字 ({len(text)}字) - {text[:30]}...")
            else:
                image_name = os.path.basename(image) if image else "unknown"
                logger.info(f"  第{i+1}部分: 图片 ({image_name})")

        # 为每个联系人创建itchat风格富文本任务
        for contact in selected_contacts:
            task = SendTask(
                id=f"{contact.wxid}_itchat_rich",
                wxid=contact.wxid,
                name=f"{contact.name}(富文本)",
                message_type="itchat_rich",
                content=content,  # 传递完整的内容，包括HTML和send_parts
                status=SendStatus.PENDING,
            )
            tasks.append(task)

        return tasks

    def create_traditional_tasks(
        self, selected_contacts, message_content, message_type, file_path
    ):
        """创建传统发送任务"""
        tasks = []

        for contact in selected_contacts:
            task = SendTask(
                id=f"{contact.wxid}_{message_type}",
                wxid=contact.wxid,
                name=contact.name,
                message_type=message_type,
                content=message_content,
                file_path=file_path if message_type in ["image", "file"] else None,
                status=SendStatus.PENDING,
            )
            tasks.append(task)

        return tasks

    def execute_rich_text_sending(self, tasks):
        """执行富文本发送任务"""
        try:
            # 检查连接状态
            if not self.connector.is_connected:
                ThemedMessageBoxHelper.show_warning(self, "提示", "请先连接微信")
                return

            if not self.connector.is_logged_in:
                ThemedMessageBoxHelper.show_warning(self, "提示", "请先登录微信")
                return

            # 清空之前的发送任务
            self.monitor.clear_tasks()

            # 添加任务到监控器
            for task in tasks:
                self.monitor.add_task(task)

            # 更新状态表格
            self.update_status_table()

            # 启动监控器
            self.monitor.start_monitoring()

            # 开始发送
            self.send_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setValue(0)

            # 异步发送
            self.run_async_task(
                self.send_rich_text_messages_async(tasks),
                on_finished=self.on_send_completed,
                on_error=self.on_send_error,
            )

        except Exception as e:
            logger.error(f"执行富文本发送失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "发送失败", f"执行发送任务时出错:\n{e}"
            )

    def execute_timed_sending(
        self, selected_contacts, message_content, message_type, file_path, timing_config
    ):
        """执行定时发送任务"""
        try:
            # 保存发送参数
            self.timed_send_params = {
                "selected_contacts": selected_contacts,
                "message_content": message_content,
                "message_type": message_type,
                "file_path": file_path,
            }

            # 计算当天需要发送的总数（根据循环间隔和有效时间计算）
            daily_total = self._calculate_daily_send_count(
                selected_contacts, timing_config
            )

            # 设置监控器为定时模式
            self.monitor.set_timed_mode(True, daily_total)

            # 创建发送回调函数
            def send_callback():
                try:
                    logger.info("执行定时发送回调")
                    self.execute_sending(
                        self.timed_send_params["selected_contacts"],
                        self.timed_send_params["message_content"],
                        self.timed_send_params["message_type"],
                        self.timed_send_params["file_path"],
                    )
                    return True
                except Exception as e:
                    logger.error(f"定时发送回调执行失败: {e}")
                    return False

            # 启动定时发送
            success = self.timed_sender.start_timed_sending(
                timing_config, send_callback
            )

            if success:
                # 更新按钮状态
                self.send_btn.setEnabled(False)
                self.pause_btn.setEnabled(True)
                self.stop_btn.setEnabled(True)

                logger.info("定时发送任务已启动")
            else:
                # 重置监控器模式
                self.monitor.set_timed_mode(False)
                logger.error("定时发送任务启动失败，请检查配置")

        except Exception as e:
            logger.error(f"执行定时发送失败: {e}")
            # 重置监控器模式
            self.monitor.set_timed_mode(False)

    def _calculate_daily_send_count(self, selected_contacts, timing_config):
        """
        计算当天需要发送的总数

        Args:
            selected_contacts: 选中的联系人列表
            timing_config: 定时配置

        Returns:
            当天预计发送的总数
        """
        try:
            cycle_interval = timing_config["cycle_interval"]  # 小时
            start_time = timing_config["start_time"]
            end_time = timing_config["end_time"]

            # 计算有效时间范围（小时）
            valid_hours = end_time - start_time

            # 计算当天可能的发送次数
            cycles_per_day = max(1, valid_hours // cycle_interval)

            # 每次发送的联系人数量
            contacts_per_cycle = len(selected_contacts)

            # 当天总发送数
            daily_total = cycles_per_day * contacts_per_cycle

            logger.info(
                f"计算当天发送总数: {cycles_per_day}次循环 × {contacts_per_cycle}个联系人 = {daily_total}"
            )

            return daily_total

        except Exception as e:
            logger.error(f"计算当天发送总数失败: {e}")
            # 返回基础数量作为备选
            return len(selected_contacts)

    def execute_timed_rich_text_sending(self, tasks, timing_config):
        """执行定时富文本发送任务"""
        try:
            # 保存发送参数
            self.timed_rich_text_tasks = tasks

            # 计算当天需要发送的总数
            # 对于富文本，需要根据任务数量计算
            daily_total = self._calculate_daily_rich_text_send_count(
                tasks, timing_config
            )

            # 设置监控器为定时模式
            self.monitor.set_timed_mode(True, daily_total)

            # 创建发送回调函数
            def send_callback():
                try:
                    logger.info("执行定时富文本发送回调")
                    self.execute_rich_text_sending(self.timed_rich_text_tasks)
                    return True
                except Exception as e:
                    logger.error(f"定时富文本发送回调执行失败: {e}")
                    return False

            # 启动定时发送
            success = self.timed_sender.start_timed_sending(
                timing_config, send_callback
            )

            if success:
                # 更新按钮状态
                self.send_btn.setEnabled(False)
                self.pause_btn.setEnabled(True)
                self.stop_btn.setEnabled(True)

                logger.info("定时富文本发送任务已启动")
            else:
                # 重置监控器模式
                self.monitor.set_timed_mode(False)
                logger.error("定时富文本发送任务启动失败，请检查配置")

        except Exception as e:
            logger.error(f"执行定时富文本发送失败: {e}")
            # 重置监控器模式
            self.monitor.set_timed_mode(False)

    def _calculate_daily_rich_text_send_count(self, tasks, timing_config):
        """
        计算当天富文本发送的总数

        Args:
            tasks: 富文本发送任务列表
            timing_config: 定时配置

        Returns:
            当天预计发送的总任务数
        """
        try:
            cycle_interval = timing_config["cycle_interval"]  # 小时
            start_time = timing_config["start_time"]
            end_time = timing_config["end_time"]

            # 计算有效时间范围（小时）
            valid_hours = end_time - start_time

            # 计算当天可能的发送次数
            cycles_per_day = max(1, valid_hours // cycle_interval)

            # 每次发送的任务数量
            tasks_per_cycle = len(tasks)

            # 当天总任务数
            daily_total = cycles_per_day * tasks_per_cycle

            logger.info(
                f"计算当天富文本发送总数: {cycles_per_day}次循环 × {tasks_per_cycle}个任务 = {daily_total}"
            )

            return daily_total

        except Exception as e:
            logger.error(f"计算当天富文本发送总数失败: {e}")
            # 返回基础任务数量作为备选
            return len(tasks)

    def execute_sending(
        self, selected_contacts, message_content, message_type, file_path
    ):
        """执行发送任务"""
        try:
            from core.send_monitor import SendTask, SendStatus

            # 清空之前的发送任务
            self.monitor.clear_tasks()

            # 创建发送任务
            tasks = []
            for contact in selected_contacts:
                # 渲染消息内容（替换变量）
                rendered_content = self.template.render_content(
                    message_content, {"name": contact.name, "wxid": contact.wxid}
                )

                task = SendTask(
                    wxid=contact.wxid,
                    name=contact.name,
                    message_type=message_type,
                    content=rendered_content,
                    file_path=file_path,
                )
                tasks.append(task)

            # 添加任务到监控器
            self.monitor.add_tasks(tasks)

            # 更新界面状态
            self.send_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setValue(0)
            self.update_status_table()

            # 开始监控
            self.monitor.start_monitoring()

            # 启动发送任务
            self.run_async_task(
                self.send_messages_async(tasks),
                on_finished=self._on_sending_finished,
                on_error=self._on_sending_error,
            )

            self.statusBar().showMessage(
                f"开始发送消息到 {len(selected_contacts)} 个联系人"
            )
            logger.info(f"开始发送任务，共 {len(tasks)} 个任务")

        except Exception as e:
            logger.error(f"创建发送任务失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "错误", f"创建发送任务失败：\n{str(e)}"
            )
            self.send_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)

    def _check_system_risk_control(self) -> bool:
        """检查系统风控设置"""
        try:
            # 检查是否启用风控保护
            if not self.config.enable_risk_control:
                logger.info("系统风控保护已禁用，允许发送")
                return True

            # 检查基本风控状态
            if not self.risk_controller.check_risk_control():
                from ui.widgets.themed_message_box import ThemedMessageBoxHelper
                ThemedMessageBoxHelper.show_warning(
                    self,
                    "风控提醒",
                    "当前风控状态不允许发送，请稍后再试或检查风控设置"
                )
                return False

            logger.info("系统风控检查通过")
            return True

        except Exception as e:
            logger.error(f"系统风控检查失败: {e}")
            # 出错时允许发送，避免阻塞
            return True

    @performance_monitor
    async def send_messages_async(self, tasks):
        """异步发送消息 - 支持批次处理（性能优化版）"""
        import asyncio
        import random

        try:
            # 性能优化：检查任务数量，采用不同策略
            if len(tasks) > 50:
                logger.info(f"大批量发送任务({len(tasks)}个)，启用性能优化模式")
                await self._send_messages_batch_optimized(tasks)
            else:
                await self._send_messages_normal(tasks)
            batch_size = self.config.batch_size
            batch_interval_seconds = self.config.batch_interval  # 已经是秒数

            # 将任务分批处理
            total_batches = (len(tasks) + batch_size - 1) // batch_size
            logger.info(
                f"开始批次发送，共 {len(tasks)} 个任务，分为 {total_batches} 批，每批 {batch_size} 个"
            )

            for batch_index in range(total_batches):
                # 检查是否需要停止
                if not self.monitor.is_running:
                    # 取消剩余任务
                    for task in tasks[batch_index * batch_size :]:
                        self.monitor.update_task_status(task.id, SendStatus.CANCELLED)
                    break

                # 获取当前批次的任务
                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, len(tasks))
                batch_tasks = tasks[start_idx:end_idx]

                logger.info(
                    f"开始发送第 {batch_index + 1}/{total_batches} 批，共 {len(batch_tasks)} 个任务"
                )

                # 发送当前批次的任务
                for i, task in enumerate(batch_tasks):
                    # 检查是否需要停止
                    if not self.monitor.is_running:
                        self.monitor.update_task_status(task.id, SendStatus.CANCELLED)
                        continue

                    # 风控检查
                    can_send, reason = self.risk_controller.can_send_message(task.wxid)
                    if not can_send:
                        logger.warning(f"风控检查失败，跳过发送: {task.name} - {reason}")
                        self.monitor.update_task_status(task.id, SendStatus.SKIPPED, reason)
                        continue

                    # 更新任务状态为发送中
                    self.monitor.update_task_status(task.id, SendStatus.SENDING)

                    try:
                        # 发送消息
                        if task.message_type == "text":
                            success = await self.connector.send_text_message(
                                task.wxid, task.content
                            )
                        elif task.message_type in ["image", "file"]:
                            success = await self.connector.send_file_message(
                                task.wxid, task.file_path
                            )
                        else:
                            success = False

                        # 更新任务状态
                        if success:
                            print(
                                f"任务 {task.name} 发送成功"
                            )
                            self.monitor.update_task_status(task.id, SendStatus.SUCCESS)
                            # 记录风控信息
                            self.risk_controller.record_send_attempt(
                                task.wxid, task.message_type, True
                            )
                        else:
                            print(
                                f"任务 {task.name} 发送失败"
                            )
                            self.monitor.update_task_status(
                                task.id, SendStatus.FAILED, "发送失败"
                            )
                            self.risk_controller.record_send_attempt(
                                task.wxid, task.message_type, False
                            )

                    except Exception as e:
                        error_msg = str(e)
                        self.monitor.update_task_status(
                            task.id, SendStatus.FAILED, error_msg
                        )
                        self.risk_controller.record_send_attempt(
                            task.wxid, task.message_type, False
                        )
                        logger.error(f"发送消息失败 {task.name}: {error_msg}")

                    # 批次内消息间隔控制
                    if i < len(batch_tasks) - 1:  # 不是批次内最后一个任务
                        delay = self.risk_controller.get_send_delay()
                        logger.info(f"等待 {delay:.1f} 秒后发送下一条消息")
                        await asyncio.sleep(delay)

                # 批次间隔控制
                if batch_index < total_batches - 1:  # 不是最后一批
                    logger.info(
                        f"第 {batch_index + 1} 批发送完成，等待 {batch_interval_seconds/3600:.1f} 小时后发送下一批"
                    )
                    await asyncio.sleep(batch_interval_seconds)

            return True

        except Exception as e:
            logger.error(f"发送消息过程中出错: {e}")
            return False

    def _on_sending_finished(self, result):
        """发送完成回调"""
        self.send_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.monitor.stop_monitoring()
        logger.info(f"发送任务完成，结果: {result}")

    def _on_sending_error(self, error):
        """发送错误回调"""
        self.send_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.monitor.stop_monitoring()
        logger.error(f"发送任务出错: {error}")
        ThemedMessageBoxHelper.show_error(
            self, "发送错误", f"发送过程中出现错误：\n{error}"
        )

    def on_send_error(self, error):
        """发送错误处理"""
        try:
            # 重置按钮状态
            self.send_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

            # 停止监控
            if hasattr(self, "monitor"):
                self.monitor.stop_monitoring()

            # 记录错误
            logger.error(f"富文本发送出错: {error}")

            # 显示错误消息
            ThemedMessageBoxHelper.show_error(
                self, "发送错误", f"富文本消息发送过程中出现错误：\n{error}"
            )

            # 更新状态栏
            self.statusBar().showMessage("发送失败")

        except Exception as e:
            logger.error(f"处理发送错误时出错: {e}")

    async def send_rich_text_messages_async(self, tasks):
        """异步发送富文本消息"""
        import asyncio
        import random

        try:
            batch_size = self.config.batch_size
            batch_interval_seconds = self.config.batch_interval

            # 将任务分批处理
            total_batches = (len(tasks) + batch_size - 1) // batch_size
            logger.info(
                f"开始发送富文本消息，共 {len(tasks)} 个任务，分 {total_batches} 批"
            )

            for batch_index in range(total_batches):
                if not self.monitor.is_running:
                    logger.info("发送被停止")
                    break

                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, len(tasks))
                batch_tasks = tasks[start_idx:end_idx]

                logger.info(
                    f"开始发送第 {batch_index + 1}/{total_batches} 批，包含 {len(batch_tasks)} 个任务"
                )

                for task in batch_tasks:
                    if not self.monitor.is_running:
                        break

                    try:
                        # 更新任务状态
                        self.monitor.update_task_status(task.id, SendStatus.SENDING)

                        # 检查连接状态
                        if not self.connector.is_connected:
                            logger.error(f"微信未连接，无法发送任务 {task.name}")
                            self.monitor.update_task_status(
                                task.id, SendStatus.FAILED, "微信未连接"
                            )
                            continue

                        if not self.connector.is_logged_in:
                            logger.error(f"微信未登录，无法发送任务 {task.name}")
                            self.monitor.update_task_status(
                                task.id, SendStatus.FAILED, "微信未登录"
                            )
                            continue

                        # 风控检查
                        can_send, reason = self.risk_controller.can_send_message(task.wxid)
                        if not can_send:
                            logger.warning(f"风控检查失败，跳过发送: {task.name} - {reason}")
                            self.monitor.update_task_status(task.id, SendStatus.SKIPPED, reason)
                            continue

                        # 发送消息
                        success = False
                        logger.info(
                            f"开始发送任务 {task.name} (类型: {task.message_type}, 目标: {task.wxid})"
                        )

                        if task.message_type == "text":
                            logger.info(f"发送文字消息: {task.content[:50]}...")
                            success = await self.connector.send_text_message(
                                task.wxid, task.content
                            )
                        elif task.message_type == "image":
                            logger.info(f"发送图片消息: {task.file_path}")
                            success = await self.connector.send_image_message(
                                task.wxid, task.file_path
                            )
                        elif task.message_type == "itchat_rich":
                            text_len = len(task.content.get("plain_text", ""))
                            image_count = len(task.content.get("images", []))
                            logger.info(
                                f"发送itchat风格富文本消息: 文字{text_len}字, 图片{image_count}张"
                            )
                            success = (
                                await self.connector.send_itchat_style_rich_message(
                                    task.wxid, task.content
                                )
                            )

                        logger.info(f"任务 {task.name} 发送结果: {success}")

                        # 更新任务状态
                        if success:
                            print(
                                f"任务 {task.name} 发送成功"
                            )
                            self.monitor.update_task_status(task.id, SendStatus.SUCCESS)
                            # 记录风控信息
                            self.risk_controller.record_send_attempt(
                                task.wxid, task.message_type, True
                            )
                        else:
                            print(
                                f"任务 {task.name} 发送失败"
                            )
                            self.monitor.update_task_status(
                                task.id, SendStatus.FAILED, "发送失败"
                            )
                            self.risk_controller.record_send_attempt(
                                task.wxid, task.message_type, False
                            )

                        # 更新进度 - 使用QMetaObject确保线程安全
                        progress = int(
                            (start_idx + batch_tasks.index(task) + 1) / len(tasks) * 100
                        )
                        QMetaObject.invokeMethod(
                            self.progress_bar,
                            "setValue",
                            Qt.ConnectionType.QueuedConnection,
                            Q_ARG(int, progress),
                        )

                        # 等待间隔
                        delay = self.risk_controller.get_send_delay()
                        await asyncio.sleep(delay)

                    except Exception as e:
                        logger.error(f"发送任务 {task.name} 失败: {e}")
                        self.monitor.update_task_status(
                            task.id, SendStatus.FAILED, str(e)
                        )

                # 批次间隔
                if batch_index < total_batches - 1 and self.monitor.is_running:
                    logger.info(f"等待批次间隔 {batch_interval_seconds} 秒")
                    await asyncio.sleep(batch_interval_seconds)

            # 获取统计信息
            statistics = self.monitor.get_statistics()
            logger.info(f"富文本消息发送完成: {statistics}")

            # 发送完成信号 - 直接调用，因为AsyncTaskRunner会处理线程安全
            return statistics

        except Exception as e:
            logger.error(f"富文本消息发送异常: {e}")
            # 重新抛出异常，让 AsyncTaskRunner 处理
            raise e

    def toggle_timing_options(self, state):
        """切换定时选项的启用状态"""
        enabled = state == 2  # Qt.CheckState.Checked
        self.timing_options_widget.setEnabled(enabled)

        if enabled:
            self.send_btn.setText("开始定时发送")
            logger.info("启用定时循环发送模式")
        else:
            self.send_btn.setText("开始发送")
            logger.info("禁用定时循环发送模式")

    def pause_sending(self):
        """暂停发送"""
        # 检查是否有定时发送任务
        if self.timed_sender.is_running:
            self.timed_sender.pause_timed_sending()

            # 更新按钮状态
            self.pause_btn.setEnabled(False)
            self.send_btn.setText("继续定时发送")
            self.send_btn.setEnabled(True)

            logger.info("定时发送任务已暂停")
            self.statusBar().showMessage("定时发送任务已暂停")
            return

        # 检查普通发送任务
        if not self.monitor.is_running:
            ThemedMessageBoxHelper.show_information(
                self, "提示", "当前没有正在进行的发送任务"
            )
            return

        # 暂停发送任务
        self.monitor.pause()

        # 更新按钮状态
        self.pause_btn.setEnabled(False)
        self.send_btn.setText("继续发送")
        self.send_btn.setEnabled(True)

        logger.info("发送任务已暂停")
        self.statusBar().showMessage("发送任务已暂停")

    def stop_sending(self):
        """停止发送"""
        # 检查是否有定时发送任务
        if self.timed_sender.is_running:
            # 确认停止定时发送
            reply = ThemedMessageBoxHelper.show_question(
                self, "确认停止", "确定要停止定时发送任务吗？\n\n后续的定时发送将被取消"
            )

            if reply:
                self.timed_sender.stop_timed_sending()

                # 重置监控器模式
                self.monitor.set_timed_mode(False)

                # 更新界面状态
                self.send_btn.setText("开始发送")
                self.send_btn.setEnabled(True)
                self.pause_btn.setEnabled(False)
                self.stop_btn.setEnabled(False)

                logger.info("定时发送任务已停止")
                self.statusBar().showMessage("定时发送任务已停止")
            return

        # 检查普通发送任务
        if not self.monitor.is_running:
            ThemedMessageBoxHelper.show_information(
                self, "提示", "当前没有正在进行的发送任务"
            )
            return

        # 确认停止
        reply = ThemedMessageBoxHelper.show_question(
            self, "确认停止", "确定要停止当前的发送任务吗？\n\n未发送的消息将被取消"
        )

        if reply:
            # 停止监控
            self.monitor.stop_monitoring()

            # 更新界面状态
            self.send_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

            self.statusBar().showMessage("发送任务已停止")
            logger.info("用户手动停止发送任务")

    def on_timed_task_started(self, task_id: str):
        """定时任务开始处理"""
        self.statusBar().showMessage(f"定时任务开始: {task_id[:8]}...")
        logger.info(f"定时任务开始: {task_id}")

    def on_timed_task_progress(self, task_id: str, sent: int, total: int):
        """定时任务进度处理"""
        progress_text = f"定时任务进度: {sent}/{total}"
        self.statusBar().showMessage(progress_text)
        logger.info(f"定时任务进度: {task_id} - {sent}/{total}")

    def on_timed_task_completed(self, task_id: str):
        """定时任务完成处理"""
        self.statusBar().showMessage(f"定时任务完成: {task_id[:8]}...")
        logger.info(f"定时任务完成: {task_id}")

    def on_timed_task_failed(self, task_id: str, error: str):
        """定时任务失败处理"""
        # 只在状态栏显示，不弹窗
        self.statusBar().showMessage(
            f"定时任务失败: {task_id[:8]}... - {error[:30]}{'...' if len(error) > 30 else ''}"
        )
        logger.error(f"定时任务失败: {task_id} - {error}")

    def on_group_status_changed(self, group_id: str, group_type: str):
        """分组状态变化处理"""
        try:
            # 更新对应页面的分组卡片状态
            if group_type == "timing" and hasattr(self, "timing_send_page"):
                # 更新定时发送页面的分组卡片状态
                self.timing_send_page.update_group_card_button_states(None, group_id)
                logger.debug(f"更新定时发送分组状态: {group_id}")

            elif group_type == "loop" and hasattr(self, "loop_send_page"):
                # 更新循环发送页面的分组卡片状态
                self.loop_send_page.refresh_groups()
                logger.debug(f"更新循环发送分组状态: {group_id}")

            # 同时更新任务状态页面
            if hasattr(self, "task_status_page"):
                if group_type == "timing":
                    self.task_status_page.refresh_timing_tasks()
                elif group_type == "loop":
                    self.task_status_page.refresh_loop_tasks()
                logger.debug(f"更新任务状态页面: {group_id} ({group_type})")

        except Exception as e:
            logger.error(f"处理分组状态变化失败: {e}")

    def on_task_deleted(self, task_id: str, task_type: str):
        """任务删除处理"""
        try:
            logger.info(f"任务已删除: {task_id} (类型: {task_type})")

            # 可以在这里添加其他需要的处理逻辑
            # 比如更新统计信息、刷新相关界面等

        except Exception as e:
            logger.error(f"处理任务删除失败: {e}")

        # 重置按钮状态
        self.send_btn.setText("开始发送")
        self.send_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)

    def on_timed_cycle_started(self, cycle_info):
        """定时循环开始处理"""
        cycle_number = cycle_info.get("cycle_number", 0)
        send_time = cycle_info.get("send_time")

        logger.info(f"开始第{cycle_number}次定时发送循环")
        self.statusBar().showMessage(f"正在执行第{cycle_number}次定时发送...")

        # 可以在这里添加进度显示逻辑

    def on_timed_cycle_completed(self, cycle_info):
        """定时循环完成处理"""
        cycle_number = cycle_info.get("cycle_number", 0)
        success = cycle_info.get("success", False)
        error = cycle_info.get("error", "")

        if success:
            logger.info(f"第{cycle_number}次定时发送循环完成")
            self.statusBar().showMessage(f"第{cycle_number}次定时发送完成")
        else:
            logger.error(f"第{cycle_number}次定时发送循环失败: {error}")
            self.statusBar().showMessage(f"第{cycle_number}次定时发送失败")

    def refresh_contacts(self):
        """获取联系人"""
        if not self.connector.is_connected:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先连接微信")
            return

        if not self.connector.is_logged_in:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先登录微信")
            return

        # 禁用获取按钮，防止重复点击
        self.refresh_contacts_btn.setEnabled(False)
        self.refresh_contacts_btn.setText("获取中...")
        self.statusBar().showMessage("正在获取联系人列表...")

        def on_refresh_finished(contacts):
            """获取完成回调"""
            print(
                f"获取到 {len(contacts)} 个联系人"
            )
            self.refresh_contacts_btn.setEnabled(True)
            self.refresh_contacts_btn.setText("获取联系人")
            self.statusBar().showMessage(f"联系人获取完成，共 {len(contacts)} 个联系人")

            # 手动更新联系人列表（确保UI更新）
            if contacts:
                # 手动调用联系人更新
                self.on_contacts_updated(contacts)

                # 强制刷新UI
                self.friends_table.viewport().update()
                self.groups_table.viewport().update()
                if hasattr(self, "friends_list"):
                    self.friends_list.viewport().update()
                if hasattr(self, "groups_list"):
                    self.groups_list.viewport().update()

            ThemedMessageBoxHelper.show_information(
                self, "成功", f"联系人获取完成！\n共获取到 {len(contacts)} 个联系人"
            )

        def on_refresh_error(error):
            """获取错误回调"""
            self.refresh_contacts_btn.setEnabled(True)
            self.refresh_contacts_btn.setText("获取联系人")
            self.statusBar().showMessage("联系人获取失败")
            ThemedMessageBoxHelper.show_warning(
                self, "错误", f"获取联系人失败：\n{error}"
            )

        self.run_async_task(
            self.connector.get_contacts(),
            on_finished=on_refresh_finished,
            on_error=on_refresh_error,
        )

    def export_contacts(self):
        """导出联系人"""
        if not self.contacts:
            ThemedMessageBoxHelper.show_warning(
                self, "提示", "没有联系人数据可导出\n请先获取联系人列表"
            )
            return

        # 选择保存文件
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出联系人",
            f"微信联系人_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)",
        )

        if not file_path:
            return

        try:
            # 准备导出数据 - 只导出联系人名称
            contact_names = []
            for contact in self.contacts:
                contact_names.append(contact.name)

            # 写入文本文件，每行一个联系人名称
            with open(file_path, "w", encoding="utf-8") as f:
                for name in contact_names:
                    f.write(name + "\n")

            self.statusBar().showMessage(f"联系人已导出到: {file_path}")
            ThemedMessageBoxHelper.show_information(
                self,
                "导出成功",
                f"联系人导出成功！\n\n文件路径: {file_path}\n导出数量: {len(contact_names)} 个联系人\n\n格式: 每行一个联系人名称",
            )

        except Exception as e:
            logger.error(f"导出联系人失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "错误", f"导出联系人失败：\n{str(e)}"
            )

    def export_contacts_from_page(self):
        """从当前页面导出联系人"""
        # 获取当前页面显示的联系人
        page_contacts = []

        # 从好友表格获取联系人
        for row in range(self.friends_table.rowCount()):
            name_item = self.friends_table.item(row, 0)
            wxid_item = self.friends_table.item(row, 1)
            remark_item = self.friends_table.item(row, 2)

            if name_item and wxid_item:
                page_contacts.append(
                    {
                        "name": name_item.text(),
                        "wxid": wxid_item.text(),
                        "remark": remark_item.text() if remark_item else "",
                        "type": "friend",
                    }
                )

        # 从群聊表格获取联系人
        for row in range(self.groups_table.rowCount()):
            name_item = self.groups_table.item(row, 0)
            wxid_item = self.groups_table.item(row, 1)

            if name_item and wxid_item:
                page_contacts.append(
                    {
                        "name": name_item.text(),
                        "wxid": wxid_item.text(),
                        "remark": "",
                        "type": "group",
                    }
                )

        if not page_contacts:
            ThemedMessageBoxHelper.show_warning(
                self, "提示", "当前页面没有联系人数据可导出"
            )
            return

        # 选择保存文件
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出当前页面联系人",
            f"页面联系人_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)",
        )

        if not file_path:
            return

        try:
            # 写入文本文件，每行一个联系人名称
            with open(file_path, "w", encoding="utf-8") as f:
                for contact in page_contacts:
                    f.write(f"{contact['name']}\n")

            self.statusBar().showMessage(f"页面联系人已导出到: {file_path}")
            ThemedMessageBoxHelper.show_information(
                self,
                "导出成功",
                f"成功导出当前页面 {len(page_contacts)} 个联系人到:\n{file_path}\n\n"
                f"好友: {sum(1 for c in page_contacts if c['type'] == 'friend')} 个\n"
                f"群聊: {sum(1 for c in page_contacts if c['type'] == 'group')} 个",
            )

        except Exception as e:
            logger.error(f"导出页面联系人失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "导出失败", f"导出页面联系人时出错:\n{e}"
            )

    def import_contacts(self):
        """导入联系人"""
        # 显示导入格式说明
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "导入联系人",
            "导入联系人支持文本格式文件\n\n"
            "文件格式要求：\n"
            "• 文本文件 (.txt)\n"
            "• 每行一个联系人名称\n"
            "• 使用UTF-8编码\n\n"
            "示例格式：\n"
            "张三\n"
            "李四\n"
            "工作群\n\n"
            "是否继续选择文件？",
        )

        if not reply:
            return

        # 选择导入文件
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入联系人", "", "文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            from core.wechatferry_connector import Contact

            # 读取文本文件
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # 处理每行数据
            contact_names = []
            for line in lines:
                name = line.strip()
                if name:  # 跳过空行
                    contact_names.append(name)

            if not contact_names:
                ThemedMessageBoxHelper.show_warning(
                    self, "提示", "文件中没有有效的联系人名称"
                )
                return

            # 转换为Contact对象
            imported_contacts = []
            for i, name in enumerate(contact_names):
                try:
                    # 生成简单的微信ID（实际使用中可能需要真实的微信ID）
                    wxid = f"imported_contact_{i+1:03d}"

                    contact = Contact(
                        wxid=wxid,
                        name=name,
                        remark="从文件导入",
                        type="friend",  # 默认为好友类型
                        avatar="",
                    )

                    # 验证联系人名称
                    from utils.validators import validate_contact_name

                    valid, error = validate_contact_name(contact.name)
                    if valid:
                        imported_contacts.append(contact)
                    else:
                        logger.warning(f"跳过无效联系人名称 {contact.name}: {error}")

                except Exception as e:
                    logger.warning(f"处理联系人名称失败: {e}")
                    continue

            if not imported_contacts:
                ThemedMessageBoxHelper.show_warning(
                    self, "提示", "没有找到有效的联系人数据"
                )
                return

            # 询问是否替换现有联系人
            reply = ThemedMessageBoxHelper.show_question(
                self,
                "确认导入",
                f"即将导入 {len(imported_contacts)} 个联系人\n\n"
                f"是否替换当前的联系人列表？\n"
                f"点击 Yes 替换，点击 No 追加到现有列表",
            )

            if reply:
                # 替换现有联系人
                self.contacts = imported_contacts
            else:
                # 追加到现有联系人
                existing_wxids = {contact.wxid for contact in self.contacts}
                new_contacts = [
                    c for c in imported_contacts if c.wxid not in existing_wxids
                ]
                self.contacts.extend(new_contacts)
                imported_contacts = new_contacts

            # 更新界面
            self.update_contact_list()
            self.update_contacts_table()

            self.statusBar().showMessage(
                f"联系人导入完成，共导入 {len(imported_contacts)} 个联系人"
            )
            ThemedMessageBoxHelper.show_information(
                self,
                "导入成功",
                f"联系人导入成功！\n\n导入数量: {len(imported_contacts)} 个联系人\n当前总数: {len(self.contacts)} 个联系人",
            )

        except Exception as e:
            logger.error(f"导入联系人失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "错误", f"导入联系人失败：\n{str(e)}"
            )

    def import_contacts_to_page(self):
        """导入联系人到当前页面"""
        # 显示导入格式说明
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "导入联系人到页面",
            "导入联系人到当前页面支持文本格式文件\n\n"
            "文件格式要求：\n"
            "• 文本文件 (.txt)\n"
            "• 每行一个联系人名称\n"
            "• 使用UTF-8编码\n\n"
            "示例格式：\n"
            "张三\n"
            "李四\n"
            "工作群\n\n"
            "注意：导入的联系人只会显示在当前页面，不会影响实际的联系人数据\n\n"
            "是否继续选择文件？",
        )

        if not reply:
            return

        # 选择导入文件
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入联系人到页面", "", "文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 读取文本文件
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # 处理每行数据
            contact_names = []
            for line in lines:
                name = line.strip()
                if name:  # 跳过空行
                    contact_names.append(name)

            if not contact_names:
                ThemedMessageBoxHelper.show_warning(
                    self, "提示", "文件中没有有效的联系人名称"
                )
                return

            # 询问导入方式
            reply = ThemedMessageBoxHelper.show_question(
                self,
                "导入方式",
                f"即将导入 {len(contact_names)} 个联系人到当前页面\n\n"
                f"选择导入方式：\n"
                f"Yes - 清空页面后导入\n"
                f"No - 追加到页面现有数据",
            )

            # 清空页面（如果选择替换）
            if reply:
                self.friends_table.setRowCount(0)
                self.groups_table.setRowCount(0)

            # 添加联系人到页面
            friends_added = 0
            groups_added = 0

            for i, name in enumerate(contact_names):
                # 简单判断：包含"群"字的作为群聊，其他作为好友
                if "群" in name:
                    # 添加到群聊表格
                    row = self.groups_table.rowCount()
                    self.groups_table.insertRow(row)
                    self.groups_table.setItem(row, 0, QTableWidgetItem(name))
                    self.groups_table.setItem(
                        row, 1, QTableWidgetItem(f"imported_group_{i+1:03d}@chatroom")
                    )
                    groups_added += 1
                else:
                    # 添加到好友表格
                    row = self.friends_table.rowCount()
                    self.friends_table.insertRow(row)
                    self.friends_table.setItem(row, 0, QTableWidgetItem(name))
                    self.friends_table.setItem(
                        row, 1, QTableWidgetItem(f"imported_friend_{i+1:03d}")
                    )
                    self.friends_table.setItem(row, 2, QTableWidgetItem("从文件导入"))
                    friends_added += 1

            # 更新统计信息
            self.update_contacts_count()

            self.statusBar().showMessage(
                f"页面联系人导入完成，好友: {friends_added} 个，群聊: {groups_added} 个"
            )
            ThemedMessageBoxHelper.show_information(
                self,
                "导入成功",
                f"联系人导入到页面成功！\n\n"
                f"好友: {friends_added} 个\n"
                f"群聊: {groups_added} 个\n"
                f"总计: {friends_added + groups_added} 个",
            )

        except Exception as e:
            logger.error(f"导入页面联系人失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "导入失败", f"导入页面联系人时出错:\n{e}"
            )

    def delete_selected_contacts(self):
        """删除选中的联系人"""
        # 获取选中的行
        selected_rows = set()
        for item in self.contacts_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要删除的联系人")
            return

        # 确认删除
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(selected_rows)} 个联系人吗？\n\n此操作不可撤销！",
        )

        if not reply:
            return

        try:
            # 按行号倒序删除，避免索引变化问题
            sorted_rows = sorted(selected_rows, reverse=True)
            deleted_count = 0

            for row in sorted_rows:
                if 0 <= row < len(self.contacts):
                    # 记录被删除的联系人信息
                    deleted_contact = self.contacts[row]
                    logger.info(
                        f"删除联系人: {deleted_contact.name} ({deleted_contact.wxid})"
                    )

                    # 从联系人列表中删除
                    del self.contacts[row]
                    deleted_count += 1

            # 更新界面
            self.update_contact_list()
            self.update_contacts_table()

            self.statusBar().showMessage(f"已删除 {deleted_count} 个联系人")
            ThemedMessageBoxHelper.show_information(
                self,
                "删除成功",
                f"成功删除 {deleted_count} 个联系人\n当前剩余: {len(self.contacts)} 个联系人",
            )

        except Exception as e:
            logger.error(f"删除联系人失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "错误", f"删除联系人失败：\n{str(e)}"
            )

    def delete_selected_friends(self):
        """删除选中的好友"""
        # 获取选中的行
        selected_rows = set()
        for item in self.friends_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要删除的好友")
            return

        # 确认删除
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(selected_rows)} 个好友吗？\n\n"
            f"注意：这只会从页面移除，不影响实际联系人数据",
        )

        if reply:
            # 从表格中删除选中的行（从后往前删除）
            rows_to_delete = sorted(selected_rows, reverse=True)
            for row in rows_to_delete:
                self.friends_table.removeRow(row)

            ThemedMessageBoxHelper.show_information(
                self, "删除成功", f"已删除 {len(selected_rows)} 个好友"
            )
            logger.info(f"从页面删除了 {len(selected_rows)} 个好友")

    def delete_selected_groups(self):
        """删除选中的群聊"""
        # 获取选中的行
        selected_rows = set()
        for item in self.groups_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要删除的群聊")
            return

        # 确认删除
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(selected_rows)} 个群聊吗？\n\n"
            f"注意：这只会从页面移除，不影响实际联系人数据",
        )

        if reply:
            # 从表格中删除选中的行（从后往前删除）
            rows_to_delete = sorted(selected_rows, reverse=True)
            for row in rows_to_delete:
                self.groups_table.removeRow(row)

            ThemedMessageBoxHelper.show_information(
                self, "删除成功", f"已删除 {len(selected_rows)} 个群聊"
            )
            logger.info(f"从页面删除了 {len(selected_rows)} 个群聊")

    def delete_selected_contacts_from_page(self):
        """从当前页面删除选中的联系人"""
        # 获取好友表格中选中的行
        friends_selected_rows = set()
        for item in self.friends_table.selectedItems():
            friends_selected_rows.add(item.row())

        # 获取群聊表格中选中的行
        groups_selected_rows = set()
        for item in self.groups_table.selectedItems():
            groups_selected_rows.add(item.row())

        total_selected = len(friends_selected_rows) + len(groups_selected_rows)

        if total_selected == 0:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要删除的联系人")
            return

        # 确认删除
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要从页面删除选中的联系人吗？\n\n"
            f"好友: {len(friends_selected_rows)} 个\n"
            f"群聊: {len(groups_selected_rows)} 个\n"
            f"总计: {total_selected} 个\n\n"
            f"注意：这只会从页面移除，不影响实际联系人数据",
        )

        if not reply:
            return

        try:
            deleted_count = 0

            # 删除好友表格中选中的行（倒序删除避免索引问题）
            for row in sorted(friends_selected_rows, reverse=True):
                if 0 <= row < self.friends_table.rowCount():
                    name_item = self.friends_table.item(row, 0)
                    if name_item:
                        logger.info(f"从页面删除好友: {name_item.text()}")
                    self.friends_table.removeRow(row)
                    deleted_count += 1

            # 删除群聊表格中选中的行（倒序删除避免索引问题）
            for row in sorted(groups_selected_rows, reverse=True):
                if 0 <= row < self.groups_table.rowCount():
                    name_item = self.groups_table.item(row, 0)
                    if name_item:
                        logger.info(f"从页面删除群聊: {name_item.text()}")
                    self.groups_table.removeRow(row)
                    deleted_count += 1

            # 更新统计信息
            self.update_contacts_count()

            self.statusBar().showMessage(f"已从页面删除 {deleted_count} 个联系人")
            ThemedMessageBoxHelper.show_information(
                self,
                "删除成功",
                f"成功从页面删除 {deleted_count} 个联系人\n\n"
                f"当前页面剩余：\n"
                f"好友: {self.friends_table.rowCount()} 个\n"
                f"群聊: {self.groups_table.rowCount()} 个",
            )

        except Exception as e:
            logger.error(f"从页面删除联系人失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "删除失败", f"从页面删除联系人时出错:\n{e}"
            )

    def clear_all_contacts(self):
        """清空所有联系人"""
        if not self.contacts:
            ThemedMessageBoxHelper.show_information(self, "提示", "联系人列表已经为空")
            return

        # 确认清空
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认清空",
            f"确定要清空所有联系人吗？\n\n当前共有 {len(self.contacts)} 个联系人\n此操作不可撤销！",
        )

        if not reply:
            return

        try:
            # 记录清空操作
            contact_count = len(self.contacts)
            logger.info(f"清空所有联系人，共 {contact_count} 个")

            # 清空联系人列表
            self.contacts.clear()

            # 更新界面
            self.update_contact_list()
            self.update_contacts_table()

            self.statusBar().showMessage("已清空所有联系人")
            ThemedMessageBoxHelper.show_information(
                self,
                "清空成功",
                f"已成功清空所有联系人\n共清空了 {contact_count} 个联系人",
            )

        except Exception as e:
            logger.error(f"清空联系人失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "错误", f"清空联系人失败：\n{str(e)}"
            )

    def clear_all_contacts_from_page(self):
        """清空当前页面的所有联系人"""
        friends_count = self.friends_table.rowCount()
        groups_count = self.groups_table.rowCount()
        total_count = friends_count + groups_count

        if total_count == 0:
            ThemedMessageBoxHelper.show_information(
                self, "提示", "当前页面没有联系人数据"
            )
            return

        # 确认清空
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认清空页面",
            f"确定要清空当前页面的所有联系人吗？\n\n"
            f"好友: {friends_count} 个\n"
            f"群聊: {groups_count} 个\n"
            f"总计: {total_count} 个\n\n"
            f"注意：这只会清空页面显示，不影响实际联系人数据",
        )

        if not reply:
            return

        try:
            # 记录清空操作
            logger.info(
                f"清空页面联系人，好友: {friends_count} 个，群聊: {groups_count} 个"
            )

            # 清空表格
            self.friends_table.setRowCount(0)
            self.groups_table.setRowCount(0)

            # 更新统计信息
            self.update_contacts_count()

            self.statusBar().showMessage("已清空页面所有联系人")
            ThemedMessageBoxHelper.show_information(
                self,
                "清空成功",
                f"已成功清空页面所有联系人\n\n"
                f"清空了：\n"
                f"好友: {friends_count} 个\n"
                f"群聊: {groups_count} 个\n"
                f"总计: {total_count} 个",
            )

        except Exception as e:
            logger.error(f"清空页面联系人失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "清空失败", f"清空页面联系人时出错:\n{e}"
            )

    def update_contacts_count(self):
        """更新联系人统计信息"""
        try:
            friends_count = self.friends_table.rowCount()
            groups_count = self.groups_table.rowCount()
            total_count = friends_count + groups_count

            # 更新状态栏
            self.statusBar().showMessage(
                f"页面联系人统计 - 好友: {friends_count} 个，群聊: {groups_count} 个，总计: {total_count} 个"
            )

            # 如果有联系人统计标签，也更新它
            if hasattr(self, "contacts_count_label"):
                self.contacts_count_label.setText(
                    f"好友: {friends_count} | 群聊: {groups_count} | 总计: {total_count}"
                )

        except Exception as e:
            logger.error(f"更新联系人统计失败: {e}")

    def show_friends_context_menu(self, position):
        """显示好友表格右键菜单"""
        if self.friends_table.itemAt(position) is None:
            return

        # 创建右键菜单
        context_menu = QMenu(self)

        # 获取选中的好友
        selected_rows = set()
        for item in self.friends_table.selectedItems():
            selected_rows.add(item.row())

        if selected_rows:
            # 添加到分组菜单
            timing_menu = context_menu.addMenu("➕ 加入定时发送分组")
            loop_menu = context_menu.addMenu("➕ 加入循环发送分组")

            # 动态加载分组
            self.populate_group_menu(timing_menu, "timing", selected_rows, "contact")
            self.populate_group_menu(loop_menu, "loop", selected_rows, "contact")

            context_menu.addSeparator()

        # 原有菜单项
        delete_action = context_menu.addAction("删除选中好友")
        delete_action.triggered.connect(self.delete_selected_friends)

        context_menu.addSeparator()

        select_all_action = context_menu.addAction("全选好友")
        select_all_action.triggered.connect(self.friends_table.selectAll)

        # 显示菜单
        context_menu.exec(self.friends_table.mapToGlobal(position))

    def show_groups_context_menu(self, position):
        """显示群聊表格右键菜单"""
        if self.groups_table.itemAt(position) is None:
            return

        # 创建右键菜单
        context_menu = QMenu(self)

        # 获取选中的群聊
        selected_rows = set()
        for item in self.groups_table.selectedItems():
            selected_rows.add(item.row())

        if selected_rows:
            # 添加到分组菜单
            timing_menu = context_menu.addMenu("➕ 加入定时发送分组")
            loop_menu = context_menu.addMenu("➕ 加入循环发送分组")

            # 动态加载分组
            self.populate_group_menu(timing_menu, "timing", selected_rows, "group")
            self.populate_group_menu(loop_menu, "loop", selected_rows, "group")

            context_menu.addSeparator()

        # 原有菜单项
        delete_action = context_menu.addAction("删除选中群聊")
        delete_action.triggered.connect(self.delete_selected_groups)

        context_menu.addSeparator()

        select_all_action = context_menu.addAction("全选群聊")
        select_all_action.triggered.connect(self.groups_table.selectAll)

        # 显示菜单
        context_menu.exec(self.groups_table.mapToGlobal(position))

    def populate_group_menu(self, menu, group_type, selected_rows, member_type):
        """填充分组菜单"""
        try:
            from core.group_manager import group_manager

            # 获取所有分组
            groups = group_manager.get_all_groups(group_type)

            if not groups:
                no_groups_action = menu.addAction("暂无分组")
                no_groups_action.setEnabled(False)
                return

            # 为每个分组创建菜单项
            for group in groups:
                # 检查选中的联系人是否已在分组中
                group_member_wxids = {member.wxid for member in group.members}
                selected_contacts = self.get_selected_contacts(
                    selected_rows, member_type
                )

                # 统计已在分组中的联系人数量
                already_in_group = 0
                for contact in selected_contacts:
                    if contact.get("wxid") in group_member_wxids:
                        already_in_group += 1

                # 创建菜单项文本
                if already_in_group == len(selected_contacts):
                    menu_text = f"✓ {group.name} (已全部加入)"
                elif already_in_group > 0:
                    menu_text = f"◐ {group.name} (部分已加入)"
                else:
                    menu_text = f"○ {group.name}"

                action = menu.addAction(menu_text)
                action.triggered.connect(
                    lambda checked, g=group, rows=selected_rows, mt=member_type: self.add_contacts_to_group(
                        g, rows, mt
                    )
                )

            # 添加新建分组选项
            menu.addSeparator()
            new_group_action = menu.addAction("➕ 新建分组...")
            new_group_action.triggered.connect(
                lambda checked, gt=group_type, rows=selected_rows, mt=member_type: self.create_new_group_with_contacts(
                    gt, rows, mt
                )
            )

        except Exception as e:
            logger.error(f"填充分组菜单失败: {e}")
            error_action = menu.addAction("加载分组失败")
            error_action.setEnabled(False)

    def get_selected_contacts(self, selected_rows, member_type):
        """获取选中的联系人信息"""
        contacts = []

        try:
            if member_type == "contact":
                table = self.friends_table
            else:  # group
                table = self.groups_table

            for row in selected_rows:
                if row < table.rowCount():
                    name_item = table.item(row, 0)  # 名称列
                    wxid_item = table.item(row, 1)  # 微信ID列

                    if name_item and wxid_item:
                        contacts.append(
                            {
                                "name": name_item.text(),
                                "wxid": wxid_item.text(),
                                "type": member_type,
                            }
                        )
        except Exception as e:
            logger.error(f"获取选中联系人失败: {e}")

        return contacts

    def add_contacts_to_group(self, group, selected_rows, member_type):
        """将联系人添加到分组"""
        try:
            from core.group_manager import GroupMember, group_manager

            contacts = self.get_selected_contacts(selected_rows, member_type)
            added_count = 0

            for contact in contacts:
                member = GroupMember(contact["wxid"], contact["name"], contact["type"])
                if group_manager.add_member_to_group(
                    group.group_id, group.group_type, member
                ):
                    added_count += 1

            if added_count > 0:
                ThemedMessageBoxHelper.show_information(
                    self,
                    "成功",
                    f"成功将 {added_count} 个联系人添加到分组 '{group.name}'",
                )
                logger.info(f"添加 {added_count} 个联系人到分组 {group.name}")
            else:
                ThemedMessageBoxHelper.show_information(
                    self, "提示", "所有选中的联系人都已在该分组中"
                )

        except Exception as e:
            logger.error(f"添加联系人到分组失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "错误", f"添加联系人到分组失败: {e}"
            )

    def create_new_group_with_contacts(self, group_type, selected_rows, member_type):
        """创建新分组并添加联系人"""
        try:
            from PyQt6.QtWidgets import QInputDialog
            from core.group_manager import GroupMember, group_manager

            # 获取分组名称
            group_name, ok = QInputDialog.getText(
                self,
                "新建分组",
                f"请输入{'定时发送' if group_type == 'timing' else '循环发送'}分组名称:",
            )

            if not ok or not group_name.strip():
                return

            # 创建分组
            group = group_manager.create_group(group_name.strip(), group_type)

            # 添加选中的联系人
            contacts = self.get_selected_contacts(selected_rows, member_type)
            added_count = 0

            for contact in contacts:
                member = GroupMember(contact["wxid"], contact["name"], contact["type"])
                if group_manager.add_member_to_group(
                    group.group_id, group_type, member
                ):
                    added_count += 1

            ThemedMessageBoxHelper.show_information(
                self,
                "成功",
                f"成功创建分组 '{group_name}' 并添加了 {added_count} 个联系人",
            )
            logger.info(f"创建分组 {group_name} 并添加 {added_count} 个联系人")

        except Exception as e:
            logger.error(f"创建新分组失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"创建新分组失败: {e}")

    def contacts_table_key_press_event(self, event):
        """联系人表格键盘事件处理"""
        from PyQt6.QtCore import Qt

        if event.key() == Qt.Key.Key_Delete:
            # Delete键删除选中的联系人
            self.delete_selected_contacts()
        else:
            # 调用原始的键盘事件处理
            QTableWidget.keyPressEvent(self.contacts_table, event)

    def import_recipients(self):
        """导入接收人列表"""
        # 显示导入格式说明
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "导入接收人列表",
            "导入接收人列表支持文本格式文件\n\n"
            "文件格式要求：\n"
            "• 文本文件 (.txt)\n"
            "• 每行一个联系人名称\n"
            "• 使用UTF-8编码\n\n"
            "示例格式：\n"
            "张三\n"
            "李四\n"
            "工作群\n\n"
            "导入后将自动选中匹配的联系人\n"
            "是否继续选择文件？",
        )

        if not reply:
            return

        # 选择导入文件
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入接收人列表", "", "文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 读取文本文件
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # 处理每行数据
            recipient_names = []
            for line in lines:
                name = line.strip()
                if name:  # 跳过空行
                    recipient_names.append(name)

            if not recipient_names:
                ThemedMessageBoxHelper.show_warning(
                    self, "提示", "文件中没有有效的联系人名称"
                )
                return

            # 清空当前选择
            self.clear_recipients()

            # 匹配并选中联系人
            matched_count = 0
            not_found = []

            # 在好友列表中匹配
            for i in range(self.friends_list.count()):
                item = self.friends_list.item(i)
                contact = item.data(Qt.ItemDataRole.UserRole)
                if contact and contact.name in recipient_names:
                    item.setSelected(True)
                    matched_count += 1
                    recipient_names.remove(contact.name)

            # 在群聊列表中匹配
            for i in range(self.groups_list.count()):
                item = self.groups_list.item(i)
                contact = item.data(Qt.ItemDataRole.UserRole)
                if contact and contact.name in recipient_names:
                    item.setSelected(True)
                    matched_count += 1
                    recipient_names.remove(contact.name)

            # 记录未找到的联系人
            not_found = recipient_names

            # 更新选中计数
            self.update_selected_count()

            # 显示结果
            result_msg = f"导入完成！\n\n"
            result_msg += f"成功匹配: {matched_count} 个联系人\n"

            if not_found:
                result_msg += f"未找到: {len(not_found)} 个联系人\n\n"
                result_msg += "未找到的联系人:\n"
                for name in not_found[:10]:  # 只显示前10个
                    result_msg += f"• {name}\n"
                if len(not_found) > 10:
                    result_msg += f"• ... 还有 {len(not_found) - 10} 个"

            ThemedMessageBoxHelper.show_information(self, "导入结果", result_msg)
            self.statusBar().showMessage(
                f"导入接收人列表完成，匹配 {matched_count} 个联系人"
            )

        except Exception as e:
            logger.error(f"导入接收人列表失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "错误", f"导入接收人列表失败：\n{str(e)}"
            )

    def clear_recipients(self):
        """清空消息发送页面的待接收人列表"""
        if not self.contacts:
            ThemedMessageBoxHelper.show_information(
                self, "提示", "待接收人列表已经为空"
            )
            return

        # 确认清空
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认清空",
            f"确定要清空消息发送页面的待接收人列表吗？\n\n"
            f"当前列表中有 {len(self.contacts)} 个联系人\n\n"
            f"注意：此操作只清空消息发送页面的接收人列表，\n"
            f"不影响联系人管理页面的待发联系人列表",
        )

        if not reply:
            return

        # 只清空发送页面的联系人数据
        self.contacts = []

        # 清空发送页面列表显示
        self.friends_list.clear()
        self.groups_list.clear()

        # 更新发送页面标签页标题
        self.contact_tabs.setTabText(0, "好友 (0)")
        self.contact_tabs.setTabText(1, "群聊 (0)")

        # 更新发送页面选中计数
        self.update_selected_count()

        logger.info("清空消息发送页面的待接收人列表")
        self.statusBar().showMessage("已清空消息发送页面的待接收人列表")

    def sync_from_send_list(self):
        """从联系人管理页面的待发联系人列表同步到消息发送页面"""
        if not hasattr(self, "send_contacts") or not self.send_contacts:
            ThemedMessageBoxHelper.show_information(
                self,
                "提示",
                "联系人管理页面的待发联系人列表为空\n\n"
                "请先在联系人管理页面添加联系人到待发列表",
            )
            return

        # 确认同步操作
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认同步",
            f"确定要从联系人管理页面同步联系人到消息发送页面吗？\n\n"
            f"待发联系人列表中有 {len(self.send_contacts)} 个联系人\n\n"
            f"此操作将：\n"
            f"• 清空当前消息发送页面的接收人列表\n"
            f"• 从联系人管理页面复制所有待发联系人\n"
            f"• 自动选中所有同步的联系人",
        )

        if not reply:
            return

        logger.info(f"开始同步联系人，待发列表数量: {len(self.send_contacts)}")

        # 完全清空当前的联系人列表
        self.contacts = []
        self.friends_list.clear()
        self.groups_list.clear()

        # 将待发联系人列表完全复制到发送页面
        self.contacts = self.send_contacts.copy()

        # 重新构建发送页面的联系人列表显示
        self.update_contact_list()

        # 自动选中所有联系人
        self.friends_list.selectAll()
        self.groups_list.selectAll()

        # 更新选中计数
        self.update_selected_count()

        logger.info(f"联系人同步完成，共同步 {len(self.contacts)} 个联系人")
        self.statusBar().showMessage(
            f"已从联系人管理页面同步 {len(self.send_contacts)} 个联系人"
        )
        ThemedMessageBoxHelper.show_information(
            self,
            "同步完成",
            f"成功从联系人管理页面同步 {len(self.send_contacts)} 个联系人\n\n"
            f"所有联系人已自动选中，可以开始编辑消息内容",
        )

    def verify_lists_sync(self):
        """验证待接收人列表和待发联系人列表是否同步"""
        # 验证列表同步
        print(f"  待发联系人列表数量: {len(self.send_contacts)}")
        print(f"  待接收人列表数量: {len(self.contacts)}")

        # 检查数量是否一致
        if len(self.send_contacts) != len(self.contacts):
            print(f"❌ ERROR: 列表数量不一致！")
            return False

        # 检查每个联系人是否一致
        for i, (send_contact, receive_contact) in enumerate(
            zip(self.send_contacts, self.contacts)
        ):
            if send_contact.wxid != receive_contact.wxid:
                print(f"❌ ERROR: 第{i+1}个联系人不一致！")
                print(f"  待发: {send_contact.name} ({send_contact.wxid})")
                print(f"  待接收: {receive_contact.name} ({receive_contact.wxid})")
                return False

        print(f"✅ SUCCESS: 列表同步验证通过")
        return True

    # 好友选择相关方法
    def select_all_friends(self):
        """全选好友"""
        self.friends_table.selectAll()
        self.statusBar().showMessage("已全选所有好友")

    def deselect_all_friends(self):
        """取消全选好友"""
        self.friends_table.clearSelection()
        self.statusBar().showMessage("已取消选择所有好友")

    def invert_friends_selection(self):
        """反选好友"""
        total_rows = self.friends_table.rowCount()
        selected_rows = set()

        # 获取当前选中的行
        for item in self.friends_table.selectedItems():
            selected_rows.add(item.row())

        # 清空选择
        self.friends_table.clearSelection()

        # 反选
        for row in range(total_rows):
            if row not in selected_rows:
                self.friends_table.selectRow(row)

        self.statusBar().showMessage("已反选好友")

    def add_friends_to_send_list(self):
        """添加选中的好友到待发列表"""
        selected_rows = set()
        for item in self.friends_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要添加的好友")
            return

        added_count = 0
        for row in selected_rows:
            # 获取好友信息
            name_item = self.friends_table.item(row, 0)
            wxid_item = self.friends_table.item(row, 1)
            remark_item = self.friends_table.item(row, 2)

            if name_item and wxid_item:
                name = name_item.text()
                wxid = wxid_item.text()
                remark = remark_item.text() if remark_item else ""

                # 检查是否已存在
                if not any(contact.wxid == wxid for contact in self.send_contacts):
                    from core.wechatferry_connector import Contact

                    contact = Contact(
                        wxid=wxid, name=name, remark=remark, type="friend"
                    )
                    self.send_contacts.append(contact)
                    added_count += 1

        self.update_send_list_table()
        self.statusBar().showMessage(f"已添加 {added_count} 个好友到待发列表")

    # 群聊选择相关方法
    def select_all_groups(self):
        """全选群聊"""
        self.groups_table.selectAll()
        self.statusBar().showMessage("已全选所有群聊")

    def deselect_all_groups(self):
        """取消全选群聊"""
        self.groups_table.clearSelection()
        self.statusBar().showMessage("已取消选择所有群聊")

    def invert_groups_selection(self):
        """反选群聊"""
        total_rows = self.groups_table.rowCount()
        selected_rows = set()

        # 获取当前选中的行
        for item in self.groups_table.selectedItems():
            selected_rows.add(item.row())

        # 清空选择
        self.groups_table.clearSelection()

        # 反选
        for row in range(total_rows):
            if row not in selected_rows:
                self.groups_table.selectRow(row)

        self.statusBar().showMessage("已反选群聊")

    def add_groups_to_send_list(self):
        """添加选中的群聊到待发列表"""
        selected_rows = set()
        for item in self.groups_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要添加的群聊")
            return

        added_count = 0
        for row in selected_rows:
            # 获取群聊信息
            name_item = self.groups_table.item(row, 0)
            wxid_item = self.groups_table.item(row, 1)
            remark_item = self.groups_table.item(row, 2)

            if name_item and wxid_item:
                name = name_item.text()
                wxid = wxid_item.text()
                remark = remark_item.text() if remark_item else ""

                # 检查是否已存在
                if not any(contact.wxid == wxid for contact in self.send_contacts):
                    from core.wechatferry_connector import Contact

                    contact = Contact(wxid=wxid, name=name, remark=remark, type="group")
                    self.send_contacts.append(contact)
                    added_count += 1

        self.update_send_list_table()
        self.statusBar().showMessage(f"已添加 {added_count} 个群聊到待发列表")

    # 待发列表相关方法
    def update_send_list_table(self):
        """更新待发联系人表格"""
        self.send_list_table.setRowCount(len(self.send_contacts))

        for i, contact in enumerate(self.send_contacts):
            self.send_list_table.setItem(i, 0, QTableWidgetItem(contact.name))
            self.send_list_table.setItem(i, 1, QTableWidgetItem(contact.wxid))
            self.send_list_table.setItem(
                i, 2, QTableWidgetItem("好友" if contact.type == "friend" else "群聊")
            )
            self.send_list_table.setItem(i, 3, QTableWidgetItem(contact.remark))

        # 更新统计
        friends_count = sum(1 for c in self.send_contacts if c.type == "friend")
        groups_count = sum(1 for c in self.send_contacts if c.type == "group")
        self.send_list_count_label.setText(
            f"待发: {len(self.send_contacts)} 个联系人 (好友: {friends_count}, 群聊: {groups_count})"
        )

    def clear_send_list(self):
        """清空当前页面的待发联系人列表"""
        if not self.send_contacts:
            ThemedMessageBoxHelper.show_information(
                self, "提示", "待发联系人列表已经为空"
            )
            return

        # 确认清空
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认清空",
            f"确定要清空当前页面的待发联系人列表吗？\n\n"
            f"当前列表中有 {len(self.send_contacts)} 个联系人\n\n"
            f"注意：此操作只清空当前页面的列表，不影响消息发送页面的接收人列表",
        )

        if not reply:
            return

        # 只清空当前页面的待发列表
        self.send_contacts.clear()

        # 更新待发列表显示
        self.update_send_list_table()

        self.statusBar().showMessage("已清空当前页面的待发联系人列表")

    def remove_selected_from_send_list(self):
        """从当前页面的待发列表中移除选中的联系人"""
        selected_rows = set()
        for item in self.send_list_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要移除的联系人")
            return

        # 确认移除
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认移除",
            f"确定要从当前页面的待发列表中移除选中的联系人吗？\n\n"
            f"选中了 {len(selected_rows)} 个联系人\n\n"
            f"注意：此操作只影响当前页面的待发列表，不影响消息发送页面的接收人列表",
        )

        if not reply:
            return

        # 获取要移除的联系人名称（用于日志）
        removed_names = []
        for row in sorted(selected_rows):
            if 0 <= row < len(self.send_contacts):
                removed_names.append(self.send_contacts[row].name)

        # 按行号倒序删除，避免索引变化
        for row in sorted(selected_rows, reverse=True):
            if 0 <= row < len(self.send_contacts):
                del self.send_contacts[row]

        # 更新待发列表显示
        self.update_send_list_table()

        logger.info(f"从待发列表移除联系人: {', '.join(removed_names)}")
        self.statusBar().showMessage(f"已从当前页面移除 {len(selected_rows)} 个联系人")

    def apply_to_send_tab(self):
        """应用待发列表到发送页面"""
        if not self.send_contacts:
            ThemedMessageBoxHelper.show_warning(
                self, "提示", "待发列表为空，请先添加联系人"
            )
            return

        # 应用到发送页面
        for i, contact in enumerate(self.send_contacts):
            print(
                f"  待发联系人 {i+1}: {contact.name} ({contact.wxid}) [{contact.type}]"
            )

        # 将待发联系人设置为发送页面的联系人列表（完全同步）
        self.contacts = self.send_contacts.copy()

        # 同步发送页面联系人

        # 更新发送页面的联系人列表显示
        self.update_contact_list()

        # 自动选中所有联系人
        self.friends_list.selectAll()
        self.groups_list.selectAll()

        # 更新选中计数
        self.update_selected_count()

        # 切换到发送页面
        self.tabs.setCurrentIndex(0)  # 发送页面是第一个标签页

        self.statusBar().showMessage(
            f"已应用 {len(self.send_contacts)} 个联系人到发送页面"
        )
        ThemedMessageBoxHelper.show_information(
            self,
            "成功",
            f"已将 {len(self.send_contacts)} 个联系人应用到发送页面\n"
            f"待接收人列表与待发联系人列表已完全同步\n"
            f"已自动切换到消息发送页面",
        )

        # 验证同步结果
        self.verify_lists_sync()

    def on_template_type_changed(self, button):
        """模板类型改变时的处理"""
        template_type_id = self.template_type_group.id(button)

        if template_type_id == 0:  # 富文本
            self.template_editor_stack.setCurrentIndex(0)
            self.template_file_path.setVisible(False)
            self.template_select_file_btn.setVisible(False)
        else:  # 传统类型
            self.template_editor_stack.setCurrentIndex(1)
            if template_type_id in [2, 3]:  # 图片或文件
                self.template_file_path.setVisible(True)
                self.template_select_file_btn.setVisible(True)
            else:  # 文本
                self.template_file_path.setVisible(False)
                self.template_select_file_btn.setVisible(False)

    def load_template_from_list(self, item):
        """从列表加载模板"""
        if not item:
            return

        template_name = item.text()

        try:
            # 从模板管理器加载模板
            template_data = self.template.load_template(template_name)

            if template_data:
                # 设置模板名称
                self.template_name_edit.setText(template_name)

                # 设置模板类型
                template_type = template_data.get("type", "rich_text")
                if template_type == "rich_text":
                    self.template_type_rich_text.setChecked(True)
                    self.template_editor_stack.setCurrentIndex(0)
                    # 加载富文本内容
                    content = template_data.get("content", {})
                    self.template_rich_text_editor.load_template_content(content)
                else:
                    # 传统类型
                    if template_type == "text":
                        self.template_type_text.setChecked(True)
                    elif template_type == "image":
                        self.template_type_image.setChecked(True)
                    elif template_type == "file":
                        self.template_type_file.setChecked(True)

                    self.template_editor_stack.setCurrentIndex(1)
                    self.template_content_edit.setText(template_data.get("content", ""))
                    self.template_file_path.setText(template_data.get("file_path", ""))

                # 触发类型切换处理
                self.on_template_type_changed(self.template_type_group.checkedButton())

                logger.info(f"已加载模板: {template_name}")
                self.statusBar().showMessage(f"已加载模板: {template_name}")
            else:
                ThemedMessageBoxHelper.show_warning(
                    self, "加载失败", f"无法加载模板: {template_name}"
                )

        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "加载失败", f"加载模板时出错:\n{e}")

    def add_new_template(self):
        """添加新模板"""
        # 清空编辑区域
        self.template_name_edit.clear()
        self.template_type_rich_text.setChecked(True)
        self.template_editor_stack.setCurrentIndex(0)
        self.template_rich_text_editor.clear_content()
        self.template_content_edit.clear()
        self.template_file_path.clear()

        # 触发类型切换处理
        self.on_template_type_changed(self.template_type_rich_text)

        logger.info("创建新模板")
        self.statusBar().showMessage("创建新模板")

    def delete_template(self):
        """删除模板"""
        current_item = self.templates_list.currentItem()
        if not current_item:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请先选择要删除的模板")
            return

        template_name = current_item.text()

        # 确认删除
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要删除模板 '{template_name}' 吗？\n\n此操作不可撤销！",
        )

        if reply:
            try:
                # 从模板管理器删除
                success = self.template.delete_template(template_name)

                if success:
                    # 从列表中移除
                    self.templates_list.takeItem(self.templates_list.row(current_item))

                    # 同时更新消息发送页面的模板列表
                    self.refresh_send_page_template_list()

                    # 清空编辑区域
                    self.add_new_template()

                    logger.info(f"已删除模板: {template_name}")
                    self.statusBar().showMessage(f"已删除模板: {template_name}")
                else:
                    ThemedMessageBoxHelper.show_warning(
                        self, "删除失败", f"无法删除模板: {template_name}"
                    )

            except Exception as e:
                logger.error(f"删除模板失败: {e}")
                ThemedMessageBoxHelper.show_error(
                    self, "删除失败", f"删除模板时出错:\n{e}"
                )

    def select_template_file(self):
        """选择模板文件"""
        template_type_id = self.template_type_group.checkedId()

        if template_type_id == 2:  # 图片
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择图片文件",
                "",
                "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp *.webp *.tiff);;所有文件 (*)",
            )
        elif template_type_id == 3:  # 文件
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择文件", "", "所有文件 (*)"
            )
        else:
            return

        if file_path:
            self.template_file_path.setText(file_path)
            logger.info(f"选择模板文件: {file_path}")

    def save_template(self):
        """保存模板"""
        template_name = self.template_name_edit.text().strip()
        if not template_name:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请输入模板名称")
            return

        try:
            template_type_id = self.template_type_group.checkedId()

            if template_type_id == 0:  # 富文本
                if not self.template_rich_text_editor.has_content():
                    ThemedMessageBoxHelper.show_warning(
                        self, "提示", "请输入富文本模板内容"
                    )
                    return

                # 检查富文本编辑器是否有必要的方法
                if not hasattr(self.template_rich_text_editor, "get_template_data"):
                    logger.error("富文本编辑器缺少 get_template_data 方法")
                    ThemedMessageBoxHelper.show_error(
                        self, "错误", "富文本编辑器缺少必要的方法"
                    )
                    return

                if not hasattr(self.template_rich_text_editor, "get_content_summary"):
                    logger.error("富文本编辑器缺少 get_content_summary 方法")
                    ThemedMessageBoxHelper.show_error(
                        self, "错误", "富文本编辑器缺少必要的方法"
                    )
                    return

                try:
                    template_content = (
                        self.template_rich_text_editor.get_template_data()
                    )
                    template_summary = (
                        self.template_rich_text_editor.get_content_summary()
                    )

                    template_data = {
                        "type": "rich_text",
                        "content": template_content,
                        "description": f"富文本模板 - {template_summary}",
                    }
                except Exception as method_error:
                    logger.error(f"调用富文本编辑器方法失败: {method_error}")
                    ThemedMessageBoxHelper.show_error(
                        self, "错误", f"获取富文本模板数据失败:\n{method_error}"
                    )
                    return
            else:
                # 传统类型
                content = self.template_content_edit.toPlainText().strip()
                file_path = self.template_file_path.text().strip()

                if template_type_id == 1:  # 文本
                    if not content:
                        ThemedMessageBoxHelper.show_warning(
                            self, "提示", "请输入文本模板内容"
                        )
                        return
                    template_data = {
                        "type": "text",
                        "content": content,
                        "description": f"文本模板 - {content[:50]}{'...' if len(content) > 50 else ''}",
                    }
                elif template_type_id == 2:  # 图片
                    if not file_path:
                        ThemedMessageBoxHelper.show_warning(
                            self, "提示", "请选择图片文件"
                        )
                        return
                    template_data = {
                        "type": "image",
                        "content": content,
                        "file_path": file_path,
                        "description": f"图片模板 - {file_path.split('/')[-1]}",
                    }
                elif template_type_id == 3:  # 文件
                    if not file_path:
                        ThemedMessageBoxHelper.show_warning(self, "提示", "请选择文件")
                        return
                    template_data = {
                        "type": "file",
                        "content": content,
                        "file_path": file_path,
                        "description": f"文件模板 - {file_path.split('/')[-1]}",
                    }

            # 保存模板
            success = self.template.save_template(template_name, template_data)

            if success:
                # 更新模板列表
                self.refresh_template_list()
                # 同时更新消息发送页面的模板列表
                self.refresh_send_page_template_list()

                # 自动跳转到新建模板状态
                self.add_new_template()

                logger.info(f"已保存模板: {template_name}")
                self.statusBar().showMessage(f"已保存模板: {template_name}")
                ThemedMessageBoxHelper.show_information(
                    self, "保存成功", f"模板 '{template_name}' 保存成功！"
                )
            else:
                ThemedMessageBoxHelper.show_warning(
                    self, "保存失败", f"无法保存模板: {template_name}"
                )

        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "保存失败", f"保存模板时出错:\n{e}")

    def refresh_template_list(self):
        """刷新模板列表"""
        try:
            self.templates_list.clear()
            template_names = self.template.get_template_list()

            for name in template_names:
                self.templates_list.addItem(name)

            logger.info(f"已刷新模板列表，共 {len(template_names)} 个模板")

        except Exception as e:
            logger.error(f"刷新模板列表失败: {e}")

    def refresh_send_page_template_list(self):
        """刷新消息发送页面的模板列表"""
        try:
            # 清空现有选项
            self.template_combo.clear()

            # 添加默认选项
            self.template_combo.addItem("无")

            # 获取模板列表
            template_names = self.template.get_template_list()

            # 添加模板到下拉框
            for template_name in template_names:
                self.template_combo.addItem(template_name)

            logger.info(f"已刷新消息发送页面模板列表，共 {len(template_names)} 个模板")

        except Exception as e:
            logger.error(f"刷新消息发送页面模板列表失败: {e}")

    def refresh_log(self):
        """刷新日志"""
        try:
            self.log_status_label.setText("正在刷新日志...")

            # 获取正确的日志目录路径
            try:
                from utils.path_manager import path_manager
                logs_dir = path_manager.app_data_dir / "logs"
            except ImportError:
                logs_dir = Path(__file__).parent.parent / "logs"

            # 定义要读取的日志文件名列表
            log_file_names = [
                # 启动和主程序日志
                "main.log",
                "startup.log",
                # 连接器日志
                "http_api_connector.log",
                "wechatferry_connector.log",
                "ui_automation_connector.log",
                "winapi_connector.log",
                # 任务管理日志
                "timing_sender.log",
                "loop_sender.log",
                "timed_sender.log",
                "send_monitor.log",
                "optimized_sender.log",
                # 注入器日志
                "auto_injector.log",
                "injector_tool.log",
                "smart_injector.log",
                "wechat_injector.log",
                "injector_adapter.log",
                "ctypes_injector.log",
                "cmdline_injector.log",
                # UI相关日志
                "ui.log",
                "timing_send_page.log",
                "loop_send_page.log",
                "task_status_page.log",
                "rich_text_editor.log",
                "custom_main_window.log",
                "custom_title_bar.log",
                "ui_optimizer.log",
                # 管理器日志
                "group_manager.log",
                "message_template.log",
                "config_manager.log",
                "group_config_manager.log",
                "version_manager.log",
                # 安全和权限日志
                "risk_control.log",
                "security.log",
                "admin_privileges.log",
                "simple_admin.log",
                # 性能和优化日志
                "performance.log",
                "performance_optimizer.log",
                # 对话框和组件日志
                "group_card.log",
                "group_detail_dialog.log",
                "group_details_dialog.log",
                "contact_selector_dialog.log",
                "member_selection_dialog.log",
                "loop_cycle_widget.log",
                "timing_cycle_widget.log",
                "performance_panel.log",
                # 工具和验证日志
                "validators.log",
                "file_handler.log",
                "dll_downloader.log",
                "wxhelper_downloader.log",
                # 系统和错误日志
                "error.log",
                "debug.log",
                "console_error.log",
                "console_output.log",
                # 兼容性日志
                "app.log",
            ]

            # 构建完整的日志文件路径列表
            log_files = [logs_dir / filename for filename in log_file_names]

            all_log_content = []
            existing_files = []
            total_size = 0

            # 读取所有存在的日志文件
            for log_file_path in log_files:
                if log_file_path.exists():
                    try:
                        with open(log_file_path, "r", encoding="utf-8") as f:
                            content = f.read().strip()
                            if content:  # 只添加非空内容
                                all_log_content.append(content)
                                existing_files.append(str(log_file_path))
                                total_size += log_file_path.stat().st_size
                    except Exception as e:
                        logger.warning(f"读取日志文件 {log_file_path} 失败: {e}")

            if all_log_content:
                # 合并所有日志内容
                combined_content = "\n".join(all_log_content)

                # 解析日志记录
                self.parse_log_content(combined_content)

                # 应用过滤器
                self.filter_logs()

                # 更新文件信息
                file_info = (
                    f"日志文件: {len(existing_files)} 个文件 ({total_size} bytes)"
                )
                if existing_files:
                    # 显示最新修改的文件时间
                    latest_time = max(os.path.getmtime(f) for f in existing_files)
                    latest_time_str = datetime.fromtimestamp(latest_time).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    file_info += f", 最新修改: {latest_time_str}"

                self.log_file_label.setText(file_info)

                logger.info(f"日志刷新完成，读取了 {len(existing_files)} 个文件")
                self.log_status_label.setText(
                    f"日志刷新完成 ({len(existing_files)} 个文件)"
                )
            else:
                self.log_text.setText("未找到日志文件")
                self.log_stats_label.setText("总记录: 0 条")
                self.log_error_count_label.setText("错误: 0 条")
                self.log_warning_count_label.setText("警告: 0 条")
                self.log_info_count_label.setText("信息: 0 条")
                # 已移除调试日志统计
                self.log_status_label.setText("未找到日志文件")

        except Exception as e:
            logger.error(f"刷新日志失败: {e}")
            self.log_text.setText(f"刷新日志失败: {e}")
            self.log_status_label.setText("刷新失败")

    def parse_log_content(self, content: str):
        """解析日志内容"""
        try:
            self.all_log_records = []
            lines = content.strip().split("\n")

            for line in lines:
                if line.strip():
                    # 解析日志行格式: 时间 - 模块 - 级别 - 消息
                    parts = line.split(" - ", 3)
                    if len(parts) >= 4:
                        timestamp = parts[0]
                        module = parts[1]
                        level = parts[2]
                        message = parts[3]

                        # 尝试解析时间戳用于排序
                        try:
                            from datetime import datetime

                            parsed_time = datetime.strptime(
                                timestamp, "%Y-%m-%d %H:%M:%S"
                            )
                        except:
                            parsed_time = None

                        self.all_log_records.append(
                            {
                                "timestamp": timestamp,
                                "module": module,
                                "level": level,
                                "message": message,
                                "raw": line,
                                "parsed_time": parsed_time,
                            }
                        )
                    else:
                        # 处理格式不标准的日志行
                        self.all_log_records.append(
                            {
                                "timestamp": "",
                                "module": "unknown",
                                "level": "INFO",
                                "message": line,
                                "raw": line,
                                "parsed_time": None,
                            }
                        )

            # 按时间排序日志记录
            self.all_log_records.sort(key=lambda x: x["parsed_time"] or datetime.min)

            logger.debug(f"解析了 {len(self.all_log_records)} 条日志记录")

        except Exception as e:
            logger.error(f"解析日志内容失败: {e}")
            self.all_log_records = []

    def filter_logs(self):
        """过滤日志"""
        try:
            selected_level = self.log_level_combo.currentText()
            selected_module = self.log_module_combo.currentText()
            search_text = self.log_search_input.text().strip().lower()

            # 定义日志级别优先级
            level_priority = {"DEBUG": 0, "INFO": 1, "WARNING": 2, "ERROR": 3}

            self.filtered_log_records = []

            for record in self.all_log_records:
                # 级别过滤
                if selected_level != "全部":
                    record_priority = level_priority.get(record["level"], 1)
                    selected_priority = level_priority.get(selected_level, 1)
                    if record_priority < selected_priority:
                        continue

                # 模块过滤
                if selected_module != "全部":
                    if selected_module.lower() not in record["module"].lower():
                        continue

                # 搜索过滤
                if search_text:
                    search_target = f"{record['timestamp']} {record['module']} {record['level']} {record['message']}".lower()
                    if search_text not in search_target:
                        continue

                self.filtered_log_records.append(record)

            # 更新显示和统计
            self.update_log_display()
            self.update_log_statistics()

        except Exception as e:
            logger.error(f"过滤日志失败: {e}")

    def update_log_display(self):
        """更新日志显示"""
        try:
            # 构建显示文本
            display_lines = []
            for record in self.filtered_log_records[-1000:]:  # 只显示最近1000条
                # 格式化显示
                level_color = {
                    "DEBUG": "#888888",
                    "INFO": "#000000",
                    "WARNING": "#FF8C00",
                    "ERROR": "#FF0000",
                }.get(record["level"], "#000000")

                # 根据显示选项格式化
                if (
                    self.show_timestamp_check.isChecked()
                    and self.show_module_check.isChecked()
                ):
                    formatted_line = (
                        f"<span style='color: {level_color}'>{record['raw']}</span>"
                    )
                elif self.show_timestamp_check.isChecked():
                    formatted_line = f"<span style='color: {level_color}'>{record['timestamp']} - {record['level']} - {record['message']}</span>"
                elif self.show_module_check.isChecked():
                    formatted_line = f"<span style='color: {level_color}'>{record['module']} - {record['level']} - {record['message']}</span>"
                else:
                    formatted_line = f"<span style='color: {level_color}'>{record['level']} - {record['message']}</span>"

                # 高亮错误
                if (
                    self.highlight_errors_check.isChecked()
                    and record["level"] == "ERROR"
                ):
                    formatted_line = f"<span style='color: {level_color}; background-color: #FFE6E6; font-weight: bold;'>{record['raw']}</span>"

                display_lines.append(formatted_line)

            # 更新文本显示
            html_content = "<br>".join(display_lines)
            self.log_text.setHtml(html_content)

            # 自动滚动到底部
            if self.auto_scroll_check.isChecked():
                cursor = self.log_text.textCursor()
                cursor.movePosition(QTextCursor.MoveOperation.End)
                self.log_text.setTextCursor(cursor)

            # 更新状态
            self.log_status_label.setText(
                f"显示 {len(self.filtered_log_records)} 条记录"
            )

        except Exception as e:
            logger.error(f"更新日志显示失败: {e}")

    def update_log_statistics(self):
        """更新日志统计"""
        try:
            error_count = sum(
                1 for r in self.filtered_log_records if r["level"] == "ERROR"
            )
            warning_count = sum(
                1 for r in self.filtered_log_records if r["level"] == "WARNING"
            )
            info_count = sum(
                1 for r in self.filtered_log_records if r["level"] == "INFO"
            )
            # 已移除调试级别统计

            self.log_stats_label.setText(f"总记录: {len(self.filtered_log_records)} 条")
            self.log_error_count_label.setText(f"错误: {error_count} 条")
            self.log_warning_count_label.setText(f"警告: {warning_count} 条")
            self.log_info_count_label.setText(f"信息: {info_count} 条")
            # 已移除调试日志统计

        except Exception as e:
            logger.error(f"更新日志统计失败: {e}")

    def show_log_context_menu(self, position):
        """显示日志右键菜单"""
        try:
            menu = QMenu(self)

            # 复制选中文本
            copy_action = menu.addAction("复制选中文本")
            copy_action.triggered.connect(self.copy_selected_log)

            # 复制全部
            copy_all_action = menu.addAction("复制全部")
            copy_all_action.triggered.connect(self.copy_all_log)

            menu.addSeparator()

            # 查找选中文本
            find_action = menu.addAction("查找选中文本")
            find_action.triggered.connect(self.find_selected_text)

            # 跳转到顶部
            goto_top_action = menu.addAction("跳转到顶部")
            goto_top_action.triggered.connect(self.goto_log_top)

            # 跳转到底部
            goto_bottom_action = menu.addAction("跳转到底部")
            goto_bottom_action.triggered.connect(self.goto_log_bottom)

            menu.addSeparator()

            # 刷新日志
            refresh_action = menu.addAction("刷新日志")
            refresh_action.triggered.connect(self.refresh_log)

            # 清空显示
            clear_action = menu.addAction("清空显示")
            clear_action.triggered.connect(self.clear_log)

            # 导出日志
            export_action = menu.addAction("导出日志")
            export_action.triggered.connect(self.export_log)

            menu.exec(self.log_text.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示日志右键菜单失败: {e}")

    def copy_selected_log(self):
        """复制选中的日志"""
        try:
            cursor = self.log_text.textCursor()
            if cursor.hasSelection():
                selected_text = cursor.selectedText()
                clipboard = QApplication.clipboard()
                clipboard.setText(selected_text)
                self.log_status_label.setText("已复制选中文本")
            else:
                ThemedMessageBoxHelper.show_information(
                    self, "提示", "请先选择要复制的文本"
                )
        except Exception as e:
            logger.error(f"复制选中日志失败: {e}")

    def copy_all_log(self):
        """复制全部日志"""
        try:
            all_text = self.log_text.toPlainText()
            clipboard = QApplication.clipboard()
            clipboard.setText(all_text)
            self.log_status_label.setText("已复制全部日志")
        except Exception as e:
            logger.error(f"复制全部日志失败: {e}")

    def find_next_log(self):
        """查找下一个匹配项"""
        try:
            search_text = self.log_search_input.text().strip()
            if not search_text:
                ThemedMessageBoxHelper.show_information(
                    self, "提示", "请在搜索框中输入要查找的内容"
                )
                return

            # 获取当前光标位置
            cursor = self.log_text.textCursor()
            current_pos = cursor.position()

            # 查找下一个匹配项
            found = self.log_text.find(
                search_text, QTextDocument.FindFlag.FindCaseSensitive
            )

            if found:
                self.current_search_index += 1
                self.log_status_label.setText(
                    f"找到第 {self.current_search_index} 个匹配项"
                )
            else:
                # 如果没找到，从头开始查找
                cursor = self.log_text.textCursor()
                cursor.setPosition(0)
                self.log_text.setTextCursor(cursor)

                found = self.log_text.find(
                    search_text, QTextDocument.FindFlag.FindCaseSensitive
                )
                if found:
                    self.current_search_index = 1
                    self.log_status_label.setText(
                        f"找到第 {self.current_search_index} 个匹配项"
                    )
                else:
                    self.log_status_label.setText("未找到匹配项")
                    ThemedMessageBoxHelper.show_information(
                        self, "查找结果", f"未找到包含 '{search_text}' 的内容"
                    )

        except Exception as e:
            logger.error(f"查找下一个日志失败: {e}")

    def find_selected_text(self):
        """查找选中的文本"""
        try:
            cursor = self.log_text.textCursor()
            if cursor.hasSelection():
                selected_text = cursor.selectedText()
                self.log_search_input.setText(selected_text)
                self.find_next_log()
            else:
                ThemedMessageBoxHelper.show_information(
                    self, "提示", "请先选择要查找的文本"
                )
        except Exception as e:
            logger.error(f"查找选中文本失败: {e}")

    def goto_log_top(self):
        """跳转到日志顶部"""
        try:
            cursor = self.log_text.textCursor()
            cursor.setPosition(0)
            self.log_text.setTextCursor(cursor)
            self.log_status_label.setText("已跳转到顶部")
        except Exception as e:
            logger.error(f"跳转到日志顶部失败: {e}")

    def goto_log_bottom(self):
        """跳转到日志底部"""
        try:
            cursor = self.log_text.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            self.log_text.setTextCursor(cursor)
            self.log_status_label.setText("已跳转到底部")
        except Exception as e:
            logger.error(f"跳转到日志底部失败: {e}")

    def clear_log(self):
        """清空日志显示"""
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认清空",
            "确定要清空日志显示吗？\n\n注意：这只会清空显示，不会删除日志文件",
        )

        if reply:
            self.log_text.clear()
            self.all_log_records = []
            self.filtered_log_records = []
            self.current_search_index = -1

            # 重置统计
            self.log_stats_label.setText("总记录: 0 条")
            self.log_error_count_label.setText("错误: 0 条")
            self.log_warning_count_label.setText("警告: 0 条")
            self.log_info_count_label.setText("信息: 0 条")
            # 已移除调试日志统计
            self.log_status_label.setText("已清空显示")

            logger.info("清空日志显示")

    def clear_log_files(self):
        """清空所有日志文件"""
        try:
            # 确认对话框
            reply = ThemedMessageBoxHelper.show_question(
                self,
                "⚠️ 危险操作确认",
                "确定要清空所有日志文件吗？\n\n"
                "⚠️ 警告：此操作将永久删除所有日志记录！\n"
                "• 包括启动日志、连接日志、任务日志、运行日志等\n"
                "• 此操作不可撤销\n"
                "• 建议在清空前先导出重要日志\n\n"
                "是否继续？",
            )

            if not reply:
                return

            # 二次确认
            reply2 = ThemedMessageBoxHelper.show_question(
                self,
                "🔴 最终确认",
                "这是最后一次确认！\n\n"
                "确定要清空所有日志文件吗？\n"
                "此操作将立即生效且无法撤销！",
            )

            if not reply2:
                return

            self.log_status_label.setText("正在清空日志文件...")

            # 获取正确的日志目录路径
            try:
                from utils.path_manager import path_manager
                logs_dir = path_manager.app_data_dir / "logs"
            except ImportError:
                # 备用方案
                logs_dir = Path(__file__).parent.parent / "logs"

            if not logs_dir.exists():
                ThemedMessageBoxHelper.show_information(self, "提示", "日志目录不存在")
                return

            cleared_count = 0
            failed_count = 0
            total_size_before = 0

            # 遍历logs目录下的所有文件
            for filename in os.listdir(str(logs_dir)):
                file_path = logs_dir / filename

                # 只处理日志文件
                if file_path.is_file() and (
                    filename.endswith(".log")
                    or filename.endswith(".txt")
                    or filename == "app.log"
                ):
                    try:
                        # 记录文件大小
                        if file_path.exists():
                            total_size_before += file_path.stat().st_size

                        # 清空文件内容（保留文件）
                        with open(file_path, "w", encoding="utf-8") as f:
                            f.write("")

                        cleared_count += 1
                        logger.info(f"已清空日志文件: {file_path}")

                    except Exception as file_error:
                        failed_count += 1
                        logger.error(f"清空日志文件失败 {file_path}: {file_error}")

            # 清空当前显示
            self.log_text.clear()
            self.all_log_records = []
            self.filtered_log_records = []
            self.current_search_index = -1

            # 重置统计
            self.log_stats_label.setText("总记录: 0 条")
            self.log_error_count_label.setText("错误: 0 条")
            self.log_warning_count_label.setText("警告: 0 条")
            self.log_info_count_label.setText("信息: 0 条")
            # 已移除调试日志统计

            # 显示结果
            size_mb = total_size_before / (1024 * 1024)
            result_message = (
                f"日志文件清空完成！\n\n"
                f"✅ 成功清空: {cleared_count} 个文件\n"
                f"❌ 清空失败: {failed_count} 个文件\n"
                f"💾 释放空间: {size_mb:.2f} MB\n\n"
                f"所有日志记录已被永久删除。"
            )

            ThemedMessageBoxHelper.show_information(self, "清空完成", result_message)

            self.log_status_label.setText(f"已清空 {cleared_count} 个日志文件")
            self.log_file_label.setText("日志文件: 已清空")

            logger.info(
                f"日志文件清空完成 - 成功: {cleared_count}, 失败: {failed_count}"
            )

        except Exception as e:
            logger.error(f"清空日志文件失败: {e}")
            ThemedMessageBoxHelper.show_error(
                self, "清空失败", f"清空日志文件时出错:\n{e}"
            )
            self.log_status_label.setText("清空失败")

    def export_log(self):
        """导出日志"""
        try:
            if not self.filtered_log_records:
                ThemedMessageBoxHelper.show_information(
                    self, "提示", "没有日志数据可导出"
                )
                return

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出日志",
                f"运行日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)",
            )

            if file_path:
                if file_path.endswith(".csv"):
                    # 导出为CSV格式
                    with open(file_path, "w", encoding="utf-8", newline="") as f:
                        import csv

                        writer = csv.writer(f)
                        writer.writerow(["时间戳", "模块", "级别", "消息"])

                        for record in self.filtered_log_records:
                            writer.writerow(
                                [
                                    record["timestamp"],
                                    record["module"],
                                    record["level"],
                                    record["message"],
                                ]
                            )
                else:
                    # 导出为文本格式
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(f"# Meet space 微信群发助手运行日志\n")
                        f.write(
                            f"# 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        )
                        f.write(f"# 日志级别: {self.log_level_combo.currentText()}\n")
                        f.write(f"# 模块过滤: {self.log_module_combo.currentText()}\n")
                        f.write(f"# 搜索关键词: {self.log_search_input.text()}\n")
                        f.write(f"# 总计: {len(self.filtered_log_records)} 条记录\n\n")

                        for record in self.filtered_log_records:
                            f.write(record["raw"] + "\n")

                logger.info(f"日志已导出到: {file_path}")
                self.log_status_label.setText(
                    f"已导出到: {os.path.basename(file_path)}"
                )
                ThemedMessageBoxHelper.show_information(
                    self,
                    "导出成功",
                    f"日志已成功导出到:\n{file_path}\n\n共导出 {len(self.filtered_log_records)} 条记录",
                )

        except Exception as e:
            logger.error(f"导出日志失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "导出失败", f"导出日志时出错:\n{e}")

    def save_settings(self):
        """保存设置"""
        try:
            # 连接设置
            if hasattr(self, "auto_start_check"):
                self.config.auto_start = self.auto_start_check.isChecked()
            if hasattr(self, "startup_delay_spin"):
                self.config.startup_delay = self.startup_delay_spin.value()
            if hasattr(self, "login_timeout_spin"):
                self.config.login_timeout = self.login_timeout_spin.value()

            # 发送设置
            if hasattr(self, "daily_limit_spin"):
                self.config.daily_send_limit = self.daily_limit_spin.value()
            if hasattr(self, "hourly_limit_spin"):
                self.config.hourly_send_limit = self.hourly_limit_spin.value()
            if hasattr(self, "enable_risk_control_check"):
                self.config.enable_risk_control = (
                    self.enable_risk_control_check.isChecked()
                )

            # 重试设置
            if hasattr(self, "max_retries_spin"):
                self.config.max_retries = self.max_retries_spin.value()
            if hasattr(self, "retry_delay_spin"):
                self.config.retry_delay = self.retry_delay_spin.value()

            # 界面设置
            if hasattr(self, "theme_combo"):
                self.config.theme = self.theme_combo.currentText()
            if hasattr(self, "font_size_combo"):
                self.config.font_size = self.font_size_combo.currentText()
            if hasattr(self, "minimize_to_tray_check"):
                self.config.minimize_to_tray = self.minimize_to_tray_check.isChecked()
            if hasattr(self, "remember_window_state_check"):
                self.config.remember_window_state = (
                    self.remember_window_state_check.isChecked()
                )

            # 高级设置
            if hasattr(self, "auto_backup_check"):
                self.config.auto_backup = self.auto_backup_check.isChecked()
            if hasattr(self, "check_updates_check"):
                self.config.check_updates = self.check_updates_check.isChecked()

            # 保存到文件
            self.config.save_to_file()

            ThemedMessageBoxHelper.show_information(
                self, "成功", "设置已保存\n\n部分设置需要重启程序后生效"
            )
            self.statusBar().showMessage("设置已保存")
            logger.info("用户设置已保存")

        except Exception as e:
            error_msg = f"保存设置失败: {e}"
            ThemedMessageBoxHelper.show_warning(self, "错误", error_msg)
            logger.error(error_msg)

    def reset_settings(self):
        """重置设置"""
        reply = ThemedMessageBoxHelper.show_question(
            self, "确认重置", "确定要重置所有设置到默认值吗？\n\n此操作不可撤销！"
        )

        if reply:
            try:
                # 重置配置到默认值
                self.config.reset_to_defaults()

                # 更新界面控件
                self.load_settings_to_ui()

                ThemedMessageBoxHelper.show_information(
                    self, "重置成功", "设置已重置到默认值"
                )
                logger.info("设置已重置到默认值")

            except Exception as e:
                logger.error(f"重置设置失败: {e}")
                ThemedMessageBoxHelper.show_error(
                    self, "重置失败", f"重置设置时出错:\n{e}"
                )

    def on_theme_changed(self, theme_name):
        """主题改变时的处理"""
        try:
            logger.info(f"切换主题: {theme_name}")

            # 保存主题设置
            self.config.theme = theme_name
            self.config.save()

            # 获取当前字体大小设置
            current_font_size = getattr(self.config, "font_size", "中")

            # 先确保字体大小正确
            modern_theme_manager.setup_dynamic_font(
                QApplication.instance(), current_font_size
            )

            # 然后应用主题
            modern_theme_manager.set_theme(QApplication.instance(), theme_name)

        except Exception as e:
            logger.error(f"切换主题失败: {e}")

    def load_settings_to_ui(self):
        """加载设置到UI控件"""
        try:
            # 更新主题选择
            if hasattr(self, "theme_combo"):
                self.theme_combo.setCurrentText(
                    getattr(self.config, "theme", "默认主题")
                )

            # 更新字体大小选择
            if hasattr(self, "font_size_combo"):
                self.font_size_combo.setCurrentText(
                    getattr(self.config, "font_size", "中")
                )

            # 更新复选框状态
            if hasattr(self, "minimize_to_tray_check"):
                self.minimize_to_tray_check.setChecked(
                    getattr(self.config, "minimize_to_tray", False)
                )

            if hasattr(self, "remember_window_state_check"):
                self.remember_window_state_check.setChecked(
                    getattr(self.config, "remember_window_state", True)
                )

            if hasattr(self, "auto_backup_check"):
                self.auto_backup_check.setChecked(
                    getattr(self.config, "auto_backup", True)
                )

            logger.info("设置已加载到UI控件")

        except Exception as e:
            logger.error(f"加载设置到UI失败: {e}")

    def notify_theme_changed(self, theme_name):
        """通知所有子页面主题已更改"""
        try:
            # 获取当前主题样式
            current_style = self.styleSheet()

            # 递归应用样式到所有子组件
            self.apply_style_recursively(self, current_style)

            logger.info(f"已通知所有页面更新主题: {theme_name}")

        except Exception as e:
            logger.error(f"通知子页面主题更改失败: {e}")

    def apply_style_recursively(self, widget, style_sheet):
        """递归地将样式应用到所有子组件"""
        try:
            # 应用样式到当前组件（主窗口）
            if widget == self:
                widget.setStyleSheet(style_sheet)

            # 特别处理标签页
            if hasattr(self, "tabs") and self.tabs:
                for i in range(self.tabs.count()):
                    tab_widget = self.tabs.widget(i)
                    if tab_widget:
                        tab_widget.setStyleSheet(style_sheet)
                        # 递归应用到标签页的子组件
                        for child in tab_widget.findChildren(QWidget):
                            if self.should_override_style(child):
                                child.setStyleSheet("")  # 清除自定义样式，让主题生效

            logger.debug(f"已递归应用样式到所有子组件")

        except Exception as e:
            logger.debug(f"应用样式到组件失败: {e}")

    def should_override_style(self, widget):
        """判断是否应该覆盖组件的样式"""
        # 对于某些特殊组件，我们总是覆盖其样式
        widget_class_name = widget.__class__.__name__
        override_classes = [
            "QLabel",
            "QPushButton",
            "QLineEdit",
            "QTextEdit",
            "QComboBox",
            "QSpinBox",
            "QGroupBox",
            "QTabWidget",
            "QListWidget",
            "QTableWidget",
            "QTreeWidget",
        ]
        return widget_class_name in override_classes

    def on_font_size_changed(self, font_size):
        """字体大小改变时的处理"""
        try:
            logger.info(f"切换字体大小: {font_size}")

            # 保存字体大小设置
            self.config.font_size = font_size
            self.config.save()

            # 使用主题管理器更新字体大小（保持当前主题）
            modern_theme_manager.update_font_size(QApplication.instance(), font_size)

            self.statusBar().showMessage(f"已切换字体大小为{font_size}")

        except Exception as e:
            logger.error(f"切换字体大小失败: {e}")

    # 旧的主题方法已移除，现在使用主题管理器统一管理

    def on_contacts_search_changed(self, text: str):
        """联系人搜索文本变化处理"""
        try:
            search_text = text.strip().lower()

            if not search_text:
                # 搜索为空，显示所有联系人
                self._show_all_contacts()
            else:
                # 执行模糊搜索
                self._filter_contacts(search_text)

        except Exception as e:
            logger.error(f"联系人搜索失败: {e}")

    def clear_contacts_search(self):
        """清空联系人搜索"""
        try:
            self.contacts_search_input.clear()
            self._show_all_contacts()
        except Exception as e:
            logger.error(f"清空联系人搜索失败: {e}")

    def _filter_contacts(self, search_text: str):
        """根据搜索文本过滤联系人"""
        try:
            if not self.contacts:
                return

            # 分类并过滤联系人
            filtered_friends = []
            filtered_groups = []

            for contact in self.contacts:
                # 模糊搜索：检查姓名、微信ID、备注
                if self._contact_matches_search(contact, search_text):
                    if contact.type == "group" or contact.wxid.endswith("@chatroom"):
                        filtered_groups.append(contact)
                    else:
                        filtered_friends.append(contact)

            # 更新表格显示
            self._update_tables_with_data(filtered_friends, filtered_groups)

            # 更新计数显示
            self._update_contacts_count(len(filtered_friends), len(filtered_groups))

            logger.debug(
                f"搜索结果: 好友 {len(filtered_friends)} 个, 群聊 {len(filtered_groups)} 个"
            )

        except Exception as e:
            logger.error(f"过滤联系人失败: {e}")

    def _contact_matches_search(self, contact, search_text: str) -> bool:
        """检查联系人是否匹配搜索条件"""
        try:
            # 检查姓名
            if search_text in contact.name.lower():
                return True

            # 检查微信ID
            if search_text in contact.wxid.lower():
                return True

            # 检查备注
            if hasattr(contact, "remark") and contact.remark:
                if search_text in contact.remark.lower():
                    return True

            # 检查拼音（如果有）
            if hasattr(contact, "pinyin") and contact.pinyin:
                if search_text in contact.pinyin.lower():
                    return True

            return False

        except Exception as e:
            logger.error(f"联系人匹配检查失败: {e}")
            return False

    def _show_all_contacts(self):
        """显示所有联系人"""
        try:
            if not self.contacts:
                return

            # 分类所有联系人
            friends = []
            groups = []

            for contact in self.contacts:
                if contact.type == "group" or contact.wxid.endswith("@chatroom"):
                    groups.append(contact)
                else:
                    friends.append(contact)

            # 更新表格显示
            self._update_tables_with_data(friends, groups)

            # 更新计数显示
            self._update_contacts_count(len(friends), len(groups))

        except Exception as e:
            logger.error(f"显示所有联系人失败: {e}")

    def _update_contacts_count(self, friends_count: int, groups_count: int):
        """更新联系人计数显示"""
        try:
            if hasattr(self, "friends_count_label"):
                self.friends_count_label.setText(f"好友: {friends_count} 个")

            if hasattr(self, "groups_count_label"):
                self.groups_count_label.setText(f"群聊: {groups_count} 个")

        except Exception as e:
            logger.error(f"更新联系人计数失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            logger.info("用户点击关闭按钮，开始清理资源...")

            # 清理主窗口资源
            self.cleanup()

            # 通知主应用程序进行全局清理
            # 通过全局变量或单例模式访问主应用程序
            try:
                # 方法1：通过QApplication的属性访问
                from PyQt6.QtWidgets import QApplication
                app = QApplication.instance()
                if app and hasattr(app, 'main_app_instance'):
                    logger.info("通知主应用程序清理资源...")
                    app.main_app_instance.cleanup()
                else:
                    logger.debug("未找到主应用程序实例，跳过全局清理")
            except Exception as e:
                logger.warning(f"通知主应用程序清理失败: {e}")

            # 确保QApplication正确退出
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                logger.info("请求应用程序退出...")
                # 延迟退出，确保清理完成
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(100, app.quit)

            logger.info("窗口关闭处理完成")
            event.accept()

        except Exception as e:
            logger.error(f"窗口关闭处理失败: {e}")
            # 即使出错也要接受关闭事件
            event.accept()

    def cleanup(self):
        """清理资源"""
        try:
            logger.info("开始清理主窗口资源...")

            # 停止定时器
            if hasattr(self, "status_timer") and self.status_timer:
                self.status_timer.stop()
                logger.debug("状态定时器已停止")

            # 断开连接
            if hasattr(self, "connector") and self.connector:
                logger.debug("断开微信连接...")
                self.run_async_task(self.connector.disconnect())

            # 清理异步任务
            if hasattr(self, "async_task_manager") and self.async_task_manager:
                logger.debug("清理异步任务管理器...")
                self.async_task_manager.cleanup()

            # 清理定时发送和循环发送
            try:
                from core.timing_sender import timing_sender
                from core.loop_sender import loop_sender

                # 停止所有任务
                timing_sender.stop_all_tasks()
                loop_sender.stop_all_tasks()

                # 保存任务数据
                timing_sender.save_tasks()
                loop_sender.save_tasks()

                logger.debug("定时发送和循环发送已清理")
            except Exception as e:
                logger.warning(f"清理发送任务失败: {e}")

            logger.info("主窗口资源清理完成")

        except Exception as e:
            logger.error(f"清理主窗口资源时出错: {e}")


