#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet space 微信群发助手 - 完整构建脚本
一键构建可执行文件和MSI安装包
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_script(script_name, description):
    """运行构建脚本"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run([sys.executable, script_name], check=True)
        elapsed = time.time() - start_time
        print(f"\n✅ {description} 完成 (耗时: {elapsed:.1f}秒)")
        return True
    except subprocess.CalledProcessError as e:
        elapsed = time.time() - start_time
        print(f"\n❌ {description} 失败 (耗时: {elapsed:.1f}秒)")
        print(f"错误代码: {e.returncode}")
        return False

def check_prerequisites():
    """检查构建前提条件"""
    print("🔍 检查构建前提条件...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"  ✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ["PyQt6", "wcferry", "pandas", "requests", "Pillow"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n❌ 缺少必要的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    # 检查PyInstaller
    try:
        subprocess.run(["pyinstaller", "--version"], check=True, capture_output=True)
        print("  ✅ PyInstaller: 已安装")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  ❌ PyInstaller: 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    # 检查WiX Toolset
    try:
        subprocess.run(["candle", "-?"], check=True, capture_output=True)
        print("  ✅ WiX Toolset: 已安装")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  ⚠️  WiX Toolset: 未安装 (MSI构建将跳过)")
        print("  可选安装: winget install WiX.Toolset")
    
    return True

def create_build_info():
    """创建构建信息文件"""
    print("📝 创建构建信息...")
    
    from datetime import datetime
    
    build_info = f"""# Meet space 微信群发助手 - 构建信息
# 自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 项目信息
- 项目名称: Meet space 微信群发助手
- 版本号: 1.0.0
- 构建日期: {datetime.now().strftime('%Y-%m-%d')}
- 构建时间: {datetime.now().strftime('%H:%M:%S')}

## 构建环境
- Python版本: {sys.version}
- 操作系统: {os.name}
- 平台: {sys.platform}

## 构建输出
- 可执行文件: dist/MeetSpaceWeChatSender/
- MSI安装包: output/MeetSpaceWeChatSender_v1.0.0.msi

## 使用说明
1. 测试可执行文件: 运行 dist/MeetSpaceWeChatSender/MeetSpaceWeChatSender.exe
2. 安装MSI包: 双击 output/MeetSpaceWeChatSender_v1.0.0.msi
3. 分发程序: 可以分发整个 dist/MeetSpaceWeChatSender/ 目录或MSI安装包

## 注意事项
- 确保目标机器已安装微信PC版
- 建议在目标机器上测试程序功能
- MSI安装包需要管理员权限安装
"""
    
    with open("BUILD_INFO.md", "w", encoding="utf-8") as f:
        f.write(build_info)
    
    print("  ✅ 已创建: BUILD_INFO.md")

def main():
    """主函数"""
    print("🏗️  Meet space 微信群发助手 - 完整构建系统")
    print("=" * 60)
    print("此脚本将完成以下步骤:")
    print("1. 检查构建前提条件")
    print("2. 构建可执行文件 (PyInstaller)")
    print("3. 构建MSI安装包 (WiX Toolset)")
    print("4. 生成构建信息")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 1. 检查前提条件
        if not check_prerequisites():
            print("\n❌ 前提条件检查失败，构建终止")
            return False
        
        # 2. 构建可执行文件
        if not run_script("build_exe.py", "构建可执行文件"):
            print("\n❌ 可执行文件构建失败，构建终止")
            return False
        
        # 3. 构建MSI安装包
        if Path("build_msi.py").exists():
            if not run_script("build_msi.py", "构建MSI安装包"):
                print("\n⚠️  MSI安装包构建失败，但可执行文件构建成功")
                print("您仍然可以使用 dist/ 目录中的可执行文件")
        
        # 4. 创建构建信息
        create_build_info()
        
        # 构建完成
        total_time = time.time() - start_time
        print(f"\n{'='*60}")
        print("🎉 构建完成！")
        print(f"{'='*60}")
        print(f"⏱️  总耗时: {total_time:.1f}秒")
        
        # 显示输出信息
        print("\n📁 构建输出:")
        
        dist_dir = Path("dist/MeetSpaceWeChatSender")
        if dist_dir.exists():
            print(f"  ✅ 可执行文件: {dist_dir}/")
            exe_file = dist_dir / "MeetSpaceWeChatSender.exe"
            if exe_file.exists():
                size_mb = exe_file.stat().st_size / (1024 * 1024)
                print(f"     主程序: MeetSpaceWeChatSender.exe ({size_mb:.1f} MB)")
        
        output_dir = Path("output")
        if output_dir.exists():
            msi_files = list(output_dir.glob("*.msi"))
            if msi_files:
                for msi_file in msi_files:
                    size_mb = msi_file.stat().st_size / (1024 * 1024)
                    print(f"  ✅ MSI安装包: {msi_file.name} ({size_mb:.1f} MB)")
        
        print("\n📋 下一步:")
        print("1. 测试可执行文件功能")
        print("2. 在不同环境中测试安装包")
        print("3. 准备发布和分发")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠️  构建被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 构建过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
