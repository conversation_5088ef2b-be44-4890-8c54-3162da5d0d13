#!/usr/bin/env python3
"""
一键打包脚本 - Meet space 微信群发助手
自动完成所有打包流程
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def run_command(cmd, description="", cwd=None):
    """运行命令并显示结果"""
    print(f"🔄 {description}...")
    
    try:
        if cwd is None:
            cwd = Path(__file__).parent
            
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            print(f"✅ {description}完成")
            return True
        else:
            print(f"❌ {description}失败:")
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ {description}出错: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Meet space 微信群发助手 - 一键打包工具")
    print("=" * 60)
    
    start_time = time.time()
    
    # 检查Python环境
    print(f"🐍 Python版本: {sys.version}")
    
    project_root = Path(__file__).parent
    print(f"📁 项目目录: {project_root}")
    
    # 步骤1: 创建图标
    print("\n📋 步骤1: 创建应用图标")
    if not run_command([sys.executable, "create_default_icon.py"], "创建图标"):
        print("⚠️  图标创建失败，但继续构建")
    
    # 步骤2: 构建可执行文件
    print("\n📋 步骤2: 构建可执行文件")
    if not run_command([sys.executable, "auto_build.py"], "构建可执行文件"):
        print("❌ 构建失败，停止打包")
        return False
    
    # 步骤3: 创建便携版
    print("\n📋 步骤3: 创建便携版")
    if not run_command([sys.executable, "create_portable_package.py"], "创建便携版"):
        print("❌ 便携版创建失败")
        return False
    
    # 显示结果
    print("\n" + "=" * 60)
    print("🎉 打包完成！")
    
    elapsed_time = time.time() - start_time
    print(f"⏱️  总耗时: {elapsed_time:.1f}秒")
    
    # 检查输出文件
    print("\n📦 输出文件:")
    
    # 可执行文件
    exe_dir = project_root / "dist" / "MeetSpaceWeChatSender"
    if exe_dir.exists():
        exe_size = sum(f.stat().st_size for f in exe_dir.rglob('*') if f.is_file()) / (1024 * 1024)
        print(f"  📁 可执行文件目录: dist/MeetSpaceWeChatSender/ ({exe_size:.1f} MB)")
    
    # 便携版ZIP
    zip_files = list(project_root.glob("MeetSpace_WeChatSender_Portable_*.zip"))
    if zip_files:
        latest_zip = max(zip_files, key=lambda x: x.stat().st_mtime)
        zip_size = latest_zip.stat().st_size / (1024 * 1024)
        print(f"  📦 便携版ZIP: {latest_zip.name} ({zip_size:.1f} MB)")
    
    # 使用说明
    print("\n📖 使用说明:")
    print("  1. 便携版: 解压ZIP文件，双击'启动程序.bat'")
    print("  2. 直接运行: 进入dist/MeetSpaceWeChatSender/目录，运行MeetSpaceWeChatSender.exe")
    print("  3. 首次使用建议以管理员身份运行")
    
    # 询问是否打开输出目录
    try:
        print("\n" + "=" * 60)
        choice = input("是否打开输出目录？(y/n): ").strip().lower()
        if choice == 'y':
            import subprocess
            subprocess.run(['explorer', str(project_root)], check=False)
    except KeyboardInterrupt:
        print("\n👋 再见！")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断打包")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 打包过程出错: {e}")
        sys.exit(1)
