#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DLL兼容性修复工具 - 尝试不同的DLL版本
"""

import sys
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def backup_current_dll():
    """备份当前DLL"""
    print("📁 备份当前DLL...")
    
    try:
        from utils.path_manager import path_manager
        
        current_dll = path_manager.get_resource_path("wxhelper_files/wxhelper.dll")
        if current_dll.exists():
            backup_dll = path_manager.get_resource_path("wxhelper_files/wxhelper_current_backup.dll")
            shutil.copy2(current_dll, backup_dll)
            print(f"✅ 已备份当前DLL到: {backup_dll.name}")
            return True
        else:
            print("❌ 当前DLL不存在")
            return False
            
    except Exception as e:
        print(f"❌ 备份DLL失败: {e}")
        return False

def try_dll_versions():
    """尝试不同的DLL版本"""
    print("\n🔧 尝试不同的DLL版本...")
    
    try:
        from utils.path_manager import path_manager
        from core.injector_tool import InjectorTool
        
        # 可用的DLL版本
        dll_versions = [
            ("最新版本", "wxhelper_files/wxhelper_latest.dll"),
            ("原始备份", "wxhelper_files/wxhelper_original_backup.dll"),
            ("x64备份", "wxhelper_files/wxhelper_x64_backup.dll"),
            ("当前版本", "wxhelper_files/wxhelper.dll")
        ]
        
        main_dll_path = path_manager.get_resource_path("wxhelper_files/wxhelper.dll")
        
        for version_name, dll_path in dll_versions:
            dll_file = path_manager.get_resource_path(dll_path)
            
            if not dll_file.exists():
                print(f"❌ {version_name}: 文件不存在")
                continue
            
            if dll_file == main_dll_path:
                print(f"⚠️  {version_name}: 跳过（当前使用的版本）")
                continue
            
            print(f"\n🧪 测试 {version_name}...")
            print(f"   DLL路径: {dll_file}")
            print(f"   文件大小: {dll_file.stat().st_size} bytes")
            
            # 替换主DLL
            try:
                shutil.copy2(dll_file, main_dll_path)
                print(f"   ✅ 已替换为 {version_name}")
            except Exception as e:
                print(f"   ❌ 替换失败: {e}")
                continue
            
            # 测试注入
            try:
                injector = InjectorTool(auto_elevate=False)
                success, message = injector.inject_dll()
                
                if success:
                    print(f"   🎉 {version_name} 注入成功！")
                    print(f"   成功消息: {message}")
                    
                    # 验证API服务
                    if injector.check_api_service():
                        print(f"   ✅ API服务正常响应")
                        print(f"\n🎯 找到可用的DLL版本: {version_name}")
                        print(f"建议：保持使用此版本")
                        return True, version_name
                    else:
                        print(f"   ⚠️  API服务未响应，但注入可能成功")
                        
                        # 等待一下再检查
                        import time
                        time.sleep(2)
                        if injector.check_api_service():
                            print(f"   ✅ API服务延迟响应正常")
                            print(f"\n🎯 找到可用的DLL版本: {version_name}")
                            return True, version_name
                else:
                    print(f"   ❌ {version_name} 注入失败: {message}")
                    
            except Exception as e:
                print(f"   ❌ {version_name} 测试异常: {e}")
        
        print("\n❌ 所有DLL版本都无法注入")
        return False, None
        
    except Exception as e:
        print(f"❌ 测试DLL版本失败: {e}")
        return False, None

def restore_backup():
    """恢复备份的DLL"""
    print("\n🔄 恢复备份的DLL...")
    
    try:
        from utils.path_manager import path_manager
        
        backup_dll = path_manager.get_resource_path("wxhelper_files/wxhelper_current_backup.dll")
        main_dll = path_manager.get_resource_path("wxhelper_files/wxhelper.dll")
        
        if backup_dll.exists():
            shutil.copy2(backup_dll, main_dll)
            print("✅ 已恢复原始DLL")
            return True
        else:
            print("❌ 备份文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 恢复备份失败: {e}")
        return False

def check_wechat_version():
    """检查微信版本信息"""
    print("\n🔍 检查微信版本信息...")
    
    try:
        import psutil
        
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and 'WeChat.exe' in proc.info['name']:
                    wechat_exe = proc.info['exe']
                    if wechat_exe:
                        print(f"微信路径: {wechat_exe}")
                        
                        # 获取文件修改时间
                        from datetime import datetime
                        file_path = Path(wechat_exe)
                        mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                        print(f"文件修改时间: {mod_time}")
                        
                        # 检查是否是最近更新的
                        now = datetime.now()
                        hours_diff = (now - mod_time).total_seconds() / 3600
                        
                        if hours_diff < 48:  # 48小时内
                            print(f"⚠️  微信在 {hours_diff:.1f} 小时前更新过！")
                            print("这可能是注入失败的原因")
                        else:
                            print(f"✅ 微信文件较旧 ({hours_diff:.1f} 小时前)")
                        
                        return True
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print("❌ 未找到微信进程")
        return False
        
    except Exception as e:
        print(f"❌ 检查微信版本失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - DLL兼容性修复")
    print("=" * 60)
    print("解决昨天能用今天不能用的DLL兼容性问题")
    print("=" * 60)
    
    # 检查微信版本
    check_wechat_version()
    
    # 备份当前DLL
    backup_success = backup_current_dll()
    
    if not backup_success:
        print("⚠️  无法备份当前DLL，继续测试...")
    
    # 尝试不同的DLL版本
    success, working_version = try_dll_versions()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 DLL兼容性测试结果:")
    
    if success:
        print(f"✅ 找到可用的DLL版本: {working_version}")
        print("\n🎉 问题已解决！")
        print("✨ 解决方案:")
        print(f"  - 使用 {working_version} 版本的DLL")
        print("  - 注入功能已恢复正常")
        print("  - API服务正常响应")
        
        print("\n📋 建议:")
        print("1. 保持使用当前DLL版本")
        print("2. 如果微信再次更新，重新运行此工具")
        print("3. 定期备份可用的DLL版本")
        
    else:
        print("❌ 所有DLL版本都无法注入")
        
        # 恢复备份
        if backup_success:
            restore_backup()
        
        print("\n🔧 进一步的解决建议:")
        print("1. 微信版本可能过新，wxhelper尚未支持")
        print("2. 尝试降级微信到之前的版本")
        print("3. 等待wxhelper更新以支持新版微信")
        print("4. 检查是否有其他版本的wxhelper可用")
        print("5. 临时关闭所有杀毒软件后重试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
