#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用原始代码重新构建 - 不修改任何注入逻辑
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_original():
    """使用原始代码构建"""
    print("🔧 使用原始代码重新构建...")
    
    # 清理
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 构建命令 - 保持简单，不过度优化
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MeetSpaceWeChatSender_Original",
        "--icon=resources/icons/app_icon.ico",
        
        # 关键工具文件 - 按原始项目结构
        "--add-data=tools;tools",
        "--add-data=wxhelper_files;wxhelper_files",
        
        # 基础资源
        "--add-data=resources;resources",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        "--add-data=README.md;.",
        
        # 核心隐藏导入 - 只包含必要的
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=ctypes",
        "--hidden-import=subprocess",
        "--hidden-import=psutil",
        "--hidden-import=requests",
        "--hidden-import=pandas",
        "--hidden-import=PIL",
        
        # 项目模块 - 按原始结构
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=ui",
        "--hidden-import=utils",
        "--hidden-import=runtime_config_generator",
        
        # 排除不需要的
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        
        # 构建选项 - 保持简单
        "--clean",
        "--noconfirm",
        
        "main.py"
    ]
    
    print("📋 开始构建...")
    print("   使用原始注入逻辑，不做任何修改")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=400
        )
        
        print("✅ 构建完成")
        
        # 检查结果
        exe_file = Path("dist") / "MeetSpaceWeChatSender_Original.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"✅ 输出文件: {exe_file}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            
            print("\n🎉 原始版本构建成功!")
            print("✨ 特点:")
            print("  🔧 使用原始的注入逻辑")
            print("  📚 包含完整的工具文件")
            print("  🚀 保持原有的工作方式")
            print("\n📋 测试:")
            print("  1. 启动微信PC版并登录")
            print("  2. 运行 MeetSpaceWeChatSender_Original.exe")
            print("  3. 点击连接微信按钮")
            print("  4. 检查是否能成功注入")
            
            return True
        else:
            print("❌ 未找到输出文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e.returncode}")
        if e.stderr:
            print("错误:", e.stderr[-1000:])
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def main():
    print("🔧 Meet space 微信群发助手 - 原始版本构建")
    print("=" * 60)
    print("恢复原始注入逻辑，不做任何修改")
    print("=" * 60)
    
    success = build_original()
    
    if success:
        print("\n✅ 原始版本构建完成！")
        print("这个版本使用了您原本可以正常工作的注入代码。")
        print("请测试这个版本是否能正常注入微信。")
    else:
        print("\n❌ 构建失败！")
    
    input("\n按回车键退出...")
    return success

if __name__ == "__main__":
    main()
