"""
性能监控和优化工具模块

提供性能监控、内存管理和异步操作优化功能。
"""

import asyncio
import functools
import gc
import psutil
import time
import weakref
from contextlib import contextmanager
from typing import Any, Callable, Dict, List, Optional, TypeVar

from utils.logger import setup_logger

logger = setup_logger("performance")

F = TypeVar("F", bound=Callable[..., Any])


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.memory_usage: List[float] = []
        self.start_time = time.time()

    def record_metric(self, name: str, value: float) -> None:
        """记录性能指标"""
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append(value)

    def get_average(self, name: str) -> Optional[float]:
        """获取指标平均值"""
        if name not in self.metrics or not self.metrics[name]:
            return None
        return sum(self.metrics[name]) / len(self.metrics[name])

    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        self.memory_usage.append(memory_mb)
        return memory_mb

    def get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        return psutil.cpu_percent(interval=0.1)

    def clear_metrics(self) -> None:
        """清空指标"""
        self.metrics.clear()
        self.memory_usage.clear()

    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            "uptime": time.time() - self.start_time,
            "memory_current": self.get_memory_usage(),
            "memory_average": (
                sum(self.memory_usage) / len(self.memory_usage)
                if self.memory_usage
                else 0
            ),
            "cpu_usage": self.get_cpu_usage(),
            "metrics": {},
        }

        for name, values in self.metrics.items():
            if values:
                summary["metrics"][name] = {
                    "count": len(values),
                    "average": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                }

        return summary


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def timing_decorator(name: Optional[str] = None) -> Callable[[F], F]:
    """
    计时装饰器

    Args:
        name: 指标名称，如果为None则使用函数名
    """

    def decorator(func: F) -> F:
        metric_name = name or f"{func.__module__}.{func.__name__}"

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                elapsed = time.time() - start_time
                performance_monitor.record_metric(metric_name, elapsed)
                if elapsed > 1.0:  # 记录超过1秒的操作
                    logger.warning(f"慢操作: {metric_name} 耗时 {elapsed:.2f}秒")

        return wrapper

    return decorator


def async_timing_decorator(name: Optional[str] = None) -> Callable[[F], F]:
    """
    异步计时装饰器

    Args:
        name: 指标名称，如果为None则使用函数名
    """

    def decorator(func: F) -> F:
        metric_name = name or f"{func.__module__}.{func.__name__}"

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                elapsed = time.time() - start_time
                performance_monitor.record_metric(metric_name, elapsed)
                if elapsed > 1.0:  # 记录超过1秒的操作
                    logger.warning(f"慢异步操作: {metric_name} 耗时 {elapsed:.2f}秒")

        return wrapper

    return decorator


@contextmanager
def performance_context(name: str):
    """性能监控上下文管理器"""
    start_time = time.time()
    start_memory = performance_monitor.get_memory_usage()

    try:
        yield
    finally:
        elapsed = time.time() - start_time
        end_memory = performance_monitor.get_memory_usage()
        memory_delta = end_memory - start_memory

        performance_monitor.record_metric(f"{name}_time", elapsed)
        performance_monitor.record_metric(f"{name}_memory", memory_delta)

        logger.debug(f"{name}: 耗时 {elapsed:.3f}秒, 内存变化 {memory_delta:.2f}MB")


class MemoryManager:
    """内存管理器"""

    def __init__(self):
        self.weak_refs: List[weakref.ref] = []
        self._last_cleanup_time = time.time()
        self._cleanup_threshold = 100  # 弱引用数量阈值
        self._memory_threshold = 500 * 1024 * 1024  # 500MB内存阈值

    def register_object(self, obj: Any) -> None:
        """注册对象用于内存监控"""
        try:
            self.weak_refs.append(weakref.ref(obj))
            
            # 自动清理失效引用（避免列表过长）
            if len(self.weak_refs) > self._cleanup_threshold:
                self.cleanup_dead_refs()
                
        except TypeError:
            # 某些对象（如dict）不支持弱引用，跳过
            pass

    def cleanup_dead_refs(self) -> int:
        """清理失效的弱引用"""
        before_count = len(self.weak_refs)
        self.weak_refs = [ref for ref in self.weak_refs if ref() is not None]
        cleaned = before_count - len(self.weak_refs)
        if cleaned > 0:
            logger.debug(f"清理了 {cleaned} 个失效的弱引用")
        return cleaned

    def force_gc(self) -> Dict[str, Any]:
        """强制垃圾回收"""
        before_memory = performance_monitor.get_memory_usage()

        # 清理失效的弱引用
        cleaned_refs = self.cleanup_dead_refs()

        # 执行多次垃圾回收以确保彻底清理
        total_collected = 0
        for generation in range(3):  # Python有3代垃圾回收
            collected = gc.collect(generation)
            total_collected += collected

        after_memory = performance_monitor.get_memory_usage()
        memory_freed = before_memory - after_memory

        result = {
            "collected_objects": total_collected,
            "cleaned_refs": cleaned_refs,
            "memory_freed_mb": memory_freed,
            "before_memory_mb": before_memory,
            "after_memory_mb": after_memory,
        }

        logger.info(f"垃圾回收完成: 清理对象={total_collected}, 释放内存={memory_freed:.1f}MB")
        return result

    def get_object_count(self) -> int:
        """获取活跃对象数量"""
        return len([ref for ref in self.weak_refs if ref() is not None])

    def get_memory_stats(self) -> Dict[str, Any]:
        """获取详细的内存统计信息"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "rss_mb": memory_info.rss / 1024 / 1024,
                "vms_mb": memory_info.vms / 1024 / 1024,
                "percent": process.memory_percent(),
                "active_objects": self.get_object_count(),
                "weak_refs_total": len(self.weak_refs),
                "gc_stats": gc.get_stats(),
            }
        except Exception as e:
            logger.error(f"获取内存统计失败: {e}")
            return {}

    def check_memory_pressure(self) -> bool:
        """检查内存压力"""
        try:
            current_memory = performance_monitor.get_memory_usage() * 1024 * 1024  # 转换为字节
            return current_memory > self._memory_threshold
        except Exception:
            return False

    def auto_cleanup_if_needed(self) -> bool:
        """根据需要自动清理"""
        current_time = time.time()
        
        # 检查是否需要清理（时间间隔或内存压力）
        time_threshold = current_time - self._last_cleanup_time > 300  # 5分钟
        memory_pressure = self.check_memory_pressure()
        
        if time_threshold or memory_pressure:
            result = self.force_gc()
            self._last_cleanup_time = current_time
            
            if memory_pressure:
                logger.warning(f"内存压力触发自动清理: {result}")
            
            return True
        
        return False


# 全局内存管理器实例
memory_manager = MemoryManager()


class AsyncTaskManager:
    """异步任务管理器"""

    def __init__(self):
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.completed_tasks: Dict[str, Any] = {}
        self.max_completed_history = 50  # 保留最近完成任务的历史
        self._task_counter = 0

    def create_task(self, coro, name: Optional[str] = None) -> asyncio.Task:
        """创建并管理异步任务"""
        task = asyncio.create_task(coro)
        
        # 生成唯一任务名
        if not name:
            self._task_counter += 1
            name = f"task_{self._task_counter}_{id(task)}"

        self.running_tasks[name] = task

        # 添加完成回调
        def cleanup_task(finished_task):
            if name in self.running_tasks:
                del self.running_tasks[name]
                
                # 记录完成的任务（用于调试）
                self.completed_tasks[name] = {
                    "completion_time": time.time(),
                    "success": not finished_task.exception(),
                    "exception": str(finished_task.exception()) if finished_task.exception() else None
                }
                
                # 限制历史记录大小
                if len(self.completed_tasks) > self.max_completed_history:
                    oldest_task = min(self.completed_tasks.keys(), 
                                    key=lambda k: self.completed_tasks[k]["completion_time"])
                    del self.completed_tasks[oldest_task]

        task.add_done_callback(cleanup_task)
        
        # 注册到内存管理器
        memory_manager.register_object(task)
        
        logger.debug(f"创建异步任务: {name}")
        return task

    def cancel_all_tasks(self) -> int:
        """取消所有运行中的任务"""
        cancelled_count = 0
        tasks_to_cancel = list(self.running_tasks.items())
        
        for task_name, task in tasks_to_cancel:
            if not task.done():
                task.cancel()
                cancelled_count += 1
                logger.debug(f"取消任务: {task_name}")

        logger.info(f"取消了 {cancelled_count} 个运行中的任务")
        return cancelled_count

    def get_running_count(self) -> int:
        """获取运行中的任务数量"""
        # 清理已完成的任务
        self._cleanup_finished_tasks()
        return len(self.running_tasks)

    def _cleanup_finished_tasks(self) -> int:
        """清理已完成的任务"""
        finished_tasks = [name for name, task in self.running_tasks.items() if task.done()]
        for name in finished_tasks:
            if name in self.running_tasks:
                del self.running_tasks[name]
        
        if finished_tasks:
            logger.debug(f"清理了 {len(finished_tasks)} 个已完成的任务")
        
        return len(finished_tasks)

    def get_task_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        self._cleanup_finished_tasks()
        
        running_count = len(self.running_tasks)
        completed_count = len(self.completed_tasks)
        
        # 统计成功/失败的任务
        successful_tasks = sum(1 for task in self.completed_tasks.values() if task["success"])
        failed_tasks = completed_count - successful_tasks
        
        return {
            "running_tasks": running_count,
            "completed_tasks": completed_count,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "task_names": list(self.running_tasks.keys())
        }

    def cancel_task(self, task_name: str) -> bool:
        """取消指定任务"""
        if task_name in self.running_tasks:
            task = self.running_tasks[task_name]
            if not task.done():
                task.cancel()
                logger.debug(f"取消指定任务: {task_name}")
                return True
        return False


# 全局异步任务管理器实例
async_task_manager = AsyncTaskManager()


def enhanced_timing_decorator(name: str = None, category: str = "function"):
    """增强的性能计时装饰器，集成到新的监控系统"""
    def decorator(func):
        from utils.enhanced_performance_monitor import enhanced_performance_monitor
        
        func_name = name or f"{func.__module__}.{func.__qualname__}"
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = performance_monitor.get_memory_usage()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                execution_time = time.time() - start_time
                end_memory = performance_monitor.get_memory_usage()
                memory_delta = end_memory - start_memory
                
                # 记录到原有系统
                performance_monitor.record_metric(f"{func_name}_time", execution_time * 1000)
                if abs(memory_delta) > 0.1:
                    performance_monitor.record_metric(f"{func_name}_memory", memory_delta)
                
                # 记录到增强监控系统
                enhanced_performance_monitor.record_function_call(func_name, execution_time)
                
                if abs(memory_delta) > 0.1:
                    enhanced_performance_monitor.record_metric(
                        f"{func_name}.memory_delta",
                        memory_delta,
                        category=category,
                        tags={"unit": "MB", "function": func_name}
                    )
        
        return wrapper
    return decorator


# 向后兼容的别名
timing_decorator = enhanced_timing_decorator


def optimize_for_memory():
    """内存优化函数"""
    # 强制垃圾回收
    memory_manager.force_gc()

    # 记录优化后的内存使用
    current_memory = performance_monitor.get_memory_usage()
    logger.info(f"内存优化完成，当前使用: {current_memory:.2f}MB")


def get_performance_report() -> Dict[str, Any]:
    """获取性能报告"""
    return {
        "monitor": performance_monitor.get_summary(),
        "memory": {
            "managed_objects": memory_manager.get_object_count(),
            "current_usage_mb": performance_monitor.get_memory_usage(),
        },
        "async_tasks": {
            "running_count": async_task_manager.get_running_count(),
            "total_tasks": len(async_task_manager.running_tasks),
        },
    }
