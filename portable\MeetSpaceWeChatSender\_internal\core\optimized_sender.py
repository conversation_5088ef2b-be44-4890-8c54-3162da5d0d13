#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的消息发送器

提供高性能的消息发送功能，支持批量处理、并发控制、内存优化等。
"""

import asyncio
import time
import random
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

from core.send_monitor import SendTask, SendStatus
from utils.logger import setup_logger
from utils.performance_optimizer import performance_optimizer, performance_monitor

logger = setup_logger("optimized_sender")


@dataclass
class SendBatch:
    """发送批次"""

    tasks: List[SendTask]
    batch_id: int
    total_batches: int


class OptimizedSender:
    """优化的消息发送器"""

    def __init__(self, connector, monitor, config):
        self.connector = connector
        self.monitor = monitor
        self.config = config

        # 性能配置
        self.max_concurrent_tasks = 5  # 最大并发任务数
        self.batch_size = min(getattr(config, "batch_size", 20), 50)  # 限制批次大小
        self.batch_interval = max(getattr(config, "batch_interval", 1), 0.5)  # 最小间隔

        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=3)

        # 统计信息
        self.stats = {
            "total_sent": 0,
            "total_failed": 0,
            "total_time": 0,
            "avg_speed": 0,
        }

        logger.info("优化发送器初始化完成")

    @performance_monitor
    async def send_messages_optimized(self, tasks: List[SendTask]) -> Dict[str, Any]:
        """优化的消息发送"""
        start_time = time.time()

        try:
            logger.info(f"开始优化发送，任务数: {len(tasks)}")

            # 根据任务数量选择策略
            if len(tasks) <= 10:
                result = await self._send_small_batch(tasks)
            elif len(tasks) <= 100:
                result = await self._send_medium_batch(tasks)
            else:
                result = await self._send_large_batch(tasks)

            # 更新统计
            elapsed_time = time.time() - start_time
            self._update_stats(len(tasks), elapsed_time)

            logger.info(f"发送完成，耗时: {elapsed_time:.2f}秒")
            return result

        except Exception as e:
            logger.error(f"优化发送失败: {e}")
            raise

    async def _send_small_batch(self, tasks: List[SendTask]) -> Dict[str, Any]:
        """小批量发送（≤10个）"""
        logger.debug("使用小批量发送模式")

        success_count = 0
        failed_count = 0

        for task in tasks:
            if not self.monitor.is_running:
                task.status = SendStatus.CANCELLED
                continue

            try:
                success = await self._send_single_message(task)
                if success:
                    success_count += 1
                    task.status = SendStatus.COMPLETED
                else:
                    failed_count += 1
                    task.status = SendStatus.FAILED

                # 短暂延迟
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"发送消息失败: {task.contact.name} - {e}")
                failed_count += 1
                task.status = SendStatus.FAILED

        return {
            "success": success_count,
            "failed": failed_count,
            "cancelled": len(tasks) - success_count - failed_count,
        }

    async def _send_medium_batch(self, tasks: List[SendTask]) -> Dict[str, Any]:
        """中等批量发送（11-100个）"""
        logger.debug("使用中等批量发送模式")

        # 分批处理
        batches = self._create_batches(tasks, self.batch_size)

        success_count = 0
        failed_count = 0
        cancelled_count = 0

        for i, batch in enumerate(batches):
            if not self.monitor.is_running:
                # 取消剩余任务
                for remaining_batch in batches[i:]:
                    for task in remaining_batch.tasks:
                        task.status = SendStatus.CANCELLED
                        cancelled_count += 1
                break

            logger.info(f"处理批次 {batch.batch_id}/{batch.total_batches}")

            # 并发发送当前批次
            batch_result = await self._send_batch_concurrent(batch.tasks)

            success_count += batch_result["success"]
            failed_count += batch_result["failed"]
            cancelled_count += batch_result["cancelled"]

            # 批次间延迟
            if i < len(batches) - 1:
                await asyncio.sleep(self.batch_interval)

            # 让出控制权
            performance_optimizer.responsiveness_manager.yield_control()

        return {
            "success": success_count,
            "failed": failed_count,
            "cancelled": cancelled_count,
        }

    async def _send_large_batch(self, tasks: List[SendTask]) -> Dict[str, Any]:
        """大批量发送（>100个）"""
        logger.debug("使用大批量发送模式")

        # 使用更小的批次和更多的并发控制
        small_batch_size = min(self.batch_size // 2, 25)
        batches = self._create_batches(tasks, small_batch_size)

        success_count = 0
        failed_count = 0
        cancelled_count = 0

        # 使用信号量控制并发
        semaphore = asyncio.Semaphore(3)  # 最多3个批次并发

        async def process_batch_with_semaphore(batch):
            async with semaphore:
                return await self._send_batch_concurrent(batch.tasks)

        # 分组处理批次
        batch_groups = [batches[i : i + 3] for i in range(0, len(batches), 3)]

        for group_index, batch_group in enumerate(batch_groups):
            if not self.monitor.is_running:
                # 取消剩余任务
                for remaining_group in batch_groups[group_index:]:
                    for batch in remaining_group:
                        for task in batch.tasks:
                            task.status = SendStatus.CANCELLED
                            cancelled_count += 1
                break

            logger.info(f"处理批次组 {group_index + 1}/{len(batch_groups)}")

            # 并发处理当前组的批次
            group_results = await asyncio.gather(
                *[process_batch_with_semaphore(batch) for batch in batch_group],
                return_exceptions=True,
            )

            # 汇总结果
            for result in group_results:
                if isinstance(result, dict):
                    success_count += result["success"]
                    failed_count += result["failed"]
                    cancelled_count += result["cancelled"]
                else:
                    logger.error(f"批次处理异常: {result}")

            # 组间延迟
            if group_index < len(batch_groups) - 1:
                await asyncio.sleep(self.batch_interval * 2)

            # 强制内存清理
            if group_index % 5 == 0:
                performance_optimizer.memory_manager.force_cleanup()

        return {
            "success": success_count,
            "failed": failed_count,
            "cancelled": cancelled_count,
        }

    async def _send_batch_concurrent(self, tasks: List[SendTask]) -> Dict[str, Any]:
        """并发发送批次"""
        semaphore = asyncio.Semaphore(self.max_concurrent_tasks)

        async def send_with_semaphore(task):
            async with semaphore:
                try:
                    if not self.monitor.is_running:
                        task.status = SendStatus.CANCELLED
                        return "cancelled"

                    success = await self._send_single_message(task)
                    if success:
                        task.status = SendStatus.COMPLETED
                        return "success"
                    else:
                        task.status = SendStatus.FAILED
                        return "failed"

                except Exception as e:
                    logger.error(f"发送任务异常: {task.contact.name} - {e}")
                    task.status = SendStatus.FAILED
                    return "failed"

        # 并发执行
        results = await asyncio.gather(
            *[send_with_semaphore(task) for task in tasks], return_exceptions=True
        )

        # 统计结果
        success_count = sum(1 for r in results if r == "success")
        failed_count = sum(1 for r in results if r == "failed")
        cancelled_count = sum(1 for r in results if r == "cancelled")

        return {
            "success": success_count,
            "failed": failed_count,
            "cancelled": cancelled_count,
        }

    async def _send_single_message(self, task: SendTask) -> bool:
        """发送单条消息"""
        try:
            # 更新任务状态
            self.monitor.update_task_status(task.id, SendStatus.SENDING)

            # 发送消息
            if hasattr(self.connector, "send_message_async"):
                success = await self.connector.send_message_async(
                    task.contact.wxid, task.content, task.message_type
                )
            else:
                # 同步方法的异步包装
                loop = asyncio.get_event_loop()
                success = await loop.run_in_executor(
                    self.thread_pool,
                    self.connector.send_message,
                    task.contact.wxid,
                    task.content,
                    task.message_type,
                )

            if success:
                logger.debug(f"发送成功: {task.contact.name}")
            else:
                logger.warning(f"发送失败: {task.contact.name}")

            return success

        except Exception as e:
            logger.error(f"发送消息异常: {task.contact.name} - {e}")
            return False

    def _create_batches(
        self, tasks: List[SendTask], batch_size: int
    ) -> List[SendBatch]:
        """创建发送批次"""
        batches = []
        total_batches = (len(tasks) + batch_size - 1) // batch_size

        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i : i + batch_size]
            batch = SendBatch(
                tasks=batch_tasks,
                batch_id=i // batch_size + 1,
                total_batches=total_batches,
            )
            batches.append(batch)

        return batches

    def _update_stats(self, task_count: int, elapsed_time: float):
        """更新统计信息"""
        self.stats["total_sent"] += task_count
        self.stats["total_time"] += elapsed_time

        if self.stats["total_time"] > 0:
            self.stats["avg_speed"] = (
                self.stats["total_sent"] / self.stats["total_time"]
            )

        logger.info(
            f"发送统计 - 总数: {self.stats['total_sent']}, "
            f"平均速度: {self.stats['avg_speed']:.2f}条/秒"
        )

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()

    def cleanup(self):
        """清理资源"""
        try:
            self.thread_pool.shutdown(wait=False)
            logger.info("优化发送器清理完成")
        except Exception as e:
            logger.error(f"清理优化发送器失败: {e}")
