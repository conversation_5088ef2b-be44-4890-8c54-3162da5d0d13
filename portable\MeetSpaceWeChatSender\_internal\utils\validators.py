"""
数据验证工具模块

提供各种数据验证功能。
"""

import os
import re
from pathlib import Path
from typing import List, Optional, Tuple

from config.settings import MAX_MESSAGE_LENGTH, SUPPORTED_FILE_TYPES
from utils.logger import setup_logger

logger = setup_logger("validators")


def validate_wxid(wxid: str) -> Tu<PERSON>[bool, str]:
    """
    验证微信ID格式

    Args:
        wxid: 微信ID

    Returns:
        (是否有效, 错误信息)
    """
    if not wxid:
        return False, "微信ID不能为空"

    if len(wxid) < 3:
        return False, "微信ID长度不能少于3个字符"

    if len(wxid) > 50:
        return False, "微信ID长度不能超过50个字符"

    # 微信ID只能包含字母、数字、下划线和连字符
    if not re.match(r"^[a-zA-Z0-9_-]+$", wxid):
        return False, "微信ID只能包含字母、数字、下划线和连字符"

    return True, ""


def validate_contact_name(name: str) -> <PERSON><PERSON>[bool, str]:
    """
    验证联系人名称

    Args:
        name: 联系人名称

    Returns:
        (是否有效, 错误信息)
    """
    if not name:
        return False, "联系人名称不能为空"

    if len(name.strip()) == 0:
        return False, "联系人名称不能为空白字符"

    if len(name) > 100:
        return False, "联系人名称长度不能超过100个字符"

    return True, ""


def validate_contact(wxid: str, name: str) -> Tuple[bool, str]:
    """
    验证联系人信息

    Args:
        wxid: 微信ID
        name: 联系人名称

    Returns:
        (是否有效, 错误信息)
    """
    # 验证微信ID
    valid, error = validate_wxid(wxid)
    if not valid:
        return False, f"微信ID无效: {error}"

    # 验证联系人名称
    valid, error = validate_contact_name(name)
    if not valid:
        return False, f"联系人名称无效: {error}"

    return True, ""


def validate_message_content(
    content: str, message_type: str = "text"
) -> Tuple[bool, str]:
    """
    验证消息内容

    Args:
        content: 消息内容
        message_type: 消息类型

    Returns:
        (是否有效, 错误信息)
    """
    if message_type != "text":
        return True, ""

    if not content:
        return False, "消息内容不能为空"

    if len(content.strip()) == 0:
        return False, "消息内容不能为空白字符"

    if len(content) > MAX_MESSAGE_LENGTH:
        return False, f"消息内容长度不能超过{MAX_MESSAGE_LENGTH}个字符"

    return True, ""


def validate_file_path(file_path: str) -> Tuple[bool, str]:
    """
    验证文件路径

    Args:
        file_path: 文件路径

    Returns:
        (是否有效, 错误信息)
    """
    if not file_path:
        return False, "文件路径不能为空"

    path = Path(file_path)

    # 检查文件是否存在
    if not path.exists():
        return False, "文件不存在"

    # 检查是否为文件
    if not path.is_file():
        return False, "路径不是一个文件"

    # 检查文件类型
    if path.suffix.lower() not in SUPPORTED_FILE_TYPES:
        return False, f"不支持的文件类型: {path.suffix}"

    # 检查文件大小（限制为100MB）
    file_size = path.stat().st_size
    max_size = 100 * 1024 * 1024  # 100MB
    if file_size > max_size:
        return False, f"文件大小超过限制 (最大100MB)"

    return True, ""


def validate_message(
    content: str, message_type: str, file_path: str = ""
) -> Tuple[bool, str]:
    """
    验证消息

    Args:
        content: 消息内容
        message_type: 消息类型
        file_path: 文件路径（如果是文件消息）

    Returns:
        (是否有效, 错误信息)
    """
    if message_type not in ["text", "image", "file"]:
        return False, "无效的消息类型"

    if message_type == "text":
        return validate_message_content(content, message_type)

    elif message_type in ["image", "file"]:
        # 验证文件路径
        valid, error = validate_file_path(file_path)
        if not valid:
            return False, error

        # 如果有文本内容，也要验证
        if content:
            valid, error = validate_message_content(content, "text")
            if not valid:
                return False, f"附加文本无效: {error}"

    return True, ""


def validate_send_interval(min_interval: int, max_interval: int) -> Tuple[bool, str]:
    """
    验证发送间隔

    Args:
        min_interval: 最小间隔
        max_interval: 最大间隔

    Returns:
        (是否有效, 错误信息)
    """
    if min_interval <= 0:
        return False, "最小发送间隔必须大于0"

    if max_interval <= 0:
        return False, "最大发送间隔必须大于0"

    if min_interval > max_interval:
        return False, "最小发送间隔不能大于最大发送间隔"

    if min_interval > 60:
        return False, "最小发送间隔不能超过60秒"

    if max_interval > 300:
        return False, "最大发送间隔不能超过300秒"

    return True, ""


def validate_batch_settings(batch_size: int, batch_interval: int) -> Tuple[bool, str]:
    """
    验证批次设置

    Args:
        batch_size: 批次大小
        batch_interval: 批次间隔（秒数）

    Returns:
        (是否有效, 错误信息)
    """
    if batch_size <= 0:
        return False, "批次大小必须大于0"

    if batch_size > 100:
        return False, "批次大小不能超过100"

    # 批次间隔现在以小时为单位，最小1小时（3600秒），最大24小时（86400秒）
    if batch_interval < 3600:
        return False, "批次间隔不能少于1小时"

    if batch_interval > 86400:
        return False, "批次间隔不能超过24小时"

    return True, ""


def validate_send_limits(daily_limit: int, hourly_limit: int) -> Tuple[bool, str]:
    """
    验证发送限制

    Args:
        daily_limit: 每日限制
        hourly_limit: 每小时限制

    Returns:
        (是否有效, 错误信息)
    """
    if daily_limit <= 0:
        return False, "每日发送限制必须大于0"

    if hourly_limit <= 0:
        return False, "每小时发送限制必须大于0"

    if hourly_limit > daily_limit:
        return False, "每小时发送限制不能大于每日限制"

    if daily_limit > 1000:
        return False, "每日发送限制不能超过1000"

    if hourly_limit > 200:
        return False, "每小时发送限制不能超过200"

    return True, ""


def validate_template_name(name: str) -> Tuple[bool, str]:
    """
    验证模板名称

    Args:
        name: 模板名称

    Returns:
        (是否有效, 错误信息)
    """
    if not name:
        return False, "模板名称不能为空"

    if len(name.strip()) == 0:
        return False, "模板名称不能为空白字符"

    if len(name) > 50:
        return False, "模板名称长度不能超过50个字符"

    # 检查是否包含非法字符
    illegal_chars = ["/", "\\", ":", "*", "?", '"', "<", ">", "|"]
    for char in illegal_chars:
        if char in name:
            return False, f"模板名称不能包含字符: {char}"

    return True, ""


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除非法字符

    Args:
        filename: 原始文件名

    Returns:
        清理后的文件名
    """
    # 移除非法字符
    illegal_chars = ["/", "\\", ":", "*", "?", '"', "<", ">", "|"]
    for char in illegal_chars:
        filename = filename.replace(char, "_")

    # 移除首尾空白字符
    filename = filename.strip()

    # 如果文件名为空，使用默认名称
    if not filename:
        filename = "untitled"

    return filename
