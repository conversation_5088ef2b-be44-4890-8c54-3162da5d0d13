#!/usr/bin/env python3
"""
创建MSI安装程序 - Meet space 微信群发助手
使用WiX Toolset创建标准的Windows MSI安装包
"""

import os
import sys
import subprocess
import urllib.request
import time
import zipfile
from pathlib import Path
import tempfile
import shutil
import xml.etree.ElementTree as ET


class MSIBuilder:
    """MSI安装程序构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.temp_dir = Path(tempfile.gettempdir()) / "wx_msi_build"
        self.wix_path = None
        
    def log(self, message: str, level: str = "INFO") -> None:
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "STEP": "🔄"
        }.get(level, "ℹ️")
        
        print(f"[{timestamp}] {prefix} {message}")
        
    def check_wix_toolset(self) -> bool:
        """检查WiX Toolset是否已安装"""
        self.log("检查WiX Toolset安装状态...", "STEP")
        
        # 检查常见的WiX安装路径
        possible_paths = [
            r"C:\Program Files (x86)\WiX Toolset v3.11\bin\candle.exe",
            r"C:\Program Files\WiX Toolset v3.11\bin\candle.exe",
            r"C:\Program Files (x86)\WiX Toolset v4.0\bin\candle.exe",
            r"C:\Program Files\WiX Toolset v4.0\bin\candle.exe",
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                self.wix_path = Path(path).parent
                self.log(f"找到WiX Toolset: {self.wix_path}", "SUCCESS")
                return True
                
        # 检查PATH环境变量
        try:
            result = subprocess.run(["candle", "-?"], capture_output=True, text=True)
            if result.returncode == 0:
                self.wix_path = "PATH"
                self.log("在PATH中找到WiX Toolset", "SUCCESS")
                return True
        except FileNotFoundError:
            pass
            
        self.log("未找到WiX Toolset", "WARNING")
        return False
        
    def download_wix_toolset(self) -> bool:
        """下载WiX Toolset"""
        self.log("下载WiX Toolset...", "STEP")
        
        try:
            # 创建临时目录
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            
            # WiX Toolset 3.11下载URL
            download_url = "https://github.com/wixtoolset/wix3/releases/download/wix3111rtm/wix311-binaries.zip"
            zip_path = self.temp_dir / "wix-toolset.zip"
            
            self.log(f"正在下载: {download_url}", "INFO")
            
            # 下载文件
            urllib.request.urlretrieve(download_url, zip_path)
            
            if zip_path.exists():
                file_size = zip_path.stat().st_size / (1024 * 1024)
                self.log(f"下载完成: {file_size:.1f} MB", "SUCCESS")
                return True
            else:
                self.log("下载失败", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"下载WiX Toolset失败: {e}", "ERROR")
            return False
            
    def install_wix_toolset(self) -> bool:
        """安装WiX Toolset"""
        self.log("安装WiX Toolset...", "STEP")
        
        try:
            zip_path = self.temp_dir / "wix-toolset.zip"
            wix_dir = self.temp_dir / "wix"
            
            if not zip_path.exists():
                self.log("WiX安装包不存在", "ERROR")
                return False
                
            # 解压WiX Toolset
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(wix_dir)
                
            # 检查candle.exe是否存在
            candle_exe = wix_dir / "candle.exe"
            if candle_exe.exists():
                self.wix_path = wix_dir
                self.log(f"WiX Toolset安装完成: {self.wix_path}", "SUCCESS")
                return True
            else:
                self.log("WiX Toolset安装失败", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"安装WiX Toolset失败: {e}", "ERROR")
            return False
            
    def ensure_wix_toolset(self) -> bool:
        """确保WiX Toolset可用"""
        if self.check_wix_toolset():
            return True
            
        self.log("需要安装WiX Toolset", "INFO")
        
        if not self.download_wix_toolset():
            return False
            
        if not self.install_wix_toolset():
            return False
            
        return True
        
    def generate_file_list(self) -> str:
        """生成文件列表的WiX组件"""
        self.log("生成文件列表...", "INFO")

        dist_dir = self.project_root / "dist" / "MeetSpaceWeChatSender"
        if not dist_dir.exists():
            raise FileNotFoundError("构建目录不存在")

        components = []
        component_refs = []
        file_id = 1

        def add_directory(dir_path: Path, relative_path: str = ""):
            nonlocal file_id, components, component_refs

            if not dir_path.exists():
                return

            # 为每个目录创建一个组件
            dir_name = dir_path.name if relative_path else "Root"
            component_id = f"Component_{dir_name}_{file_id}"
            component_refs.append(f'      <ComponentRef Id="{component_id}" />')

            # 创建目录组件
            target_dir = f"INSTALLFOLDER\\{relative_path}" if relative_path else "INSTALLFOLDER"
            component_xml = f'    <Component Id="{component_id}" Directory="INSTALLFOLDER" Guid="*">\n'

            # 添加文件
            for file_path in dir_path.iterdir():
                if file_path.is_file():
                    rel_source = file_path.relative_to(self.project_root)
                    file_xml_id = f"File_{file_id}"
                    target_name = file_path.name

                    component_xml += f'      <File Id="{file_xml_id}" Source="{rel_source}" Name="{target_name}" />\n'
                    file_id += 1

            component_xml += '    </Component>\n'
            components.append(component_xml)

            # 递归处理子目录
            for subdir in dir_path.iterdir():
                if subdir.is_dir() and subdir.name != "__pycache__":
                    sub_relative = f"{relative_path}\\{subdir.name}" if relative_path else subdir.name
                    add_directory(subdir, sub_relative)

        # 处理主目录
        add_directory(dist_dir)

        return "\n".join(components), "\n".join(component_refs)

    def create_wxs_file(self) -> Path:
        """创建WiX源文件(.wxs)"""
        self.log("创建WiX源文件...", "STEP")

        # 生成文件列表
        try:
            components_xml, component_refs_xml = self.generate_file_list()
        except Exception as e:
            self.log(f"生成文件列表失败: {e}", "ERROR")
            # 使用简化版本
            components_xml = '''    <Component Id="MainExecutable" Directory="INSTALLFOLDER" Guid="*">
      <File Id="MainExe" Source="dist\\MeetSpaceWeChatSender\\MeetSpaceWeChatSender.exe" KeyPath="yes" />
    </Component>'''
            component_refs_xml = '      <ComponentRef Id="MainExecutable" />'

        wxs_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*"
           Name="Meet space 微信群发助手"
           Language="1033"
           Version="*******"
           Manufacturer="Meet space"
           UpgradeCode="{{12345678-1234-1234-1234-123456789012}}">

    <Package InstallerVersion="200"
             Compressed="yes"
             InstallScope="perMachine"
             Description="Meet space WeChat Sender"
             Comments="A safe and efficient WeChat mass messaging tool" />

    <MajorUpgrade DowngradeErrorMessage="A newer version is already installed." />

    <MediaTemplate EmbedCab="yes" />

    <Feature Id="ProductFeature" Title="Main Program" Level="1">
{component_refs_xml}
    </Feature>

    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="MeetSpaceWeChatSender" />
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Meet space WeChat Sender"/>
      </Directory>
      <Directory Id="DesktopFolder" Name="Desktop"/>
    </Directory>

{components_xml}

    <DirectoryRef Id="ApplicationProgramsFolder">
      <Component Id="ApplicationShortcut" Guid="*">
        <Shortcut Id="ApplicationStartMenuShortcut"
                  Name="Meet space WeChat Sender"
                  Description="Meet space WeChat Sender"
                  Target="[INSTALLFOLDER]MeetSpaceWeChatSender.exe"
                  WorkingDirectory="INSTALLFOLDER" />
        <Shortcut Id="UninstallProduct"
                  Name="Uninstall Meet space WeChat Sender"
                  Description="Uninstall Meet space WeChat Sender"
                  Target="[System64Folder]msiexec.exe"
                  Arguments="/x [ProductCode]" />
        <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
        <RegistryValue Root="HKCU"
                       Key="Software\\Microsoft\\MeetSpaceWeChatSender"
                       Name="installed"
                       Type="integer"
                       Value="1"
                       KeyPath="yes"/>
      </Component>
    </DirectoryRef>

    <DirectoryRef Id="DesktopFolder">
      <Component Id="DesktopShortcut" Guid="*">
        <Shortcut Id="ApplicationDesktopShortcut"
                  Name="Meet space WeChat Sender"
                  Description="Meet space WeChat Sender"
                  Target="[INSTALLFOLDER]MeetSpaceWeChatSender.exe"
                  WorkingDirectory="INSTALLFOLDER" />
        <RegistryValue Root="HKCU"
                       Key="Software\\Microsoft\\MeetSpaceWeChatSender"
                       Name="desktop_shortcut"
                       Type="integer"
                       Value="1"
                       KeyPath="yes"/>
      </Component>
    </DirectoryRef>

    <Feature Id="DesktopShortcutFeature" Title="Desktop Shortcut" Level="1000">
      <ComponentRef Id="DesktopShortcut" />
    </Feature>

    <Feature Id="StartMenuShortcutFeature" Title="Start Menu Shortcut" Level="1">
      <ComponentRef Id="ApplicationShortcut" />
    </Feature>

    <Property Id="ARPPRODUCTICON" Value="[INSTALLFOLDER]MeetSpaceWeChatSender.exe" />
    <Property Id="ARPHELPLINK" Value="https://meetspace.cn/support" />
    <Property Id="ARPURLINFOABOUT" Value="https://meetspace.cn" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />

  </Product>
</Wix>'''

        wxs_file = self.project_root / "installer" / "product.wxs"
        wxs_file.parent.mkdir(parents=True, exist_ok=True)
        wxs_file.write_text(wxs_content, encoding='utf-8')

        self.log(f"WiX源文件已创建: {wxs_file}", "SUCCESS")
        return wxs_file
        
    def use_heat_to_generate_files(self) -> bool:
        """使用Heat工具生成文件列表"""
        self.log("使用Heat工具生成文件列表...", "INFO")

        try:
            work_dir = self.project_root / "installer"
            dist_dir = self.project_root / "dist" / "MeetSpaceWeChatSender"

            if self.wix_path == "PATH":
                heat_cmd = ["heat"]
            else:
                heat_cmd = [str(self.wix_path / "heat.exe")]

            # 使用Heat生成文件列表
            heat_cmd.extend([
                "dir", str(dist_dir),
                "-cg", "ApplicationFiles",
                "-gg", "-g1", "-sf", "-srd",
                "-dr", "INSTALLFOLDER",
                "-var", "var.SourceDir",
                "-out", "files.wxs"
            ])

            result = subprocess.run(heat_cmd, cwd=work_dir, capture_output=True, text=True)

            if result.returncode == 0:
                self.log("Heat工具生成文件列表成功", "SUCCESS")
                return True
            else:
                self.log(f"Heat工具失败: {result.stderr}", "WARNING")
                return False

        except Exception as e:
            self.log(f"Heat工具执行失败: {e}", "WARNING")
            return False

    def create_main_wxs_file(self) -> Path:
        """创建主WiX源文件"""
        self.log("创建主WiX源文件...", "INFO")

        wxs_content = '''<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*"
           Name="Meet space WeChat Sender"
           Language="1033"
           Version="*******"
           Manufacturer="Meet space"
           UpgradeCode="{12345678-1234-1234-1234-123456789012}">

    <Package InstallerVersion="200"
             Compressed="yes"
             InstallScope="perMachine"
             Description="Meet space WeChat Sender Installer"
             Comments="A safe and efficient WeChat mass messaging tool" />

    <MajorUpgrade DowngradeErrorMessage="A newer version is already installed." />

    <MediaTemplate EmbedCab="yes" />

    <Feature Id="ProductFeature" Title="Main Program" Level="1">
      <ComponentGroupRef Id="ApplicationFiles" />
      <ComponentRef Id="ApplicationShortcut" />
    </Feature>

    <Feature Id="DesktopShortcutFeature" Title="Desktop Shortcut" Level="1000">
      <ComponentRef Id="DesktopShortcut" />
    </Feature>

    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="MeetSpaceWeChatSender" />
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Meet space WeChat Sender"/>
      </Directory>
      <Directory Id="DesktopFolder" Name="Desktop"/>
    </Directory>

    <DirectoryRef Id="ApplicationProgramsFolder">
      <Component Id="ApplicationShortcut" Guid="*">
        <Shortcut Id="ApplicationStartMenuShortcut"
                  Name="Meet space WeChat Sender"
                  Description="Meet space WeChat Sender"
                  Target="[INSTALLFOLDER]MeetSpaceWeChatSender.exe"
                  WorkingDirectory="INSTALLFOLDER" />
        <Shortcut Id="UninstallProduct"
                  Name="Uninstall Meet space WeChat Sender"
                  Description="Uninstall Meet space WeChat Sender"
                  Target="[System64Folder]msiexec.exe"
                  Arguments="/x [ProductCode]" />
        <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
        <RegistryValue Root="HKCU"
                       Key="Software\\Microsoft\\MeetSpaceWeChatSender"
                       Name="installed"
                       Type="integer"
                       Value="1"
                       KeyPath="yes"/>
      </Component>
    </DirectoryRef>

    <DirectoryRef Id="DesktopFolder">
      <Component Id="DesktopShortcut" Guid="*">
        <Shortcut Id="ApplicationDesktopShortcut"
                  Name="Meet space WeChat Sender"
                  Description="Meet space WeChat Sender"
                  Target="[INSTALLFOLDER]MeetSpaceWeChatSender.exe"
                  WorkingDirectory="INSTALLFOLDER" />
        <RegistryValue Root="HKCU"
                       Key="Software\\Microsoft\\MeetSpaceWeChatSender"
                       Name="desktop_shortcut"
                       Type="integer"
                       Value="1"
                       KeyPath="yes"/>
      </Component>
    </DirectoryRef>

    <Property Id="ARPPRODUCTICON" Value="[INSTALLFOLDER]MeetSpaceWeChatSender.exe" />
    <Property Id="ARPHELPLINK" Value="https://meetspace.cn/support" />
    <Property Id="ARPURLINFOABOUT" Value="https://meetspace.cn" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />

  </Product>
</Wix>'''

        wxs_file = self.project_root / "installer" / "main.wxs"
        wxs_file.write_text(wxs_content, encoding='utf-8')

        self.log(f"主WiX源文件已创建: {wxs_file}", "SUCCESS")
        return wxs_file

    def build_msi(self) -> bool:
        """构建MSI安装包"""
        self.log("构建MSI安装包...", "STEP")

        if not self.wix_path:
            self.log("WiX Toolset不可用", "ERROR")
            return False

        # 检查可执行文件是否存在
        exe_file = self.project_root / "dist" / "MeetSpaceWeChatSender" / "MeetSpaceWeChatSender.exe"
        if not exe_file.exists():
            self.log("可执行文件不存在，请先构建", "ERROR")
            return False

        try:
            work_dir = self.project_root / "installer"
            work_dir.mkdir(parents=True, exist_ok=True)

            # 1. 使用Heat生成文件列表
            heat_success = self.use_heat_to_generate_files()

            # 2. 创建主WiX文件
            if heat_success:
                main_wxs = self.create_main_wxs_file()
                wxs_files = ["main.wxs", "files.wxs"]
            else:
                # 如果Heat失败，使用简化版本
                main_wxs = self.create_wxs_file()
                wxs_files = ["product.wxs"]

            # 3. 编译WiX源文件
            if self.wix_path == "PATH":
                candle_cmd = ["candle"]
                light_cmd = ["light"]
            else:
                candle_cmd = [str(self.wix_path / "candle.exe")]
                light_cmd = [str(self.wix_path / "light.exe")]

            # 编译所有WiX文件
            obj_files = []
            for wxs_file in wxs_files:
                obj_file = wxs_file.replace('.wxs', '.wixobj')
                obj_files.append(obj_file)

                cmd = candle_cmd + [
                    "-out", obj_file,
                    "-dSourceDir=" + str(self.project_root / "dist" / "MeetSpaceWeChatSender"),
                    wxs_file
                ]

                self.log(f"编译 {wxs_file}...", "INFO")
                result = subprocess.run(cmd, cwd=work_dir, capture_output=True, text=True)

                if result.returncode != 0:
                    self.log(f"编译 {wxs_file} 失败: {result.stderr}", "ERROR")
                    self.log(f"编译输出: {result.stdout}", "INFO")
                    return False

            # 4. 链接生成MSI文件
            msi_name = "MeetSpace_WeChatSender_v1.0.0.msi"
            light_cmd.extend(["-out", msi_name, "-ext", "WixUIExtension"] + obj_files)

            self.log("生成MSI安装包...", "INFO")
            result = subprocess.run(light_cmd, cwd=work_dir, capture_output=True, text=True)

            if result.returncode != 0:
                self.log(f"生成MSI失败: {result.stderr}", "ERROR")
                self.log(f"链接输出: {result.stdout}", "INFO")
                return False

            # 5. 检查生成的MSI文件
            msi_file = work_dir / msi_name
            if msi_file.exists():
                file_size = msi_file.stat().st_size / (1024 * 1024)
                self.log(f"MSI安装包生成成功: {file_size:.1f} MB", "SUCCESS")

                # 移动到项目根目录
                final_path = self.project_root / msi_file.name
                if final_path.exists():
                    final_path.unlink()
                shutil.move(str(msi_file), str(final_path))
                self.log(f"MSI安装包已移动到: {final_path}", "SUCCESS")

                return True
            else:
                self.log("MSI文件未生成", "ERROR")
                return False

        except Exception as e:
            self.log(f"构建MSI失败: {e}", "ERROR")
            return False
            
    def cleanup(self) -> None:
        """清理临时文件"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                self.log("临时文件已清理", "INFO")
        except Exception as e:
            self.log(f"清理临时文件失败: {e}", "WARNING")
            
    def build(self) -> bool:
        """执行完整的构建流程"""
        self.log("=== 开始创建MSI安装程序 ===", "STEP")
        start_time = time.time()
        
        try:
            # 1. 确保WiX Toolset可用
            if not self.ensure_wix_toolset():
                return False
                
            # 2. 构建MSI安装包
            if not self.build_msi():
                return False
                
            # 构建完成
            elapsed_time = time.time() - start_time
            self.log(f"=== MSI安装程序创建完成 (耗时: {elapsed_time:.1f}秒) ===", "SUCCESS")
            
            return True
            
        except KeyboardInterrupt:
            self.log("构建被用户中断", "WARNING")
            return False
        except Exception as e:
            self.log(f"构建过程出错: {e}", "ERROR")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    print("🚀 Meet space 微信群发助手 - MSI安装程序创建工具")
    print("=" * 60)
    
    builder = MSIBuilder()
    success = builder.build()
    
    if success:
        print("\n🎉 MSI安装程序创建成功！")
        
        # 显示结果文件
        msi_files = list(Path(".").glob("MeetSpace_WeChatSender_*.msi"))
        if msi_files:
            msi_file = msi_files[0]
            file_size = msi_file.stat().st_size / (1024 * 1024)
            print(f"📦 MSI安装程序: {msi_file.name} ({file_size:.1f} MB)")
            
        # 询问是否打开文件夹
        try:
            choice = input("\n是否打开输出文件夹？(y/n): ").strip().lower()
            if choice == 'y':
                subprocess.run(['explorer', str(Path(__file__).parent)], check=False)
        except KeyboardInterrupt:
            pass
            
        sys.exit(0)
    else:
        print("\n💥 MSI安装程序创建失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
