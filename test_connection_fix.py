#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连接微信卡死问题修复
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_async_injection():
    """测试异步注入不会卡死"""
    print("🔧 测试异步注入不会卡死...")
    
    try:
        from core.http_api_connector import HTTPAPIConnector
        
        # 创建连接器
        connector = HTTPAPIConnector(
            api_type="wxhelper",
            base_url="http://localhost:19088",
            auto_inject=False
        )
        
        print("✅ 连接器创建成功")
        
        # 测试无GUI注入方法
        print("\n测试无GUI注入方法...")
        success, message = connector._inject_without_gui()
        
        if success:
            print(f"✅ 无GUI注入成功: {message}")
        else:
            print(f"⚠️  无GUI注入失败: {message}")
            if message == "NEED_ELEVATION":
                print("✅ 正确识别需要权限提升")
            else:
                print(f"❌ 其他错误: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_connection():
    """测试异步连接流程"""
    print("\n🔗 测试异步连接流程...")
    
    try:
        from core.http_api_connector import HTTPAPIConnector
        
        # 创建连接器
        connector = HTTPAPIConnector(
            api_type="wxhelper",
            base_url="http://localhost:19088",
            auto_inject=False
        )
        
        print("开始异步连接测试...")
        
        # 测试连接（这应该不会卡死）
        try:
            result = await connector.connect()
            success, message = result
            
            if success:
                print(f"✅ 连接成功: {message}")
            else:
                print(f"⚠️  连接失败: {message}")
                
        except PermissionError as e:
            print(f"✅ 正确捕获权限异常: {e}")
            print("这表明异步处理正常，不会卡死")
        except Exception as e:
            print(f"❌ 连接异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步连接测试失败: {e}")
        return False

def test_gui_permission_handling():
    """测试GUI权限处理（模拟）"""
    print("\n🔐 测试GUI权限处理...")
    
    try:
        # 模拟权限提升处理
        from utils.simple_admin import is_admin
        
        current_admin = is_admin()
        print(f"当前权限状态: {'✅ 管理员' if current_admin else '❌ 普通用户'}")
        
        if current_admin:
            print("✅ 已有管理员权限，GUI权限处理应该正常工作")
        else:
            print("⚠️  无管理员权限，需要测试权限提升对话框")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI权限处理测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 连接卡死问题修复测试")
    print("=" * 60)
    print("测试修复后的异步连接和权限处理")
    print("=" * 60)
    
    tests = [
        ("异步注入测试", test_async_injection),
        ("GUI权限处理测试", test_gui_permission_handling),
        ("异步连接流程测试", lambda: asyncio.create_task(test_async_connection()))
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            elif callable(test_func):
                result_or_task = test_func()
                if asyncio.iscoroutine(result_or_task):
                    result = await result_or_task
                else:
                    result = result_or_task
            else:
                result = await test_func
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 连接卡死修复测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed >= 2:
        print("\n🎉 连接卡死问题修复成功！")
        print("\n✨ 修复内容:")
        print("  🔄 分离了GUI操作和异步操作")
        print("  🔐 权限提升在主线程中处理")
        print("  ⚡ 注入操作在线程池中执行")
        print("  🛡️  异常处理机制完善")
        
        print("\n📋 现在点击连接微信应该不会卡死了！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    print("\n按回车键退出...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n用户取消测试")
    except Exception as e:
        print(f"\n测试异常: {e}")
    
    input()
