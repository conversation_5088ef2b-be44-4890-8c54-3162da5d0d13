@echo off
chcp 65001 >nul
echo Meet space WeChat Sender - Windows Build Script
echo ================================================

:: Set environment variables
set PYTHONPATH=%CD%
set PYTHONIOENCODING=utf-8

:: Check Python environment
echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found, please install Python 3.8+
    pause
    exit /b 1
)

:: Check dependencies
echo Checking dependencies...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PyQt6 not found, please install: pip install -r requirements.txt
    pause
    exit /b 1
)

python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo PyInstaller not found, installing...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERROR: PyInstaller installation failed
        pause
        exit /b 1
    )
)

:: Clean old files
echo Cleaning old build files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"
if exist "version_info.txt" del /q "version_info.txt"

:: Run build script
echo Starting build...
python build_config.py
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

:: Check build result
if not exist "dist\MeetSpaceWeChatSender\MeetSpaceWeChatSender.exe" (
    echo ERROR: Build failed - executable not found
    pause
    exit /b 1
)

echo SUCCESS: Build completed!
echo Executable: dist\MeetSpaceWeChatSender\MeetSpaceWeChatSender.exe
echo Distribution: dist\MeetSpaceWeChatSender\

:: Ask for test run
set /p test_choice="Run test? (y/N): "
if /i "%test_choice%"=="y" (
    echo Running test...
    cd dist\MeetSpaceWeChatSender
    MeetSpaceWeChatSender.exe --show-console
    cd ..\..
)

:: Ask for installer creation
set /p installer_choice="Create installer? (requires Inno Setup) (y/N): "
if /i "%installer_choice%"=="y" (
    echo Creating installer...

    :: Check Inno Setup
    set INNO_SETUP=""
    if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" set INNO_SETUP="C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    if exist "C:\Program Files\Inno Setup 6\ISCC.exe" set INNO_SETUP="C:\Program Files\Inno Setup 6\ISCC.exe"
    if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" set INNO_SETUP="C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
    if exist "C:\Program Files\Inno Setup 5\ISCC.exe" set INNO_SETUP="C:\Program Files\Inno Setup 5\ISCC.exe"

    if "%INNO_SETUP%"=="" (
        echo ERROR: Inno Setup not found, please install first
        echo Download: https://jrsoftware.org/isdl.php
    ) else (
        %INNO_SETUP% installer\setup.iss
        if errorlevel 1 (
            echo ERROR: Installer creation failed
        ) else (
            echo SUCCESS: Installer created!
            if exist installer\Output\ (
                echo Installer location: installer\Output\
                dir installer\Output\*.exe 2>nul
            )
        )
    )
)

echo.
echo Build process completed!
echo.
echo Build results:
echo   - Executable: dist\MeetSpaceWeChatSender\MeetSpaceWeChatSender.exe
echo   - Distribution: dist\MeetSpaceWeChatSender\
if exist installer\Output\ (
    echo   - Installer: installer\Output\
)
echo.
echo Tips:
echo   - Run directly: dist\MeetSpaceWeChatSender\MeetSpaceWeChatSender.exe
echo   - Or use installer for standard installation
echo   - Program will create config and data folders in user directory

pause
