#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分组独立性和新建分组初始参数
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_new_group_default_config():
    """测试新建分组的默认配置"""
    print("🔧 测试新建分组的默认配置...")
    
    try:
        from core.group_manager import ContactGroup
        from datetime import datetime

        # 创建新的定时发送分组
        new_group = ContactGroup("test_group_001", "测试分组1", "timing")
        
        # 检查默认配置
        config = new_group.config
        
        # 1. 检查执行日期和时间是否是当前的
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_time = datetime.now().strftime("%H:%M")
        
        default_date = config.get("schedule_settings", {}).get("default_date", "")
        default_time = config.get("schedule_settings", {}).get("default_time", "")
        
        if default_date == current_date:
            print(f"✅ 默认执行日期正确: {default_date}")
        else:
            print(f"❌ 默认执行日期错误: 期望{current_date}, 实际{default_date}")
            return False
        
        # 时间可能有1-2分钟的差异，这是正常的
        print(f"✅ 默认执行时间: {default_time} (当前时间: {current_time})")
        
        # 2. 检查发送设置
        send_settings = config.get("send_settings", {})
        
        if send_settings.get("interval_seconds") == 5:
            print("✅ 默认发送间隔: 5秒")
        else:
            print(f"❌ 默认发送间隔错误: {send_settings.get('interval_seconds')}")
            return False
        
        if send_settings.get("batch_size") == 10:
            print("✅ 默认批量大小: 10个")
        else:
            print(f"❌ 默认批量大小错误: {send_settings.get('batch_size')}")
            return False
        
        if send_settings.get("use_system_risk_control") == True:
            print("✅ 默认使用系统风控设置")
        else:
            print(f"❌ 默认风控设置错误: {send_settings.get('use_system_risk_control')}")
            return False
        
        # 3. 检查消息设置
        message_settings = config.get("message_settings", {})
        
        if message_settings.get("default_content") == "":
            print("✅ 默认消息内容为空")
        else:
            print(f"❌ 默认消息内容不为空: {message_settings.get('default_content')}")
            return False
        
        if message_settings.get("default_type") == "rich_text":
            print("✅ 默认消息类型: 富文本消息")
        else:
            print(f"❌ 默认消息类型错误: {message_settings.get('default_type')}")
            return False
        
        if message_settings.get("template_id") == "":
            print("✅ 默认消息模板: 手动输入")
        else:
            print(f"❌ 默认消息模板错误: {message_settings.get('template_id')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_group_independence():
    """测试分组设置独立性"""
    print("\n🔧 测试分组设置独立性...")
    
    try:
        from core.group_manager import ContactGroup

        # 创建两个不同的分组
        group1 = ContactGroup("test_group_001", "测试分组1", "timing")
        group2 = ContactGroup("test_group_002", "测试分组2", "timing")
        
        # 为分组1设置不同的配置
        group1.update_config("message_settings.default_content", "分组1的消息内容")
        group1.update_config("message_settings.default_type", "text")
        group1.update_config("schedule_settings.default_time", "10:00")
        group1.update_config("send_settings.use_system_risk_control", False)
        
        # 为分组2设置不同的配置
        group2.update_config("message_settings.default_content", "分组2的富文本消息")
        group2.update_config("message_settings.default_type", "rich_text")
        group2.update_config("schedule_settings.default_time", "14:00")
        group2.update_config("send_settings.use_system_risk_control", True)
        
        # 验证分组1的配置
        if group1.get_config("message_settings.default_content") == "分组1的消息内容":
            print("✅ 分组1消息内容独立")
        else:
            print("❌ 分组1消息内容不独立")
            return False
        
        if group1.get_config("message_settings.default_type") == "text":
            print("✅ 分组1消息类型独立")
        else:
            print("❌ 分组1消息类型不独立")
            return False
        
        if group1.get_config("schedule_settings.default_time") == "10:00":
            print("✅ 分组1执行时间独立")
        else:
            print("❌ 分组1执行时间不独立")
            return False
        
        if group1.get_config("send_settings.use_system_risk_control") == False:
            print("✅ 分组1风控设置独立")
        else:
            print("❌ 分组1风控设置不独立")
            return False
        
        # 验证分组2的配置
        if group2.get_config("message_settings.default_content") == "分组2的富文本消息":
            print("✅ 分组2消息内容独立")
        else:
            print("❌ 分组2消息内容不独立")
            return False
        
        if group2.get_config("message_settings.default_type") == "rich_text":
            print("✅ 分组2消息类型独立")
        else:
            print("❌ 分组2消息类型不独立")
            return False
        
        if group2.get_config("schedule_settings.default_time") == "14:00":
            print("✅ 分组2执行时间独立")
        else:
            print("❌ 分组2执行时间不独立")
            return False
        
        if group2.get_config("send_settings.use_system_risk_control") == True:
            print("✅ 分组2风控设置独立")
        else:
            print("❌ 分组2风控设置不独立")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_load_config_logic():
    """测试加载配置逻辑"""
    print("\n🔧 测试加载配置逻辑...")
    
    try:
        import inspect
        from ui.timing_send_page import TimingSendPage
        
        # 检查load_group_config_to_ui方法
        method_source = inspect.getsource(TimingSendPage.load_group_config_to_ui)
        
        checks = [
            ("分组独立日志", "独立配置"),
            ("执行日期加载", "default_date"),
            ("执行时间加载", "default_time"),
            ("消息类型设置", "default_type"),
            ("消息内容加载", "default_content"),
            ("风控设置加载", "use_system_risk_control"),
            ("信号断开重连", "disconnect")
        ]
        
        for check_name, keyword in checks:
            if keyword in method_source:
                print(f"✅ {check_name}: 包含 {keyword}")
            else:
                print(f"❌ {check_name}: 缺少 {keyword}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 分组独立性和初始参数测试")
    print("=" * 60)
    print("测试新建分组的初始参数和分组设置独立性")
    print("=" * 60)
    
    tests = [
        ("新建分组默认配置测试", test_new_group_default_config),
        ("分组设置独立性测试", test_group_independence),
        ("加载配置逻辑测试", test_load_config_logic)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 分组独立性和初始参数测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 分组独立性和初始参数修复成功！")
        print("\n✨ 新建分组的初始参数:")
        print("  📅 执行日期: 当前日期")
        print("  ⏰ 执行时间: 当前时间")
        print("  📊 发送间隔: 5秒")
        print("  📦 批量大小: 10个")
        print("  🛡️  风控设置: 默认使用系统风控")
        print("  📝 消息内容: 空的")
        print("  📄 消息类型: 富文本消息")
        print("  📋 消息模板: 手动输入")
        
        print("\n✨ 分组设置独立性:")
        print("  🔒 每个分组有独立的消息内容")
        print("  🔒 每个分组有独立的消息类型")
        print("  🔒 每个分组有独立的执行时间")
        print("  🔒 每个分组有独立的风控设置")
        print("  🔄 切换分组时正确加载各自的配置")
        print("  💾 保存时不会影响其他分组")
        
        print("\n🎯 现在分组设置完全独立，不会互相影响！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
