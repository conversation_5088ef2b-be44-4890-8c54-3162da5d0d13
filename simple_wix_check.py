#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WiX检查工具
"""

import subprocess
import os
from pathlib import Path

def check_wix_simple():
    """简单检查WiX"""
    print("🔍 检查WiX Toolset...")
    
    # 检查常见的WiX安装路径
    possible_paths = [
        r"C:\Program Files (x86)\WiX Toolset v3.11\bin",
        r"C:\Program Files\WiX Toolset v3.11\bin", 
        r"C:\Program Files (x86)\WiX Toolset v3.14\bin",
        r"C:\Program Files\WiX Toolset v3.14\bin",
        r"C:\Program Files (x86)\WiX Toolset v4.0\bin",
        r"C:\Program Files\WiX Toolset v4.0\bin",
        r"C:\Users\<USER>\.dotnet\tools",  # dotnet工具路径
        r"C:\Program Files\dotnet\tools",
    ]
    
    print("📁 检查可能的安装路径:")
    found_paths = []
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"  ✅ 找到目录: {path}")
            
            # 检查是否有candle.exe
            candle_path = os.path.join(path, "candle.exe")
            if os.path.exists(candle_path):
                print(f"    ✅ 找到candle.exe")
                found_paths.append(path)
            else:
                print(f"    ❌ 未找到candle.exe")
        else:
            print(f"  ❌ 目录不存在: {path}")
    
    # 检查PATH环境变量
    print(f"\n🔍 检查PATH环境变量:")
    path_env = os.environ.get("PATH", "")
    wix_in_path = False
    
    for path_item in path_env.split(os.pathsep):
        if "wix" in path_item.lower():
            print(f"  ✅ PATH中包含WiX: {path_item}")
            wix_in_path = True
    
    if not wix_in_path:
        print("  ❌ PATH中未找到WiX相关路径")
    
    # 尝试直接运行命令
    print(f"\n🧪 测试WiX命令:")
    
    try:
        result = subprocess.run(["candle"], capture_output=True, text=True, timeout=5)
        print("  ✅ candle命令可用")
        return True
    except FileNotFoundError:
        print("  ❌ candle命令不可用")
    except Exception as e:
        print(f"  ❌ candle命令错误: {e}")
    
    # 如果找到了安装路径但命令不可用，提供解决方案
    if found_paths:
        print(f"\n💡 解决方案:")
        print(f"找到WiX安装但命令不可用，请将以下路径添加到系统PATH:")
        for path in found_paths:
            print(f"  {path}")
        
        print(f"\n📋 添加PATH的步骤:")
        print(f"1. 右键'此电脑' -> 属性")
        print(f"2. 高级系统设置 -> 环境变量")
        print(f"3. 在系统变量中找到PATH，点击编辑")
        print(f"4. 添加新路径: {found_paths[0] if found_paths else 'WiX安装路径'}")
        print(f"5. 重启命令提示符")
        
        return False
    else:
        print(f"\n❌ 未找到WiX Toolset安装")
        print(f"请安装WiX Toolset:")
        print(f"1. 双击运行: C:\\Users\\<USER>\\Downloads\\Programs\\wix-cli-x64.msi")
        print(f"2. 或使用命令: msiexec /i \"C:\\Users\\<USER>\\Downloads\\Programs\\wix-cli-x64.msi\"")
        return False

if __name__ == "__main__":
    print("🔧 WiX Toolset 简单检查")
    print("=" * 40)
    
    success = check_wix_simple()
    
    if success:
        print(f"\n🎉 WiX Toolset 可用！可以构建MSI了")
    else:
        print(f"\n⚠️  需要安装或配置WiX Toolset")
    
    input(f"\n按回车键退出...")
