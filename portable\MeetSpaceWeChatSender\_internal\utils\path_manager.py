#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径管理器

处理程序打包后的路径问题，支持开发环境和打包环境。
适配Windows安装程序的目录结构。
"""

import os
import sys
from pathlib import Path
from typing import Optional, Union

from utils.logger import setup_logger

logger = setup_logger("path_manager")


class PathManager:
    """路径管理器"""
    
    def __init__(self):
        self._app_name = "MeetSpaceWeChatSender"
        self._version = "1.0.0"
        self._is_frozen = getattr(sys, 'frozen', False)
        self._executable_dir = None
        self._app_data_dir = None
        self._user_data_dir = None
        self._temp_dir = None
        
        self._initialize_paths()
    
    def _initialize_paths(self):
        """初始化所有路径"""
        try:
            # 确定可执行文件目录
            if self._is_frozen:
                # 打包后的环境
                if hasattr(sys, '_MEIPASS'):
                    # PyInstaller临时目录
                    self._executable_dir = Path(sys.executable).parent
                else:
                    # 其他打包工具
                    self._executable_dir = Path(sys.executable).parent
            else:
                # 开发环境
                self._executable_dir = Path(__file__).parent.parent
            
            # 用户数据目录（配置、日志等）
            app_data = os.environ.get('APPDATA')
            if app_data:
                self._app_data_dir = Path(app_data) / self._app_name
            else:
                self._app_data_dir = Path.home() / f".{self._app_name.lower()}"
            
            # 用户文档目录（备份、导出等）
            documents = self._get_documents_folder()
            self._user_data_dir = documents / self._app_name
            
            # 临时目录
            import tempfile
            self._temp_dir = Path(tempfile.gettempdir()) / self._app_name
            
            # 创建必要的目录
            self._create_directories()
            
            logger.info(f"路径管理器初始化完成")
            logger.info(f"可执行文件目录: {self._executable_dir}")
            logger.info(f"应用数据目录: {self._app_data_dir}")
            logger.info(f"用户数据目录: {self._user_data_dir}")
            logger.info(f"是否打包环境: {self._is_frozen}")
            
        except Exception as e:
            logger.error(f"路径管理器初始化失败: {e}")
            raise
    
    def _get_documents_folder(self) -> Path:
        """获取用户文档文件夹"""
        try:
            import winreg
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders") as key:
                documents = winreg.QueryValueEx(key, "Personal")[0]
                return Path(documents)
        except:
            # 备用方案
            return Path.home() / "Documents"
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self._app_data_dir,
            self._app_data_dir / "config",
            self._app_data_dir / "logs",
            self._app_data_dir / "cache",
            self._app_data_dir / "temp",
            self._user_data_dir,
            self._user_data_dir / "backups",
            self._user_data_dir / "exports",
            self._user_data_dir / "templates",
            self._temp_dir
        ]
        
        for directory in directories:
            try:
                directory.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                logger.warning(f"创建目录失败 {directory}: {e}")
    
    @property
    def is_frozen(self) -> bool:
        """是否为打包环境"""
        return self._is_frozen
    
    @property
    def executable_dir(self) -> Path:
        """可执行文件目录"""
        return self._executable_dir
    
    @property
    def app_data_dir(self) -> Path:
        """应用数据目录"""
        return self._app_data_dir
    
    @property
    def user_data_dir(self) -> Path:
        """用户数据目录"""
        return self._user_data_dir
    
    @property
    def temp_dir(self) -> Path:
        """临时目录"""
        return self._temp_dir
    
    def get_resource_path(self, relative_path: Union[str, Path]) -> Path:
        """获取资源文件路径"""
        relative_path = Path(relative_path)
        
        if self._is_frozen:
            # 打包环境：资源文件在可执行文件目录下
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller
                base_path = Path(sys._MEIPASS)
            else:
                base_path = self._executable_dir
        else:
            # 开发环境：相对于项目根目录
            base_path = self._executable_dir
        
        resource_path = base_path / relative_path
        
        if not resource_path.exists():
            logger.warning(f"资源文件不存在: {resource_path}")
        
        return resource_path
    
    def get_config_path(self, filename: str) -> Path:
        """获取配置文件路径"""
        return self._app_data_dir / "config" / filename
    
    def get_log_path(self, filename: str) -> Path:
        """获取日志文件路径"""
        return self._app_data_dir / "logs" / filename
    
    def get_cache_path(self, filename: str) -> Path:
        """获取缓存文件路径"""
        return self._app_data_dir / "cache" / filename
    
    def get_backup_path(self, filename: str) -> Path:
        """获取备份文件路径"""
        return self._user_data_dir / "backups" / filename
    
    def get_export_path(self, filename: str) -> Path:
        """获取导出文件路径"""
        return self._user_data_dir / "exports" / filename
    
    def get_template_path(self, filename: str) -> Path:
        """获取模板文件路径"""
        # 优先使用用户模板目录
        user_template = self._user_data_dir / "templates" / filename
        if user_template.exists():
            return user_template
        
        # 备用：使用内置模板
        return self.get_resource_path(f"resources/templates/{filename}")
    
    def get_temp_path(self, filename: str) -> Path:
        """获取临时文件路径"""
        return self._temp_dir / filename
    
    def get_data_path(self, relative_path: Union[str, Path]) -> Path:
        """获取数据文件路径"""
        relative_path = Path(relative_path)
        
        # 数据文件存储在应用数据目录
        data_path = self._app_data_dir / relative_path
        
        # 确保父目录存在
        data_path.parent.mkdir(parents=True, exist_ok=True)
        
        return data_path
    
    def migrate_old_data(self):
        """迁移旧版本数据"""
        try:
            # 检查是否有旧版本数据需要迁移
            old_paths = [
                self._executable_dir / "config",
                self._executable_dir / "logs",
                self._executable_dir / "data",
                Path.cwd() / "config",
                Path.cwd() / "logs",
                Path.cwd() / "data"
            ]
            
            for old_path in old_paths:
                if old_path.exists() and old_path.is_dir():
                    logger.info(f"发现旧数据目录: {old_path}")
                    self._migrate_directory(old_path)
                    
        except Exception as e:
            logger.error(f"数据迁移失败: {e}")
    
    def _migrate_directory(self, old_dir: Path):
        """迁移目录数据"""
        try:
            import shutil
            
            if old_dir.name == "config":
                target_dir = self._app_data_dir / "config"
            elif old_dir.name == "logs":
                target_dir = self._app_data_dir / "logs"
            elif old_dir.name == "data":
                target_dir = self._app_data_dir
            else:
                return
            
            # 复制文件
            for file_path in old_dir.rglob("*"):
                if file_path.is_file():
                    relative_path = file_path.relative_to(old_dir)
                    target_path = target_dir / relative_path
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    if not target_path.exists():
                        shutil.copy2(file_path, target_path)
                        logger.info(f"迁移文件: {file_path} -> {target_path}")
            
        except Exception as e:
            logger.error(f"迁移目录失败 {old_dir}: {e}")
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if self._temp_dir.exists():
                import shutil
                shutil.rmtree(self._temp_dir, ignore_errors=True)
                self._temp_dir.mkdir(parents=True, exist_ok=True)
                logger.info("临时文件清理完成")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")
    
    def get_install_info(self) -> dict:
        """获取安装信息"""
        return {
            "app_name": self._app_name,
            "version": self._version,
            "is_frozen": self._is_frozen,
            "executable_dir": str(self._executable_dir),
            "app_data_dir": str(self._app_data_dir),
            "user_data_dir": str(self._user_data_dir),
            "temp_dir": str(self._temp_dir),
            "python_version": sys.version,
            "platform": sys.platform
        }


# 全局路径管理器实例
path_manager = PathManager()


def get_resource_path(relative_path: Union[str, Path]) -> Path:
    """获取资源文件路径的便捷函数"""
    return path_manager.get_resource_path(relative_path)


def get_config_path(filename: str) -> Path:
    """获取配置文件路径的便捷函数"""
    return path_manager.get_config_path(filename)


def get_log_path(filename: str) -> Path:
    """获取日志文件路径的便捷函数"""
    return path_manager.get_log_path(filename)


def get_data_path(relative_path: Union[str, Path]) -> Path:
    """获取数据文件路径的便捷函数"""
    return path_manager.get_data_path(relative_path)


if __name__ == "__main__":
    # 测试路径管理器
    print("🧪 测试路径管理器")
    print("=" * 50)
    
    pm = PathManager()
    
    print(f"应用名称: {pm._app_name}")
    print(f"是否打包: {pm.is_frozen}")
    print(f"可执行目录: {pm.executable_dir}")
    print(f"应用数据目录: {pm.app_data_dir}")
    print(f"用户数据目录: {pm.user_data_dir}")
    print(f"临时目录: {pm.temp_dir}")
    
    print("\n路径示例:")
    print(f"配置文件: {pm.get_config_path('config.json')}")
    print(f"日志文件: {pm.get_log_path('app.log')}")
    print(f"资源文件: {pm.get_resource_path('icons/app.ico')}")
    print(f"备份文件: {pm.get_backup_path('backup.zip')}")
    
    print("\n安装信息:")
    info = pm.get_install_info()
    for key, value in info.items():
        print(f"  {key}: {value}")
