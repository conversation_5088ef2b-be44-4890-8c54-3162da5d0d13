"""
微信配置管理模块

管理微信群发助手的各种配置参数。
"""

import json
import os
from dataclasses import asdict, dataclass
from typing import Any, Dict, Optional

from utils.security import security_manager


@dataclass
class WeChatConfig:
    """微信配置类"""

    # 连接设置
    auto_start: bool = False
    startup_delay: int = 5
    login_timeout: int = 30

    # 发送设置
    send_interval_min: int = 3
    send_interval_max: int = 8
    batch_size: int = 10
    batch_interval: int = 60
    daily_send_limit: int = 200
    hourly_send_limit: int = 50

    # 安全设置
    simulate_human: bool = True
    random_delay: bool = True
    enable_risk_control: bool = True

    # 重试设置
    max_retries: int = 3
    retry_delay: int = 5

    # 文件路径（将在运行时由路径管理器设置）
    config_file: str = "wechat_config.json"
    log_dir: str = "logs"
    template_dir: str = "templates"

    # 安全设置
    encrypt_config: bool = False
    api_key: Optional[str] = None

    # 界面设置
    theme: str = "默认主题"
    font_size: str = "中"
    minimize_to_tray: bool = False
    remember_window_state: bool = True

    # 高级设置
    debug_mode: bool = False
    auto_backup: bool = True
    check_updates: bool = True

    @classmethod
    def load_from_file(cls, config_file: str = None) -> "WeChatConfig":
        """从文件加载配置"""
        if config_file is None:
            # 使用路径管理器获取配置文件路径
            try:
                from utils.path_manager import path_manager
                config_file = str(path_manager.get_config_path(cls().config_file))
            except ImportError:
                config_file = cls.config_file

        if os.path.exists(config_file):
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    content = f.read()

                # 检查是否为加密配置
                try:
                    # 尝试直接解析JSON
                    data = json.loads(content)
                except json.JSONDecodeError:
                    # 如果解析失败，尝试解密
                    try:
                        decrypted_content = security_manager.decrypt_data(content)
                        data = json.loads(decrypted_content)
                    except Exception:
                        print("配置文件解密失败，使用默认配置")
                        return cls()

                return cls(**data)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return cls()
        else:
            # 创建默认配置文件
            config = cls()
            config.save_to_file(config_file)
            return config

    def save(self) -> bool:
        """保存配置（简化方法）"""
        return self.save_to_file()

    def save_to_file(self, config_file: str = None) -> bool:
        """保存配置到文件"""
        if config_file is None:
            # 使用路径管理器获取配置文件路径
            try:
                from utils.path_manager import path_manager
                config_file = str(path_manager.get_config_path(self.config_file))
            except ImportError:
                config_file = self.config_file

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(config_file), exist_ok=True)

            # 自动备份功能
            if self.auto_backup:
                self._create_backup(config_file)

            # 准备配置数据
            config_data = asdict(self)
            json_content = json.dumps(config_data, ensure_ascii=False, indent=2)

            # 根据设置决定是否加密
            if self.encrypt_config:
                content_to_save = security_manager.encrypt_data(json_content)
            else:
                content_to_save = json_content

            with open(config_file, "w", encoding="utf-8") as f:
                f.write(content_to_save)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False

    def _create_backup(self, config_file: str) -> None:
        """创建配置文件备份"""
        try:
            if not os.path.exists(config_file):
                return

            # 创建备份目录
            backup_dir = os.path.join(os.path.dirname(config_file), "backups")
            os.makedirs(backup_dir, exist_ok=True)

            # 生成备份文件名（包含时间戳）
            from datetime import datetime

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"config_backup_{timestamp}.json"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 复制当前配置文件到备份目录
            import shutil

            shutil.copy2(config_file, backup_path)

            # 清理旧备份（保留最近10个）
            self._cleanup_old_backups(backup_dir)

            print(f"配置文件已备份到: {backup_path}")

        except Exception as e:
            print(f"创建配置备份失败: {e}")

    def _cleanup_old_backups(self, backup_dir: str, keep_count: int = 10) -> None:
        """清理旧的备份文件，保留最近的几个"""
        try:
            backup_files = []
            for filename in os.listdir(backup_dir):
                if filename.startswith("config_backup_") and filename.endswith(".json"):
                    filepath = os.path.join(backup_dir, filename)
                    backup_files.append((filepath, os.path.getmtime(filepath)))

            # 按修改时间排序，最新的在前
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # 删除超出保留数量的备份文件
            for filepath, _ in backup_files[keep_count:]:
                os.remove(filepath)
                print(f"已删除旧备份: {os.path.basename(filepath)}")

        except Exception as e:
            print(f"清理旧备份失败: {e}")

    def update(self, **kwargs) -> None:
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def validate(self) -> bool:
        """验证配置参数的有效性"""
        if self.send_interval_min <= 0 or self.send_interval_max <= 0:
            return False
        if self.send_interval_min > self.send_interval_max:
            return False
        if self.batch_size <= 0 or self.batch_interval <= 0:
            return False
        if self.daily_send_limit <= 0 or self.hourly_send_limit <= 0:
            return False
        if self.max_retries < 0 or self.retry_delay <= 0:
            return False
        return True

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    def reset_to_defaults(self) -> None:
        """重置所有设置到默认值"""
        # 连接设置
        self.auto_start = False
        self.startup_delay = 5
        self.login_timeout = 30

        # 发送设置
        self.send_interval_min = 3
        self.send_interval_max = 8
        self.batch_size = 10
        self.batch_interval = 60
        self.daily_send_limit = 200
        self.hourly_send_limit = 50

        # 安全设置
        self.simulate_human = True
        self.random_delay = True
        self.enable_risk_control = True

        # 重试设置
        self.max_retries = 3
        self.retry_delay = 5

        # 保存到文件
        self.save_to_file()
