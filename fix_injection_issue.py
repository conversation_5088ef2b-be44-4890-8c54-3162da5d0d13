#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复注入问题的构建脚本
确保注入工具和DLL文件在运行时可以正确访问
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    for dir_name in ["build", "dist", "temp_build"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")

def setup_environment():
    """设置构建环境"""
    print("🔧 设置构建环境...")
    
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    print("  ✅ 环境已设置")

def create_injection_helper():
    """创建注入帮助脚本"""
    print("📝 创建注入帮助脚本...")
    
    helper_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注入帮助模块 - 确保在打包环境中正确找到注入工具
"""

import os
import sys
import shutil
import tempfile
from pathlib import Path

def get_resource_path(relative_path):
    """获取资源文件路径，兼容打包和开发环境"""
    try:
        # PyInstaller打包后的路径
        base_path = Path(sys._MEIPASS)
    except AttributeError:
        # 开发环境路径
        base_path = Path(__file__).parent
    
    return base_path / relative_path

def extract_injection_tools():
    """提取注入工具到临时目录"""
    print("🔧 提取注入工具...")
    
    # 创建临时目录
    temp_dir = Path(tempfile.gettempdir()) / "MeetSpaceWeChatSender"
    temp_dir.mkdir(exist_ok=True)
    
    tools_dir = temp_dir / "tools"
    wxhelper_dir = temp_dir / "wxhelper_files"
    
    tools_dir.mkdir(exist_ok=True)
    wxhelper_dir.mkdir(exist_ok=True)
    
    # 提取注入工具
    try:
        injector_src = get_resource_path("tools/Injector.exe")
        injector_dst = tools_dir / "Injector.exe"
        
        if injector_src.exists():
            shutil.copy2(injector_src, injector_dst)
            print(f"  ✅ 提取注入工具: {injector_dst}")
        else:
            print(f"  ❌ 未找到注入工具: {injector_src}")
            return None
    except Exception as e:
        print(f"  ❌ 提取注入工具失败: {e}")
        return None
    
    # 提取DLL文件
    dll_files = ["wxhelper.dll", "wxhelper_latest.dll"]
    for dll_file in dll_files:
        try:
            dll_src = get_resource_path(f"wxhelper_files/{dll_file}")
            dll_dst = wxhelper_dir / dll_file
            
            if dll_src.exists():
                shutil.copy2(dll_src, dll_dst)
                print(f"  ✅ 提取DLL: {dll_dst}")
            else:
                print(f"  ⚠️  未找到DLL: {dll_src}")
        except Exception as e:
            print(f"  ❌ 提取DLL失败: {e}")
    
    return {
        "tools_dir": str(tools_dir),
        "wxhelper_dir": str(wxhelper_dir),
        "injector_path": str(injector_dst)
    }

def get_injection_paths():
    """获取注入工具路径"""
    return extract_injection_tools()

if __name__ == "__main__":
    paths = extract_injection_tools()
    if paths:
        print("✅ 注入工具提取成功")
        for key, value in paths.items():
            print(f"  {key}: {value}")
    else:
        print("❌ 注入工具提取失败")
'''
    
    with open("injection_helper.py", "w", encoding="utf-8") as f:
        f.write(helper_content)
    
    print("  ✅ 已创建: injection_helper.py")

def build_with_injection_fix():
    """构建修复注入问题的版本"""
    print("🔨 构建修复注入问题的版本...")
    
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MeetSpaceWeChatSender",
        "--icon=resources/icons/app_icon.ico",
        
        # === 关键：确保注入工具可访问 ===
        # 注入工具 - 放在tools目录
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64;tools/x64",
        "--add-data=tools/Win32;tools/Win32",
        "--add-data=tools/ARM64;tools/ARM64",
        
        # DLL文件 - 放在wxhelper_files目录
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_original_backup.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_x64_backup.dll;wxhelper_files",
        
        # 注入帮助脚本
        "--add-data=injection_helper.py;.",
        
        # 基础资源
        "--add-data=resources/icons;resources/icons",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        "--add-data=README.md;.",
        
        # === 核心隐藏导入 ===
        # PyQt6
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        
        # 微信相关
        "--hidden-import=wcferry",
        
        # 系统和进程
        "--hidden-import=ctypes",
        "--hidden-import=ctypes.wintypes",
        "--hidden-import=subprocess",
        "--hidden-import=psutil",
        "--hidden-import=tempfile",
        "--hidden-import=shutil",
        
        # 数据处理
        "--hidden-import=pandas",
        "--hidden-import=requests",
        "--hidden-import=PIL",
        "--hidden-import=json",
        
        # 项目模块
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=core.injector_tool",
        "--hidden-import=core.auto_injector", 
        "--hidden-import=core.wechatferry_connector",
        "--hidden-import=ui",
        "--hidden-import=utils",
        "--hidden-import=runtime_config_generator",
        "--hidden-import=injection_helper",
        
        # 排除不需要的模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy.testing",
        
        # 构建选项
        "--clean",
        "--noconfirm",
        "--noupx",
        
        "main.py"
    ]
    
    print("📋 执行构建...")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=600
        )
        
        print("✅ 构建完成")
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e.returncode}")
        if e.stderr:
            print("错误输出:")
            print(e.stderr[-1500:])
        return False

def verify_build():
    """验证构建结果"""
    print("🔍 验证构建结果...")
    
    exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
    
    if not exe_file.exists():
        print("❌ 可执行文件不存在")
        return False
    
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"✅ 可执行文件: {exe_file}")
    print(f"📊 文件大小: {size_mb:.1f} MB")
    
    return True

def create_usage_instructions():
    """创建使用说明"""
    print("📄 创建使用说明...")
    
    instructions = """# Meet space 微信群发助手 - 使用说明

## 🚀 快速开始

1. **运行程序**
   - 双击 `MeetSpaceWeChatSender.exe`
   - 程序会自动提取注入工具到临时目录

2. **微信连接**
   - 确保微信PC版已启动并登录
   - 程序会自动尝试连接微信
   - 如果连接失败，请重启微信后再试

3. **注入工具**
   - 程序包含完整的注入工具和DLL文件
   - 运行时会自动提取到系统临时目录
   - 无需手动配置

## 🔧 故障排除

### 连接失败
- 重启微信PC版
- 以管理员身份运行程序
- 检查防火墙设置

### 注入失败
- 确保微信版本兼容
- 关闭杀毒软件
- 重启计算机后再试

## 📞 技术支持

如有问题，请检查程序日志文件：
- 位置：`%APPDATA%/MeetSpaceWeChatSender/logs/`
"""
    
    with open("dist/使用说明.txt", "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print("  ✅ 已创建: dist/使用说明.txt")

def main():
    """主函数"""
    print("🔧 修复微信注入问题 - 构建脚本")
    print("=" * 60)
    
    try:
        # 1. 清理
        clean_build()
        
        # 2. 设置环境
        setup_environment()
        
        # 3. 创建注入帮助脚本
        create_injection_helper()
        
        # 4. 构建
        if not build_with_injection_fix():
            print("❌ 构建失败")
            return False
        
        # 5. 验证
        if not verify_build():
            print("❌ 验证失败")
            return False
        
        # 6. 创建使用说明
        create_usage_instructions()
        
        print("\n🎉 修复版本构建成功!")
        print("📁 输出: dist/MeetSpaceWeChatSender.exe")
        print("\n✨ 修复内容:")
        print("  🔧 注入工具运行时自动提取")
        print("  📚 DLL文件正确打包")
        print("  🚀 修复路径访问问题")
        print("  ⚙️  增强错误处理")
        print("\n📋 现在应该可以:")
        print("  ✅ 正确连接微信")
        print("  ✅ 成功注入微信进程")
        print("  ✅ 使用所有群发功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
