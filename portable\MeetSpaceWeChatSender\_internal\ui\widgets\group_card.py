"""
分组卡片UI组件

实现卡片式的分组显示和操作界面。
"""

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QMouseEvent
from PyQt6.QtWidgets import (
    QFrame,
    QHBoxLayout,
    QLabel,
    QProgressBar,
    QPushButton,
    QVBoxLayout,
    QWidget,
)

from core.group_manager import ContactGroup, group_manager
from ui.modern_theme_manager import theme_manager as modern_theme_manager
from utils.logger import setup_logger

logger = setup_logger("group_card")


class GroupCard(QFrame):
    """分组卡片组件"""

    # 信号定义
    card_selected = pyqtSignal(str, bool)  # group_id, selected
    start_requested = pyqtSignal(str)  # group_id (启动任务)
    stop_requested = pyqtSignal(str)  # group_id (停止任务)
    pause_requested = pyqtSignal(str)  # group_id (暂停任务)
    delete_requested = pyqtSignal(str)  # group_id (删除分组)
    details_requested = pyqtSignal(str)  # group_id (查看详情)
    group_chosen = pyqtSignal(str)  # group_id (选择分组)

    def __init__(self, group: ContactGroup, group_type: str, parent=None):
        super().__init__(parent)
        self.group = group
        self.group_type = group_type
        self.is_selected = False

        self.setup_ui()
        self.update_display()

        # 注册到现代主题管理器
        modern_theme_manager.register_widget(self, "group_card")
        modern_theme_manager.theme_changed.connect(self.on_theme_changed)

        self.update_selected_style()

    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setLineWidth(1)
        self.setCursor(Qt.CursorShape.PointingHandCursor)  # 设置鼠标指针为手型

        # 样式将由主题管理器设置，不再硬编码

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(12, 12, 12, 12)
        main_layout.setSpacing(8)

        # 头部布局（选中图标 + 分组名 + 标签）
        header_layout = QHBoxLayout()

        # 选中图标（初始隐藏）
        self.selected_icon = QLabel("✅")
        self.selected_icon.setFixedSize(20, 20)
        self.selected_icon.setVisible(False)
        header_layout.addWidget(self.selected_icon)

        # 分组图标
        folder_icon = QLabel("📁")
        folder_icon.setFixedSize(20, 20)
        header_layout.addWidget(folder_icon)

        # 分组名称
        self.name_label = QLabel()
        self.name_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(self.name_label)

        # 成员数量
        self.member_count_label = QLabel()
        self.member_count_label.setObjectName("memberCountLabel")
        header_layout.addWidget(self.member_count_label)

        # 标签区域
        self.tags_label = QLabel()
        self.tags_label.setObjectName("tagsLabel")
        header_layout.addWidget(self.tags_label)

        header_layout.addStretch()
        main_layout.addLayout(header_layout)

        # 双进度条区域
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(6)

        # 第一个进度条 - 总体任务进度条
        overall_progress_layout = QVBoxLayout()
        overall_progress_layout.setSpacing(2)

        # 总体进度条标签
        self.overall_progress_label = QLabel("📊 总体任务进度")
        self.overall_progress_label.setStyleSheet(
            "font-size: 10px; color: #666; font-weight: bold;"
        )
        overall_progress_layout.addWidget(self.overall_progress_label)

        # 总体进度条
        self.overall_progress_bar = QProgressBar()
        self.overall_progress_bar.setMaximum(100)
        self.overall_progress_bar.setTextVisible(True)
        self.overall_progress_bar.setObjectName("overallProgressBar")
        self.overall_progress_bar.setFixedHeight(20)  # 较大的高度
        overall_progress_layout.addWidget(self.overall_progress_bar)

        # 总体进度详情
        self.overall_progress_detail = QLabel()
        self.overall_progress_detail.setObjectName("overallProgressDetail")
        self.overall_progress_detail.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.overall_progress_detail.setStyleSheet("font-size: 10px; color: #333;")
        overall_progress_layout.addWidget(self.overall_progress_detail)

        progress_layout.addLayout(overall_progress_layout)

        # 第二个进度条 - 实时发送进度条
        realtime_progress_layout = QVBoxLayout()
        realtime_progress_layout.setSpacing(2)

        # 实时进度条标签
        self.realtime_progress_label = QLabel("⚡ 实时发送进度")
        self.realtime_progress_label.setStyleSheet(
            "font-size: 9px; color: #888; font-weight: bold;"
        )
        realtime_progress_layout.addWidget(self.realtime_progress_label)

        # 实时进度条
        self.realtime_progress_bar = QProgressBar()
        self.realtime_progress_bar.setMaximum(100)
        self.realtime_progress_bar.setTextVisible(True)
        self.realtime_progress_bar.setObjectName("realtimeProgressBar")
        self.realtime_progress_bar.setFixedHeight(15)  # 较小的高度
        realtime_progress_layout.addWidget(self.realtime_progress_bar)

        # 实时进度详情
        self.realtime_progress_detail = QLabel()
        self.realtime_progress_detail.setObjectName("realtimeProgressDetail")
        self.realtime_progress_detail.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.realtime_progress_detail.setStyleSheet("font-size: 9px; color: #666;")
        realtime_progress_layout.addWidget(self.realtime_progress_detail)

        progress_layout.addLayout(realtime_progress_layout)

        # 初始化进度条状态
        self._init_progress_bars()

        main_layout.addLayout(progress_layout)

        # 成员显示区域
        self.members_label = QLabel()
        self.members_label.setWordWrap(True)
        self.members_label.setObjectName("membersLabel")
        self.members_label.setMaximumHeight(40)
        main_layout.addWidget(self.members_label)

        # 统计信息
        self.stats_label = QLabel()
        self.stats_label.setObjectName("statsLabel")
        main_layout.addWidget(self.stats_label)

        # 操作按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(6)

        # 启动按钮
        self.start_btn = QPushButton("🟢启动")
        self.start_btn.setObjectName("startButton")
        self.start_btn.clicked.connect(self.on_start_clicked)
        buttons_layout.addWidget(self.start_btn)

        # 停止按钮
        self.stop_btn = QPushButton("🔴停止")
        self.stop_btn.setObjectName("stopButton")
        self.stop_btn.clicked.connect(self.on_stop_clicked)
        buttons_layout.addWidget(self.stop_btn)

        # 删除按钮
        self.delete_btn = QPushButton("🗑️删除")
        self.delete_btn.setObjectName("deleteButton")
        self.delete_btn.clicked.connect(self.on_delete_clicked)
        buttons_layout.addWidget(self.delete_btn)

        # 暂停按钮
        self.pause_btn = QPushButton("⏸️暂停")
        self.pause_btn.setObjectName("pauseButton")
        self.pause_btn.clicked.connect(self.on_pause_clicked)
        buttons_layout.addWidget(self.pause_btn)

        # 详情按钮
        self.details_btn = QPushButton("📊详情")
        self.details_btn.setObjectName("detailsButton")
        self.details_btn.clicked.connect(self.on_details_clicked)
        buttons_layout.addWidget(self.details_btn)

        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)

    def update_display(self):
        """更新显示内容"""
        # 更新分组名称和成员数
        icon = "📁" if self.group_type == "timing" else "🔄"
        self.name_label.setText(f"{icon} {self.group.name}")
        self.member_count_label.setText(f"({self.group.get_member_count()}人)")

        # 更新标签
        if self.group.tags:
            tags_text = " ".join([f"[{tag}]" for tag in self.group.tags])
            self.tags_label.setText(tags_text)
        else:
            self.tags_label.setText("")

        # 更新进度
        self.update_progress()

        # 更新成员显示
        self.update_members_display()

        # 更新统计信息
        self.update_stats()

    def _init_progress_bars(self):
        """初始化双进度条状态"""
        # 初始化总体进度条
        self.overall_progress_bar.setValue(0)
        self.overall_progress_bar.setStyleSheet(
            """
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                font-size: 10px;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #dc3545;
                border-radius: 3px;
            }
        """
        )
        self.overall_progress_detail.setText("0% (0/0已处理)")

        # 初始化实时进度条
        self.realtime_progress_bar.setValue(0)
        self.realtime_progress_bar.setStyleSheet(
            """
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
                font-size: 9px;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 2px;
            }
        """
        )
        self.realtime_progress_detail.setText("等待任务开始...")

    def update_progress(self):
        """更新双进度条显示"""
        # 更新总体任务进度条
        self.update_overall_progress()

        # 更新实时发送进度条（如果有正在执行的任务）
        self.update_realtime_progress()

    def update_overall_progress(self):
        """更新总体任务进度条"""
        try:
            # 获取分组的总体进度数据
            sent_count, total_count, progress_percent = (
                group_manager.get_group_progress_detail(self.group.group_id)
            )

            # 设置总体进度条值
            self.overall_progress_bar.setValue(int(progress_percent))

            # 设置总体进度条颜色
            if progress_percent >= 80:
                color = "#28a745"  # 绿色
            elif progress_percent >= 50:
                color = "#ffc107"  # 黄色
            else:
                color = "#dc3545"  # 红色

            self.overall_progress_bar.setStyleSheet(
                f"""
                QProgressBar {{
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    text-align: center;
                    font-size: 10px;
                    font-weight: bold;
                }}
                QProgressBar::chunk {{
                    background-color: {color};
                    border-radius: 3px;
                }}
            """
            )

            # 设置总体进度详情
            self.overall_progress_detail.setText(
                f"{progress_percent:.0f}% ({sent_count}/{total_count}已处理)"
            )

        except Exception as e:
            # 出错时显示默认状态
            self.overall_progress_bar.setValue(0)
            self.overall_progress_detail.setText("数据获取失败")

    def update_realtime_progress(self):
        """更新实时发送进度条"""
        try:
            # 检查是否有正在执行的定时任务
            from core.timing_sender import timing_sender

            # 获取该分组的正在执行的任务
            running_tasks = [
                task
                for task in timing_sender.get_all_tasks()
                if task.group_id == self.group.group_id and task.status == "executing"
            ]

            if running_tasks:
                # 有正在执行的任务，显示实时进度
                task = running_tasks[0]  # 取第一个正在执行的任务

                if task.total_count > 0:
                    realtime_percent = (task.sent_count / task.total_count) * 100
                    self.realtime_progress_bar.setValue(int(realtime_percent))
                    self.realtime_progress_detail.setText(
                        f"正在发送: {task.sent_count}/{task.total_count}"
                    )
                else:
                    self.realtime_progress_bar.setValue(0)
                    self.realtime_progress_detail.setText("准备发送...")

                # 实时进度条使用蓝色
                self.realtime_progress_bar.setStyleSheet(
                    """
                    QProgressBar {
                        border: 1px solid #007bff;
                        border-radius: 3px;
                        text-align: center;
                        font-size: 9px;
                        background-color: #e3f2fd;
                    }
                    QProgressBar::chunk {
                        background-color: #007bff;
                        border-radius: 2px;
                    }
                """
                )
            else:
                # 没有正在执行的任务，重置实时进度条
                self.realtime_progress_bar.setValue(0)
                self.realtime_progress_detail.setText("等待任务开始...")

                # 重置为默认样式
                self.realtime_progress_bar.setStyleSheet(
                    """
                    QProgressBar {
                        border: 1px solid #ccc;
                        border-radius: 3px;
                        text-align: center;
                        font-size: 9px;
                        background-color: #f8f9fa;
                    }
                    QProgressBar::chunk {
                        background-color: #6c757d;
                        border-radius: 2px;
                    }
                """
                )

        except Exception as e:
            # 出错时重置实时进度条
            self.realtime_progress_bar.setValue(0)
            self.realtime_progress_detail.setText("状态获取失败")

    def reset_realtime_progress(self):
        """重置实时进度条（新任务开始时调用）"""
        self.realtime_progress_bar.setValue(0)
        self.realtime_progress_detail.setText("任务开始...")

        # 设置为活跃状态的样式
        self.realtime_progress_bar.setStyleSheet(
            """
            QProgressBar {
                border: 1px solid #007bff;
                border-radius: 3px;
                text-align: center;
                font-size: 9px;
                background-color: #e3f2fd;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 2px;
            }
        """
        )

    def update_realtime_progress_value(self, sent_count: int, total_count: int):
        """更新实时进度条的具体数值（由外部调用）"""
        try:
            if total_count > 0:
                realtime_percent = (sent_count / total_count) * 100
                self.realtime_progress_bar.setValue(int(realtime_percent))
                self.realtime_progress_detail.setText(
                    f"正在发送: {sent_count}/{total_count}"
                )
            else:
                self.realtime_progress_bar.setValue(0)
                self.realtime_progress_detail.setText("准备发送...")
        except Exception as e:
            self.realtime_progress_detail.setText("更新失败")

    def update_members_display(self):
        """更新成员显示"""
        if not self.group.members:
            self.members_label.setText("暂无成员")
            return

        # 显示前5个成员，超出显示省略号
        display_members = []
        for i, member in enumerate(self.group.members[:5]):
            icon = "👤" if member.member_type == "contact" else "👥"
            display_members.append(f"{icon}{member.name}")

        if len(self.group.members) > 5:
            display_members.append("...")

        self.members_label.setText(" ".join(display_members))

    def update_stats(self):
        """更新统计信息"""
        # 使用频率和最后使用时间
        if self.group.last_used:
            last_used = self.group.last_used.strftime("%m-%d %H:%M")
            stats_text = f"使用频率：{self.group.use_count}次 | 最后使用：{last_used}"
        else:
            stats_text = f"使用频率：{self.group.use_count}次 | 最后使用：从未使用"

        self.stats_label.setText(stats_text)

    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.is_selected = selected
        if hasattr(self, "selected_icon"):
            self.selected_icon.setVisible(selected)
        self.update_selected_style()

    def update_selected_style(self):
        """更新选中状态的样式"""
        # 设置选中属性，让主题管理器处理样式
        self.setProperty("selected", self.is_selected)
        self.style().unpolish(self)
        self.style().polish(self)

    def on_theme_changed(self, theme_name: str):
        """主题变更处理"""
        try:
            # 重新应用样式
            self.update_selected_style()
            logger.debug(f"分组卡片主题已更新: {theme_name}")
        except Exception as e:
            logger.error(f"分组卡片主题更新失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            # 取消注册现代主题管理器
            modern_theme_manager.unregister_widget(self)
        except Exception as e:
            logger.error(f"清理分组卡片资源失败: {e}")

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查点击位置是否在按钮区域
            if not self._is_click_on_buttons(event.position().toPoint()):
                # 点击卡片内容区域，选择分组
                self.group_chosen.emit(self.group.group_id)
        super().mousePressEvent(event)

    def _is_click_on_buttons(self, pos) -> bool:
        """检查点击位置是否在按钮区域"""
        # 获取按钮区域的几何位置
        buttons = [
            self.start_btn,
            self.stop_btn,
            self.delete_btn,
            self.pause_btn,
            self.details_btn,
        ]
        for button in buttons:
            if button.geometry().contains(pos):
                return True
        return False

    def on_start_clicked(self):
        """启动分组任务"""
        self.start_requested.emit(self.group.group_id)

    def on_stop_clicked(self):
        """停止分组任务"""
        self.stop_requested.emit(self.group.group_id)

    def on_delete_clicked(self):
        """删除分组"""
        self.delete_requested.emit(self.group.group_id)

    def on_pause_clicked(self):
        """暂停/继续分组任务"""
        # 根据按钮文本判断是暂停还是继续
        if self.pause_btn.text() == "▶️继续":
            # 发送继续信号（复用启动信号）
            self.start_requested.emit(self.group.group_id)
        else:
            # 发送暂停信号
            self.pause_requested.emit(self.group.group_id)

    def on_details_clicked(self):
        """查看详情"""
        self.details_requested.emit(self.group.group_id)

    def refresh(self):
        """刷新显示"""
        # 重新获取分组数据
        updated_group = group_manager.get_group(self.group.group_id, self.group_type)
        if updated_group:
            self.group = updated_group
            self.update_display()

    def update_button_states(
        self, has_running_task: bool = False, has_paused_task: bool = False
    ):
        """根据任务状态更新按钮状态

        实现具体的交互行为：
        - 初始状态：启动可点击，暂停/停止灰色不可点击
        - 运行状态：启动灰色不可点击，暂停/停止可点击
        - 暂停状态：启动可点击，暂停变为"继续"可点击，停止可点击
        """

        if has_running_task and not has_paused_task:
            # 任务运行中状态
            self.start_btn.setEnabled(False)
            self.start_btn.setStyleSheet(
                "QPushButton { color: #999999; background-color: #f0f0f0; }"
            )

            self.pause_btn.setEnabled(True)
            self.pause_btn.setText("⏸️暂停")
            self.pause_btn.setStyleSheet("")

            self.stop_btn.setEnabled(True)
            self.stop_btn.setStyleSheet("")

        elif has_paused_task:
            # 任务暂停状态
            self.start_btn.setEnabled(True)
            self.start_btn.setStyleSheet("")

            self.pause_btn.setEnabled(True)
            self.pause_btn.setText("▶️继续")
            self.pause_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """
            )

            self.stop_btn.setEnabled(True)
            self.stop_btn.setStyleSheet("")

        else:
            # 初始状态（无任务运行）
            self.start_btn.setEnabled(True)
            self.start_btn.setStyleSheet("")

            self.pause_btn.setEnabled(False)
            self.pause_btn.setText("⏸️暂停")
            self.pause_btn.setStyleSheet(
                "QPushButton { color: #999999; background-color: #f0f0f0; }"
            )

            self.stop_btn.setEnabled(False)
            self.stop_btn.setStyleSheet(
                "QPushButton { color: #999999; background-color: #f0f0f0; }"
            )

        # 删除按钮和详情按钮：总是可用
        self.delete_btn.setEnabled(True)
        self.details_btn.setEnabled(True)


class NewGroupCard(QFrame):
    """新建分组卡片"""

    create_group_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

        # 注册到现代主题管理器
        modern_theme_manager.register_widget(self, "group_card")
        modern_theme_manager.theme_changed.connect(self.on_theme_changed)

    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setLineWidth(1)
        self.setObjectName("newGroupCard")

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 图标
        icon_label = QLabel("➕")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setObjectName("newGroupIcon")
        layout.addWidget(icon_label)

        # 文本
        text_label = QLabel("新建分组")
        text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_label.setObjectName("newGroupText")
        layout.addWidget(text_label)

        # 设置固定高度
        self.setFixedHeight(120)

        # 点击事件
        self.mousePressEvent = self.on_clicked

    def on_theme_changed(self, theme_name: str):
        """主题变更处理"""
        try:
            # 重新应用样式
            self.style().unpolish(self)
            self.style().polish(self)
            logger.debug(f"新建分组卡片主题已更新: {theme_name}")
        except Exception as e:
            logger.error(f"新建分组卡片主题更新失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            # 取消注册主题管理器
            modern_theme_manager.unregister_widget(self)
        except Exception as e:
            logger.error(f"清理新建分组卡片资源失败: {e}")

    def on_clicked(self, event):
        """点击事件"""
        self.create_group_requested.emit()
