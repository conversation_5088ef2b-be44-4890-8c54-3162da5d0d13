#!/usr/bin/env python3
"""
项目打包脚本
使用PyInstaller打包微信无感群发助手
支持Windows 11
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_dependencies():
    """检查打包依赖"""
    print("🔍 检查打包依赖...")

    # 检查PyInstaller
    try:
        import PyInstaller

        print(f"   ✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("   ❌ PyInstaller 未安装")
        print("   请运行: pip install pyinstaller")
        return False

    # 检查主要依赖
    dependencies = [
        ("PyQt6", "PyQt6"),
        ("pandas", "pandas"),
        ("requests", "requests"),
        ("PIL", "Pillow"),
        ("yaml", "pyyaml"),
    ]

    missing = []
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} 未安装")
            missing.append(package)

    if missing:
        print(f"   请运行: pip install {' '.join(missing)}")
        return False

    return True


def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")

    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   ✅ 已删除 {dir_name}")

    # 删除spec文件
    spec_file = "WeChatMassSender.spec"
    if os.path.exists(spec_file):
        os.remove(spec_file)
        print(f"   ✅ 已删除 {spec_file}")


def create_spec_file():
    """创建PyInstaller的spec文件"""
    print("📝 创建spec文件...")

    spec_content = """# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

project_root = Path(os.getcwd())

# 数据文件
datas = [
    # 配置文件
    (str(project_root / 'config'), 'config'),
    # 资源文件
    (str(project_root / 'resources'), 'resources'),
    # wxhelper文件
    (str(project_root / 'wxhelper_files'), 'wxhelper_files'),
    # 工具文件
    (str(project_root / 'tools'), 'tools'),
    # 数据目录
    (str(project_root / 'data'), 'data'),
    # 日志目录（创建空目录）
    (str(project_root / 'logs'), 'logs'),
    # 临时图片目录
    (str(project_root / 'temp_images'), 'temp_images'),
    # 使用指南和说明文档
    (str(project_root / '定时发送使用指南.md'), '.'),
    (str(project_root / '新注入方式使用说明.md'), '.'),
    (str(project_root / 'wxhelper_API使用说明.md'), '.'),
    (str(project_root / '权限问题解决方案.md'), '.'),
]

# 隐藏导入
hiddenimports = [
    # PyQt6相关
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtNetwork',
    'PyQt6.sip',

    # 数据处理
    'pandas',
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.worksheet',
    'openpyxl.styles',

    # 网络请求
    'requests',
    'aiohttp',
    'urllib3',

    # 图像处理
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageTk',

    # 配置文件
    'yaml',
    'json',

    # 时间处理
    'dateutil',
    'dateutil.parser',
    'dateutil.tz',

    # 系统工具
    'psutil',
    'ctypes',
    'ctypes.wintypes',
    'subprocess',
    'tempfile',
    'shutil',
    'zipfile',

    # 微信相关（可选）
    'wcferry',

    # 标准库
    'asyncio',
    'threading',
    'logging',
    'pathlib',
    'datetime',
    'time',
    'os',
    'sys',
    'csv',
    'xml.etree.ElementTree',
    'xml.dom.minidom',
    'html.parser',
    'urllib.parse',
    'urllib.request',
    'base64',
    'hashlib',
    'pickle',
    'sqlite3',
    'sqlite3.dbapi2',

    # 项目模块
    'core',
    'core.timing_sender',
    'core.loop_sender',
    'core.group_manager',
    'core.http_api_connector',
    'core.auto_injector',
    'core.injector_tool',
    'ui',
    'ui.main_window',
    'ui.timing_send_page',
    'ui.loop_send_page',
                    'ui.modern_theme_manager',
    'utils',
    'utils.logger',
    'utils.validators',
    'utils.admin_privileges',
    'utils.simple_admin',
    'config',
    'config.settings',
]

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='微信无感群发助手',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='微信无感群发助手'
)
"""
    with open("WeChatMassSender.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    print("   ✅ 已生成 WeChatMassSender.spec 文件")


def run_pyinstaller():
    """运行PyInstaller进行打包"""
    print("📦 开始打包...")

    cmd = [
        sys.executable,
        "-m",
        "PyInstaller",
        "--clean",  # 清理缓存
        "--noconfirm",  # 不询问覆盖
        "WeChatMassSender.spec",
    ]

    print(f"   运行命令: {' '.join(cmd)}")

    try:
        # 使用系统默认编码，不捕获输出以避免编码问题
        result = subprocess.run(cmd, check=True)
        print("   ✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 打包失败: {e}")
        return False


def post_build_tasks():
    """打包后处理任务"""
    print("🔧 执行打包后处理...")

    dist_dir = Path("dist/微信无感群发助手")
    if not dist_dir.exists():
        print("   ❌ 打包目录不存在")
        return False

    # 创建启动脚本
    startup_script = dist_dir / "启动程序.bat"
    with open(startup_script, "w", encoding="gbk") as f:
        f.write(
            """@echo off
chcp 65001 >nul
echo 正在启动微信无感群发助手...
echo.
echo 请确保：
echo 1. 微信已经登录
echo 2. 以管理员权限运行（如果需要注入功能）
echo 3. 关闭杀毒软件的实时保护（如果被误报）
echo.
pause
"微信无感群发助手.exe"
"""
        )

    # 创建说明文件
    readme_file = dist_dir / "使用说明.txt"
    with open(readme_file, "w", encoding="utf-8") as f:
        f.write(
            """微信无感群发助手 - 使用说明

系统要求：
- Windows 10/11 (64位)
- 微信PC版 (最新版本)

使用步骤：
1. 双击"启动程序.bat"或直接运行"微信无感群发助手.exe"
2. 首次运行可能需要以管理员权限启动
3. 确保微信已登录
4. 在程序中点击"连接微信"
5. 开始使用定时发送或循环发送功能

注意事项：
- 如果被杀毒软件误报，请添加到白名单
- 使用前请仔细阅读"定时发送使用指南.md"
- 请合理使用，避免发送垃圾信息

技术支持：
- 查看日志文件了解详细错误信息
- 确保网络连接正常
- 如有问题请查看程序内的运行日志页面

版本信息：
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Python版本: {sys.version}
"""
        )

    print(f"   ✅ 已创建启动脚本: {startup_script}")
    print(f"   ✅ 已创建说明文件: {readme_file}")

    # 显示打包结果
    exe_file = dist_dir / "微信无感群发助手.exe"
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file} ({size_mb:.1f} MB)")

    return True


def main():
    """主函数"""
    print("🚀 微信无感群发助手 - 打包工具")
    print("=" * 50)

    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装缺失的依赖包")
        return False

    # 清理构建目录
    clean_build_dirs()

    # 创建spec文件
    create_spec_file()

    # 运行打包
    if not run_pyinstaller():
        print("❌ 打包失败")
        return False

    # 打包后处理
    if not post_build_tasks():
        print("❌ 打包后处理失败")
        return False

    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print(f"📁 输出目录: {Path('dist/微信无感群发助手').absolute()}")
    print("📝 请查看使用说明.txt了解使用方法")
    print("🚀 双击'启动程序.bat'开始使用")

    return True


if __name__ == "__main__":
    import datetime

    success = main()
    if not success:
        input("按回车键退出...")
        sys.exit(1)
