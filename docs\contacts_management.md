# 联系人管理功能使用说明

## 功能概述

联系人管理模块提供了完整的联系人操作功能，包括刷新、导入、导出和搜索等。

## 主要功能

### 1. 刷新联系人 🔄

**功能说明**：从微信客户端获取最新的联系人列表

**使用步骤**：
1. 确保已连接并登录微信
2. 切换到"联系人管理"标签页
3. 点击"刷新联系人"按钮
4. 等待刷新完成

**注意事项**：
- 需要先连接微信客户端
- 刷新过程中按钮会被禁用，防止重复操作
- 刷新完成后会显示获取到的联系人数量

### 2. 导出联系人 📤

**功能说明**：将当前联系人列表导出为文件

**支持格式**：
- 文本文件 (*.txt)

**使用步骤**：
1. 确保已有联系人数据（通过刷新获取）
2. 点击"导出联系人"按钮
3. 选择保存位置
4. 确认导出

**导出内容**：
- 每行一个联系人名称
- 使用UTF-8编码
- 简洁的文本格式

### 3. 导入联系人 📥

**功能说明**：从文件导入联系人数据

**支持格式**：
- 文本文件 (*.txt)

**文件格式要求**：

#### 格式说明
- 每行一个联系人名称
- 使用UTF-8编码
- 空行会被自动跳过
- 简单易用的文本格式

#### 示例格式
```txt
张三
李四
王五
工作群
技术交流群
家庭群
```

**使用步骤**：
1. 准备符合格式要求的文件
2. 点击"导入联系人"按钮
3. 阅读格式说明，点击"Yes"继续
4. 选择要导入的文件
5. 选择导入方式：
   - **替换**：清空现有联系人，使用导入的数据
   - **追加**：保留现有联系人，添加新的联系人
6. 确认导入

**数据验证**：
- 自动验证微信ID和姓名格式
- 跳过无效的联系人数据
- 显示导入结果统计

### 4. 联系人搜索 🔍

**功能说明**：在联系人列表中快速查找特定联系人

**搜索范围**：
- 联系人姓名
- 微信ID
- 备注信息

**使用方法**：
1. 在搜索框中输入关键词
2. 系统自动过滤显示匹配的联系人
3. 清空搜索框显示所有联系人

**搜索特性**：
- 实时搜索，无需按回车
- 不区分大小写
- 支持部分匹配

### 5. 联系人选择 ✅

**功能说明**：在发送消息时选择接收人

**选择方式**：
- **单选**：点击联系人名称
- **多选**：按住Ctrl键点击多个联系人
- **全选**：点击"全选"按钮
- **取消全选**：点击"取消全选"按钮
- **反选**：点击"反选"按钮

**选择状态**：
- 实时显示已选择的联系人数量
- 支持在搜索结果中进行选择

## 使用技巧

### 1. 批量管理
- 使用Excel编辑大量联系人数据
- 利用导出功能备份联系人信息
- 通过导入功能快速恢复联系人列表

### 2. 数据维护
- 定期刷新联系人列表保持数据最新
- 使用备注字段添加重要信息
- 合理分类好友和群聊

### 3. 搜索优化
- 使用简短关键词提高搜索效率
- 利用备注信息进行分类搜索
- 结合选择功能快速定位目标联系人

## 常见问题

### Q: 刷新联系人失败怎么办？
A: 
1. 检查微信客户端是否正常运行
2. 确认已正确登录微信
3. 检查网络连接状态
4. 尝试重新连接微信

### Q: 导入文件格式错误怎么办？
A:
1. 检查文件是否包含必需的列（姓名、微信ID）
2. 确认列名完全匹配（区分大小写）
3. 参考模板文件格式
4. 使用UTF-8编码保存CSV文件

### Q: 导出的文件在哪里？
A:
1. 导出时可以选择保存位置
2. 默认文件名包含时间戳
3. 导出成功后会显示完整文件路径

### Q: 搜索不到联系人怎么办？
A:
1. 检查搜索关键词是否正确
2. 尝试使用部分关键词
3. 清空搜索框查看所有联系人
4. 确认联系人数据已正确加载

## 注意事项

1. **数据安全**：导出的联系人文件包含敏感信息，请妥善保管
2. **格式兼容**：建议使用UTF-8编码保存CSV文件，避免中文乱码
3. **数据备份**：重要联系人数据建议定期备份
4. **权限要求**：某些功能需要微信客户端的相应权限
5. **性能考虑**：大量联系人可能影响界面响应速度

## 更新日志

- v1.0.0：初始版本，支持基本的联系人管理功能
- 支持CSV和Excel格式的导入导出
- 实现实时搜索和多种选择方式
- 添加数据验证和错误处理
