#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet space 微信群发助手 - 简化版主程序
"""

import sys
import os
from pathlib import Path

def main():
    """主函数"""
    try:
        print("🎉 Meet space 微信群发助手启动中...")
        
        # 导入PyQt6
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QIcon
        
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("Meet space 微信群发助手")
        app.setApplicationVersion("1.0.0")
        
        # 创建简单窗口
        window = QMainWindow()
        window.setWindowTitle("Meet space 微信群发助手 v1.0.0")
        window.setGeometry(100, 100, 800, 600)
        
        # 创建中心部件
        central_widget = QWidget()
        layout = QVBoxLayout()
        
        # 添加标签
        label = QLabel("Meet space 微信群发助手")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50; margin: 50px;")
        layout.addWidget(label)
        
        status_label = QLabel("程序已成功启动！\n单文件版本运行正常。")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        layout.addWidget(status_label)
        
        info_label = QLabel("这是一个测试版本，用于验证单文件打包功能。\n完整功能版本正在开发中...")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("font-size: 12px; color: #7f8c8d; margin: 20px;")
        layout.addWidget(info_label)
        
        central_widget.setLayout(layout)
        window.setCentralWidget(central_widget)
        
        # 显示窗口
        window.show()
        
        print("✅ 窗口已显示")
        
        # 运行应用
        return app.exec()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
