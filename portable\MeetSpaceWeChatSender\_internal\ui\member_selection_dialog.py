#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成员选择对话框

用于分组管理中添加成员的对话框，支持从好友列表和群聊列表中选择成员
"""

from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QTabWidget,
    QWidget,
    QLabel,
    QTableWidget,
    QTableWidgetItem,
    QLineEdit,
    QPushButton,
    QCheckBox,
    QHeaderView,
    QMessageBox,
    QProgressBar,
    QGroupBox,
    QSplitter,
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QPixmap, QIcon

from core.group_manager import GroupMember
from ui.modern_theme_manager import theme_manager as modern_theme_manager
from ui.themed_dialog_base import ThemedDialogBase
from ui.themed_message_box import ThemedMessageBoxHelper
from utils.logger import setup_logger

logger = setup_logger("member_selection_dialog")


class ContactLoadThread(QThread):
    """联系人加载线程"""

    contacts_loaded = pyqtSignal(list)  # 联系人加载完成
    error_occurred = pyqtSignal(str)  # 错误发生

    def __init__(self, connector, contact_type="all"):
        super().__init__()
        self.connector = connector
        self.contact_type = contact_type  # "friends", "groups", "all"

    def run(self):
        """运行线程"""
        try:
            if not self.connector or not self.connector.is_connected:
                self.error_occurred.emit("微信未连接")
                return

            contacts = []

            if self.contact_type in ["friends", "all"]:
                # 获取好友列表
                friends = self.connector.get_contacts()
                if friends:
                    for friend in friends:
                        contacts.append(
                            {
                                "wxid": friend.get("wxid", ""),
                                "name": friend.get("name", ""),
                                "remark": friend.get("remark", ""),
                                "type": "contact",
                                "avatar": friend.get("avatar", ""),
                            }
                        )

            if self.contact_type in ["groups", "all"]:
                # 获取群聊列表
                groups = self.connector.get_chatrooms()
                if groups:
                    for group in groups:
                        contacts.append(
                            {
                                "wxid": group.get("wxid", ""),
                                "name": group.get("name", ""),
                                "remark": "",
                                "type": "chatroom",
                                "avatar": group.get("avatar", ""),
                            }
                        )

            self.contacts_loaded.emit(contacts)

        except Exception as e:
            logger.error(f"加载联系人失败: {e}")
            self.error_occurred.emit(str(e))


class MemberSelectionDialog(ThemedDialogBase):
    """成员选择对话框"""

    members_selected = pyqtSignal(list)  # 成员选择完成信号

    def __init__(self, connector, existing_members=None, parent=None):
        super().__init__(parent)
        self.connector = connector
        self.existing_members = existing_members or []
        self.existing_wxids = {member.wxid for member in self.existing_members}

        self.friends_data = []
        self.groups_data = []
        self.selected_members = []

        self.setup_ui()
        self.load_contacts()

        # 主题支持已由 ThemedDialogBase 自动设置

        self.setWindowTitle("选择成员")
        self.setModal(True)
        self.resize(800, 600)

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 标题和统计
        header_layout = QHBoxLayout()
        title_label = QLabel("👥 选择要添加的成员")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        self.selected_count_label = QLabel("已选择: 0 个成员")
        header_layout.addWidget(self.selected_count_label)

        layout.addLayout(header_layout)

        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入昵称或微信ID进行搜索...")
        self.search_input.textChanged.connect(self.on_search_changed)
        search_layout.addWidget(self.search_input)

        self.clear_search_btn = QPushButton("清空")
        self.clear_search_btn.clicked.connect(self.clear_search)
        search_layout.addWidget(self.clear_search_btn)

        layout.addLayout(search_layout)

        # 标签页
        self.tab_widget = QTabWidget()

        # 好友列表标签页
        self.create_friends_tab()

        # 群聊列表标签页
        self.create_groups_tab()

        layout.addWidget(self.tab_widget)

        # 已选择成员区域
        self.create_selected_area(layout)

        # 按钮区域
        self.create_buttons(layout)

        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

    def create_friends_tab(self):
        """创建好友列表标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 好友列表表格
        self.friends_table = QTableWidget()
        self.friends_table.setColumnCount(4)
        self.friends_table.setHorizontalHeaderLabels(["选择", "昵称", "微信ID", "备注"])

        # 设置列宽
        header = self.friends_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)

        # 设置选择模式
        self.friends_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )

        layout.addWidget(self.friends_table)

        self.tab_widget.addTab(widget, "👤 好友列表")

    def create_groups_tab(self):
        """创建群聊列表标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 群聊列表表格
        self.groups_table = QTableWidget()
        self.groups_table.setColumnCount(3)
        self.groups_table.setHorizontalHeaderLabels(["选择", "群名称", "微信ID"])

        # 设置列宽
        header = self.groups_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)

        # 设置选择模式
        self.groups_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )

        layout.addWidget(self.groups_table)

        self.tab_widget.addTab(widget, "👥 群聊列表")

    def create_selected_area(self, parent_layout):
        """创建已选择成员区域"""
        selected_group = QGroupBox("已选择的成员")
        selected_layout = QVBoxLayout(selected_group)

        # 已选择成员列表
        self.selected_table = QTableWidget()
        self.selected_table.setColumnCount(3)
        self.selected_table.setHorizontalHeaderLabels(["类型", "名称", "微信ID"])
        self.selected_table.setMaximumHeight(150)

        # 设置列宽
        header = self.selected_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)

        selected_layout.addWidget(self.selected_table)

        # 清空选择按钮
        clear_selected_btn = QPushButton("清空选择")
        clear_selected_btn.clicked.connect(self.clear_selected)
        selected_layout.addWidget(clear_selected_btn)

        parent_layout.addWidget(selected_group)

    def create_buttons(self, parent_layout):
        """创建按钮区域"""
        buttons_layout = QHBoxLayout()

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新联系人")
        refresh_btn.clicked.connect(self.load_contacts)
        buttons_layout.addWidget(refresh_btn)

        buttons_layout.addStretch()

        # 确认和取消按钮
        confirm_btn = QPushButton("确认添加")
        confirm_btn.clicked.connect(self.confirm_selection)
        buttons_layout.addWidget(confirm_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        parent_layout.addLayout(buttons_layout)

    def load_contacts(self):
        """加载联系人"""
        if not self.connector:
            ThemedMessageBoxHelper.show_warning(self, "警告", "连接器未初始化")
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 启动加载线程
        self.load_thread = ContactLoadThread(self.connector, "all")
        self.load_thread.contacts_loaded.connect(self.on_contacts_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()

    def on_contacts_loaded(self, contacts):
        """联系人加载完成"""
        self.progress_bar.setVisible(False)

        # 分类联系人
        self.friends_data = [c for c in contacts if c["type"] == "contact"]
        self.groups_data = [c for c in contacts if c["type"] == "chatroom"]

        # 更新表格
        self.update_friends_table()
        self.update_groups_table()

        logger.info(
            f"加载联系人完成: 好友 {len(self.friends_data)} 个, 群聊 {len(self.groups_data)} 个"
        )

    def on_load_error(self, error_msg):
        """加载错误处理"""
        self.progress_bar.setVisible(False)
        ThemedMessageBoxHelper.show_error(self, "错误", f"加载联系人失败:\n{error_msg}")

    def update_friends_table(self, filter_text=""):
        """更新好友表格"""
        # 过滤数据
        filtered_data = self.friends_data
        if filter_text:
            filtered_data = [
                friend
                for friend in self.friends_data
                if (
                    filter_text.lower() in friend["name"].lower()
                    or filter_text.lower() in friend["wxid"].lower()
                    or filter_text.lower() in friend.get("remark", "").lower()
                )
            ]

        self.friends_table.setRowCount(len(filtered_data))

        for row, friend in enumerate(filtered_data):
            # 复选框
            checkbox = QCheckBox()
            checkbox.setChecked(
                friend["wxid"] in [m["wxid"] for m in self.selected_members]
            )
            checkbox.stateChanged.connect(
                lambda state, f=friend: self.on_friend_selection_changed(f, state)
            )

            # 如果已经在分组中，禁用复选框
            if friend["wxid"] in self.existing_wxids:
                checkbox.setEnabled(False)
                checkbox.setToolTip("该成员已在分组中")

            self.friends_table.setCellWidget(row, 0, checkbox)

            # 昵称
            self.friends_table.setItem(row, 1, QTableWidgetItem(friend["name"]))

            # 微信ID
            self.friends_table.setItem(row, 2, QTableWidgetItem(friend["wxid"]))

            # 备注
            remark = friend.get("remark", "")
            self.friends_table.setItem(row, 3, QTableWidgetItem(remark))

    def update_groups_table(self, filter_text=""):
        """更新群聊表格"""
        # 过滤数据
        filtered_data = self.groups_data
        if filter_text:
            filtered_data = [
                group
                for group in self.groups_data
                if (
                    filter_text.lower() in group["name"].lower()
                    or filter_text.lower() in group["wxid"].lower()
                )
            ]

        self.groups_table.setRowCount(len(filtered_data))

        for row, group in enumerate(filtered_data):
            # 复选框
            checkbox = QCheckBox()
            checkbox.setChecked(
                group["wxid"] in [m["wxid"] for m in self.selected_members]
            )
            checkbox.stateChanged.connect(
                lambda state, g=group: self.on_group_selection_changed(g, state)
            )

            # 如果已经在分组中，禁用复选框
            if group["wxid"] in self.existing_wxids:
                checkbox.setEnabled(False)
                checkbox.setToolTip("该成员已在分组中")

            self.groups_table.setCellWidget(row, 0, checkbox)

            # 群名称
            self.groups_table.setItem(row, 1, QTableWidgetItem(group["name"]))

            # 微信ID
            self.groups_table.setItem(row, 2, QTableWidgetItem(group["wxid"]))

    def on_friend_selection_changed(self, friend, state):
        """好友选择状态变化"""
        if state == Qt.CheckState.Checked.value:
            # 添加到选择列表
            if friend["wxid"] not in [m["wxid"] for m in self.selected_members]:
                self.selected_members.append(
                    {
                        "wxid": friend["wxid"],
                        "name": friend["name"],
                        "type": "contact",
                        "remark": friend.get("remark", ""),
                    }
                )
        else:
            # 从选择列表移除
            self.selected_members = [
                m for m in self.selected_members if m["wxid"] != friend["wxid"]
            ]

        self.update_selected_display()

    def on_group_selection_changed(self, group, state):
        """群聊选择状态变化"""
        if state == Qt.CheckState.Checked.value:
            # 添加到选择列表
            if group["wxid"] not in [m["wxid"] for m in self.selected_members]:
                self.selected_members.append(
                    {
                        "wxid": group["wxid"],
                        "name": group["name"],
                        "type": "chatroom",
                        "remark": "",
                    }
                )
        else:
            # 从选择列表移除
            self.selected_members = [
                m for m in self.selected_members if m["wxid"] != group["wxid"]
            ]

        self.update_selected_display()

    def update_selected_display(self):
        """更新已选择成员显示"""
        self.selected_count_label.setText(
            f"已选择: {len(self.selected_members)} 个成员"
        )

        # 更新已选择成员表格
        self.selected_table.setRowCount(len(self.selected_members))

        for row, member in enumerate(self.selected_members):
            # 类型
            type_text = "👤 好友" if member["type"] == "contact" else "👥 群聊"
            self.selected_table.setItem(row, 0, QTableWidgetItem(type_text))

            # 名称
            self.selected_table.setItem(row, 1, QTableWidgetItem(member["name"]))

            # 微信ID
            self.selected_table.setItem(row, 2, QTableWidgetItem(member["wxid"]))

    def on_search_changed(self, text):
        """搜索文本变化"""
        # 延迟搜索，避免频繁更新
        if hasattr(self, "search_timer"):
            self.search_timer.stop()

        self.search_timer = QTimer()
        self.search_timer.timeout.connect(lambda: self.perform_search(text))
        self.search_timer.setSingleShot(True)
        self.search_timer.start(300)  # 300ms延迟

    def perform_search(self, text):
        """执行搜索"""
        self.update_friends_table(text)
        self.update_groups_table(text)

    def clear_search(self):
        """清空搜索"""
        self.search_input.clear()
        self.update_friends_table()
        self.update_groups_table()

    def clear_selected(self):
        """清空选择"""
        self.selected_members.clear()
        self.update_selected_display()

        # 更新表格中的复选框状态
        self.update_friends_table()
        self.update_groups_table()

    def confirm_selection(self):
        """确认选择"""
        if not self.selected_members:
            ThemedMessageBoxHelper.show_information(self, "提示", "请至少选择一个成员")
            return

        # 转换为GroupMember对象
        group_members = []
        for member in self.selected_members:
            group_member = GroupMember(
                wxid=member["wxid"],
                name=member["name"],
                member_type=member["type"],
                remark=member.get("remark", ""),
            )
            group_members.append(group_member)

        self.members_selected.emit(group_members)
        self.accept()

    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 停止加载线程
            if hasattr(self, "load_thread") and self.load_thread.isRunning():
                self.load_thread.quit()
                self.load_thread.wait()

            # 主题清理已由 ThemedDialogBase 自动处理
        except Exception as e:
            logger.error(f"清理成员选择对话框资源失败: {e}")

        super().closeEvent(event)
