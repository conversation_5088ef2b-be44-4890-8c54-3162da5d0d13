#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet space 微信群发助手 - 可执行文件打包脚本
使用PyInstaller将项目打包为可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目信息
PROJECT_NAME = "MeetSpaceWeChatSender"
PROJECT_VERSION = "1.0.0"
COMPANY_NAME = "Meet space 会客创意空间"

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 清理.spec文件
    spec_files = [f for f in os.listdir(".") if f.endswith(".spec")]
    for spec_file in spec_files:
        os.remove(spec_file)
        print(f"  ✅ 已清理: {spec_file}")

def create_pyinstaller_spec():
    """创建PyInstaller配置文件"""
    print("📝 创建PyInstaller配置文件...")
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# Meet space 微信群发助手 - PyInstaller配置文件

import os
from pathlib import Path

# 项目根目录
project_root = Path(__file__).parent

block_cipher = None

# 数据文件配置
datas = [
    # 资源文件
    ('resources', 'resources'),
    # 配置文件
    ('config/*.json', 'config'),
    # 工具文件
    ('tools', 'tools'),
    # 微信助手文件
    ('wxhelper_files', 'wxhelper_files'),
    # 版本信息
    ('version_info.txt', '.'),
    # 许可证
    ('LICENSE.txt', '.'),
    # 说明文档
    ('README.md', '.'),
]

# 隐藏导入
hiddenimports = [
    # PyQt6相关
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.QtNetwork',
    
    # 项目模块
    'core',
    'core.wechatferry_connector',
    'core.http_api_connector',
    'core.timing_sender',
    'core.loop_sender',
    'core.send_monitor',
    'core.risk_control',
    'core.group_manager',
    'core.message_template',
    'core.config_manager',
    'core.message_sender_core',
    
    # UI模块
    'ui',
    'ui.main_window',
    'ui.timing_send_page',
    'ui.loop_send_page',
    'ui.task_status_page',
    'ui.modern_theme_manager',
    'ui.rich_text_editor',
    'ui.themed_dialog_base',
    'ui.themed_message_box',
    
    # 工具模块
    'utils',
    'utils.logger',
    'utils.path_manager',
    'utils.performance_optimizer',
    'utils.icon_manager',
    
    # 第三方库
    'wcferry',
    'pandas',
    'openpyxl',
    'requests',
    'aiohttp',
    'PIL',
    'yaml',
    'dateutil',
    'psutil',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
]

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=['hooks'],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{PROJECT_NAME}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',  # 版本信息文件
    icon='resources/icons/app_icon.ico' if os.path.exists('resources/icons/app_icon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='{PROJECT_NAME}',
)
'''
    
    with open(f"{PROJECT_NAME}.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print(f"  ✅ 已创建: {PROJECT_NAME}.spec")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--clean",
        "--noconfirm",
        f"{PROJECT_NAME}.spec"
    ]
    
    print(f"  📋 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("  ✅ 构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"  ❌ 构建失败: {e}")
        print(f"  错误输出: {e.stderr}")
        return False

def copy_additional_files():
    """复制额外文件到dist目录"""
    print("📁 复制额外文件...")
    
    dist_dir = Path("dist") / PROJECT_NAME
    if not dist_dir.exists():
        print("  ❌ dist目录不存在")
        return False
    
    # 要复制的文件和目录
    additional_items = [
        ("README.md", "README.md"),
        ("LICENSE.txt", "LICENSE.txt"),
        ("requirements.txt", "requirements.txt"),
    ]
    
    for src, dst in additional_items:
        if os.path.exists(src):
            dst_path = dist_dir / dst
            if os.path.isdir(src):
                shutil.copytree(src, dst_path, dirs_exist_ok=True)
            else:
                shutil.copy2(src, dst_path)
            print(f"  ✅ 已复制: {src} -> {dst}")
    
    return True

def create_installer_config():
    """创建安装程序配置"""
    print("📦 创建安装程序配置...")
    
    # 确保installer目录存在
    installer_dir = Path("installer")
    installer_dir.mkdir(exist_ok=True)
    
    # 创建WiX配置文件
    wix_config = f'''<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" 
           Name="{PROJECT_NAME}" 
           Language="2052" 
           Version="{PROJECT_VERSION}" 
           Manufacturer="{COMPANY_NAME}" 
           UpgradeCode="{{12345678-1234-1234-1234-123456789012}}">
    
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine" 
             Description="{PROJECT_NAME} 安装程序"
             Comments="Meet space 微信群发助手安装程序" />
    
    <MajorUpgrade DowngradeErrorMessage="已安装更新版本的 {PROJECT_NAME}。" />
    <MediaTemplate EmbedCab="yes" />
    
    <!-- 功能定义 -->
    <Feature Id="ProductFeature" Title="{PROJECT_NAME}" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>
    
    <!-- 目录结构 -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="{PROJECT_NAME}" />
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="{PROJECT_NAME}" />
      </Directory>
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>
    
    <!-- 组件组 -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <!-- 主程序文件 -->
      <Component Id="MainExecutable" Guid="{{11111111-1111-1111-1111-111111111111}}">
        <File Id="MainExe" Source="dist\\{PROJECT_NAME}\\{PROJECT_NAME}.exe" KeyPath="yes" />
      </Component>
      
      <!-- 程序菜单快捷方式 -->
      <Component Id="ProgramMenuShortcut" Directory="ApplicationProgramsFolder" Guid="{{*************-2222-2222-************}}">
        <Shortcut Id="ApplicationStartMenuShortcut"
                  Name="{PROJECT_NAME}"
                  Description="Meet space 微信群发助手"
                  Target="[INSTALLFOLDER]{PROJECT_NAME}.exe"
                  WorkingDirectory="INSTALLFOLDER" />
        <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
        <RegistryValue Root="HKCU" Key="Software\\{COMPANY_NAME}\\{PROJECT_NAME}" Name="installed" Type="integer" Value="1" KeyPath="yes" />
      </Component>
      
      <!-- 桌面快捷方式 -->
      <Component Id="DesktopShortcut" Directory="DesktopFolder" Guid="{{*************-3333-3333-************}}">
        <Shortcut Id="ApplicationDesktopShortcut"
                  Name="{PROJECT_NAME}"
                  Description="Meet space 微信群发助手"
                  Target="[INSTALLFOLDER]{PROJECT_NAME}.exe"
                  WorkingDirectory="INSTALLFOLDER" />
        <RegistryValue Root="HKCU" Key="Software\\{COMPANY_NAME}\\{PROJECT_NAME}" Name="desktop" Type="integer" Value="1" KeyPath="yes" />
      </Component>
    </ComponentGroup>
    
  </Product>
</Wix>
'''
    
    wix_file = installer_dir / "setup.wxs"
    with open(wix_file, "w", encoding="utf-8") as f:
        f.write(wix_config)
    
    print(f"  ✅ 已创建: {wix_file}")
    
    return True

def main():
    """主函数"""
    print("🚀 Meet space 微信群发助手 - 可执行文件打包")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    # 检查PyInstaller
    try:
        subprocess.run(["pyinstaller", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到PyInstaller，请先安装: pip install pyinstaller")
        return False
    
    try:
        # 1. 清理构建目录
        clean_build_dirs()
        
        # 2. 创建PyInstaller配置
        create_pyinstaller_spec()
        
        # 3. 构建可执行文件
        if not build_executable():
            return False
        
        # 4. 复制额外文件
        if not copy_additional_files():
            return False
        
        # 5. 创建安装程序配置
        create_installer_config()
        
        print("\n🎉 打包完成！")
        print(f"📁 可执行文件位置: dist/{PROJECT_NAME}/")
        print(f"📦 安装程序配置: installer/setup.wxs")
        print("\n📋 下一步:")
        print("1. 测试可执行文件: dist/{PROJECT_NAME}/{PROJECT_NAME}.exe")
        print("2. 使用WiX Toolset创建MSI安装包")
        print("3. 运行: python build_msi.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
