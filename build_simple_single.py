#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的单文件EXE构建脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_single_exe():
    """构建单文件EXE"""
    print("🚀 构建单文件EXE...")
    
    # 清理之前的构建
    for cleanup_dir in ["build", "dist"]:
        if Path(cleanup_dir).exists():
            shutil.rmtree(cleanup_dir)
            print(f"✅ 已清理: {cleanup_dir}")
    
    # 简单的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 无控制台
        "--name=MeetSpaceWeChatSender", # 程序名
        "--icon=resources/icons/app_icon.ico",  # 图标
        
        # 添加数据文件
        "--add-data=resources;resources",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        "--add-data=README.md;.",
        
        # 隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=pandas",
        "--hidden-import=requests",
        "--hidden-import=PIL",
        "--hidden-import=yaml",
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=ui",
        "--hidden-import=utils",
        
        # 排除模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy.testing",
        
        # 优化选项
        "--clean",
        "--noconfirm",
        
        # 主程序
        "main_encrypted.py"
    ]
    
    print(f"📋 执行命令:")
    print(f"  {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        
        # 检查输出文件
        exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"✅ 单文件EXE生成成功!")
            print(f"📁 文件位置: {exe_file}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            return True
        else:
            print("❌ 未找到生成的EXE文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def main():
    """主函数"""
    print("📦 Meet space 微信群发助手 - 单文件构建")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "main_encrypted.py",
        "resources/icons/app_icon.ico",
        "version_info.txt"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少必要文件: {file_path}")
            return False
        else:
            print(f"✅ 找到文件: {file_path}")
    
    # 构建
    if build_single_exe():
        print("\n🎉 构建完成!")
        print("📁 输出文件: dist/MeetSpaceWeChatSender.exe")
        print("\n✨ 特性:")
        print("  - 单个EXE文件，无需安装")
        print("  - 运行时自动生成配置文件")
        print("  - 便携式，可在任何Windows系统运行")
        return True
    else:
        print("\n❌ 构建失败!")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
