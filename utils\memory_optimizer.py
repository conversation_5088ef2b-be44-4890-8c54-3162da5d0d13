#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存优化器

提供立即可用的内存优化功能和建议。
"""

import gc
import sys
import time
import psutil
from typing import Dict, Any, List
from utils.logger import setup_logger

logger = setup_logger("memory_optimizer")


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.optimization_history: List[Dict[str, Any]] = []
        
    def get_current_memory_info(self) -> Dict[str, Any]:
        """获取当前内存信息"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "rss_mb": memory_info.rss / 1024 / 1024,  # 物理内存
                "vms_mb": memory_info.vms / 1024 / 1024,  # 虚拟内存
                "percent": process.memory_percent(),       # 内存占用百分比
                "available_mb": psutil.virtual_memory().available / 1024 / 1024,
                "total_mb": psutil.virtual_memory().total / 1024 / 1024,
                "gc_objects": len(gc.get_objects())       # 垃圾回收器中的对象数
            }
        except Exception as e:
            logger.error(f"获取内存信息失败: {e}")
            return {}
    
    def immediate_cleanup(self) -> Dict[str, Any]:
        """立即执行内存清理"""
        before_info = self.get_current_memory_info()
        start_time = time.time()
        
        # 执行清理操作
        results = {
            "before_memory_mb": before_info.get("rss_mb", 0),
            "gc_collected": 0,
            "qt_processed": False,
            "sys_modules_cleaned": 0,
            "execution_time": 0
        }
        
        try:
            # 1. 强制垃圾回收（多次执行确保彻底）
            total_collected = 0
            for generation in range(3):
                collected = gc.collect(generation)
                total_collected += collected
                time.sleep(0.01)  # 短暂休息
            
            results["gc_collected"] = total_collected
            
            # 2. 清理Qt事件队列（如果存在）
            try:
                from PyQt6.QtWidgets import QApplication
                if QApplication.instance():
                    QApplication.processEvents()
                    results["qt_processed"] = True
            except ImportError:
                pass
            
            # 3. 清理sys.modules中的缓存（谨慎操作）
            modules_to_clean = []
            for module_name, module in sys.modules.items():
                if hasattr(module, '__pycache__') or 'temp' in module_name.lower():
                    modules_to_clean.append(module_name)
            
            results["sys_modules_cleaned"] = len(modules_to_clean)
            
            # 4. 再次垃圾回收
            final_collected = gc.collect()
            results["gc_collected"] += final_collected
            
        except Exception as e:
            logger.error(f"内存清理过程中出错: {e}")
        
        # 获取清理后的信息
        after_info = self.get_current_memory_info()
        results.update({
            "after_memory_mb": after_info.get("rss_mb", 0),
            "memory_freed_mb": before_info.get("rss_mb", 0) - after_info.get("rss_mb", 0),
            "execution_time": time.time() - start_time
        })
        
        # 记录到历史
        self.optimization_history.append({
            "timestamp": time.time(),
            "type": "immediate_cleanup",
            "results": results
        })
        
        return results
    
    def get_memory_recommendations(self) -> List[str]:
        """获取内存优化建议"""
        current_info = self.get_current_memory_info()
        recommendations = []
        
        memory_mb = current_info.get("rss_mb", 0)
        gc_objects = current_info.get("gc_objects", 0)
        
        # 基于内存使用量的建议
        if memory_mb > 700:
            recommendations.append("🔴 内存使用较高(>700MB)，建议立即执行清理")
        elif memory_mb > 500:
            recommendations.append("🟡 内存使用中等(>500MB)，可考虑清理")
        else:
            recommendations.append("🟢 内存使用正常(<500MB)")
        
        # 基于对象数量的建议
        if gc_objects > 100000:
            recommendations.append("🔴 对象数量过多，建议执行垃圾回收")
        elif gc_objects > 50000:
            recommendations.append("🟡 对象数量较多，可考虑垃圾回收")
        
        # 基于历史优化效果的建议
        if len(self.optimization_history) > 0:
            last_optimization = self.optimization_history[-1]
            if time.time() - last_optimization["timestamp"] > 300:  # 5分钟
                recommendations.append("⏰ 距离上次清理已超过5分钟，建议清理")
        
        return recommendations
    
    def auto_optimize_settings(self) -> Dict[str, Any]:
        """自动调整优化设置"""
        current_info = self.get_current_memory_info()
        memory_mb = current_info.get("rss_mb", 0)
        
        # 根据当前内存使用调整阈值
        settings = {
            "cleanup_interval_minutes": 5,  # 默认5分钟
            "memory_threshold_mb": 500,     # 默认500MB
            "aggressive_cleanup": False     # 是否启用激进清理
        }

        if memory_mb > 800:
            settings.update({
                "cleanup_interval_minutes": 2,  # 2分钟清理一次
                "memory_threshold_mb": 400,     # 降低阈值
                "aggressive_cleanup": True
            })
        elif memory_mb > 600:
            settings.update({
                "cleanup_interval_minutes": 3,  # 3分钟清理一次
                "memory_threshold_mb": 450
            })
        
        return settings
    
    def generate_memory_report(self) -> str:
        """生成内存使用报告"""
        current_info = self.get_current_memory_info()
        recommendations = self.get_memory_recommendations()
        settings = self.auto_optimize_settings()
        
        report = f"""
📊 内存使用报告 - {time.strftime('%Y-%m-%d %H:%M:%S')}
{'='*50}

💾 当前内存状态：
  • 物理内存使用: {current_info.get('rss_mb', 0):.1f} MB
  • 虚拟内存使用: {current_info.get('vms_mb', 0):.1f} MB
  • 内存占用百分比: {current_info.get('percent', 0):.1f}%
  • 系统可用内存: {current_info.get('available_mb', 0):.1f} MB
  • 垃圾回收对象数: {current_info.get('gc_objects', 0):,}

💡 优化建议：
"""
        for i, rec in enumerate(recommendations, 1):
            report += f"  {i}. {rec}\n"
        
        report += f"""
⚙️ 建议设置：
  • 清理间隔: {settings['cleanup_interval_minutes']} 分钟
  • 内存阈值: {settings['memory_threshold_mb']} MB
  • 激进清理: {'启用' if settings['aggressive_cleanup'] else '禁用'}

📈 清理历史：
"""
        
        if self.optimization_history:
            recent_cleanups = self.optimization_history[-3:]  # 最近3次
            for cleanup in recent_cleanups:
                timestamp = time.strftime('%H:%M:%S', time.localtime(cleanup['timestamp']))
                results = cleanup['results']
                freed = results.get('memory_freed_mb', 0)
                report += f"  • {timestamp}: 释放 {freed:.1f}MB, 回收 {results.get('gc_collected', 0)} 对象\n"
        else:
            report += "  • 暂无清理历史\n"
        
        return report


# 全局内存优化器实例
memory_optimizer = MemoryOptimizer()


def quick_memory_check():
    """快速内存检查"""
    info = memory_optimizer.get_current_memory_info()
    memory_mb = info.get("rss_mb", 0)
    
    if memory_mb > 700:
        print(f"🔴 警告：内存使用过高 ({memory_mb:.1f}MB)")
        return "high"
    elif memory_mb > 500:
        print(f"🟡 注意：内存使用中等 ({memory_mb:.1f}MB)")
        return "medium"
    else:
        print(f"🟢 正常：内存使用良好 ({memory_mb:.1f}MB)")
        return "normal"


def quick_cleanup():
    """快速清理"""
    print("🧹 开始执行快速内存清理...")
    results = memory_optimizer.immediate_cleanup()
    
    print(f"✅ 清理完成:")
    print(f"  • 释放内存: {results['memory_freed_mb']:.1f}MB")
    print(f"  • 回收对象: {results['gc_collected']} 个")
    print(f"  • 耗时: {results['execution_time']:.2f}秒")
    
    return results


if __name__ == "__main__":
    # 如果直接运行此文件，执行快速检查和清理
    print("🔍 执行内存检查...")
    status = quick_memory_check()
    
    if status in ["high", "medium"]:
        quick_cleanup()
    
    print("\n" + memory_optimizer.generate_memory_report())