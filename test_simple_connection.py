#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试连接修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_injection_without_gui():
    """测试无GUI注入"""
    print("🔧 测试无GUI注入...")
    
    try:
        from core.http_api_connector import HTTPAPIConnector
        
        # 创建连接器
        connector = HTTPAPIConnector(
            api_type="wxhelper",
            base_url="http://localhost:19088",
            auto_inject=False
        )
        
        print("✅ 连接器创建成功")
        
        # 测试无GUI注入方法
        print("开始无GUI注入测试...")
        success, message = connector._inject_without_gui()
        
        print(f"注入结果: {success}")
        print(f"注入消息: {message}")
        
        if success:
            print("✅ 无GUI注入成功")
        elif message == "NEED_ELEVATION":
            print("✅ 正确识别需要权限提升")
        else:
            print(f"⚠️  注入失败: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 简单连接测试")
    print("=" * 50)
    
    success = test_injection_without_gui()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试完成")
        print("🎯 修复要点:")
        print("  - 分离了GUI操作和线程操作")
        print("  - 权限提升在主线程处理")
        print("  - 注入在线程池中执行")
    else:
        print("❌ 测试失败")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
