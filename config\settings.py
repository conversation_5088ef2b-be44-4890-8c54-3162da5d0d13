"""
全局设置模块

管理应用程序的全局设置和常量。
"""

import os
from pathlib import Path

# 使用路径管理器获取正确的路径
def _get_paths():
    """获取路径配置"""
    try:
        from utils.path_manager import path_manager
        return {
            'PROJECT_ROOT': path_manager.executable_dir,
            'CONFIG_DIR': path_manager.app_data_dir / "config",
            'LOGS_DIR': path_manager.app_data_dir / "logs",
            'RESOURCES_DIR': path_manager.get_resource_path("resources"),
            'TEMPLATES_DIR': path_manager.get_resource_path("resources/templates"),
            'ICONS_DIR': path_manager.get_resource_path("resources/icons"),
            'CONFIG_FILE': path_manager.get_config_path("wechat_config.json"),
            'LOG_FILE': path_manager.get_log_path("app.log")
        }
    except ImportError:
        # 备用方案：使用相对路径
        project_root = Path(__file__).parent.parent
        return {
            'PROJECT_ROOT': project_root,
            'CONFIG_DIR': project_root / "config",
            'LOGS_DIR': project_root / "logs",
            'RESOURCES_DIR': project_root / "resources",
            'TEMPLATES_DIR': project_root / "resources" / "templates",
            'ICONS_DIR': project_root / "resources" / "icons",
            'CONFIG_FILE': project_root / "config" / "wechat_config.json",
            'LOG_FILE': project_root / "logs" / "app.log"
        }

# 获取路径配置
_paths = _get_paths()
PROJECT_ROOT = _paths['PROJECT_ROOT']
CONFIG_DIR = _paths['CONFIG_DIR']
LOGS_DIR = _paths['LOGS_DIR']
RESOURCES_DIR = _paths['RESOURCES_DIR']
TEMPLATES_DIR = _paths['TEMPLATES_DIR']
ICONS_DIR = _paths['ICONS_DIR']
CONFIG_FILE = _paths['CONFIG_FILE']
LOG_FILE = _paths['LOG_FILE']

# 应用程序信息
APP_NAME = "微信无感群发助手"
APP_VERSION = "1.0.0"
APP_AUTHOR = "开发者"
APP_DESCRIPTION = "一个安全、高效的微信群发工具"

# UI设置
WINDOW_MIN_WIDTH = 900
WINDOW_MIN_HEIGHT = 600
WINDOW_DEFAULT_WIDTH = 1200
WINDOW_DEFAULT_HEIGHT = 800

# 日志设置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5

# 消息设置
MAX_MESSAGE_LENGTH = 1000
SUPPORTED_FILE_TYPES = [
    ".txt",
    ".doc",
    ".docx",
    ".pdf",
    ".xls",
    ".xlsx",
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".mp4",
    ".avi",
    ".mov",
    ".wmv",
]

# 安全设置
MIN_SEND_INTERVAL = 1
MAX_SEND_INTERVAL = 60
MIN_BATCH_SIZE = 1
MAX_BATCH_SIZE = 100
MIN_BATCH_INTERVAL = 10
MAX_BATCH_INTERVAL = 3600

# 默认模板变量
DEFAULT_TEMPLATE_VARIABLES = {
    "name": "联系人名称",
    "wxid": "联系人微信ID",
    "current_time": "当前时间",
    "current_date": "当前日期",
    "random_emoji": "随机表情",
}

# 表情符号列表
EMOJI_LIST = [
    "😊",
    "😄",
    "😃",
    "😀",
    "😆",
    "😂",
    "🤣",
    "😍",
    "🥰",
    "😘",
    "😗",
    "😙",
    "😚",
    "🤗",
    "🤔",
    "😎",
    "🤓",
    "😏",
    "😊",
    "😌",
]


def ensure_directories():
    """确保必要的目录存在"""
    directories = [CONFIG_DIR, LOGS_DIR, RESOURCES_DIR, TEMPLATES_DIR, ICONS_DIR]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)


def get_resource_path(filename: str) -> Path:
    """获取资源文件路径"""
    return RESOURCES_DIR / filename


def get_template_path(filename: str) -> Path:
    """获取模板文件路径"""
    return TEMPLATES_DIR / filename


def get_icon_path(filename: str) -> Path:
    """获取图标文件路径"""
    return ICONS_DIR / filename
