#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试注入修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_wechat_process_detection():
    """测试微信进程检测"""
    print("🔍 测试微信进程检测...")
    
    try:
        from core.injector_tool import InjectorTool
        
        # 创建注入器
        injector = InjectorTool(auto_elevate=False)
        
        # 查找微信进程
        wechat_process = injector.find_wechat_process()
        
        if wechat_process:
            print(f"✅ 找到微信进程: {wechat_process.info['name']} (PID: {wechat_process.info['pid']})")
            return True
        else:
            print("❌ 未找到微信进程")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_injection():
    """测试注入功能"""
    print("\n💉 测试注入功能...")
    
    try:
        from core.injector_tool import InjectorTool
        
        # 创建注入器（不自动提升权限）
        injector = InjectorTool(auto_elevate=False)
        
        print(f"注入器路径: {injector.injector_path}")
        print(f"DLL路径: {injector.dll_path}")
        
        # 检查文件
        if not injector.injector_path.exists():
            print(f"❌ 注入器不存在: {injector.injector_path}")
            return False
        
        if not injector.dll_path.exists():
            print(f"❌ DLL不存在: {injector.dll_path}")
            return False
        
        print("✅ 注入器和DLL文件都存在")
        
        # 尝试注入
        print("\n开始注入...")
        success, message = injector.inject_dll()
        
        if success:
            print(f"✅ 注入成功: {message}")
            return True
        else:
            print(f"❌ 注入失败: {message}")
            
            # 提供解决建议
            if "Could not create thread in remote process" in message:
                print("\n🔧 解决建议:")
                print("1. 尝试重启微信")
                print("2. 确保微信版本兼容")
                print("3. 检查杀毒软件设置")
                print("4. 尝试使用不同的DLL版本")
            elif "General Error" in message:
                print("\n🔧 解决建议:")
                print("1. 检查DLL文件完整性")
                print("2. 确保微信进程没有被保护")
                print("3. 尝试以管理员身份运行")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 注入修复测试")
    print("=" * 60)
    
    tests = [
        ("微信进程检测", test_wechat_process_detection),
        ("注入功能测试", test_injection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 注入功能修复成功！")
        print("✨ 改进内容:")
        print("  🔍 改进了微信进程检测逻辑")
        print("  📋 支持多种微信进程类型")
        print("  🎯 优先选择主微信进程")
    else:
        print("\n⚠️  注入仍有问题，需要进一步调试")
        print("\n🔧 常见解决方案:")
        print("1. 重启微信并重试")
        print("2. 检查微信版本兼容性")
        print("3. 临时关闭杀毒软件")
        print("4. 确保以管理员身份运行")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
