#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证工具

提供统一的配置验证和修复功能，确保配置的一致性和正确性。
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

from utils.logger import setup_logger

logger = setup_logger("config_validator")


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        """初始化配置验证器"""
        self.validation_rules = {
            "message_settings.default_type": {
                "type": str,
                "allowed_values": ["text", "rich_text"],
                "default": "text"
            },
            "message_settings.default_content": {
                "type": [str, dict],
                "required": False,
                "default": ""
            },
            "send_settings.interval_seconds": {
                "type": int,
                "min_value": 1,
                "max_value": 3600,
                "default": 5
            },
            "send_settings.batch_size": {
                "type": int,
                "min_value": 1,
                "max_value": 100,
                "default": 10
            }
        }
    
    def validate_config_value(self, key: str, value: Any) -> Tuple[bool, str, Any]:
        """
        验证单个配置值
        
        Args:
            key: 配置键名
            value: 配置值
            
        Returns:
            (是否有效, 错误信息, 修正后的值)
        """
        try:
            if key not in self.validation_rules:
                return True, "", value
                
            rule = self.validation_rules[key]
            
            # 类型检查
            expected_type = rule.get("type")
            if expected_type:
                if isinstance(expected_type, list):
                    if not any(isinstance(value, t) for t in expected_type):
                        default_value = rule.get("default", "")
                        return False, f"类型错误，期望 {expected_type}，实际 {type(value)}", default_value
                else:
                    if not isinstance(value, expected_type):
                        default_value = rule.get("default", "")
                        return False, f"类型错误，期望 {expected_type}，实际 {type(value)}", default_value
            
            # 值范围检查
            if "allowed_values" in rule:
                if value not in rule["allowed_values"]:
                    default_value = rule.get("default", rule["allowed_values"][0])
                    return False, f"值不在允许范围内: {rule['allowed_values']}", default_value
            
            # 数值范围检查
            if isinstance(value, (int, float)):
                if "min_value" in rule and value < rule["min_value"]:
                    return False, f"值小于最小值 {rule['min_value']}", rule["min_value"]
                if "max_value" in rule and value > rule["max_value"]:
                    return False, f"值大于最大值 {rule['max_value']}", rule["max_value"]
            
            return True, "", value
            
        except Exception as e:
            logger.error(f"验证配置值失败: {key}={value}, {e}")
            default_value = self.validation_rules.get(key, {}).get("default", "")
            return False, f"验证异常: {e}", default_value
    
    def validate_message_content(self, content: Any, content_type: str) -> Tuple[bool, str]:
        """
        验证消息内容
        
        Args:
            content: 消息内容
            content_type: 消息类型 ("text" 或 "rich_text")
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            if content_type == "text":
                if not isinstance(content, str):
                    return False, "文本消息内容必须是字符串"
                if not content.strip():
                    return False, "文本消息内容不能为空"
                    
            elif content_type == "rich_text":
                if isinstance(content, str):
                    # 尝试解析JSON
                    try:
                        content_data = json.loads(content)
                    except json.JSONDecodeError:
                        return False, "富文本消息内容JSON格式错误"
                elif isinstance(content, dict):
                    content_data = content
                else:
                    return False, "富文本消息内容格式错误"
                
                # 验证富文本内容结构
                required_keys = ["type", "html", "plain_text", "images"]
                for key in required_keys:
                    if key not in content_data:
                        return False, f"富文本消息缺少必要字段: {key}"
                
                # 检查是否有实际内容
                plain_text = content_data.get("plain_text", "").strip()
                images = content_data.get("images", [])
                
                if not plain_text and not images:
                    return False, "富文本消息没有实际内容（文字或图片）"
            
            else:
                return False, f"不支持的消息类型: {content_type}"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"验证消息内容失败: {e}")
            return False, f"验证异常: {e}"
    
    def fix_config_inconsistencies(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        修复配置不一致问题
        
        Args:
            config_data: 配置数据
            
        Returns:
            修复后的配置数据
        """
        try:
            fixed_config = config_data.copy()
            fixes_applied = []
            
            # 修复消息类型标识不一致
            message_type = fixed_config.get("message_settings", {}).get("default_type", "text")
            if message_type in ["富文本消息", "富文本"]:
                fixed_config.setdefault("message_settings", {})["default_type"] = "rich_text"
                fixes_applied.append("消息类型标识统一为 'rich_text'")
            elif message_type in ["文本消息", "普通文本", "文本"]:
                fixed_config.setdefault("message_settings", {})["default_type"] = "text"
                fixes_applied.append("消息类型标识统一为 'text'")
            
            # 验证和修复所有配置值
            for key, rule in self.validation_rules.items():
                keys = key.split(".")
                current = fixed_config
                
                # 导航到配置位置
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                
                final_key = keys[-1]
                if final_key in current:
                    is_valid, error_msg, corrected_value = self.validate_config_value(key, current[final_key])
                    if not is_valid:
                        current[final_key] = corrected_value
                        fixes_applied.append(f"{key}: {error_msg}")
            
            if fixes_applied:
                logger.info(f"应用了 {len(fixes_applied)} 个配置修复:")
                for fix in fixes_applied:
                    logger.info(f"  - {fix}")
            
            return fixed_config
            
        except Exception as e:
            logger.error(f"修复配置不一致问题失败: {e}")
            return config_data


class MessageContentValidator:
    """消息内容验证器"""
    
    @staticmethod
    def validate_rich_text_content(content_data: Dict[str, Any]) -> Tuple[bool, str, Dict[str, Any]]:
        """
        验证富文本内容
        
        Args:
            content_data: 富文本内容数据
            
        Returns:
            (是否有效, 详细信息, 统计数据)
        """
        try:
            stats = {
                "has_content": False,
                "has_text": False,
                "has_images": False,
                "text_length": 0,
                "image_count": 0,
                "valid_images": 0
            }
            
            # 检查文字内容
            plain_text = content_data.get("plain_text", "").strip()
            if plain_text:
                stats["has_text"] = True
                stats["text_length"] = len(plain_text)
            
            # 检查图片内容
            images = content_data.get("images", [])
            if images:
                stats["image_count"] = len(images)
                # 检查图片文件是否存在
                valid_images = 0
                for img in images:
                    if isinstance(img, dict):
                        img_path = img.get("path", "")
                    else:
                        img_path = str(img)
                    
                    if img_path and Path(img_path).exists():
                        valid_images += 1
                
                stats["valid_images"] = valid_images
                if valid_images > 0:
                    stats["has_images"] = True
            
            # 判断是否有内容
            stats["has_content"] = stats["has_text"] or stats["has_images"]
            
            # 生成详细信息
            if stats["has_content"]:
                details = []
                if stats["has_text"]:
                    details.append(f"文字{stats['text_length']}字")
                if stats["has_images"]:
                    details.append(f"图片{stats['valid_images']}/{stats['image_count']}张")
                detail_msg = f"内容验证通过: {', '.join(details)}"
            else:
                detail_msg = "内容为空"
            
            return stats["has_content"], detail_msg, stats
            
        except Exception as e:
            logger.error(f"验证富文本内容失败: {e}")
            return False, f"验证异常: {e}", {}


# 全局实例
config_validator = ConfigValidator()
message_content_validator = MessageContentValidator()
