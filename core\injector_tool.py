#!/usr/bin/env python3
"""
基于Injector.exe的DLL注入器 - 项目唯一注入方式
使用nefarius/Injector工具进行DLL注入
"""

import os
import sys
import time
import ctypes
import subprocess
import requests
from pathlib import Path
from typing import Tuple, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import psutil
except ImportError:
    print("警告: psutil库未安装，某些功能可能不可用")
    psutil = None

from utils.logger import setup_logger
from utils.simple_admin import is_admin, ensure_admin_privileges
from utils.subprocess_helper import safe_powershell_run, safe_subprocess_run

logger = setup_logger("injector_tool")


def get_resource_path(relative_path: str) -> Path:
    """获取资源文件路径，支持开发环境和打包环境，优先使用临时解压"""
    try:
        import tempfile

        # 对于注入工具，优先检查临时解压目录
        if hasattr(sys, '_MEIPASS') and (
            relative_path.startswith('tools/') or
            relative_path.startswith('wxhelper_files/')
        ):
            temp_base = Path(tempfile.gettempdir()) / "MeetSpaceWeChatSender"
            temp_path = temp_base / relative_path
            if temp_path.exists():
                logger.debug(f"使用临时解压文件: {temp_path}")
                return temp_path

        # 常规路径处理
        # PyInstaller打包后的路径
        base_path = Path(sys._MEIPASS)
        logger.debug(f"使用打包环境路径: {base_path}")
    except AttributeError:
        # 开发环境路径
        base_path = Path(__file__).parent.parent
        logger.debug(f"使用开发环境路径: {base_path}")

    full_path = base_path / relative_path
    logger.debug(f"资源路径: {relative_path} -> {full_path}")
    return full_path



class InjectorTool:
    """Injector.exe + wxhelper.dll 注入器 - 项目唯一支持的注入方式"""

    def __init__(self, auto_elevate: bool = True):
        # 文件路径
        self.dll_path = get_resource_path("wxhelper_files/wxhelper.dll")

        # 自动选择正确架构的注入器
        self.injector_path = self._get_correct_injector_path()

        # API配置
        self.api_port = 19088
        self.api_url = f"http://localhost:{self.api_port}"

        # 权限管理
        self.auto_elevate = auto_elevate

        logger.info(f"初始化 Injector.exe + wxhelper.dll 注入器")
        logger.info(f"DLL路径: {self.dll_path}")
        logger.info(f"注入工具路径: {self.injector_path}")
        logger.info(f"API端口: {self.api_port}")
        logger.info(f"当前权限: {'管理员' if is_admin() else '普通用户'}")
        logger.info(f"自动提升权限: {'启用' if auto_elevate else '禁用'}")

        # 检查必要文件
        self._check_files()

    def _get_correct_injector_path(self) -> str:
        """根据系统和微信架构选择正确的注入器 - 增强版选择"""
        try:
            # 检查微信进程架构
            wechat_is_64bit = self._check_wechat_architecture()

            # 准备候选注入器列表（按优先级排序）
            candidates = []

            if wechat_is_64bit:
                logger.info("检测到64位微信，准备64位注入器候选列表")
                candidates = [
                    ("64位专用", "tools/x64/Injector.exe"),
                    ("默认版本", "tools/Injector.exe"),
                    ("32位兼容", "tools/Win32/Injector.exe"),  # 某些情况下可能兼容
                ]
            else:
                logger.info("检测到32位微信，准备32位注入器候选列表")
                candidates = [
                    ("32位专用", "tools/Win32/Injector.exe"),
                    ("默认版本", "tools/Injector.exe"),
                    ("64位降级", "tools/x64/Injector.exe"),  # 作为最后尝试
                ]

            # 检查ARM64架构（如果是ARM64系统）
            if self._is_arm64_system():
                logger.info("检测到ARM64系统，优先使用ARM64注入器")
                candidates.insert(0, ("ARM64专用", "tools/ARM64/Injector.exe"))

            # 按优先级尝试每个候选注入器
            for desc, relative_path in candidates:
                injector_path = get_resource_path(relative_path)
                if os.path.exists(injector_path):
                    logger.info(f"选择注入器: {desc} - {injector_path}")
                    return injector_path
                else:
                    logger.debug(f"注入器不存在: {desc} - {injector_path}")

            # 如果所有候选都不存在，使用默认路径（即使不存在）
            default_path = get_resource_path("tools/Injector.exe")
            logger.error(f"所有注入器候选都不存在，使用默认路径: {default_path}")
            return default_path

        except Exception as e:
            logger.error(f"选择注入器失败: {e}")
            # 最终回退
            return get_resource_path("tools/Injector.exe")

    def _is_arm64_system(self) -> bool:
        """检查是否为ARM64系统"""
        try:
            import platform
            machine = platform.machine().lower()
            processor = platform.processor().lower()

            # 检查常见的ARM64标识
            arm64_indicators = ['arm64', 'aarch64', 'arm']

            is_arm64 = any(indicator in machine for indicator in arm64_indicators) or \
                      any(indicator in processor for indicator in arm64_indicators)

            if is_arm64:
                logger.info(f"检测到ARM64系统: machine={machine}, processor={processor}")

            return is_arm64

        except Exception as e:
            logger.warning(f"ARM64系统检测失败: {e}")
            return False

    def _check_wechat_architecture(self) -> bool:
        """检查微信进程是否为64位 - 增强版检测"""
        try:
            # 方法1: 使用psutil直接检查进程架构
            wechat_process = self.find_wechat_process()
            if wechat_process:
                try:
                    # 尝试获取进程的可执行文件路径
                    exe_path = wechat_process.exe()
                    logger.info(f"微信可执行文件路径: {exe_path}")

                    # 使用file命令或PowerShell检查文件架构
                    return self._check_file_architecture(exe_path)
                except (psutil.AccessDenied, psutil.NoSuchProcess):
                    logger.warning("无法直接访问微信进程，使用备用方法")

            # 方法2: 使用PowerShell检查进程路径（原方法）
            import subprocess
            cmd = "Get-Process WeChat | Select-Object -ExpandProperty Path"
            result = safe_powershell_run(cmd, timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                wechat_path = result.stdout.strip()
                logger.info(f"微信路径: {wechat_path}")

                # 先尝试检查文件架构
                file_arch = self._check_file_architecture(wechat_path)
                if file_arch is not None:
                    return file_arch

                # 如果文件检查失败，使用路径判断（原逻辑）
                if "Program Files (x86)" in wechat_path:
                    logger.info("根据路径检测到32位微信")
                    return False
                elif "Program Files" in wechat_path:
                    logger.info("根据路径检测到64位微信")
                    return True
                else:
                    logger.warning("无法从路径判断微信架构")
                    return True  # 默认假设64位
            else:
                logger.warning("无法获取微信进程路径")
                return True  # 默认假设64位

        except Exception as e:
            logger.error(f"检查微信架构失败: {e}")
            return True  # 默认假设64位

    def _check_file_architecture(self, file_path: str) -> bool:
        """检查可执行文件的架构"""
        try:
            import subprocess

            # 使用PowerShell的Get-ItemProperty检查文件架构
            cmd = f'Get-ItemProperty -Path "{file_path}" | Select-Object -ExpandProperty VersionInfo | Select-Object -ExpandProperty FileDescription'
            result = safe_powershell_run(cmd, timeout=5)

            # 如果PowerShell方法失败，尝试使用file命令（如果可用）
            if result.returncode != 0:
                # 使用dumpbin检查PE文件头（如果Visual Studio工具可用）
                cmd2 = f'powershell -Command "& {{$bytes = [System.IO.File]::ReadAllBytes(\'{file_path}\'); $arch = [System.BitConverter]::ToUInt16($bytes, 4 + [System.BitConverter]::ToInt32($bytes, 60) + 4); if ($arch -eq 0x014c) {{\'32-bit\'}} elseif ($arch -eq 0x8664) {{\'64-bit\'}} else {{\'unknown\'}}}}"'
                result2 = subprocess.run(cmd2, capture_output=True, text=True, timeout=5, shell=True)

                if result2.returncode == 0 and result2.stdout.strip():
                    arch_info = result2.stdout.strip()
                    if "64-bit" in arch_info:
                        logger.info("文件架构检测: 64位")
                        return True
                    elif "32-bit" in arch_info:
                        logger.info("文件架构检测: 32位")
                        return False

            logger.warning("无法检测文件架构")
            return None

        except Exception as e:
            logger.warning(f"文件架构检测失败: {e}")
            return None

    def _check_files(self) -> None:
        """检查必要文件是否存在"""
        if not os.path.exists(self.dll_path):
            logger.error(f"DLL文件不存在: {self.dll_path}")
        else:
            logger.info(f"DLL文件检查通过: {self.dll_path}")

        if not os.path.exists(self.injector_path):
            logger.error(f"注入工具不存在: {self.injector_path}")
        else:
            logger.info(f"注入工具检查通过: {self.injector_path}")

    def check_admin_privileges(self) -> bool:
        """检查是否具有管理员权限"""
        return is_admin()

    def find_wechat_process(self):
        """查找微信进程"""
        if not psutil:
            logger.error("psutil库不可用，无法查找进程")
            return None

        try:
            for proc in psutil.process_iter(["pid", "name", "exe"]):
                try:
                    if proc.info["name"] and "WeChat.exe" in proc.info["name"]:
                        logger.info(
                            f"找到微信进程: {proc.info['name']} (PID: {proc.info['pid']})"
                        )
                        return proc
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            logger.warning("未找到微信进程")
            return None

        except Exception as e:
            logger.error(f"查找微信进程失败: {e}")
            return None

    def check_api_service(self) -> bool:
        """检查wxhelper API服务是否可用"""
        try:
            response = requests.get(f"{self.api_url}/api/checkLogin", timeout=3)
            if response.status_code == 200:
                logger.debug("API服务响应正常")
                return True
            else:
                logger.debug(f"API服务响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.debug(f"API服务不可用: {e}")
            return False

    def check_dll_injected(self, process) -> bool:
        """检查DLL是否已注入到进程"""
        if not psutil or not process:
            return False

        try:
            # 获取进程加载的模块
            for dll in process.memory_maps():
                if dll.path and "wxhelper.dll" in dll.path.lower():
                    logger.debug(f"检测到已注入的DLL: {dll.path}")
                    return True
            return False
        except (psutil.NoSuchProcess, psutil.AccessDenied, AttributeError):
            # 如果无法获取内存映射，尝试通过API服务检查
            return self.check_api_service()

    def inject_dll(self) -> Tuple[bool, str]:
        """执行DLL注入"""
        try:
            # 检查管理员权限
            if not is_admin():
                if self.auto_elevate:
                    logger.info("检测到权限不足，尝试自动提升管理员权限...")
                    try:
                        success = ensure_admin_privileges(auto_elevate=True)
                        if success:
                            # 权限提升成功，但当前进程会退出，新进程会接管
                            return True, "权限提升成功，程序将重新启动"
                        else:
                            return False, "权限提升失败，请手动以管理员身份运行程序"
                    except Exception as e:
                        logger.error(f"权限提升异常: {e}")
                        return False, f"权限提升异常: {e}"
                else:
                    return False, "需要管理员权限进行DLL注入，请以管理员身份运行程序"

            # 检查必要文件
            if not self.dll_path.exists():
                return False, f"DLL文件不存在: {self.dll_path}"

            if not self.injector_path.exists():
                return False, f"注入工具不存在: {self.injector_path}"

            # 查找微信进程
            wechat_process = self.find_wechat_process()
            if not wechat_process:
                return False, "未找到微信进程，请先启动微信"

            process_pid = wechat_process.pid
            logger.info(f"目标进程: WeChat.exe (PID: {process_pid})")

            # 检查是否已注入
            if self.check_dll_injected(wechat_process):
                logger.info("DLL已经注入，检查API服务状态")
                if self.check_api_service():
                    return True, "DLL已注入且API服务正常"
                else:
                    logger.warning("DLL已注入但API服务未响应")

            # 使用Injector.exe执行注入
            logger.info("开始使用Injector.exe执行DLL注入...")

            # 构建命令行参数
            cmd = [
                str(self.injector_path.absolute()),
                "--process-id",
                str(process_pid),
                "--inject",
                str(self.dll_path.absolute()),
            ]

            logger.info(f"执行命令: {' '.join(cmd)}")

            # 执行注入命令（隐藏窗口）
            result = safe_subprocess_run(
                cmd,
                timeout=30,  # 30秒超时
                cwd=str(self.injector_path.parent),  # 设置工作目录
                creationflags=(
                    subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                ),
            )

            if result.returncode == 0:
                logger.info(f"Injector.exe执行成功")
                if result.stdout:
                    logger.info(f"输出: {result.stdout.strip()}")

                # 等待API服务启动
                return self._wait_for_api_service()
            else:
                error_msg = result.stderr.strip() if result.stderr else "未知错误"
                logger.error(
                    f"Injector.exe执行失败 (返回码: {result.returncode}): {error_msg}"
                )
                return False, f"注入失败: {error_msg}"

        except subprocess.TimeoutExpired:
            logger.error("Injector.exe执行超时")
            return False, "注入超时，请重试"
        except FileNotFoundError:
            logger.error(f"找不到注入工具: {self.injector_path}")
            return False, "注入工具不存在"
        except Exception as e:
            logger.error(f"注入过程异常: {e}")
            return False, f"注入异常: {e}"

    def _wait_for_api_service(self) -> Tuple[bool, str]:
        """等待API服务启动"""
        logger.info("等待wxhelper API服务启动...")
        max_retries = 30  # 30秒
        retry_interval = 1

        for i in range(max_retries):
            time.sleep(retry_interval)

            if self.check_api_service():
                logger.info("wxhelper注入完成，API服务正常运行")
                return True, "注入成功，API服务已启动"
            else:
                logger.debug(f"等待API服务启动 {i+1}/{max_retries}...")

        logger.warning("注入成功但API服务未在预期时间内启动")
        return True, "注入成功但API服务启动超时，请手动重启微信"

    def check_injection_status(self) -> Tuple[bool, str]:
        """检查注入状态"""
        try:
            # 检查微信进程
            wechat_process = self.find_wechat_process()
            if not wechat_process:
                return False, "未找到微信进程"

            # 检查DLL是否注入
            if self.check_dll_injected(wechat_process):
                # 检查API服务
                if self.check_api_service():
                    return True, "wxhelper已注入且API服务正常"
                else:
                    return True, "wxhelper已注入但API服务未响应"
            else:
                return False, "wxhelper未注入"

        except Exception as e:
            logger.error(f"检查注入状态异常: {e}")
            return False, f"检查状态异常: {e}"


if __name__ == "__main__":
    # 生产环境下不执行测试代码
    pass
