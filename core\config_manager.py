"""
配置管理器

统一管理系统配置、分组配置和发送设置。
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

from utils.logger import setup_logger

logger = setup_logger("config_manager")


@dataclass
class SendSettings:
    """发送设置"""

    interval_seconds: int = 5
    batch_size: int = 10
    retry_count: int = 3
    timeout: int = 30
    use_system_risk_control: bool = True
    max_per_hour: int = 100
    detection_mode: str = "smart"


@dataclass
class SystemConfig:
    """系统配置"""

    auto_connect: bool = False
    startup_delay: int = 1
    login_timeout: int = 30
    api_timeout: int = 10
    log_level: str = "INFO"


class ConfigManager:
    """配置管理器"""

    def __init__(self):
        # 使用路径管理器获取配置目录
        try:
            from utils.path_manager import path_manager
            self.config_dir = path_manager.app_data_dir / "config"
        except ImportError:
            self.config_dir = Path("config")

        self.config_dir.mkdir(exist_ok=True)

        self.system_config_file = self.config_dir / "system_config.json"
        self.send_settings_file = self.config_dir / "send_settings.json"

        # 加载配置
        self.system_config = self._load_system_config()
        self.send_settings = self._load_send_settings()

        logger.info("配置管理器初始化完成")

    def _load_system_config(self) -> SystemConfig:
        """加载系统配置"""
        try:
            if self.system_config_file.exists():
                with open(self.system_config_file, "r", encoding="utf-8") as f:
                    data = json.load(f)

                # 移除已废弃的debug_mode字段（兼容性处理）
                if "debug_mode" in data:
                    logger.info("移除已废弃的debug_mode配置字段")
                    del data["debug_mode"]

                return SystemConfig(**data)
            else:
                # 创建默认配置
                config = SystemConfig()
                self._save_system_config(config)
                return config
        except Exception as e:
            logger.error(f"加载系统配置失败: {e}")
            return SystemConfig()

    def _load_send_settings(self) -> SendSettings:
        """加载发送设置"""
        try:
            if self.send_settings_file.exists():
                with open(self.send_settings_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                return SendSettings(**data)
            else:
                # 创建默认设置
                settings = SendSettings()
                self._save_send_settings(settings)
                return settings
        except Exception as e:
            logger.error(f"加载发送设置失败: {e}")
            return SendSettings()

    def _save_system_config(self, config: SystemConfig) -> bool:
        """保存系统配置"""
        try:
            with open(self.system_config_file, "w", encoding="utf-8") as f:
                json.dump(asdict(config), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存系统配置失败: {e}")
            return False

    def _save_send_settings(self, settings: SendSettings) -> bool:
        """保存发送设置"""
        try:
            with open(self.send_settings_file, "w", encoding="utf-8") as f:
                json.dump(asdict(settings), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存发送设置失败: {e}")
            return False

    def get_system_config(self) -> SystemConfig:
        """获取系统配置"""
        return self.system_config

    def update_system_config(self, **kwargs) -> bool:
        """更新系统配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.system_config, key):
                    setattr(self.system_config, key, value)
            return self._save_system_config(self.system_config)
        except Exception as e:
            logger.error(f"更新系统配置失败: {e}")
            return False

    def get_send_settings(self) -> SendSettings:
        """获取发送设置"""
        return self.send_settings

    def update_send_settings(self, **kwargs) -> bool:
        """更新发送设置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.send_settings, key):
                    setattr(self.send_settings, key, value)
            return self._save_send_settings(self.send_settings)
        except Exception as e:
            logger.error(f"更新发送设置失败: {e}")
            return False

    def get_merged_send_settings(
        self, group_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """获取合并的发送设置

        优先级：分组配置 > 系统配置 > 默认配置
        """
        # 基础设置
        base_settings = asdict(self.send_settings)

        # 如果有分组配置，合并分组的发送设置
        if group_config and "send_settings" in group_config:
            group_send_settings = group_config["send_settings"]
            base_settings.update(group_send_settings)

        return base_settings

    def get_config(self) -> Dict[str, Any]:
        """获取完整配置字典"""
        try:
            # 合并系统配置和发送设置
            config = {
                "system": asdict(self.system_config),
                "send_settings": asdict(self.send_settings),
                # 添加风控配置
                "risk_control": {
                    "enable_risk_control": self.send_settings.use_system_risk_control,
                    "send_interval_min": self.send_settings.interval_seconds,
                    "batch_size": self.send_settings.batch_size,
                    "max_per_hour": self.send_settings.max_per_hour,
                    "detection_mode": self.send_settings.detection_mode,
                },
            }
            return config
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            return {}

    def get_config_value(self, config_path: str, default: Any = None) -> Any:
        """获取配置值

        支持点分隔的路径，如: "send_settings.interval_seconds"
        """
        try:
            keys = config_path.split(".")

            # 确定配置源
            if keys[0] == "system":
                current = asdict(self.system_config)
                keys = keys[1:]  # 移除 "system" 前缀
            elif keys[0] == "send_settings":
                current = asdict(self.send_settings)
                keys = keys[1:]  # 移除 "send_settings" 前缀
            else:
                # 尝试从系统配置开始
                current = asdict(self.system_config)
                if keys[0] not in current:
                    # 如果不在系统配置中，尝试发送设置
                    current = asdict(self.send_settings)

            # 导航到目标值
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default

            return current

        except Exception as e:
            logger.error(f"获取配置值失败: {config_path}, {e}")
            return default

    def set_config_value(self, config_path: str, value: Any) -> bool:
        """设置配置值"""
        try:
            keys = config_path.split(".")

            # 确定配置目标
            if keys[0] == "system":
                return self.update_system_config(**{keys[1]: value})
            elif keys[0] == "send_settings":
                return self.update_send_settings(**{keys[1]: value})
            else:
                # 默认更新发送设置
                return self.update_send_settings(**{keys[0]: value})

        except Exception as e:
            logger.error(f"设置配置值失败: {config_path}={value}, {e}")
            return False

    def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            self.system_config = self._load_system_config()
            self.send_settings = self._load_send_settings()
            logger.info("配置重新加载完成")
            return True
        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
            return False

    def export_config(self, export_path: str) -> bool:
        """导出配置"""
        try:
            export_data = {
                "system_config": asdict(self.system_config),
                "send_settings": asdict(self.send_settings),
                "export_time": datetime.now().isoformat(),
            }

            with open(export_path, "w", encoding="utf-8") as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            logger.info(f"配置导出成功: {export_path}")
            return True
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False

    def import_config(self, import_path: str) -> bool:
        """导入配置"""
        try:
            with open(import_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            if "system_config" in data:
                self.system_config = SystemConfig(**data["system_config"])
                self._save_system_config(self.system_config)

            if "send_settings" in data:
                self.send_settings = SendSettings(**data["send_settings"])
                self._save_send_settings(self.send_settings)

            logger.info(f"配置导入成功: {import_path}")
            return True
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False


# 全局配置管理器实例
config_manager = ConfigManager()
