"""
动画连接微信按钮
使用Qt原生动画系统实现原始CSS代码中的所有效果
"""

from PyQt6.QtWidgets import QPushButton
from PyQt6.QtCore import (
    QPropertyAnimation,
    QEasingCurve,
    QTimer,
    pyqtProperty,
    QRect,
    QRectF,
)
from PyQt6.QtGui import (
    QPainter,
    QLinearGradient,
    QRadialGradient,
    QColor,
    QPen,
    QBrush,
    QFont,
    QPainterPath,
    QFontMetrics,
    QTransform,
)
from PyQt6.QtCore import Qt, QPoint, QPointF
import math
from utils.logger import setup_logger

logger = setup_logger(__name__)


class AnimatedConnectButton(QPushButton):
    """动画连接微信按钮 - 使用Qt原生动画系统"""

    def __init__(self, text="连接微信", parent=None):
        super().__init__(text, parent)

        # 动画属性
        self._gradient_position = 0.0  # 渐变位置 0.0-1.0
        self._scale_factor = 1.0  # 缩放因子
        self._glow_intensity = 0.0  # 发光强度
        self._text_glow_intensity = 0.0  # 文字发光强度
        self._border_glow_intensity = 0.0  # 边框发光强度
        self._inner_shadow_depth = 0.0  # 内阴影深度
        self._is_pressed = False  # 按下状态

        # 设置基本样式
        self.setMinimumSize(80, 32)  # 保持原有最小尺寸
        self.setFont(QFont("Microsoft YaHei", 9, QFont.Weight.Bold))

        # 创建渐变位置动画 - 精确的5秒循环
        self.gradient_animation = QPropertyAnimation(self, b"gradientPosition")
        self.gradient_animation.setDuration(5000)  # 精确5秒
        self.gradient_animation.setStartValue(0.0)
        self.gradient_animation.setEndValue(1.0)
        self.gradient_animation.setEasingCurve(
            QEasingCurve.Type.Linear
        )  # 线性缓动，匀速流动
        self.gradient_animation.setLoopCount(-1)  # 无限循环

        # 创建缩放动画 - 模拟CSS的scale(0.95)
        self.scale_animation = QPropertyAnimation(self, b"scaleFactor")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        # 创建发光动画
        self.glow_animation = QPropertyAnimation(self, b"glowIntensity")
        self.glow_animation.setDuration(300)
        self.glow_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)

        # 创建文字发光动画
        self.text_glow_animation = QPropertyAnimation(self, b"textGlowIntensity")
        self.text_glow_animation.setDuration(300)
        self.text_glow_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)

        # 创建边框发光动画
        self.border_glow_animation = QPropertyAnimation(self, b"borderGlowIntensity")
        self.border_glow_animation.setDuration(300)
        self.border_glow_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)

        # 创建内阴影动画
        self.inner_shadow_animation = QPropertyAnimation(self, b"innerShadowDepth")
        self.inner_shadow_animation.setDuration(150)
        self.inner_shadow_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        # 连接信号
        self.pressed.connect(self._on_pressed)
        self.released.connect(self._on_released)
        self.enterEvent = self._on_enter
        self.leaveEvent = self._on_leave

        # 启动渐变动画
        self.start_gradient_animation()

        logger.info("完整动画连接微信按钮初始化完成")

    @property
    def gradient_position(self) -> float:
        """渐变位置属性"""
        return self._gradient_position

    @gradient_position.setter
    def gradient_position(self, value: float):
        self._gradient_position = value
        self.update()  # 触发重绘

    @property
    def scale(self) -> float:
        """缩放因子属性"""
        return self._scale_factor

    @scale.setter
    def scale(self, value: float):
        self._scale_factor = value
        self.update()

    @property
    def glow_intensity(self) -> float:
        """发光强度属性"""
        return self._glow_intensity

    @glow_intensity.setter
    def glow_intensity(self, value: float):
        self._glow_intensity = value
        self.update()

    @property
    def text_glow_intensity(self) -> float:
        """文字发光强度属性"""
        return self._text_glow_intensity

    @text_glow_intensity.setter
    def text_glow_intensity(self, value: float):
        self._text_glow_intensity = value
        self.update()

    @property
    def border_glow_intensity(self) -> float:
        """边框发光强度属性"""
        return self._border_glow_intensity

    @border_glow_intensity.setter
    def border_glow_intensity(self, value: float):
        self._border_glow_intensity = value
        self.update()

    @property
    def inner_shadow_depth(self) -> float:
        """内阴影深度属性"""
        return self._inner_shadow_depth

    @inner_shadow_depth.setter
    def inner_shadow_depth(self, value: float):
        self._inner_shadow_depth = value
        self.update()

    def start_gradient_animation(self):
        """启动渐变动画"""
        self.gradient_animation.start()
        logger.info("连接微信按钮渐变动画已启动")

    def stop_gradient_animation(self):
        """停止渐变动画"""
        self.gradient_animation.stop()
        logger.info("连接微信按钮渐变动画已停止")

    def _on_pressed(self):
        """按下时的完整动画效果"""
        self._is_pressed = True

        # 缩放动画 - 模拟CSS的scale(0.95)
        self.scale_animation.stop()
        self.scale_animation.setStartValue(self._scale_factor)
        self.scale_animation.setEndValue(0.95)
        self.scale_animation.start()

        # 内阴影动画 - 模拟按下的深度感
        self.inner_shadow_animation.stop()
        self.inner_shadow_animation.setStartValue(self._inner_shadow_depth)
        self.inner_shadow_animation.setEndValue(1.0)
        self.inner_shadow_animation.start()

        # 文字发光减弱 - 模拟按下时的内陷效果
        self.text_glow_animation.stop()
        self.text_glow_animation.setStartValue(self._text_glow_intensity)
        self.text_glow_animation.setEndValue(0.3)
        self.text_glow_animation.start()

    def _on_released(self):
        """释放时恢复所有效果"""
        self._is_pressed = False

        # 恢复缩放
        self.scale_animation.stop()
        self.scale_animation.setStartValue(self._scale_factor)
        self.scale_animation.setEndValue(1.0)
        self.scale_animation.start()

        # 恢复内阴影
        self.inner_shadow_animation.stop()
        self.inner_shadow_animation.setStartValue(self._inner_shadow_depth)
        self.inner_shadow_animation.setEndValue(0.0)
        self.inner_shadow_animation.start()

        # 恢复文字发光
        self.text_glow_animation.stop()
        self.text_glow_animation.setStartValue(self._text_glow_intensity)
        self.text_glow_animation.setEndValue(self._glow_intensity)  # 根据悬停状态决定
        self.text_glow_animation.start()

    def _on_enter(self, event):
        """鼠标进入时的完整发光动画"""
        # 整体发光
        self.glow_animation.stop()
        self.glow_animation.setStartValue(self._glow_intensity)
        self.glow_animation.setEndValue(1.0)
        self.glow_animation.start()

        # 文字发光
        self.text_glow_animation.stop()
        self.text_glow_animation.setStartValue(self._text_glow_intensity)
        self.text_glow_animation.setEndValue(1.0)
        self.text_glow_animation.start()

        # 边框发光
        self.border_glow_animation.stop()
        self.border_glow_animation.setStartValue(self._border_glow_intensity)
        self.border_glow_animation.setEndValue(1.0)
        self.border_glow_animation.start()

        super().enterEvent(event)

    def _on_leave(self, event):
        """鼠标离开时取消所有发光效果"""
        # 取消整体发光
        self.glow_animation.stop()
        self.glow_animation.setStartValue(self._glow_intensity)
        self.glow_animation.setEndValue(0.0)
        self.glow_animation.start()

        # 取消文字发光
        self.text_glow_animation.stop()
        self.text_glow_animation.setStartValue(self._text_glow_intensity)
        self.text_glow_animation.setEndValue(0.0)
        self.text_glow_animation.start()

        # 取消边框发光
        self.border_glow_animation.stop()
        self.border_glow_animation.setStartValue(self._border_glow_intensity)
        self.border_glow_animation.setEndValue(0.0)
        self.border_glow_animation.start()

        super().leaveEvent(event)

    def paintEvent(self, event):
        """自定义绘制 - 完整实现原始CSS的所有视觉效果"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.TextAntialiasing)

        # 获取按钮矩形
        rect = self.rect()

        # 应用缩放变换
        if self._scale_factor != 1.0:
            center = rect.center()
            painter.translate(center)
            painter.scale(self._scale_factor, self._scale_factor)
            painter.translate(-center)

        # 1. 绘制外发光效果（border-glow）
        if self._border_glow_intensity > 0:
            self._draw_outer_glow(painter, rect)

        # 2. 绘制双重背景系统
        self._draw_dual_background(painter, rect)

        # 3. 绘制内阴影效果
        if self._inner_shadow_depth > 0:
            self._draw_inner_shadow(painter, rect)

        # 4. 绘制文字（包含发光效果）
        self._draw_text_with_glow(painter, rect)

        painter.end()

    def _draw_outer_glow(self, painter, rect):
        """绘制外发光效果 - 模拟CSS的box-shadow外发光"""
        glow_radius = int(8 * self._border_glow_intensity)
        glow_rect = rect.adjusted(-glow_radius, -glow_radius, glow_radius, glow_radius)

        # 创建径向渐变发光
        glow_gradient = QRadialGradient(rect.center(), glow_radius + 25)
        glow_color = QColor(255, 255, 255, int(100 * self._border_glow_intensity))
        glow_gradient.setColorAt(0.7, glow_color)
        glow_gradient.setColorAt(1.0, QColor(255, 255, 255, 0))

        painter.setBrush(QBrush(glow_gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(glow_rect, 30, 30)

    def _draw_dual_background(self, painter, rect):
        """绘制双重背景系统 - 完整实现CSS的双重背景"""
        # 计算动态渐变位置（精确模拟background-position动画）
        # 使用正弦波函数创造更平滑的流动效果
        wave_pos = math.sin(self._gradient_position * 2 * math.pi) * 0.5 + 0.5

        # 1. 绘制边框渐变背景（border-box）
        border_gradient = QLinearGradient(0, 0, rect.width(), rect.height())
        border_gradient.setColorAt(0.0, QColor(255, 255, 255, 0))  # transparent
        border_gradient.setColorAt(0.35 + wave_pos * 0.3, QColor("#e81cff"))
        border_gradient.setColorAt(0.65 + wave_pos * 0.3, QColor("#40c9ff"))
        border_gradient.setColorAt(1.0, QColor(255, 255, 255, 0))  # transparent

        painter.setBrush(QBrush(border_gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(rect, 25, 25)

        # 2. 绘制内容背景（padding-box）
        inner_rect = rect.adjusted(2, 2, -2, -2)  # 模拟border宽度
        content_gradient = QLinearGradient(
            0, 0, inner_rect.width(), inner_rect.height()
        )

        # 基础深色背景
        base_color1 = QColor("#2a2a3e")
        base_color2 = QColor("#1a1a2e")

        # 根据悬停状态调整亮度
        if self._glow_intensity > 0:
            brightness = int(20 * self._glow_intensity)
            base_color1 = QColor(
                min(255, base_color1.red() + brightness),
                min(255, base_color1.green() + brightness),
                min(255, base_color1.blue() + brightness),
            )
            base_color2 = QColor(
                min(255, base_color2.red() + brightness),
                min(255, base_color2.green() + brightness),
                min(255, base_color2.blue() + brightness),
            )

        content_gradient.setColorAt(0.0, base_color1)
        content_gradient.setColorAt(1.0, base_color2)

        painter.setBrush(QBrush(content_gradient))
        painter.drawRoundedRect(inner_rect, 23, 23)

    def _draw_inner_shadow(self, painter, rect):
        """绘制内阴影效果 - 模拟CSS的box-shadow: inset"""
        shadow_depth = int(12 * self._inner_shadow_depth)
        shadow_rect = rect.adjusted(2, 2, -2, -2)

        # 创建内阴影渐变
        shadow_gradient = QLinearGradient(
            0, shadow_rect.top(), 0, shadow_rect.top() + shadow_depth
        )
        shadow_color = QColor(0, 0, 0, int(150 * self._inner_shadow_depth))
        shadow_gradient.setColorAt(0.0, shadow_color)
        shadow_gradient.setColorAt(1.0, QColor(0, 0, 0, 0))

        painter.setBrush(QBrush(shadow_gradient))
        painter.setPen(Qt.PenStyle.NoPen)

        # 使用裁剪路径绘制内阴影
        clip_path = QPainterPath()
        clip_path.addRoundedRect(QRectF(shadow_rect), 23, 23)
        painter.setClipPath(clip_path)
        painter.drawRect(shadow_rect.adjusted(0, -2, 0, shadow_depth))
        painter.setClipping(False)

    def _draw_text_with_glow(self, painter, rect):
        """绘制带发光效果的文字 - 模拟CSS的text-shadow"""
        text = self.text()
        font = self.font()
        painter.setFont(font)

        # 计算文字位置
        font_metrics = QFontMetrics(font)
        text_rect = font_metrics.boundingRect(text)
        text_x = rect.center().x() - text_rect.width() // 2
        text_y = rect.center().y() + text_rect.height() // 4

        # 1. 绘制文字发光效果（text-shadow）
        if self._text_glow_intensity > 0:
            glow_color = QColor(255, 255, 255, int(200 * self._text_glow_intensity))

            # 多层发光效果
            for offset in range(1, 4):
                painter.setPen(
                    QPen(
                        QColor(
                            255, 255, 255, int(50 * self._text_glow_intensity / offset)
                        )
                    )
                )
                for dx in [-offset, 0, offset]:
                    for dy in [-offset, 0, offset]:
                        if dx != 0 or dy != 0:
                            painter.drawText(text_x + dx, text_y + dy, text)

        # 2. 绘制主文字
        if self._is_pressed:
            # 按下时的文字颜色（内陷效果）
            text_color = QColor("#c8cdd4")
        elif self._text_glow_intensity > 0:
            # 悬停时的文字颜色（发光效果）
            text_color = QColor("#ffffff")
        else:
            # 正常状态的文字颜色
            text_color = QColor("#ffe7ff")

        painter.setPen(QPen(text_color))
        painter.drawText(text_x, text_y, text)

    def sizeHint(self):
        """返回建议大小"""
        return self.minimumSize()

    def __del__(self):
        """析构函数 - 停止所有动画"""
        animations = [
            "gradient_animation",
            "scale_animation",
            "glow_animation",
            "text_glow_animation",
            "border_glow_animation",
            "inner_shadow_animation",
        ]

        for anim_name in animations:
            if hasattr(self, anim_name):
                getattr(self, anim_name).stop()
