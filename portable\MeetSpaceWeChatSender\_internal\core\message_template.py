"""
消息模板模块

处理消息模板的创建、编辑、保存和变量替换。
"""

import json
import os
import re
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import random

from config.settings import TEMPLATES_DIR, DEFAULT_TEMPLATE_VARIABLES, EMOJI_LIST
from utils.logger import setup_logger

logger = setup_logger("message_template")


@dataclass
class Template:
    """消息模板"""

    id: str
    name: str
    type: str  # rich_text, text, image, file
    content: Any  # 可以是字符串或字典（富文本）
    file_path: str = ""
    variables: Dict[str, str] = None
    description: str = ""
    created_at: str = ""
    updated_at: str = ""

    def __post_init__(self):
        if self.variables is None:
            self.variables = {}
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()


class MessageTemplate:
    """消息模板管理器"""

    def __init__(self):
        self.templates: Dict[str, Template] = {}
        self.templates_file = TEMPLATES_DIR / "templates.json"
        self.load_templates()

    def load_templates(self) -> None:
        """从文件加载模板"""
        try:
            if self.templates_file.exists():
                with open(self.templates_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    for template_data in data:
                        template = Template(**template_data)
                        self.templates[template.id] = template
                logger.info(f"加载了 {len(self.templates)} 个模板")
            else:
                # 创建默认模板
                self.create_default_templates()
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            self.create_default_templates()

    def save_templates(self) -> bool:
        """保存模板到文件"""
        try:
            # 确保目录存在
            self.templates_file.parent.mkdir(parents=True, exist_ok=True)

            templates_data = [asdict(template) for template in self.templates.values()]
            with open(self.templates_file, "w", encoding="utf-8") as f:
                json.dump(templates_data, f, ensure_ascii=False, indent=2)

            logger.info(f"保存了 {len(self.templates)} 个模板")
            return True
        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            return False

    def create_default_templates(self) -> None:
        """创建默认模板"""
        default_templates = [
            Template(
                id="greeting",
                name="问候模板",
                type="text",
                content="你好 {{name}}，今天是 {{current_date}}，祝你有美好的一天！{{random_emoji}}",
            ),
            Template(
                id="notification",
                name="通知模板",
                type="text",
                content="亲爱的 {{name}}，我们有重要通知需要告知您，请查收。发送时间：{{current_time}}",
            ),
            Template(
                id="invitation",
                name="邀请模板",
                type="text",
                content="{{name}}，诚挚邀请您参加我们的活动，期待您的到来！{{random_emoji}}",
            ),
        ]

        for template in default_templates:
            self.templates[template.id] = template

        self.save_templates()

    def add_template(self, template: Template) -> bool:
        """添加模板"""
        try:
            template.updated_at = datetime.now().isoformat()
            self.templates[template.id] = template
            self.save_templates()
            logger.info(f"添加模板: {template.name}")
            return True
        except Exception as e:
            logger.error(f"添加模板失败: {e}")
            return False

    def update_template(self, template: Template) -> bool:
        """更新模板"""
        try:
            if template.id not in self.templates:
                return False

            template.updated_at = datetime.now().isoformat()
            self.templates[template.id] = template
            self.save_templates()
            logger.info(f"更新模板: {template.name}")
            return True
        except Exception as e:
            logger.error(f"更新模板失败: {e}")
            return False

    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        try:
            if template_id in self.templates:
                template_name = self.templates[template_id].name
                del self.templates[template_id]
                self.save_templates()
                logger.info(f"删除模板: {template_name}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除模板失败: {e}")
            return False

    def save_template(self, name: str, template_data: Dict[str, Any]) -> bool:
        """
        保存模板（新的接口，支持富文本）

        Args:
            name: 模板名称
            template_data: 模板数据字典
                - type: 模板类型 (rich_text, text, image, file)
                - content: 模板内容
                - file_path: 文件路径（可选）
                - description: 模板描述（可选）

        Returns:
            是否保存成功
        """
        try:
            # 生成模板ID
            template_id = name.lower().replace(" ", "_").replace("-", "_")

            # 创建模板对象
            template = Template(
                id=template_id,
                name=name,
                type=template_data.get("type", "text"),
                content=template_data.get("content", ""),
                file_path=template_data.get("file_path", ""),
                description=template_data.get("description", ""),
                updated_at=datetime.now().isoformat(),
            )

            # 如果是更新现有模板，保留创建时间
            if template_id in self.templates:
                template.created_at = self.templates[template_id].created_at

            self.templates[template_id] = template
            self.save_templates()

            logger.info(f"保存模板: {name} (类型: {template.type})")
            return True

        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            return False

    def load_template(self, name: str) -> Optional[Dict[str, Any]]:
        """
        加载模板（新的接口，支持富文本）

        Args:
            name: 模板名称

        Returns:
            模板数据字典，如果不存在则返回None
        """
        try:
            # 查找模板
            template = None
            for t in self.templates.values():
                if t.name == name:
                    template = t
                    break

            if not template:
                logger.warning(f"模板不存在: {name}")
                return None

            return {
                "type": template.type,
                "content": template.content,
                "file_path": template.file_path,
                "description": template.description,
                "created_at": template.created_at,
                "updated_at": template.updated_at,
            }

        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            return None

    def delete_template(self, name: str) -> bool:
        """
        删除模板（新的接口）

        Args:
            name: 模板名称

        Returns:
            是否删除成功
        """
        try:
            # 查找并删除模板
            template_id = None
            for tid, template in self.templates.items():
                if template.name == name:
                    template_id = tid
                    break

            if template_id:
                del self.templates[template_id]
                self.save_templates()
                logger.info(f"删除模板: {name}")
                return True
            else:
                logger.warning(f"模板不存在: {name}")
                return False

        except Exception as e:
            logger.error(f"删除模板失败: {e}")
            return False

    def get_template_list(self) -> List[str]:
        """
        获取模板名称列表

        Returns:
            模板名称列表
        """
        try:
            return [template.name for template in self.templates.values()]
        except Exception as e:
            logger.error(f"获取模板列表失败: {e}")
            return []

    def get_template(self, template_id: str) -> Optional[Template]:
        """获取模板"""
        return self.templates.get(template_id)

    def get_all_templates(self) -> List[Template]:
        """获取所有模板"""
        return list(self.templates.values())

    def render_template(
        self, template_id: str, variables: Dict[str, Any] = None
    ) -> Optional[str]:
        """
        渲染模板

        Args:
            template_id: 模板ID
            variables: 变量字典

        Returns:
            渲染后的内容
        """
        template = self.get_template(template_id)
        if not template:
            return None

        return self.render_content(template.content, variables)

    def render_content(self, content: str, variables: Dict[str, Any] = None) -> str:
        """
        渲染内容

        Args:
            content: 模板内容
            variables: 变量字典

        Returns:
            渲染后的内容
        """
        if variables is None:
            variables = {}

        # 合并默认变量
        all_variables = {**DEFAULT_TEMPLATE_VARIABLES, **variables}

        # 添加动态变量
        all_variables.update(self._get_dynamic_variables())

        # 替换变量
        return self._replace_variables(content, all_variables)

    def _get_dynamic_variables(self) -> Dict[str, str]:
        """获取动态变量"""
        return {
            "current_time": datetime.now().strftime("%H:%M:%S"),
            "current_date": datetime.now().strftime("%Y-%m-%d"),
            "random_emoji": random.choice(EMOJI_LIST),
        }

    def _replace_variables(self, content: str, variables: Dict[str, Any]) -> str:
        """替换变量"""
        rendered_content = content
        for var_name, var_value in variables.items():
            pattern = r"\{\{\s*" + re.escape(var_name) + r"\s*\}\}"
            rendered_content = re.sub(pattern, str(var_value), rendered_content)

        return rendered_content

    def extract_variables(self, content: str) -> List[str]:
        """
        提取模板中的变量

        Args:
            content: 模板内容

        Returns:
            变量名列表
        """
        pattern = r"\{\{\s*(\w+)\s*\}\}"
        variables = re.findall(pattern, content)
        return list(set(variables))  # 去重
