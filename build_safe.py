#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的项目构建脚本 - 解决编码问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def setup_environment():
    """设置构建环境"""
    print("🔧 设置构建环境...")
    
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    print("  ✅ 编码环境已设置")

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"  ✅ 已清理: {dir_name}")

def check_problematic_files():
    """检查可能有编码问题的文件"""
    print("🔍 检查文件编码...")
    
    problematic_files = []
    
    # 检查Python文件
    for py_file in Path(".").rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                f.read()
        except UnicodeDecodeError:
            try:
                with open(py_file, 'r', encoding='gbk') as f:
                    content = f.read()
                # 转换为UTF-8
                with open(py_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  🔄 已转换编码: {py_file}")
            except:
                problematic_files.append(str(py_file))
    
    if problematic_files:
        print(f"  ⚠️  发现编码问题文件: {problematic_files}")
    else:
        print("  ✅ 文件编码检查通过")
    
    return len(problematic_files) == 0

def build_with_error_handling():
    """带错误处理的构建"""
    print("🔨 开始安全构建...")
    
    # 基础命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--name=MeetSpaceWeChatSender",
        "--clean",
        "--noconfirm",
    ]
    
    # 添加图标（如果存在）
    icon_path = "resources/icons/app_icon.ico"
    if Path(icon_path).exists():
        cmd.extend(["--icon", icon_path])
    
    # 添加关键的隐藏导入
    hidden_imports = [
        "PyQt6.QtCore", "PyQt6.QtGui", "PyQt6.QtWidgets",
        "config", "core", "ui", "utils",
        "runtime_config_generator"
    ]
    
    for module in hidden_imports:
        cmd.extend(["--hidden-import", module])
    
    # 排除问题模块
    excludes = ["tkinter", "matplotlib", "numpy.testing"]
    for module in excludes:
        cmd.extend(["--exclude-module", module])
    
    # 主程序
    cmd.append("main.py")
    
    print(f"📋 执行命令: pyinstaller [参数] main.py")
    
    try:
        # 使用安全的环境运行
        env = os.environ.copy()
        
        result = subprocess.run(
            cmd, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8',
            errors='replace',  # 替换无法解码的字符
            env=env,
            timeout=600  # 10分钟超时
        )
        
        print("✅ 构建完成")
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败，返回码: {e.returncode}")
        
        # 安全地显示错误信息
        if e.stdout:
            stdout = e.stdout.replace('\x00', '').strip()
            if stdout:
                print("标准输出:")
                print(stdout[-1000:])  # 最后1000字符
        
        if e.stderr:
            stderr = e.stderr.replace('\x00', '').strip()
            if stderr:
                print("错误输出:")
                print(stderr[-1000:])  # 最后1000字符
        
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def verify_output():
    """验证输出文件"""
    print("🔍 验证输出文件...")
    
    exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
    
    if not exe_file.exists():
        print("❌ 输出文件不存在")
        return False
    
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"✅ 输出文件: {exe_file}")
    print(f"📊 文件大小: {size_mb:.1f} MB")
    
    # 简单测试文件是否可执行
    if size_mb < 10:
        print("⚠️  文件大小异常小，可能构建不完整")
        return False
    
    return True

def main():
    """主函数"""
    print("🛡️  Meet space 微信群发助手 - 安全构建")
    print("=" * 60)
    
    try:
        # 1. 设置环境
        setup_environment()
        
        # 2. 检查文件编码
        if not check_problematic_files():
            print("⚠️  存在编码问题，但继续尝试构建...")
        
        # 3. 清理
        clean_build()
        
        # 4. 构建
        if not build_with_error_handling():
            print("❌ 构建失败")
            return False
        
        # 5. 验证
        if not verify_output():
            print("❌ 输出验证失败")
            return False
        
        print("\n🎉 构建成功完成!")
        print("📁 输出: dist/MeetSpaceWeChatSender.exe")
        print("\n✨ 这是您完整项目的单文件版本")
        print("  - 包含所有功能模块")
        print("  - 运行时自动生成配置")
        print("  - 解决了编码问题")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  构建被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 构建过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
