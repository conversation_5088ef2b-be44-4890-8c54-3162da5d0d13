# Meet space 微信群发助手 - 依赖包列表
# Copyright © 2024 Meet space 会客创意空间

# ==================== 核心依赖 ====================

# GUI框架 - PyQt6现代界面框架
PyQt6>=6.4.0,<7.0.0

# 微信接口 - WeChatFerry微信自动化库
wcferry>=39.0.0

# ==================== 数据处理 ====================

# 数据分析和处理
pandas>=1.5.0,<3.0.0
openpyxl>=3.0.0,<4.0.0

# 配置文件处理
pyyaml>=6.0,<7.0.0

# 时间处理
python-dateutil>=2.8.0,<3.0.0

# ==================== 网络和通信 ====================

# HTTP请求库
requests>=2.28.0,<3.0.0
aiohttp>=3.8.0,<4.0.0

# ==================== 图像和媒体 ====================

# 图像处理库
Pillow>=9.0.0,<11.0.0

# ==================== 系统工具 ====================

# 系统进程和性能监控
psutil>=5.9.0,<6.0.0

# ==================== 开发工具（可选） ====================

# 代码格式化
black>=22.0.0,<25.0.0

# 代码检查
flake8>=5.0.0,<8.0.0

# 测试框架
pytest>=7.0.0,<9.0.0
pytest-qt>=4.0.0,<5.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.0.0,<6.0.0

# 类型检查
mypy>=1.0.0,<2.0.0

# ==================== 安装说明 ====================
#
# 基础安装（仅核心功能）：
# pip install PyQt6 wcferry pandas pyyaml python-dateutil requests Pillow psutil
#
# 完整安装（包含开发工具）：
# pip install -r requirements.txt
#
# 注意事项：
# 1. wcferry需要微信PC版支持
# 2. PyQt6需要Python 3.8+
# 3. 建议使用虚拟环境安装
