"""
定时循环发送组件

从原有消息发送页面提取的定时循环发送功能。
"""

from datetime import datetime

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QCheckBox,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QSpinBox,
    QTimeEdit,
    QVBoxLayout,
    QWidget,
)

from utils.logger import setup_logger

logger = setup_logger("timing_cycle_widget")


class TimingCycleWidget(QGroupBox):
    """定时循环发送组件"""

    # 信号定义
    timing_enabled_changed = pyqtSignal(bool)  # 定时功能启用状态改变
    config_changed = pyqtSignal(dict)  # 配置改变

    def __init__(self, title="定时循环发送", parent=None):
        super().__init__(title, parent)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 启用定时发送
        self.enable_timing_check = QCheckBox("启用定时循环发送")
        layout.addWidget(self.enable_timing_check)

        # 定时设置容器
        self.timing_options_widget = QWidget()
        timing_options_layout = QVBoxLayout(self.timing_options_widget)

        # 星期选择
        weekday_layout = QVBoxLayout()
        weekday_layout.addWidget(QLabel("执行星期:"))

        weekday_h_layout = QHBoxLayout()
        self.weekday_checks = []
        weekdays = ["一", "二", "三", "四", "五", "六", "日"]
        for i, day in enumerate(weekdays):
            check = QCheckBox(f"周{day}")
            if i < 5:  # 默认选中工作日
                check.setChecked(True)
            self.weekday_checks.append(check)
            weekday_h_layout.addWidget(check)

        weekday_layout.addLayout(weekday_h_layout)
        timing_options_layout.addLayout(weekday_layout)

        layout.addWidget(self.timing_options_widget)

        # 默认禁用定时选项
        self.timing_options_widget.setEnabled(False)

    def connect_signals(self):
        """连接信号"""
        self.enable_timing_check.stateChanged.connect(self.on_timing_enabled_changed)

        # 连接星期选择信号
        for check in self.weekday_checks:
            check.stateChanged.connect(self.on_config_changed)

    def on_timing_enabled_changed(self, state):
        """定时功能启用状态改变"""
        enabled = state == Qt.CheckState.Checked.value
        self.timing_options_widget.setEnabled(enabled)
        self.timing_enabled_changed.emit(enabled)

        if enabled:
            logger.info("启用定时循环发送模式")
        else:
            logger.info("禁用定时循环发送模式")

    def on_config_changed(self):
        """配置改变"""
        if self.enable_timing_check.isChecked():
            config = self.get_timing_config()
            self.config_changed.emit(config)

    def is_timing_enabled(self) -> bool:
        """是否启用定时功能"""
        return self.enable_timing_check.isChecked()

    def set_timing_enabled(self, enabled: bool):
        """设置定时功能启用状态"""
        self.enable_timing_check.setChecked(enabled)

    def get_timing_config(self) -> dict:
        """获取定时配置"""
        # 获取选中的星期
        selected_weekdays = []
        for i, check in enumerate(self.weekday_checks):
            if check.isChecked():
                selected_weekdays.append(i)

        config = {
            "selected_weekdays": selected_weekdays,
        }

        return config

    def set_timing_config(self, config: dict):
        """设置定时配置"""
        try:
            # 设置选中的星期
            if "selected_weekdays" in config:
                selected_weekdays = config["selected_weekdays"]
                for i, check in enumerate(self.weekday_checks):
                    check.setChecked(i in selected_weekdays)

            logger.info(f"设置定时配置: {config}")

        except Exception as e:
            logger.error(f"设置定时配置失败: {e}")

    def get_timing_info_text(self) -> str:
        """获取定时信息文本"""
        if not self.is_timing_enabled():
            return ""

        config = self.get_timing_config()

        # 星期名称
        weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        selected_weekday_names = [weekday_names[i] for i in config["selected_weekdays"]]

        timing_info = f"定时设置:\n" f"• 执行星期: {', '.join(selected_weekday_names)}"

        return timing_info

    def validate_config(self) -> tuple:
        """验证配置"""
        if not self.is_timing_enabled():
            return True, ""

        config = self.get_timing_config()

        # 检查是否选择了星期
        if not config["selected_weekdays"]:
            return False, "请至少选择一个执行星期"

        return True, ""

    def reset_to_defaults(self):
        """重置为默认值"""
        self.enable_timing_check.setChecked(False)

        # 默认选中工作日
        for i, check in enumerate(self.weekday_checks):
            check.setChecked(i < 5)

        logger.info("重置定时循环配置为默认值")

    def get_summary(self) -> str:
        """获取配置摘要"""
        if not self.is_timing_enabled():
            return "定时循环: 未启用"

        config = self.get_timing_config()
        weekday_count = len(config["selected_weekdays"])

        return f"定时循环: {weekday_count}个工作日"
