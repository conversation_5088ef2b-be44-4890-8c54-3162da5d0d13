#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UAC权限注入修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_admin_privileges():
    """测试管理员权限"""
    print("🔐 测试管理员权限...")
    
    try:
        from utils.simple_admin import is_admin
        
        current_admin = is_admin()
        print(f"当前权限状态: {'✅ 管理员' if current_admin else '❌ 普通用户'}")
        
        if not current_admin:
            print("⚠️  当前没有管理员权限")
            print("建议：以管理员身份运行此测试")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 权限检查失败: {e}")
        return False

def test_injector_with_uac():
    """测试带UAC的注入器"""
    print("\n💉 测试带UAC的注入器...")
    
    try:
        from core.injector_tool import InjectorTool
        
        # 创建注入器（启用自动权限提升）
        injector = InjectorTool(auto_elevate=True)
        
        print(f"注入器路径: {injector.injector_path}")
        print(f"DLL路径: {injector.dll_path}")
        print(f"自动权限提升: {'✅ 启用' if injector.auto_elevate else '❌ 禁用'}")
        
        # 检查文件
        if not injector.injector_path.exists():
            print(f"❌ 注入器不存在: {injector.injector_path}")
            return False
        
        if not injector.dll_path.exists():
            print(f"❌ DLL不存在: {injector.dll_path}")
            return False
        
        print("✅ 注入器和DLL文件都存在")
        
        # 查找微信进程
        wechat_process = injector.find_wechat_process()
        if not wechat_process:
            print("❌ 未找到微信进程，请先启动微信")
            return False
        
        print(f"✅ 找到微信进程: {wechat_process.info['name']} (PID: {wechat_process.pid})")
        
        # 尝试注入
        print("\n开始注入（使用UAC权限提升）...")
        success, message = injector.inject_dll()
        
        if success:
            print(f"✅ 注入成功: {message}")
            
            # 验证API服务
            if injector.check_api_service():
                print("✅ API服务正常响应")
            else:
                print("⚠️  API服务未响应，但注入可能成功")
            
            return True
        else:
            print(f"❌ 注入失败: {message}")
            
            # 分析失败原因
            if "Could not create thread in remote process" in message:
                print("\n🔧 UAC权限分析:")
                print("1. 虽然程序有管理员权限，但注入器可能需要更高权限")
                print("2. 新的UAC权限提升机制应该解决这个问题")
                print("3. 如果仍然失败，可能是微信版本兼容性问题")
            elif "用户取消" in message:
                print("\n✅ UAC对话框正常工作，用户取消了权限提升")
            elif "权限提升成功" in message:
                print("\n✅ UAC权限提升成功，程序将重新启动")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_shellexecute_method():
    """测试ShellExecute方法"""
    print("\n🔧 测试ShellExecute权限提升方法...")
    
    try:
        import ctypes
        
        # 测试ShellExecute是否可用
        try:
            shell32 = ctypes.windll.shell32
            print("✅ ShellExecute API可用")
        except Exception as e:
            print(f"❌ ShellExecute API不可用: {e}")
            return False
        
        # 测试权限检查
        from utils.simple_admin import is_admin
        if is_admin():
            print("✅ 当前已有管理员权限，ShellExecute将直接执行")
        else:
            print("⚠️  当前无管理员权限，ShellExecute将请求权限提升")
        
        return True
        
    except Exception as e:
        print(f"❌ ShellExecute测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - UAC权限注入测试")
    print("=" * 60)
    print("测试修复后的UAC权限提升功能")
    print("=" * 60)
    
    tests = [
        ("管理员权限检查", test_admin_privileges),
        ("ShellExecute方法测试", test_shellexecute_method),
        ("UAC注入器测试", test_injector_with_uac)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 UAC权限测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 UAC权限修复成功！")
        print("\n✨ 修复内容:")
        print("  🔐 改进了权限检查逻辑")
        print("  🚀 使用ShellExecute以管理员权限运行注入器")
        print("  💬 添加了用户友好的权限提升对话框")
        print("  🛡️  增强了权限提升错误处理")
        
        print("\n📋 现在注入应该可以正常工作了！")
    else:
        print("\n⚠️  部分测试失败")
        
        if passed >= 2:
            print("✅ UAC机制正常，如果注入仍失败可能是微信兼容性问题")
        else:
            print("❌ UAC机制有问题，需要进一步检查")
        
        print("\n🔧 建议:")
        print("1. 确保以管理员身份运行程序")
        print("2. 检查Windows UAC设置")
        print("3. 临时关闭杀毒软件")
        print("4. 重启微信后重试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
