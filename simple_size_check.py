#!/usr/bin/env python3
"""
简化的尺寸检查工具
检查是否移除了所有影响尺寸的CSS属性
"""

import sys
import os
from pathlib import Path

def check_size_properties():
    """检查尺寸相关属性"""
    print("🔍 检查主题文件中的尺寸属性...")
    
    theme_file = Path(__file__).parent / "ui" / "modern_theme_manager.py"
    
    if not theme_file.exists():
        print("❌ 主题文件不存在")
        return False
    
    try:
        content = theme_file.read_text(encoding='utf-8')
        
        # 检查不应该存在的尺寸属性
        size_checks = {
            "width: 16px": content.count("width: 16px"),
            "height: 16px": content.count("height: 16px"),
            "min-width: 150px": content.count("min-width: 150px"),
            "min-width: 180px": content.count("min-width: 180px"),
            "min-width: 80px": content.count("min-width: 80px"),
            "min-width: 120px": content.count("min-width: 120px"),
            "padding: 4px 8px": content.count("padding: 4px 8px"),
            "padding: 2px 4px": content.count("padding: 2px 4px"),
            "border-radius: 3px": content.count("border-radius: 3px"),
            "border-radius: 8px": content.count("border-radius: 8px"),
        }
        
        print("\n📊 尺寸属性检查结果:")
        all_clean = True
        for prop, count in size_checks.items():
            if count == 0:
                print(f"  ✅ {prop}: 已移除")
            else:
                print(f"  ❌ {prop}: 发现{count}个")
                all_clean = False
        
        # 检查颜色属性是否保留
        color_checks = {
            "background-color:": content.count("background-color:"),
            "color:": content.count("color:"),
            "background: qlineargradient": content.count("background: qlineargradient"),
            "border: 1px solid": content.count("border: 1px solid"),  # border颜色也是颜色属性
        }
        
        print("\n🎨 颜色属性检查结果:")
        for prop, count in color_checks.items():
            if count > 0:
                print(f"  ✅ {prop}: {count}个 (保留)")
            else:
                print(f"  ⚠️  {prop}: 0个")
        
        if all_clean:
            print("\n🎉 所有尺寸属性已成功移除！")
            print("📋 修复效果:")
            print("  - 移除了所有width、height设置")
            print("  - 移除了所有min-width、padding设置")
            print("  - 移除了所有border-radius设置")
            print("  - 保留了所有颜色相关属性（包括border颜色）")
            print("  - 所有主题现在使用默认尺寸")
            
            return True
        else:
            print("\n❌ 仍有尺寸属性需要清理")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 简化尺寸检查工具")
    print("=" * 50)
    
    success = check_size_properties()
    
    if success:
        print("\n✅ 检查完成！所有主题现在使用默认尺寸")
        print("\n📋 测试建议:")
        print("1. 启动程序: python main.py")
        print("2. 进入系统设置页面")
        print("3. 在默认主题下观察控件尺寸")
        print("4. 切换到科技主题")
        print("5. 确认控件尺寸与默认主题完全一致")
        print("6. 测试其他主题，确认尺寸一致性")
        
        print("\n🎯 预期效果:")
        print("  - 所有主题下控件尺寸完全相同")
        print("  - 只有颜色和视觉效果不同")
        print("  - 布局和交互体验一致")
        
        return 0
    else:
        print("\n❌ 检查失败，需要进一步清理")
        return 1

if __name__ == "__main__":
    sys.exit(main())
