#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复版本 - 解决临时目录问题
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path

def cleanup_temp_dirs():
    """清理PyInstaller临时目录"""
    print("🧹 清理PyInstaller临时目录...")
    
    temp_dir = Path(tempfile.gettempdir())
    mei_dirs = list(temp_dir.glob("_MEI*"))
    
    cleaned = 0
    for mei_dir in mei_dirs:
        try:
            if mei_dir.exists() and mei_dir.is_dir():
                # 尝试强制删除
                shutil.rmtree(mei_dir, ignore_errors=True)
                if not mei_dir.exists():
                    cleaned += 1
                    print(f"  ✅ 已清理: {mei_dir.name}")
        except Exception as e:
            print(f"  ⚠️  清理失败: {mei_dir.name} - {e}")
    
    if cleaned > 0:
        print(f"  总共清理了 {cleaned} 个临时目录")
    else:
        print("  没有需要清理的临时目录")

def build_final_version():
    """构建最终版本"""
    print("🔧 构建最终修复版本...")
    
    # 清理构建目录
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONUTF8': '1',
        'PYTHONLEGACYWINDOWSSTDIO': '1'
    })
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MeetSpaceWeChatSender_Final",
        "--icon=resources/icons/app_icon.ico",
        
        # 数据文件 - 保持原始结构
        "--add-data=tools;tools",
        "--add-data=wxhelper_files;wxhelper_files", 
        "--add-data=resources;resources",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        "--add-data=README.md;.",
        
        # 核心隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=psutil",
        "--hidden-import=requests",
        "--hidden-import=ctypes",
        "--hidden-import=subprocess",
        "--hidden-import=pandas",
        "--hidden-import=PIL",
        
        # 项目模块
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=ui", 
        "--hidden-import=utils",
        
        # 排除不需要的模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy.testing",
        
        # 构建选项
        "--clean",
        "--noconfirm",
        "--noupx",  # 禁用UPX压缩
        
        # 添加运行时选项来处理临时目录
        "--runtime-tmpdir=.",  # 使用当前目录作为临时目录
        
        "main.py"
    ]
    
    print("📋 开始构建最终版本...")
    print("   - 使用原始注入代码")
    print("   - 修复临时目录问题")
    print("   - 优化资源管理")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env,
            timeout=400
        )
        
        print("✅ 构建完成")
        
        # 检查结果
        exe_file = Path("dist") / "MeetSpaceWeChatSender_Final.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"✅ 输出文件: {exe_file}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            
            # 创建使用说明
            create_usage_guide()
            
            return True
        else:
            print("❌ 未找到输出文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e.returncode}")
        if e.stderr:
            print("错误输出:")
            print(e.stderr[-1000:])
        return False
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def create_usage_guide():
    """创建使用指南"""
    print("📄 创建使用指南...")
    
    guide_content = """# Meet space 微信群发助手 - 最终版本使用指南

## 🚀 快速开始

### 1. 准备工作
- ✅ 确保微信PC版已安装并登录
- ✅ 关闭杀毒软件（可能会误报）
- ✅ 建议以管理员身份运行

### 2. 运行程序
1. 双击 `MeetSpaceWeChatSender_Final.exe`
2. 程序启动后，点击"连接微信"按钮
3. 等待注入完成（可能需要几秒钟）
4. 连接成功后即可使用所有功能

## 🔧 功能特点

### ✨ 完整功能
- 🕐 **定时发送** - 设定时间自动发送消息
- 🔄 **循环发送** - 按间隔重复发送消息
- 👥 **分组管理** - 管理联系人分组
- 🎨 **5套主题** - 多种界面风格
- 🛡️ **风险控制** - 智能发送频率控制
- 📊 **发送监控** - 实时监控发送状态

### 🔧 技术特点
- ✅ 使用原始的、已验证的注入代码
- ✅ 单文件运行，无需安装
- ✅ 自动生成配置文件
- ✅ 完整的错误处理

## 🚨 故障排除

### 连接失败
1. **重启微信** - 关闭微信后重新启动
2. **管理员权限** - 右键"以管理员身份运行"
3. **关闭杀毒** - 临时关闭杀毒软件
4. **检查版本** - 确保微信版本兼容

### 注入失败
1. **检查进程** - 确保微信完全启动
2. **重启程序** - 关闭程序后重新运行
3. **系统重启** - 重启计算机后再试
4. **防火墙** - 检查防火墙设置

### 临时目录警告
- 程序退出时可能显示临时目录清理警告
- 这是正常现象，不影响程序功能
- 可以安全忽略此警告

## 📞 技术支持

### 日志文件位置
- 配置目录: `%APPDATA%/MeetSpaceWeChatSender/`
- 日志目录: `%APPDATA%/MeetSpaceWeChatSender/logs/`

### 常见问题
1. **微信版本兼容性** - 支持主流微信版本
2. **系统要求** - Windows 10/11 64位
3. **网络要求** - 需要本地网络连接（localhost）

## 🎯 使用建议

1. **首次使用** - 建议先测试少量联系人
2. **发送频率** - 遵循平台规则，避免频繁发送
3. **内容质量** - 发送有价值的内容
4. **定期更新** - 关注程序更新

---

**版本**: 1.0.0 Final
**更新日期**: 2025-08-05
**技术支持**: Meet space 会客创意空间
"""
    
    try:
        with open("dist/使用指南.txt", "w", encoding="utf-8") as f:
            f.write(guide_content)
        print("  ✅ 使用指南已创建: dist/使用指南.txt")
    except Exception as e:
        print(f"  ⚠️  创建使用指南失败: {e}")

def main():
    print("🔧 Meet space 微信群发助手 - 最终修复版本")
    print("=" * 70)
    print("解决临时目录问题，使用原始注入代码")
    print("=" * 70)
    
    # 清理临时目录
    cleanup_temp_dirs()
    
    # 构建最终版本
    success = build_final_version()
    
    if success:
        print("\n🎉 最终版本构建成功!")
        print("📁 输出文件:")
        print("  - MeetSpaceWeChatSender_Final.exe (主程序)")
        print("  - 使用指南.txt (详细说明)")
        
        print("\n✨ 修复内容:")
        print("  ✅ 解决临时目录清理问题")
        print("  ✅ 使用原始的注入代码")
        print("  ✅ 优化资源管理")
        print("  ✅ 完整的错误处理")
        
        print("\n📋 现在可以:")
        print("  1. 正常运行程序（无临时目录警告）")
        print("  2. 成功连接和注入微信")
        print("  3. 使用所有群发功能")
        print("  4. 在任何Windows系统上运行")
        
    else:
        print("\n❌ 构建失败!")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
