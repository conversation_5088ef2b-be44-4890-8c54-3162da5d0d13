@echo off
chcp 65001 >nul
title Meet space 微信群发助手

echo.
echo ========================================
echo   Meet space 微信群发助手
echo   便携版启动器
echo ========================================
echo.

cd /d "%~dp0MeetSpaceWeChatSender"

if not exist "MeetSpaceWeChatSender.exe" (
    echo ❌ 错误: 找不到主程序文件
    echo    请确保文件完整性
    pause
    exit /b 1
)

echo ✅ 启动程序...
start "" "MeetSpaceWeChatSender.exe"

echo.
echo 程序已启动，可以关闭此窗口
timeout /t 3 >nul
