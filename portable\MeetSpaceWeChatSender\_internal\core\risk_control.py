"""
风控管理模块

实现发送频率控制、风险检测和安全保护功能。
"""

import random
import time
from collections import defaultdict, deque
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Optional, Tuple

from config.wechat_config import WeChatConfig
from utils.logger import setup_logger

logger = setup_logger("risk_control")


@dataclass
class SendRecord:
    """发送记录"""

    timestamp: float
    wxid: str
    message_type: str
    success: bool


class RiskController:
    """风控控制器"""

    def __init__(self, config: WeChatConfig):
        self.config = config
        self.send_records: deque = deque(maxlen=10000)  # 最多保存10000条记录
        self.hourly_counts: Dict[str, int] = defaultdict(int)  # 每小时发送计数
        self.daily_counts: Dict[str, int] = defaultdict(int)  # 每日发送计数
        self.last_send_time: float = 0
        self.consecutive_failures: int = 0
        self.is_blocked = False
        self.block_until: Optional[float] = None

    def can_send_message(self, wxid: str) -> Tuple[bool, str]:
        """
        检查是否可以发送消息

        Args:
            wxid: 接收者微信ID

        Returns:
            (是否可以发送, 原因)
        """
        if not self.config.enable_risk_control:
            return True, "风控已禁用"

        # 检查是否被阻止
        block_check = self._check_block_status()
        if not block_check[0]:
            return block_check

        # 检查发送间隔
        interval_check = self._check_send_interval()
        if not interval_check[0]:
            return interval_check

        # 检查每小时限制
        hourly_check = self._check_hourly_limit()
        if not hourly_check[0]:
            return hourly_check

        # 检查每日限制
        daily_check = self._check_daily_limit()
        if not daily_check[0]:
            return daily_check

        # 检查连续失败次数
        if self.consecutive_failures >= 5:
            return False, "连续发送失败次数过多，请检查网络或微信状态"

        return True, "可以发送"

    def check_risk_control(self) -> bool:
        """检查风控状态（简化版本，用于定时任务）"""
        try:
            if not self.config.enable_risk_control:
                return True

            # 检查是否被阻止
            if self.is_blocked and self.block_until:
                if time.time() < self.block_until:
                    return False
                else:
                    self.is_blocked = False
                    self.block_until = None

            # 检查连续失败次数
            if self.consecutive_failures >= 5:
                return False

            return True

        except Exception as e:
            logger.error(f"风控检查失败: {e}")
            return True  # 出错时允许发送，避免阻塞

    def _check_block_status(self) -> Tuple[bool, str]:
        """检查阻止状态"""
        if self.is_blocked and self.block_until:
            if time.time() < self.block_until:
                remaining = int(self.block_until - time.time())
                return False, f"发送被阻止，剩余 {remaining} 秒"
            else:
                self.is_blocked = False
                self.block_until = None
        return True, ""

    def _check_send_interval(self) -> Tuple[bool, str]:
        """检查发送间隔"""
        current_time = time.time()
        if self.last_send_time > 0:
            elapsed = current_time - self.last_send_time
            min_interval = self.config.send_interval_min
            if elapsed < min_interval:
                wait_time = min_interval - elapsed
                return False, f"发送间隔不足，需等待 {wait_time:.1f} 秒"
        return True, ""

    def _check_hourly_limit(self) -> Tuple[bool, str]:
        """检查每小时限制"""
        current_hour = datetime.now().strftime("%Y-%m-%d-%H")
        if self.hourly_counts[current_hour] >= self.config.hourly_send_limit:
            return False, f"已达到每小时发送限制 ({self.config.hourly_send_limit})"
        return True, ""

    def _check_daily_limit(self) -> Tuple[bool, str]:
        """检查每日限制"""
        current_date = datetime.now().strftime("%Y-%m-%d")
        if self.daily_counts[current_date] >= self.config.daily_send_limit:
            return False, f"已达到每日发送限制 ({self.config.daily_send_limit})"
        return True, ""

    def get_send_delay(self) -> float:
        """
        获取发送延迟时间

        Returns:
            延迟秒数
        """
        min_delay = self.config.send_interval_min
        max_delay = self.config.send_interval_max

        if self.config.random_delay:
            # 随机延迟
            delay = random.uniform(min_delay, max_delay)
        else:
            # 固定延迟
            delay = min_delay

        # 如果启用人工模拟，增加一些随机性
        if self.config.simulate_human:
            # 添加小幅随机波动
            variation = delay * 0.1  # 10%的波动
            delay += random.uniform(-variation, variation)
            delay = max(delay, 0.5)  # 最小0.5秒

        return delay

    def record_send_attempt(self, wxid: str, message_type: str, success: bool) -> None:
        """
        记录发送尝试

        Args:
            wxid: 接收者微信ID
            message_type: 消息类型
            success: 是否成功
        """
        current_time = time.time()

        # 记录发送记录
        record = SendRecord(
            timestamp=current_time,
            wxid=wxid,
            message_type=message_type,
            success=success,
        )
        self.send_records.append(record)

        # 更新最后发送时间
        self.last_send_time = current_time

        # 更新计数器
        if success:
            current_hour = datetime.now().strftime("%Y-%m-%d-%H")
            current_date = datetime.now().strftime("%Y-%m-%d")
            self.hourly_counts[current_hour] += 1
            self.daily_counts[current_date] += 1
            self.consecutive_failures = 0
            logger.info(f"发送成功记录: {wxid} ({message_type})")
        else:
            self.consecutive_failures += 1
            logger.warning(
                f"发送失败记录: {wxid} ({message_type}), 连续失败 {self.consecutive_failures} 次"
            )

            # 如果连续失败过多，临时阻止发送
            if self.consecutive_failures >= 3:
                block_duration = min(60 * self.consecutive_failures, 300)  # 最多5分钟
                self.is_blocked = True
                self.block_until = current_time + block_duration
                logger.warning(f"连续失败过多，阻止发送 {block_duration} 秒")

    def get_send_statistics(self) -> Dict[str, any]:
        """
        获取发送统计信息

        Returns:
            统计信息字典
        """
        current_time = time.time()
        current_hour = datetime.now().strftime("%Y-%m-%d-%H")
        current_date = datetime.now().strftime("%Y-%m-%d")

        # 统计最近1小时的发送记录
        hour_ago = current_time - 3600
        recent_records = [r for r in self.send_records if r.timestamp > hour_ago]

        # 统计成功率
        if recent_records:
            success_count = sum(1 for r in recent_records if r.success)
            success_rate = success_count / len(recent_records) * 100
        else:
            success_rate = 0

        return {
            "hourly_sent": self.hourly_counts[current_hour],
            "hourly_limit": self.config.hourly_send_limit,
            "daily_sent": self.daily_counts[current_date],
            "daily_limit": self.config.daily_send_limit,
            "recent_success_rate": success_rate,
            "consecutive_failures": self.consecutive_failures,
            "is_blocked": self.is_blocked,
            "block_remaining": (
                max(0, int(self.block_until - current_time)) if self.block_until else 0
            ),
            "total_records": len(self.send_records),
        }

    def reset_daily_counters(self) -> None:
        """重置每日计数器"""
        current_date = datetime.now().strftime("%Y-%m-%d")

        # 清理过期的计数器
        keys_to_remove = []
        for key in self.daily_counts.keys():
            if key != current_date:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.daily_counts[key]

        logger.info("重置每日发送计数器")

    def reset_hourly_counters(self) -> None:
        """重置每小时计数器"""
        current_hour = datetime.now().strftime("%Y-%m-%d-%H")

        # 清理过期的计数器
        keys_to_remove = []
        for key in self.hourly_counts.keys():
            if key != current_hour:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.hourly_counts[key]

        logger.info("重置每小时发送计数器")

    def clear_all_records(self) -> None:
        """清空所有记录"""
        self.send_records.clear()
        self.hourly_counts.clear()
        self.daily_counts.clear()
        self.last_send_time = 0
        self.consecutive_failures = 0
        self.is_blocked = False
        self.block_until = None
        logger.info("清空所有发送记录")

    def update_config(self, config: WeChatConfig) -> None:
        """更新配置"""
        self.config = config
        logger.info("更新风控配置")
