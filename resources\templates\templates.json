[{"id": "notification", "name": "通知模板", "type": "text", "content": "亲爱的 {{name}}，我们有重要通知需要告知您，请查收。发送时间：{{current_time}}", "file_path": "", "variables": {}, "description": "", "created_at": "2025-07-17T08:55:06.952332", "updated_at": "2025-07-17T08:55:06.952335"}, {"id": "invitation", "name": "邀请模板", "type": "text", "content": "{{name}}，诚挚邀请您参加我们的活动，期待您的到来！{{random_emoji}}", "file_path": "", "variables": {}, "description": "", "created_at": "2025-07-17T08:55:06.952338", "updated_at": "2025-07-17T08:55:06.952339"}, {"id": "123", "name": "123", "type": "rich_text", "content": {"name": "", "content": "123", "html": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">123</p>\n<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>", "images": [{"path": "temp_images\\clipboard_image_1753840878_948f09ca.png", "name": "clipboard_image_1753840878_948f09ca.png"}]}, "file_path": "", "variables": {}, "description": "富文本模板 - 混合消息 (文字4字, 图片1张)", "created_at": "2025-07-28T11:36:15.721561", "updated_at": "2025-07-30T10:01:19.686040"}, {"id": "321", "name": "321", "type": "rich_text", "content": {"name": "", "content": "123", "html": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">123</p></body></html>", "images": []}, "file_path": "", "variables": {}, "description": "富文本模板 - 文字消息 (3字)", "created_at": "2025-07-30T10:02:22.717629", "updated_at": "2025-07-30T10:02:22.717607"}, {"id": "444", "name": "444", "type": "rich_text", "content": {"name": "", "content": "123", "html": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">123</p>\n<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>", "images": [{"path": "temp_images\\clipboard_image_1753842743_01d33114.png", "name": "clipboard_image_1753842743_01d33114.png"}]}, "file_path": "", "variables": {}, "description": "富文本模板 - 混合消息 (文字4字, 图片1张)", "created_at": "2025-07-30T10:32:24.634743", "updated_at": "2025-07-30T10:32:24.634728"}, {"id": "5555", "name": "5555", "type": "rich_text", "content": {"name": "", "content": "5555", "html": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">5555</p>\n<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>", "images": [{"path": "temp_images\\clipboard_image_1753843013_720bed56.png", "name": "clipboard_image_1753843013_720bed56.png"}]}, "file_path": "", "variables": {}, "description": "富文本模板 - 混合消息 (文字5字, 图片1张)", "created_at": "2025-07-30T10:36:55.592857", "updated_at": "2025-07-30T10:36:55.592845"}]