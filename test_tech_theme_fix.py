#!/usr/bin/env python3
"""
科技主题修复验证工具
验证科技主题是否使用默认尺寸
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QLabel, QGroupBox, QSpinBox, QComboBox, QPushButton,
                            QCheckBox, QRadioButton)
from PyQt6.QtCore import Qt
from ui.modern_theme_manager import theme_manager
from ui.themed_dialog_base import ThemedDialogBase


class TechThemeTestDialog(ThemedDialogBase):
    """科技主题测试对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("科技主题尺寸验证")
        self.setFixedSize(700, 500)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("科技主题尺寸验证 - 对比默认主题")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 控件测试组
        controls_group = QGroupBox("控件尺寸对比测试")
        controls_layout = QVBoxLayout(controls_group)
        
        # SpinBox测试
        spinbox_row = QHBoxLayout()
        spinbox_row.addWidget(QLabel("数字输入框:"))
        self.spinbox = QSpinBox()
        self.spinbox.setRange(0, 100)
        self.spinbox.setValue(50)
        spinbox_row.addWidget(self.spinbox)
        spinbox_row.addStretch()
        controls_layout.addLayout(spinbox_row)
        
        # ComboBox测试
        combobox_row = QHBoxLayout()
        combobox_row.addWidget(QLabel("下拉框:"))
        self.combobox = QComboBox()
        self.combobox.addItems(["默认主题", "浅色主题", "深色主题", "护眼主题", "科技主题"])
        combobox_row.addWidget(self.combobox)
        combobox_row.addStretch()
        controls_layout.addLayout(combobox_row)
        
        # CheckBox测试
        checkbox_row = QHBoxLayout()
        checkbox_row.addWidget(QLabel("复选框:"))
        self.checkbox1 = QCheckBox("选项1")
        self.checkbox2 = QCheckBox("选项2")
        checkbox_row.addWidget(self.checkbox1)
        checkbox_row.addWidget(self.checkbox2)
        checkbox_row.addStretch()
        controls_layout.addLayout(checkbox_row)
        
        # RadioButton测试
        radio_row = QHBoxLayout()
        radio_row.addWidget(QLabel("单选按钮:"))
        self.radio1 = QRadioButton("选项A")
        self.radio2 = QRadioButton("选项B")
        self.radio1.setChecked(True)
        radio_row.addWidget(self.radio1)
        radio_row.addWidget(self.radio2)
        radio_row.addStretch()
        controls_layout.addLayout(radio_row)
        
        layout.addWidget(controls_group)
        
        # 尺寸信息显示
        info_group = QGroupBox("尺寸信息")
        info_layout = QVBoxLayout(info_group)
        
        self.size_info_label = QLabel("点击'检查尺寸'按钮查看控件尺寸信息")
        info_layout.addWidget(self.size_info_label)
        
        layout.addWidget(info_group)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        check_size_btn = QPushButton("检查尺寸")
        check_size_btn.clicked.connect(self.check_sizes)
        
        switch_theme_btn = QPushButton("切换到默认主题对比")
        switch_theme_btn.clicked.connect(self.switch_to_default)
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(check_size_btn)
        button_layout.addWidget(switch_theme_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def check_sizes(self):
        """检查控件尺寸"""
        current_theme = theme_manager.get_current_theme()
        
        size_info = []
        size_info.append(f"📊 当前主题: {current_theme}")
        size_info.append("")
        
        # 检查各控件尺寸
        controls = [
            ("SpinBox", self.spinbox),
            ("ComboBox", self.combobox),
            ("CheckBox1", self.checkbox1),
            ("CheckBox2", self.checkbox2),
            ("RadioButton1", self.radio1),
            ("RadioButton2", self.radio2),
        ]
        
        for name, control in controls:
            width = control.size().width()
            height = control.size().height()
            size_info.append(f"  {name}: {width} x {height} px")
        
        # 更新显示
        self.size_info_label.setText("\n".join(size_info))
        
        # 同时在控制台输出
        print("\n" + "="*50)
        for line in size_info:
            print(line)
        print("="*50)
        
    def switch_to_default(self):
        """切换到默认主题进行对比"""
        app = QApplication.instance()
        theme_manager.set_theme(app, "默认主题")
        self.setWindowTitle("默认主题尺寸对比")
        
        # 自动检查尺寸
        self.check_sizes()


def test_tech_theme():
    """测试科技主题"""
    app = QApplication(sys.argv)
    
    print("🔧 科技主题修复验证工具")
    print("=" * 50)
    
    # 首先应用科技主题
    theme_manager.set_theme(app, "科技主题")
    print("✅ 已应用科技主题")
    
    dialog = TechThemeTestDialog()
    dialog.show()
    
    print("\n📋 测试说明:")
    print("1. 当前显示的是科技主题")
    print("2. 点击'检查尺寸'查看控件尺寸")
    print("3. 点击'切换到默认主题对比'进行对比")
    print("4. 科技主题的控件尺寸应该与默认主题完全相同")
    print("5. 只有颜色和视觉效果不同")
    
    result = dialog.exec()
    if result == QDialog.DialogCode.Accepted:
        print("✅ 科技主题测试完成")
    
    print("\n🎉 科技主题验证完成！")
    sys.exit(0)


def check_tech_theme_styles():
    """检查科技主题样式"""
    print("🔍 检查科技主题样式定义...")
    
    theme_file = Path(__file__).parent / "ui" / "modern_theme_manager.py"
    
    if not theme_file.exists():
        print("❌ 主题文件不存在")
        return False
    
    try:
        content = theme_file.read_text(encoding='utf-8')
        
        # 查找科技主题部分
        tech_theme_start = content.find("def _get_tech_theme(self)")
        if tech_theme_start == -1:
            print("❌ 未找到科技主题定义")
            return False
        
        # 查找科技主题结束位置
        tech_theme_end = content.find("def ", tech_theme_start + 1)
        if tech_theme_end == -1:
            tech_theme_end = len(content)
        
        tech_theme_content = content[tech_theme_start:tech_theme_end]
        
        # 检查是否还有尺寸相关属性
        size_properties = [
            "width:",
            "height:", 
            "min-width:",
            "padding:",
            "border-radius:",
        ]
        
        found_issues = []
        for prop in size_properties:
            if prop in tech_theme_content:
                count = tech_theme_content.count(prop)
                found_issues.append(f"{prop} ({count}个)")
        
        print(f"\n📊 科技主题样式检查:")
        if not found_issues:
            print("  ✅ 未发现尺寸相关属性")
            print("  ✅ 科技主题已正确修复为只包含颜色属性")
        else:
            print("  ❌ 发现以下尺寸属性:")
            for issue in found_issues:
                print(f"    - {issue}")
        
        # 检查颜色属性
        color_properties = ["background:", "color:", "border: 1px solid"]
        color_count = sum(tech_theme_content.count(prop) for prop in color_properties)
        print(f"  ✅ 颜色相关属性: {color_count}个")
        
        return len(found_issues) == 0
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 科技主题修复验证工具")
    print("=" * 50)
    
    # 检查样式定义
    styles_ok = check_tech_theme_styles()
    
    if styles_ok:
        print("\n✅ 科技主题样式检查通过！")
        print("\n📋 修复确认:")
        print("  - 已移除所有尺寸相关属性")
        print("  - 保留了所有颜色相关属性")
        print("  - 科技主题现在使用默认尺寸")
        
        print("\n🧪 现在可以运行测试:")
        print("python test_tech_theme_fix.py --test")
    else:
        print("\n❌ 科技主题样式检查失败")
        print("可能仍有尺寸属性需要清理")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_tech_theme()


if __name__ == "__main__":
    main()
