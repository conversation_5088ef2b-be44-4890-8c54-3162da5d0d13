#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet space 微信群发助手 - 单文件EXE构建脚本
打包成单个可执行文件，运行时自动生成加密配置文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 项目信息
PROJECT_NAME = "MeetSpaceWeChatSender"
PROJECT_VERSION = "1.0.0"

def create_single_exe_spec():
    """创建单文件EXE的spec配置"""
    print("📝 创建单文件EXE配置...")
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# Meet space 微信群发助手 - 单文件EXE配置

import os
from pathlib import Path

# 项目根目录
project_root = Path(SPECPATH)

block_cipher = None

# 最小化的数据文件 - 只包含必要资源
datas = [
    # 应用图标
    ('resources/icons/app_icon.ico', 'resources/icons'),
    # 版本信息
    ('version_info.txt', '.'),
]

# 核心隐藏导入
hiddenimports = [
    # PyQt6核心
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    
    # 微信接口
    'wcferry',
    
    # 数据处理
    'pandas',
    'openpyxl',
    'requests',
    'aiohttp',
    
    # 图像处理
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    
    # 配置和工具
    'yaml',
    'json',
    'datetime',
    'pathlib',
    'psutil',
    
    # 业务模块
    'config',
    'config.settings',
    'config.wechat_config',
    'core',
    'core.wechatferry_connector',
    'core.timing_sender',
    'core.loop_sender',
    'core.message_sender_core',
    'ui',
    'ui.main_window',
    'ui.modern_theme_manager',
    'utils',
    'utils.logger',
    'utils.path_manager',
]

# 排除不需要的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy.testing',
    'pytest',
    'setuptools',
    'distutils',
]

# 二进制文件
binaries = []

# 主程序分析
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤不需要的文件
def filter_files(toc):
    """过滤不需要的文件"""
    filtered = []
    exclude_patterns = [
        'test_',
        '_test',
        '.pyc',
        '__pycache__',
        '.git',
        'examples',
        'docs',
        'tests',
    ]
    
    for name, path, typecode in toc:
        should_exclude = False
        for pattern in exclude_patterns:
            if pattern in name.lower() or pattern in path.lower():
                should_exclude = True
                break
        
        if not should_exclude:
            filtered.append((name, path, typecode))
    
    return filtered

# 应用过滤
a.binaries = filter_files(a.binaries)
a.datas = filter_files(a.datas)

# PYZ归档
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 单文件EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{PROJECT_NAME}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='resources/icons/app_icon.ico',
    # 单文件模式
    onefile=True,
)
'''
    
    spec_file = Path(f"{PROJECT_NAME}_Single.spec")
    with open(spec_file, "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print(f"  ✅ 已创建: {spec_file}")
    return spec_file

def build_single_exe():
    """构建单文件EXE"""
    print("🔨 构建单文件EXE...")
    
    # 1. 创建spec文件
    spec_file = create_single_exe_spec()
    
    # 2. 清理之前的构建
    print("  🧹 清理之前的构建...")
    for cleanup_dir in ["build", "dist"]:
        if Path(cleanup_dir).exists():
            shutil.rmtree(cleanup_dir)
            print(f"    ✅ 已清理: {cleanup_dir}")
    
    # 3. 运行PyInstaller
    print("  📦 运行PyInstaller...")
    cmd = [
        "pyinstaller",
        "--clean",
        "--noconfirm",
        str(spec_file)
    ]
    
    print(f"  📋 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("  ✅ PyInstaller构建成功")
        
        # 检查输出文件
        exe_file = Path("dist") / f"{PROJECT_NAME}.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"  ✅ 单文件EXE生成成功: {exe_file}")
            print(f"  📊 文件大小: {size_mb:.1f} MB")
            return True
        else:
            print("  ❌ 未找到生成的EXE文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"  ❌ PyInstaller构建失败: {e}")
        if e.stdout:
            print(f"  标准输出: {e.stdout}")
        if e.stderr:
            print(f"  错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 Meet space 微信群发助手 - 单文件EXE构建")
    print("=" * 60)
    
    # 检查环境
    print("🔍 检查构建环境...")
    
    # 检查PyInstaller
    try:
        result = subprocess.run(["pyinstaller", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"  ✅ PyInstaller: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  ❌ PyInstaller未安装")
        print("  请运行: pip install pyinstaller")
        return False
    
    # 检查必要文件
    required_files = [
        "main.py",
        "version_info.txt",
        "resources/icons/app_icon.ico"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"  ❌ 缺少必要文件: {file_path}")
            return False
        else:
            print(f"  ✅ 找到文件: {file_path}")
    
    # 构建单文件EXE
    if not build_single_exe():
        return False
    
    print("\n🎉 单文件EXE构建完成！")
    print(f"📁 输出文件: dist/{PROJECT_NAME}.exe")
    print("\n📋 特性:")
    print("  ✅ 单个EXE文件，无需安装")
    print("  ✅ 运行时自动生成配置文件")
    print("  ✅ 配置文件加密存储")
    print("  ✅ 便携式，可在任何Windows系统运行")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
