#!/usr/bin/env python3
"""
创建简单的MSI安装程序
使用Python的bdist_msi功能创建MSI安装包
"""

import os
import sys
import subprocess
import time
from pathlib import Path
import shutil
import tempfile


class SimpleMSIBuilder:
    """简单MSI构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        
    def log(self, message: str, level: str = "INFO") -> None:
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "STEP": "🔄"
        }.get(level, "ℹ️")
        
        print(f"[{timestamp}] {prefix} {message}")
        
    def create_setup_py(self) -> Path:
        """创建setup.py文件用于生成MSI"""
        self.log("创建setup.py文件...", "STEP")
        
        setup_content = '''#!/usr/bin/env python3
"""
Setup script for creating MSI installer
"""

from cx_Freeze import setup, Executable
import sys
from pathlib import Path

# 项目信息
APP_NAME = "Meet space 微信群发助手"
APP_VERSION = "1.0.0"
DESCRIPTION = "一个安全、高效的微信群发工具"
AUTHOR = "Meet space 会客创意空间"

# 构建选项
build_exe_options = {
    "packages": [
        "PyQt6", "PyQt6.QtCore", "PyQt6.QtGui", "PyQt6.QtWidgets",
        "requests", "aiohttp", "PIL", "psutil", "yaml", "dateutil",
        "cryptography", "sqlite3", "json", "xml", "email",
        "asyncio", "concurrent", "threading", "multiprocessing"
    ],
    "excludes": [
        "tkinter", "matplotlib", "scipy", "jupyter", "IPython", 
        "notebook", "pytest", "unittest", "test", "tests"
    ],
    "include_files": [
        ("resources", "resources"),
        ("config", "config"),
        ("tools", "tools"),
        ("wxhelper_files", "wxhelper_files"),
        ("README.md", "README.md"),
        ("LICENSE.txt", "LICENSE.txt"),
    ],
    "include_msvcrt": True,
    "optimize": 2,
}

# MSI构建选项
bdist_msi_options = {
    "upgrade_code": "{12345678-1234-1234-1234-123456789012}",
    "add_to_path": False,
    "initial_target_dir": r"[ProgramFilesFolder]\\MeetSpaceWeChatSender",
    "install_icon": "resources/icons/app.ico" if Path("resources/icons/app.ico").exists() else None,
}

# 可执行文件配置
executables = [
    Executable(
        "main.py",
        base="Win32GUI",  # 无控制台窗口
        target_name="MeetSpaceWeChatSender.exe",
        icon="resources/icons/app.ico" if Path("resources/icons/app.ico").exists() else None,
        shortcut_name=APP_NAME,
        shortcut_dir="ProgramMenuFolder",
    )
]

setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=DESCRIPTION,
    author=AUTHOR,
    options={
        "build_exe": build_exe_options,
        "bdist_msi": bdist_msi_options,
    },
    executables=executables,
)
'''
        
        setup_file = self.project_root / "setup_msi.py"
        setup_file.write_text(setup_content, encoding='utf-8')
        
        self.log(f"setup.py文件已创建: {setup_file}", "SUCCESS")
        return setup_file
        
    def install_cx_freeze(self) -> bool:
        """安装cx_Freeze"""
        self.log("检查cx_Freeze...", "STEP")
        
        try:
            import cx_Freeze
            self.log("cx_Freeze已安装", "SUCCESS")
            return True
        except ImportError:
            self.log("安装cx_Freeze...", "INFO")
            
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", "cx_Freeze"
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log("cx_Freeze安装成功", "SUCCESS")
                    return True
                else:
                    self.log(f"cx_Freeze安装失败: {result.stderr}", "ERROR")
                    return False
                    
            except Exception as e:
                self.log(f"安装cx_Freeze失败: {e}", "ERROR")
                return False
                
    def build_msi(self) -> bool:
        """构建MSI安装包"""
        self.log("构建MSI安装包...", "STEP")
        
        try:
            # 创建setup.py文件
            setup_file = self.create_setup_py()
            
            # 运行setup.py bdist_msi
            cmd = [sys.executable, str(setup_file), "bdist_msi"]
            
            self.log("执行MSI构建命令...", "INFO")
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log("MSI构建成功", "SUCCESS")
                
                # 查找生成的MSI文件
                dist_dir = self.project_root / "dist"
                msi_files = list(dist_dir.glob("*.msi"))
                
                if msi_files:
                    msi_file = msi_files[0]
                    file_size = msi_file.stat().st_size / (1024 * 1024)
                    self.log(f"MSI文件: {msi_file.name} ({file_size:.1f} MB)", "SUCCESS")
                    
                    # 移动到项目根目录
                    final_path = self.project_root / msi_file.name
                    if final_path.exists():
                        final_path.unlink()
                    shutil.move(str(msi_file), str(final_path))
                    self.log(f"MSI文件已移动到: {final_path}", "SUCCESS")
                    
                    return True
                else:
                    self.log("未找到生成的MSI文件", "ERROR")
                    return False
                    
            else:
                self.log("MSI构建失败", "ERROR")
                if result.stdout:
                    self.log(result.stdout, "ERROR")
                if result.stderr:
                    self.log(result.stderr, "ERROR")
                return False
                
        except Exception as e:
            self.log(f"构建MSI失败: {e}", "ERROR")
            return False
            
    def create_alternative_msi(self) -> bool:
        """创建替代的MSI安装包（使用现有的exe文件）"""
        self.log("创建替代MSI安装包...", "STEP")
        
        try:
            # 检查是否有现成的exe文件
            exe_dir = self.project_root / "dist" / "MeetSpaceWeChatSender"
            if not exe_dir.exists():
                self.log("可执行文件目录不存在", "ERROR")
                return False
                
            # 使用Windows SDK的MsiTran.exe或者第三方工具
            # 这里我们创建一个批处理脚本来模拟MSI安装
            self.log("创建MSI模拟安装脚本...", "INFO")
            
            # 创建一个自解压的安装程序
            installer_script = f'''@echo off
chcp 65001 >nul
title Meet space 微信群发助手 - 安装程序

echo.
echo ========================================
echo   Meet space 微信群发助手
echo   Windows安装程序 v1.0.0
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限检查通过
) else (
    echo ❌ 需要管理员权限，请以管理员身份运行
    pause
    exit /b 1
)

:: 设置安装目录
set "INSTALL_DIR=%ProgramFiles%\\MeetSpaceWeChatSender"

echo 📁 安装目录: %INSTALL_DIR%
echo.

:: 创建安装目录
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo ✅ 创建安装目录成功
) else (
    echo ℹ️ 安装目录已存在
)

:: 复制文件
echo 📋 正在复制程序文件...
xcopy /E /I /Y "%~dp0MeetSpaceWeChatSender" "%INSTALL_DIR%" >nul
if %errorLevel% == 0 (
    echo ✅ 程序文件复制完成
) else (
    echo ❌ 文件复制失败
    pause
    exit /b 1
)

:: 创建开始菜单快捷方式
echo 📋 创建开始菜单快捷方式...
set "START_MENU=%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs"
if not exist "%START_MENU%\\Meet space 微信群发助手" (
    mkdir "%START_MENU%\\Meet space 微信群发助手"
)

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\\Meet space 微信群发助手\\Meet space 微信群发助手.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\MeetSpaceWeChatSender.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

:: 创建桌面快捷方式（可选）
set /p desktop_shortcut="是否创建桌面快捷方式？(y/n): "
if /i "%desktop_shortcut%"=="y" (
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Meet space 微信群发助手.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\MeetSpaceWeChatSender.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"
    echo ✅ 桌面快捷方式已创建
)

:: 注册到控制面板
echo 📋 注册到控制面板...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MeetSpaceWeChatSender" /v "DisplayName" /t REG_SZ /d "Meet space 微信群发助手" /f >nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MeetSpaceWeChatSender" /v "DisplayVersion" /t REG_SZ /d "1.0.0" /f >nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MeetSpaceWeChatSender" /v "Publisher" /t REG_SZ /d "Meet space 会客创意空间" /f >nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MeetSpaceWeChatSender" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MeetSpaceWeChatSender" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\\uninstall.bat" /f >nul

:: 创建卸载脚本
echo 📋 创建卸载脚本...
echo @echo off > "%INSTALL_DIR%\\uninstall.bat"
echo title Meet space 微信群发助手 - 卸载程序 >> "%INSTALL_DIR%\\uninstall.bat"
echo echo 正在卸载 Meet space 微信群发助手... >> "%INSTALL_DIR%\\uninstall.bat"
echo taskkill /f /im MeetSpaceWeChatSender.exe 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo timeout /t 2 /nobreak ^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo rd /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\\uninstall.bat"
echo del /q "%START_MENU%\\Meet space 微信群发助手\\*.lnk" 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo rd "%START_MENU%\\Meet space 微信群发助手" 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo del /q "%USERPROFILE%\\Desktop\\Meet space 微信群发助手.lnk" 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo reg delete "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MeetSpaceWeChatSender" /f 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo echo 卸载完成！ >> "%INSTALL_DIR%\\uninstall.bat"
echo pause >> "%INSTALL_DIR%\\uninstall.bat"

echo.
echo ✅ 安装完成！
echo.
echo 📁 安装位置: %INSTALL_DIR%
echo 🚀 可从开始菜单启动程序
echo.

:: 询问是否立即启动
set /p launch_now="是否立即启动程序？(y/n): "
if /i "%launch_now%"=="y" (
    start "" "%INSTALL_DIR%\\MeetSpaceWeChatSender.exe"
)

echo.
echo 感谢使用 Meet space 微信群发助手！
pause
'''
            
            # 保存安装脚本
            installer_bat = self.project_root / "MeetSpace_WeChatSender_Installer.bat"
            installer_bat.write_text(installer_script, encoding='utf-8')
            
            self.log(f"Windows安装脚本已创建: {installer_bat}", "SUCCESS")
            
            # 创建自解压安装包
            self.create_self_extracting_installer()
            
            return True
            
        except Exception as e:
            self.log(f"创建替代MSI失败: {e}", "ERROR")
            return False
            
    def create_self_extracting_installer(self) -> bool:
        """创建自解压安装包"""
        self.log("创建自解压安装包...", "INFO")
        
        try:
            # 使用7-Zip创建自解压安装包（如果可用）
            seven_zip_paths = [
                r"C:\Program Files\7-Zip\7z.exe",
                r"C:\Program Files (x86)\7-Zip\7z.exe",
            ]
            
            seven_zip = None
            for path in seven_zip_paths:
                if Path(path).exists():
                    seven_zip = path
                    break
                    
            if seven_zip:
                self.log("使用7-Zip创建自解压安装包...", "INFO")
                
                # 创建临时目录
                temp_dir = Path(tempfile.mkdtemp())
                
                # 复制文件到临时目录
                dist_source = self.project_root / "dist" / "MeetSpaceWeChatSender"
                installer_bat = self.project_root / "MeetSpace_WeChatSender_Installer.bat"
                
                shutil.copytree(dist_source, temp_dir / "MeetSpaceWeChatSender")
                shutil.copy2(installer_bat, temp_dir / "install.bat")
                
                # 创建自解压配置
                sfx_config = temp_dir / "config.txt"
                sfx_config.write_text('''
;!@Install@!UTF-8!
Title="Meet space 微信群发助手 安装程序"
BeginPrompt="是否安装 Meet space 微信群发助手？"
RunProgram="install.bat"
;!@InstallEnd@!
''', encoding='utf-8')
                
                # 创建自解压文件
                output_file = self.project_root / "MeetSpace_WeChatSender_Setup.exe"
                
                cmd = [
                    seven_zip, "a", "-sfx7z.sfx", "-t7z",
                    str(output_file), str(temp_dir / "*")
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                # 清理临时目录
                shutil.rmtree(temp_dir)
                
                if result.returncode == 0 and output_file.exists():
                    file_size = output_file.stat().st_size / (1024 * 1024)
                    self.log(f"自解压安装包已创建: {output_file.name} ({file_size:.1f} MB)", "SUCCESS")
                    return True
                    
            self.log("7-Zip不可用，跳过自解压安装包创建", "WARNING")
            return True
            
        except Exception as e:
            self.log(f"创建自解压安装包失败: {e}", "WARNING")
            return True  # 不影响主流程
            
    def build(self) -> bool:
        """执行构建流程"""
        self.log("=== 开始创建MSI安装程序 ===", "STEP")
        start_time = time.time()
        
        try:
            # 1. 安装cx_Freeze
            if not self.install_cx_freeze():
                self.log("cx_Freeze不可用，使用替代方案", "WARNING")
                return self.create_alternative_msi()
                
            # 2. 构建MSI
            if not self.build_msi():
                self.log("标准MSI构建失败，使用替代方案", "WARNING")
                return self.create_alternative_msi()
                
            elapsed_time = time.time() - start_time
            self.log(f"=== MSI安装程序创建完成 (耗时: {elapsed_time:.1f}秒) ===", "SUCCESS")
            
            return True
            
        except Exception as e:
            self.log(f"构建过程出错: {e}", "ERROR")
            return False


def main():
    """主函数"""
    print("🚀 Meet space 微信群发助手 - 简单MSI创建工具")
    print("=" * 60)
    
    builder = SimpleMSIBuilder()
    success = builder.build()
    
    if success:
        print("\n🎉 安装程序创建成功！")
        
        # 显示结果文件
        result_files = []
        for pattern in ["*.msi", "*Setup*.exe", "*Installer*.bat"]:
            result_files.extend(Path(".").glob(pattern))
            
        if result_files:
            print("\n📦 生成的安装文件:")
            for file in result_files:
                file_size = file.stat().st_size / (1024 * 1024)
                print(f"  📄 {file.name} ({file_size:.1f} MB)")
                
        # 询问是否打开文件夹
        try:
            choice = input("\n是否打开输出文件夹？(y/n): ").strip().lower()
            if choice == 'y':
                subprocess.run(['explorer', str(Path(__file__).parent)], check=False)
        except KeyboardInterrupt:
            pass
            
        sys.exit(0)
    else:
        print("\n💥 安装程序创建失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
