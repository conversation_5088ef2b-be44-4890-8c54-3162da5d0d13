"""
备份管理对话框

提供配置备份的管理界面，包括创建、恢复、导入、导出和删除备份。
"""

import os
from datetime import datetime
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QMessageBox,
    QFileDialog,
    QInputDialog,
    QGroupBox,
    QTextEdit,
    QSplitter,
)

from core.config_backup_manager import backup_manager, BackupInfo
from ui.themed_dialog_base import ThemedDialogBase
from ui.themed_message_box import ThemedMessageBoxHelper
from utils.logger import setup_logger

logger = setup_logger("backup_manager_dialog")


class BackupManagerDialog(ThemedDialogBase):
    """备份管理对话框"""

    backup_restored = pyqtSignal()  # 备份恢复完成信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("配置备份管理")
        self.setModal(True)
        self.resize(800, 600)

        self.setup_ui()
        self.refresh_backup_list()

        # 主题支持已由 ThemedDialogBase 自动设置

        logger.info("备份管理对话框初始化完成")

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)

        # 左侧：备份列表
        left_widget = self.create_backup_list_widget()
        splitter.addWidget(left_widget)

        # 右侧：备份详情和操作
        right_widget = self.create_backup_details_widget()
        splitter.addWidget(right_widget)

        # 设置分割器比例
        splitter.setSizes([500, 300])

        # 底部按钮
        button_layout = QHBoxLayout()

        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)

        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def create_backup_list_widget(self):
        """创建备份列表组件"""
        widget = QGroupBox("备份列表")
        layout = QVBoxLayout(widget)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.create_backup_btn = QPushButton("📝 创建备份")
        self.create_backup_btn.clicked.connect(self.create_backup)
        button_layout.addWidget(self.create_backup_btn)

        self.import_backup_btn = QPushButton("📥 导入备份")
        self.import_backup_btn.clicked.connect(self.import_backup)
        button_layout.addWidget(self.import_backup_btn)

        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.refresh_backup_list)
        button_layout.addWidget(self.refresh_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 备份表格
        self.backup_table = QTableWidget()
        self.backup_table.setColumnCount(4)
        self.backup_table.setHorizontalHeaderLabels(
            ["文件名", "创建时间", "大小", "描述"]
        )

        # 设置表格属性
        header = self.backup_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)

        self.backup_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.backup_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.backup_table.itemSelectionChanged.connect(self.on_backup_selected)

        layout.addWidget(self.backup_table)

        return widget

    def create_backup_details_widget(self):
        """创建备份详情组件"""
        widget = QGroupBox("备份详情")
        layout = QVBoxLayout(widget)

        # 详情显示
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(150)
        layout.addWidget(self.details_text)

        # 操作按钮
        button_layout = QVBoxLayout()

        self.restore_btn = QPushButton("🔄 恢复备份")
        self.restore_btn.clicked.connect(self.restore_backup)
        self.restore_btn.setEnabled(False)
        button_layout.addWidget(self.restore_btn)

        self.export_btn = QPushButton("📤 导出备份")
        self.export_btn.clicked.connect(self.export_backup)
        self.export_btn.setEnabled(False)
        button_layout.addWidget(self.export_btn)

        self.delete_btn = QPushButton("🗑️ 删除备份")
        self.delete_btn.clicked.connect(self.delete_backup)
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """
        )
        button_layout.addWidget(self.delete_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        return widget

    def refresh_backup_list(self):
        """刷新备份列表"""
        try:
            backups = backup_manager.get_backup_list()

            self.backup_table.setRowCount(len(backups))

            for row, backup in enumerate(backups):
                # 文件名
                self.backup_table.setItem(row, 0, QTableWidgetItem(backup.filename))

                # 创建时间
                time_str = backup.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                self.backup_table.setItem(row, 1, QTableWidgetItem(time_str))

                # 文件大小
                size_str = self.format_file_size(backup.size)
                self.backup_table.setItem(row, 2, QTableWidgetItem(size_str))

                # 描述
                self.backup_table.setItem(row, 3, QTableWidgetItem(backup.description))

                # 存储备份信息
                self.backup_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, backup)

            logger.info(f"刷新备份列表完成，共 {len(backups)} 个备份")

        except Exception as e:
            logger.error(f"刷新备份列表失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"刷新备份列表失败:\n{e}")

    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

    def on_backup_selected(self):
        """备份选择变化"""
        selected_items = self.backup_table.selectedItems()

        if selected_items:
            backup = selected_items[0].data(Qt.ItemDataRole.UserRole)
            self.show_backup_details(backup)

            # 启用操作按钮
            self.restore_btn.setEnabled(True)
            self.export_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
        else:
            self.details_text.clear()

            # 禁用操作按钮
            self.restore_btn.setEnabled(False)
            self.export_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)

    def show_backup_details(self, backup: BackupInfo):
        """显示备份详情"""
        details = f"""备份文件：{backup.filename}
创建时间：{backup.timestamp.strftime("%Y-%m-%d %H:%M:%S")}
文件大小：{self.format_file_size(backup.size)}
文件路径：{backup.filepath}
备份描述：{backup.description}
"""
        self.details_text.setText(details)

    def create_backup(self):
        """创建新备份"""
        try:
            description, ok = QInputDialog.getText(
                self,
                "创建备份",
                "请输入备份描述（可选）:",
                text=f"手动备份 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            )

            if ok:
                backup_path = backup_manager.create_backup(description)
                if backup_path:
                    ThemedMessageBoxHelper.show_information(
                        self, "成功", f"备份创建成功:\n{backup_path}"
                    )
                    self.refresh_backup_list()
                else:
                    ThemedMessageBoxHelper.show_warning(self, "失败", "备份创建失败")

        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"创建备份失败:\n{e}")

    def restore_backup(self):
        """恢复备份"""
        selected_items = self.backup_table.selectedItems()
        if not selected_items:
            return

        backup = selected_items[0].data(Qt.ItemDataRole.UserRole)

        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认恢复",
            f"确定要恢复备份 '{backup.filename}' 吗？\n\n"
            "此操作将覆盖当前配置，建议先创建当前配置的备份。",
        )

        if reply:
            try:
                if backup_manager.restore_backup(backup.filepath):
                    ThemedMessageBoxHelper.show_information(
                        self, "成功", "备份恢复成功！\n\n程序需要重启以应用新配置。"
                    )
                    self.backup_restored.emit()
                    self.close()
                else:
                    ThemedMessageBoxHelper.show_warning(self, "失败", "备份恢复失败")

            except Exception as e:
                logger.error(f"恢复备份失败: {e}")
                ThemedMessageBoxHelper.show_error(self, "错误", f"恢复备份失败:\n{e}")

    def export_backup(self):
        """导出备份"""
        selected_items = self.backup_table.selectedItems()
        if not selected_items:
            return

        backup = selected_items[0].data(Qt.ItemDataRole.UserRole)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出备份", backup.filename, "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                if backup_manager.export_backup(backup.filepath, file_path):
                    ThemedMessageBoxHelper.show_information(
                        self, "成功", f"备份导出成功:\n{file_path}"
                    )
                else:
                    ThemedMessageBoxHelper.show_warning(self, "失败", "备份导出失败")

            except Exception as e:
                logger.error(f"导出备份失败: {e}")
                ThemedMessageBoxHelper.show_error(self, "错误", f"导出备份失败:\n{e}")

    def import_backup(self):
        """导入备份"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入备份", "", "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                imported_path = backup_manager.import_backup(file_path)
                if imported_path:
                    ThemedMessageBoxHelper.show_information(
                        self,
                        "成功",
                        f"备份导入成功:\n{os.path.basename(imported_path)}",
                    )
                    self.refresh_backup_list()
                else:
                    ThemedMessageBoxHelper.show_warning(self, "失败", "备份导入失败")

            except Exception as e:
                logger.error(f"导入备份失败: {e}")
                ThemedMessageBoxHelper.show_error(self, "错误", f"导入备份失败:\n{e}")

    def delete_backup(self):
        """删除备份"""
        selected_items = self.backup_table.selectedItems()
        if not selected_items:
            return

        backup = selected_items[0].data(Qt.ItemDataRole.UserRole)

        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要删除备份 '{backup.filename}' 吗？\n\n此操作不可撤销。",
        )

        if reply:
            try:
                if backup_manager.delete_backup(backup.filepath):
                    ThemedMessageBoxHelper.show_information(
                        self, "成功", "备份删除成功"
                    )
                    self.refresh_backup_list()
                else:
                    ThemedMessageBoxHelper.show_warning(self, "失败", "备份删除失败")

            except Exception as e:
                logger.error(f"删除备份失败: {e}")
                ThemedMessageBoxHelper.show_error(self, "错误", f"删除备份失败:\n{e}")
