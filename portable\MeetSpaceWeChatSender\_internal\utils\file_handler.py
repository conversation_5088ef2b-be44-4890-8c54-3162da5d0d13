"""
文件处理工具模块

提供文件操作相关的工具函数。
"""

import os
import json
import csv
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import mimetypes

from config.settings import SUPPORTED_FILE_TYPES
from utils.logger import setup_logger

logger = setup_logger("file_handler")


class FileHandler:
    """文件处理器"""

    @staticmethod
    def is_file_supported(file_path: Union[str, Path]) -> bool:
        """
        检查文件类型是否支持

        Args:
            file_path: 文件路径

        Returns:
            是否支持
        """
        file_path = Path(file_path)
        return file_path.suffix.lower() in SUPPORTED_FILE_TYPES

    @staticmethod
    def get_file_type(file_path: Union[str, Path]) -> str:
        """
        获取文件类型

        Args:
            file_path: 文件路径

        Returns:
            文件类型 (image, document, video, other)
        """
        file_path = Path(file_path)
        suffix = file_path.suffix.lower()

        image_types = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]
        document_types = [
            ".txt",
            ".doc",
            ".docx",
            ".pdf",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
        ]
        video_types = [".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv"]

        if suffix in image_types:
            return "image"
        elif suffix in document_types:
            return "document"
        elif suffix in video_types:
            return "video"
        else:
            return "other"

    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """
        获取文件大小

        Args:
            file_path: 文件路径

        Returns:
            文件大小（字节）
        """
        try:
            return os.path.getsize(file_path)
        except OSError:
            return 0

    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小

        Args:
            size_bytes: 文件大小（字节）

        Returns:
            格式化后的大小字符串
        """
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    @staticmethod
    def read_text_file(
        file_path: Union[str, Path], encoding: str = "utf-8"
    ) -> Optional[str]:
        """
        读取文本文件

        Args:
            file_path: 文件路径
            encoding: 编码格式

        Returns:
            文件内容
        """
        try:
            with open(file_path, "r", encoding=encoding) as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            return None

    @staticmethod
    def write_text_file(
        file_path: Union[str, Path], content: str, encoding: str = "utf-8"
    ) -> bool:
        """
        写入文本文件

        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 编码格式

        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, "w", encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            logger.error(f"写入文件失败 {file_path}: {e}")
            return False

    @staticmethod
    def read_json_file(file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        读取JSON文件

        Args:
            file_path: 文件路径

        Returns:
            JSON数据
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"读取JSON文件失败 {file_path}: {e}")
            return None

    @staticmethod
    def write_json_file(file_path: Union[str, Path], data: Dict[str, Any]) -> bool:
        """
        写入JSON文件

        Args:
            file_path: 文件路径
            data: JSON数据

        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"写入JSON文件失败 {file_path}: {e}")
            return False

    @staticmethod
    def read_csv_file(file_path: Union[str, Path]) -> Optional[List[Dict[str, str]]]:
        """
        读取CSV文件

        Args:
            file_path: 文件路径

        Returns:
            CSV数据列表
        """
        try:
            data = []
            with open(file_path, "r", encoding="utf-8", newline="") as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(row)
            return data
        except Exception as e:
            logger.error(f"读取CSV文件失败 {file_path}: {e}")
            return None

    @staticmethod
    def write_csv_file(
        file_path: Union[str, Path],
        data: List[Dict[str, str]],
        fieldnames: Optional[List[str]] = None,
    ) -> bool:
        """
        写入CSV文件

        Args:
            file_path: 文件路径
            data: CSV数据列表
            fieldnames: 字段名列表

        Returns:
            是否成功
        """
        try:
            if not data:
                return False

            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            if fieldnames is None:
                fieldnames = list(data[0].keys())

            with open(file_path, "w", encoding="utf-8", newline="") as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            return True
        except Exception as e:
            logger.error(f"写入CSV文件失败 {file_path}: {e}")
            return False

    @staticmethod
    def copy_file(src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        复制文件

        Args:
            src: 源文件路径
            dst: 目标文件路径

        Returns:
            是否成功
        """
        try:
            dst = Path(dst)
            dst.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src, dst)
            return True
        except Exception as e:
            logger.error(f"复制文件失败 {src} -> {dst}: {e}")
            return False

    @staticmethod
    def move_file(src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        移动文件

        Args:
            src: 源文件路径
            dst: 目标文件路径

        Returns:
            是否成功
        """
        try:
            dst = Path(dst)
            dst.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(src, dst)
            return True
        except Exception as e:
            logger.error(f"移动文件失败 {src} -> {dst}: {e}")
            return False

    @staticmethod
    def delete_file(file_path: Union[str, Path]) -> bool:
        """
        删除文件

        Args:
            file_path: 文件路径

        Returns:
            是否成功
        """
        try:
            os.remove(file_path)
            return True
        except Exception as e:
            logger.error(f"删除文件失败 {file_path}: {e}")
            return False
