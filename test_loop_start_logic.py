#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试循环发送启动逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_start_methods_exist():
    """测试启动方法是否存在"""
    print("🔧 测试启动方法是否存在...")
    
    try:
        from ui.loop_send_page import LoopSendPage
        
        # 检查原有的启动方法
        if hasattr(LoopSendPage, 'start_group_task'):
            print("✅ start_group_task 方法存在")
        else:
            print("❌ start_group_task 方法不存在")
            return False
        
        # 检查新增的方法
        if hasattr(LoopSendPage, 'create_and_start_task_for_group'):
            print("✅ create_and_start_task_for_group 方法存在")
        else:
            print("❌ create_and_start_task_for_group 方法不存在")
            return False
        
        if hasattr(LoopSendPage, 'create_and_start_task_from_saved_config'):
            print("✅ create_and_start_task_from_saved_config 方法存在")
        else:
            print("❌ create_and_start_task_from_saved_config 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_start_logic_structure():
    """测试启动逻辑结构"""
    print("\n🔧 测试启动逻辑结构...")
    
    try:
        import inspect
        from ui.loop_send_page import LoopSendPage
        
        # 检查start_group_task方法的逻辑
        start_source = inspect.getsource(LoopSendPage.start_group_task)
        
        # 检查是否包含自动创建任务的逻辑
        if 'create_and_start_task_for_group' in start_source:
            print("✅ start_group_task 包含当前分组自动创建逻辑")
        else:
            print("❌ start_group_task 缺少当前分组自动创建逻辑")
            return False
        
        if 'create_and_start_task_from_saved_config' in start_source:
            print("✅ start_group_task 包含保存配置自动创建逻辑")
        else:
            print("❌ start_group_task 缺少保存配置自动创建逻辑")
            return False
        
        # 检查是否删除了提示信息
        if '请先在循环发送设置中创建任务' not in start_source:
            print("✅ 已删除创建任务提示信息")
        else:
            print("❌ 仍有创建任务提示信息")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_create_methods_logic():
    """测试创建方法逻辑"""
    print("\n🔧 测试创建方法逻辑...")
    
    try:
        import inspect
        from ui.loop_send_page import LoopSendPage
        
        # 检查create_and_start_task_for_group方法
        method1_source = inspect.getsource(LoopSendPage.create_and_start_task_for_group)
        
        if 'self.create_loop_task()' in method1_source:
            print("✅ create_and_start_task_for_group 调用了 create_loop_task")
        else:
            print("❌ create_and_start_task_for_group 未调用 create_loop_task")
            return False
        
        if 'loop_sender.start_task' in method1_source:
            print("✅ create_and_start_task_for_group 包含启动任务逻辑")
        else:
            print("❌ create_and_start_task_for_group 缺少启动任务逻辑")
            return False
        
        # 检查create_and_start_task_from_saved_config方法
        method2_source = inspect.getsource(LoopSendPage.create_and_start_task_from_saved_config)
        
        if 'group.get_config' in method2_source:
            print("✅ create_and_start_task_from_saved_config 读取分组配置")
        else:
            print("❌ create_and_start_task_from_saved_config 未读取分组配置")
            return False
        
        if 'loop_sender.create_task' in method2_source:
            print("✅ create_and_start_task_from_saved_config 包含创建任务逻辑")
        else:
            print("❌ create_and_start_task_from_saved_config 缺少创建任务逻辑")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 循环发送启动逻辑测试")
    print("=" * 60)
    print("测试优化后的循环发送启动逻辑")
    print("=" * 60)
    
    tests = [
        ("启动方法存在性测试", test_start_methods_exist),
        ("启动逻辑结构测试", test_start_logic_structure),
        ("创建方法逻辑测试", test_create_methods_logic)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 循环发送启动逻辑测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 循环发送启动逻辑优化成功！")
        print("\n✨ 优化内容:")
        print("  🚀 点击启动按钮自动创建并启动任务")
        print("  🎯 当前分组：使用UI中的配置")
        print("  💾 其他分组：使用保存的配置")
        print("  🔄 自动启动创建的任务")
        print("  ❌ 删除了创建任务提示")
        
        print("\n📋 现在的启动流程:")
        print("  1️⃣ 点击分组卡片的启动按钮")
        print("  2️⃣ 系统检查是否有现有任务")
        print("  3️⃣ 如果有任务：直接启动/恢复")
        print("  4️⃣ 如果没有任务：自动创建并启动")
        print("  5️⃣ 任务开始运行")
        
        print("\n🎯 现在点击启动按钮就能直接运行了！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
