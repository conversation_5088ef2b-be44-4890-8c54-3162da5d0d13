#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速构建脚本 - 简化版
"""

import os
import sys
import subprocess
from pathlib import Path

def quick_build_exe():
    """快速构建可执行文件"""
    print("🚀 快速构建可执行文件...")
    
    # 简单的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onedir",
        "--windowed",
        "--name=MeetSpaceWeChatSender",
        "--add-data=resources;resources",
        "--add-data=config;config",
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=pandas",
        "--hidden-import=requests",
        "--hidden-import=PIL",
        "--hidden-import=yaml",
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--clean",
        "--noconfirm",
        "main.py"
    ]
    
    print(f"📋 执行命令:")
    print(f"  {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 可执行文件构建成功！")
        print(f"📁 输出位置: dist/MeetSpaceWeChatSender/")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def test_wix():
    """测试WiX"""
    print("🧪 测试WiX...")
    
    try:
        result = subprocess.run(["wix", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ WiX可用: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ WiX不可用")
    return False

def quick_build_msi():
    """快速构建MSI"""
    print("📦 快速构建MSI...")
    
    # 检查dist目录
    dist_dir = Path("dist/MeetSpaceWeChatSender")
    if not dist_dir.exists():
        print("❌ 未找到可执行文件，请先构建")
        return False
    
    # 创建简单的WiX配置
    installer_dir = Path("installer")
    installer_dir.mkdir(exist_ok=True)
    
    wix_content = '''<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  <Package Name="Meet space 微信群发助手" 
           Version="1.0.0" 
           Manufacturer="Meet space 会客创意空间" 
           UpgradeCode="{12345678-1234-1234-1234-123456789012}"
           Language="2052">
    
    <SummaryInformation Description="Meet space 微信群发助手 安装程序"
                        Manufacturer="Meet space 会客创意空间" />
    
    <MajorUpgrade DowngradeErrorMessage="已安装更新版本。" />
    
    <Feature Id="ProductFeature" Title="主程序" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>
    
    <StandardDirectory Id="ProgramFilesFolder">
      <Directory Id="INSTALLFOLDER" Name="MeetSpaceWeChatSender" />
    </StandardDirectory>
    
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="MainExecutable">
        <File Source="dist\\MeetSpaceWeChatSender\\MeetSpaceWeChatSender.exe" />
      </Component>
    </ComponentGroup>
    
  </Package>
</Wix>
'''
    
    wix_file = installer_dir / "simple.wxs"
    with open(wix_file, "w", encoding="utf-8") as f:
        f.write(wix_content)
    
    print(f"✅ 创建WiX配置: {wix_file}")
    
    # 构建MSI
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    msi_file = output_dir / "MeetSpaceWeChatSender_v1.0.0.msi"
    
    cmd = [
        "wix",
        "build",
        str(wix_file),
        "-o", str(msi_file)
    ]
    
    print(f"📋 构建MSI:")
    print(f"  {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ MSI构建成功: {msi_file}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ MSI构建失败: {e}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        return False

def main():
    """主函数"""
    print("⚡ 快速构建工具")
    print("=" * 40)
    
    # 1. 构建可执行文件
    if not quick_build_exe():
        return False
    
    # 2. 测试WiX
    if not test_wix():
        print("⚠️  跳过MSI构建")
        return True
    
    # 3. 构建MSI
    if not quick_build_msi():
        print("⚠️  MSI构建失败，但可执行文件构建成功")
        return True
    
    print("\n🎉 构建完成！")
    print("📁 可执行文件: dist/MeetSpaceWeChatSender/")
    print("📦 MSI安装包: output/MeetSpaceWeChatSender_v1.0.0.msi")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
