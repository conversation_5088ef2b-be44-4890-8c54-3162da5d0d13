# -*- coding: utf-8 -*-
"""
运行时钩子：修复dis模块导入问题

这个钩子确保dis模块在运行时可用，解决PyInstaller打包后的模块缺失问题。
"""

import sys

# 确保dis模块可用
try:
    import dis
    print("dis module loaded successfully")
except ImportError as e:
    print(f"Failed to import dis module: {e}")
    # 尝试手动添加dis模块路径
    try:
        import importlib.util
        import os
        
        # 查找dis模块
        for path in sys.path:
            dis_path = os.path.join(path, 'dis.py')
            if os.path.exists(dis_path):
                spec = importlib.util.spec_from_file_location("dis", dis_path)
                if spec and spec.loader:
                    dis = importlib.util.module_from_spec(spec)
                    sys.modules['dis'] = dis
                    spec.loader.exec_module(dis)
                    print("dis module loaded manually")
                    break
    except Exception as manual_error:
        print(f"Manual dis import also failed: {manual_error}")

# 确保inspect模块可用
try:
    import inspect
    print("inspect module loaded successfully")
except ImportError as e:
    print(f"Failed to import inspect module: {e}")

# 确保其他相关模块可用
for module_name in ['types', 'collections.abc', 'opcode', 'keyword', 'token', 'tokenize']:
    try:
        __import__(module_name)
        print(f"{module_name} module loaded successfully")
    except ImportError as e:
        print(f"Failed to import {module_name} module: {e}")
