"""
循环发送设置组件

专门用于循环发送页面的循环间隔和工作时间设置。
"""

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QCheckBox,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QSpinBox,
    QVBoxLayout,
    QWidget,
)

from utils.logger import setup_logger

logger = setup_logger("loop_cycle_widget")


class LoopCycleWidget(QGroupBox):
    """循环发送设置组件"""

    # 信号定义
    cycle_enabled_changed = pyqtSignal(bool)  # 循环功能启用状态改变
    config_changed = pyqtSignal()  # 配置改变

    def __init__(self, title: str = "循环间隔设置", parent=None):
        super().__init__(title, parent)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 启用循环设置
        self.enable_cycle_check = QCheckBox("启用循环间隔设置")
        self.enable_cycle_check.setChecked(False)
        layout.addWidget(self.enable_cycle_check)

        # 循环设置容器
        self.cycle_options_widget = QWidget()
        cycle_options_layout = QVBoxLayout(self.cycle_options_widget)

        # 循环间隔设置
        interval_group = QGroupBox("循环间隔")
        interval_group_layout = QVBoxLayout(interval_group)
        
        interval_h_layout = QHBoxLayout()
        interval_h_layout.addWidget(QLabel("间隔时间:"))
        self.cycle_interval_spin = QSpinBox()
        self.cycle_interval_spin.setObjectName("loopCycleIntervalSpinBox")  # 设置对象名称用于样式
        self.cycle_interval_spin.setRange(1, 24)
        self.cycle_interval_spin.setValue(2)  # 默认2小时
        self.cycle_interval_spin.setSuffix(" 小时")
        self.cycle_interval_spin.setMinimumWidth(100)
        interval_h_layout.addWidget(self.cycle_interval_spin)
        interval_h_layout.addStretch()

        interval_group_layout.addLayout(interval_h_layout)
        cycle_options_layout.addWidget(interval_group)

        # 星期选择
        weekday_group = QGroupBox("执行星期")
        weekday_group_layout = QVBoxLayout(weekday_group)
        
        weekday_h_layout = QHBoxLayout()
        weekday_h_layout.setSpacing(10)  # 增加间距
        self.weekday_checks = []
        weekdays = ["一", "二", "三", "四", "五", "六", "日"]
        for i, day in enumerate(weekdays):
            check = QCheckBox(f"周{day}")
            check.setMinimumWidth(55)  # 设置最小宽度
            if i < 5:  # 默认选中工作日
                check.setChecked(True)
            self.weekday_checks.append(check)
            weekday_h_layout.addWidget(check)

        weekday_h_layout.addStretch()
        weekday_group_layout.addLayout(weekday_h_layout)
        cycle_options_layout.addWidget(weekday_group)

        # 工作时间范围设置
        time_range_group = QGroupBox("工作时间")
        time_range_group_layout = QVBoxLayout(time_range_group)
        
        time_h_layout = QHBoxLayout()
        time_h_layout.setSpacing(15)  # 增加间距

        # 开始时间
        time_h_layout.addWidget(QLabel("从"))
        self.start_time_spin = QSpinBox()
        self.start_time_spin.setRange(0, 23)
        self.start_time_spin.setValue(9)  # 默认9点
        self.start_time_spin.setSuffix(" 时")
        self.start_time_spin.setMinimumWidth(80)
        time_h_layout.addWidget(self.start_time_spin)

        # 结束时间
        time_h_layout.addWidget(QLabel("到"))
        self.end_time_spin = QSpinBox()
        self.end_time_spin.setRange(0, 23)
        self.end_time_spin.setValue(18)  # 默认18点
        self.end_time_spin.setSuffix(" 时")
        self.end_time_spin.setMinimumWidth(80)
        time_h_layout.addWidget(self.end_time_spin)

        time_h_layout.addStretch()
        time_range_group_layout.addLayout(time_h_layout)
        cycle_options_layout.addWidget(time_range_group)

        layout.addWidget(self.cycle_options_widget)

        # 默认禁用循环选项
        self.cycle_options_widget.setEnabled(False)

    def connect_signals(self):
        """连接信号"""
        self.enable_cycle_check.stateChanged.connect(self.on_cycle_enabled_changed)
        self.cycle_interval_spin.valueChanged.connect(self.on_config_changed)
        self.start_time_spin.valueChanged.connect(self.on_config_changed)
        self.end_time_spin.valueChanged.connect(self.on_config_changed)

        # 连接星期选择信号
        for check in self.weekday_checks:
            check.stateChanged.connect(self.on_config_changed)

    def on_cycle_enabled_changed(self, state):
        """循环功能启用状态改变"""
        enabled = state == Qt.CheckState.Checked.value
        self.cycle_options_widget.setEnabled(enabled)
        self.cycle_enabled_changed.emit(enabled)

        if enabled:
            self.on_config_changed()

    def on_config_changed(self):
        """配置改变"""
        if self.is_cycle_enabled():
            self.config_changed.emit()

    def is_cycle_enabled(self) -> bool:
        """是否启用循环设置"""
        return self.enable_cycle_check.isChecked()

    def set_cycle_enabled(self, enabled: bool):
        """设置循环功能启用状态"""
        self.enable_cycle_check.setChecked(enabled)

    def get_cycle_config(self) -> dict:
        """获取循环配置"""
        # 获取选中的星期
        selected_weekdays = []
        for i, check in enumerate(self.weekday_checks):
            if check.isChecked():
                selected_weekdays.append(i)

        config = {
            "cycle_interval": self.cycle_interval_spin.value(),
            "selected_weekdays": selected_weekdays,
            "start_time": self.start_time_spin.value(),
            "end_time": self.end_time_spin.value(),
        }

        return config

    def set_cycle_config(self, config: dict):
        """设置循环配置"""
        try:
            # 设置循环间隔
            if "cycle_interval" in config:
                self.cycle_interval_spin.setValue(config["cycle_interval"])

            # 设置选中的星期
            if "selected_weekdays" in config:
                selected_weekdays = config["selected_weekdays"]
                for i, check in enumerate(self.weekday_checks):
                    check.setChecked(i in selected_weekdays)

            # 设置时间范围
            if "start_time" in config:
                self.start_time_spin.setValue(config["start_time"])

            if "end_time" in config:
                self.end_time_spin.setValue(config["end_time"])

            logger.info(f"设置循环配置: {config}")

        except Exception as e:
            logger.error(f"设置循环配置失败: {e}")

    def get_cycle_info_text(self) -> str:
        """获取循环信息文本"""
        if not self.is_cycle_enabled():
            return ""

        config = self.get_cycle_config()

        # 星期名称
        weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        selected_weekday_names = [weekday_names[i] for i in config["selected_weekdays"]]

        cycle_info = (
            f"循环设置:\n"
            f"• 循环间隔: 每 {config['cycle_interval']} 小时\n"
            f"• 执行星期: {', '.join(selected_weekday_names)}\n"
            f"• 工作时间: {config['start_time']:02d}:00 - {config['end_time']:02d}:00"
        )

        return cycle_info

    def validate_config(self) -> tuple:
        """验证配置"""
        if not self.is_cycle_enabled():
            return True, ""

        config = self.get_cycle_config()

        # 检查是否选择了星期
        if not config["selected_weekdays"]:
            return False, "请至少选择一个执行星期"

        # 检查时间范围
        if config["start_time"] >= config["end_time"]:
            return False, "开始时间必须小于结束时间"

        # 检查循环间隔是否合理
        time_range = config["end_time"] - config["start_time"]
        if config["cycle_interval"] > time_range:
            return (
                False,
                f"循环间隔({config['cycle_interval']}小时)不能大于工作时间范围({time_range}小时)",
            )

        return True, ""

    def reset_to_defaults(self):
        """重置为默认值"""
        self.enable_cycle_check.setChecked(False)
        self.cycle_interval_spin.setValue(2)
        self.start_time_spin.setValue(9)
        self.end_time_spin.setValue(18)

        # 默认选中工作日
        for i, check in enumerate(self.weekday_checks):
            check.setChecked(i < 5)

        logger.info("重置循环配置为默认值")

    def get_summary(self) -> str:
        """获取配置摘要"""
        if not self.is_cycle_enabled():
            return "循环设置: 未启用"

        config = self.get_cycle_config()
        weekday_count = len(config["selected_weekdays"])
        time_range = config["end_time"] - config["start_time"]

        return (
            f"循环设置: 每{config['cycle_interval']}小时, "
            f"{weekday_count}个工作日, "
            f"{time_range}小时工作时间"
        )
