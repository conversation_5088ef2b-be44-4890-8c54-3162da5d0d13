# 开发指南

本文档为微信无感群发助手的开发者提供详细的开发指南。

## 开发环境设置

### 1. 系统要求

- **操作系统**: Windows 10/11, macOS 10.15+, Linux
- **Python版本**: Python 3.8+
- **内存**: 建议4GB以上
- **存储**: 至少500MB可用空间

### 2. 环境准备

```bash
# 克隆项目
git clone https://github.com/username/wx-mass-sender.git
cd wx-mass-sender

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装开发依赖
pip install -e .[dev]
```

### 3. IDE配置

推荐使用以下IDE之一：

- **PyCharm**: 专业的Python IDE
- **VS Code**: 轻量级编辑器，配合Python扩展
- **Sublime Text**: 快速的文本编辑器

#### VS Code配置

创建 `.vscode/settings.json`:

```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length=88"],
    "editor.formatOnSave": true,
    "editor.rulers": [88],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".mypy_cache": true,
        ".pytest_cache": true
    }
}
```

## 项目结构

```
wx-mass-sender/
├── main.py                    # 应用程序入口
├── requirements.txt           # 生产依赖
├── pyproject.toml            # 项目配置
├── setup.py                  # 安装脚本
├── run_tests.py              # 测试运行脚本
├── config/                   # 配置模块
│   ├── __init__.py
│   ├── settings.py          # 全局设置
│   └── wechat_config.py     # 微信配置
├── core/                     # 核心业务逻辑
│   ├── __init__.py
│   ├── wechatferry_connector.py  # WeChatFerry连接器
│   ├── http_api_connector.py     # HTTP API连接器
│   ├── message_template.py       # 消息模板
│   ├── send_monitor.py           # 发送监控
│   ├── risk_control.py           # 风控管理
│   └── timed_sender.py           # 定时发送
├── ui/                       # 用户界面
│   ├── __init__.py
│   ├── main_window.py       # 主窗口
│   ├── rich_text_editor.py  # 富文本编辑器
│   ├── dialogs/             # 对话框
│   └── widgets/             # 自定义控件
├── utils/                    # 工具模块
│   ├── __init__.py
│   ├── logger.py            # 日志工具
│   ├── validators.py        # 数据验证
│   ├── exceptions.py        # 异常处理
│   ├── performance.py       # 性能监控
│   └── file_handler.py      # 文件处理
├── tests/                    # 测试文件
│   ├── __init__.py
│   ├── test_validators.py
│   ├── test_config.py
│   └── test_performance.py
├── docs/                     # 文档
│   ├── API.md
│   ├── DEVELOPMENT.md
│   └── user_guide.md
├── resources/                # 资源文件
│   ├── icons/
│   └── templates/
└── logs/                     # 日志文件
```

## 编码规范

### 1. Python代码规范

遵循 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 规范：

- **缩进**: 使用4个空格，不使用Tab
- **行长度**: 最大88字符
- **命名规范**:
  - 变量和函数: `snake_case`
  - 类名: `PascalCase`
  - 常量: `UPPER_CASE`
  - 私有成员: 以单下划线开头 `_private`

### 2. 类型注解

所有公共函数和方法都应该有类型注解：

```python
from typing import List, Optional, Tuple

def process_contacts(
    contacts: List[Contact], 
    filter_active: bool = True
) -> Tuple[List[Contact], int]:
    """
    处理联系人列表
    
    Args:
        contacts: 联系人列表
        filter_active: 是否过滤活跃联系人
        
    Returns:
        (处理后的联系人列表, 处理数量)
    """
    # 实现代码
    pass
```

### 3. 文档字符串

使用Google风格的文档字符串：

```python
def send_message(wxid: str, content: str, message_type: str = "text") -> bool:
    """
    发送消息到指定联系人
    
    Args:
        wxid: 联系人微信ID
        content: 消息内容
        message_type: 消息类型，支持 'text', 'image', 'file'
        
    Returns:
        发送是否成功
        
    Raises:
        ValidationError: 当参数验证失败时
        ConnectionError: 当连接失败时
        
    Example:
        >>> success = send_message("test_user", "Hello World")
        >>> print(success)
        True
    """
    pass
```

### 4. 异常处理

使用具体的异常类型，避免裸露的 `except:`：

```python
from utils.exceptions import ValidationError, ConnectionError

try:
    result = risky_operation()
except ValidationError as e:
    logger.error(f"验证失败: {e}")
    return False
except ConnectionError as e:
    logger.error(f"连接失败: {e}")
    return False
except Exception as e:
    logger.error(f"未知错误: {e}")
    raise
```

## 测试指南

### 1. 测试结构

- **单元测试**: 测试单个函数或方法
- **集成测试**: 测试模块间的交互
- **端到端测试**: 测试完整的用户流程

### 2. 编写测试

使用pytest框架：

```python
import pytest
from unittest.mock import Mock, patch

from core.message_template import MessageTemplate

class TestMessageTemplate:
    """消息模板测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.template = MessageTemplate()
    
    def test_create_template(self):
        """测试创建模板"""
        success = self.template.create_template("test", {
            "type": "text",
            "content": "Hello {{name}}"
        })
        assert success is True
        assert "test" in self.template.templates
    
    @pytest.mark.asyncio
    async def test_async_operation(self):
        """测试异步操作"""
        result = await some_async_function()
        assert result is not None
    
    def test_with_mock(self):
        """使用Mock的测试"""
        with patch('core.message_template.some_function') as mock_func:
            mock_func.return_value = "mocked_result"
            result = self.template.some_method()
            assert result == "mocked_result"
            mock_func.assert_called_once()
```

### 3. 运行测试

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试文件
python run_tests.py tests/test_validators.py

# 运行带覆盖率的测试
python run_tests.py --coverage

# 运行代码检查
python run_tests.py --lint

# 运行类型检查
python run_tests.py --type-check

# 运行所有检查
python run_tests.py --all-checks
```

## 性能优化

### 1. 异步编程

使用异步编程提高性能：

```python
import asyncio
from utils.performance import async_timing_decorator

@async_timing_decorator("batch_send")
async def batch_send_messages(contacts: List[Contact], message: str):
    """批量发送消息"""
    tasks = []
    for contact in contacts:
        task = send_message_async(contact.wxid, message)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

### 2. 内存管理

使用内存管理工具：

```python
from utils.performance import memory_manager, performance_context

def process_large_dataset(data):
    """处理大数据集"""
    with performance_context("process_large_dataset"):
        # 注册大对象到内存管理器
        memory_manager.register_object(data)
        
        # 处理数据
        result = expensive_operation(data)
        
        # 手动触发垃圾回收
        memory_manager.force_gc()
        
        return result
```

### 3. 性能监控

使用性能装饰器：

```python
from utils.performance import timing_decorator

@timing_decorator("database_query")
def query_database(sql: str):
    """数据库查询"""
    # 执行查询
    pass
```

## 调试指南

### 1. 日志配置

```python
from utils.logger import setup_logger

# 创建模块专用日志器
logger = setup_logger("my_module", level="DEBUG")

# 使用不同级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")
```

### 2. 调试技巧

使用Python调试器：

```python
import pdb

def problematic_function():
    # 设置断点
    pdb.set_trace()
    
    # 或者使用更现代的breakpoint()
    breakpoint()
    
    # 代码继续执行
    pass
```

### 3. 性能分析

```python
import cProfile
import pstats

def profile_function():
    """性能分析示例"""
    pr = cProfile.Profile()
    pr.enable()
    
    # 执行需要分析的代码
    expensive_function()
    
    pr.disable()
    
    # 输出分析结果
    stats = pstats.Stats(pr)
    stats.sort_stats('cumulative')
    stats.print_stats(10)  # 显示前10个最耗时的函数
```

## 发布流程

### 1. 版本管理

使用语义化版本号 (Semantic Versioning):

- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 2. 发布检查清单

- [ ] 所有测试通过
- [ ] 代码覆盖率 > 80%
- [ ] 代码检查通过
- [ ] 文档更新
- [ ] CHANGELOG更新
- [ ] 版本号更新

### 3. 构建和分发

```bash
# 清理构建文件
rm -rf build/ dist/ *.egg-info/

# 构建分发包
python setup.py sdist bdist_wheel

# 检查分发包
twine check dist/*

# 上传到PyPI（测试环境）
twine upload --repository-url https://test.pypi.org/legacy/ dist/*

# 上传到PyPI（生产环境）
twine upload dist/*
```

## 贡献指南

### 1. 提交代码

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

### 2. 提交信息规范

使用约定式提交 (Conventional Commits):

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型包括:
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例:
```
feat(core): add batch sending functionality

Add support for sending messages to multiple contacts
in batches with configurable delay between batches.

Closes #123
```

### 3. 代码审查

所有代码更改都需要通过代码审查：

- 功能是否正确实现
- 代码是否遵循项目规范
- 是否有足够的测试覆盖
- 文档是否更新
- 性能是否有影响

## 常见问题

### 1. 依赖冲突

```bash
# 清理pip缓存
pip cache purge

# 重新安装依赖
pip uninstall -y -r requirements.txt
pip install -r requirements.txt
```

### 2. 测试失败

```bash
# 清理测试缓存
rm -rf .pytest_cache/

# 重新运行测试
python run_tests.py --verbose
```

### 3. 性能问题

使用性能分析工具：

```bash
# 生成性能报告
python -m cProfile -o profile.stats main.py

# 分析性能报告
python -c "
import pstats
p = pstats.Stats('profile.stats')
p.sort_stats('cumulative').print_stats(20)
"
```
