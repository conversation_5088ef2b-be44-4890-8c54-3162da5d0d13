"""
基于HTTP API的微信连接器

支持多种微信HTTP API方案：
1. WeChatAPIs/WeChatApi - 商业级方案
2. wxhelper - 开源Hook方案
3. 其他HTTP API方案
"""

import asyncio
import aiohttp
import json
import os
from typing import Dict, List, Optional, Tuple, Any
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QMetaObject, Qt
from dataclasses import dataclass

from utils.logger import setup_logger

logger = setup_logger("http_api_connector")


@dataclass
class Contact:
    """联系人数据类"""

    wxid: str
    name: str
    remark: str = ""
    type: str = "friend"  # friend 或 group
    avatar: str = ""


class HTTPAPIConnector(QObject):
    """基于HTTP API的微信连接器"""

    # 信号定义
    login_success = pyqtSignal(dict)  # 登录成功
    login_failed = pyqtSignal(str)  # 登录失败
    logout = pyqtSignal()  # 登出
    contact_updated = pyqtSignal(list)  # 联系人更新
    connection_lost = pyqtSignal()  # 连接丢失
    message_received = pyqtSignal(dict)  # 收到消息

    def __init__(self, api_type="wxhelper", base_url=None, auto_inject=False):
        # 确保在主线程中创建QObject
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app and QThread.currentThread() != app.thread():
            logger.warning("HTTPAPIConnector应该在主线程中创建")
        super().__init__()

        self.api_type = api_type  # wxhelper, wechatapis, custom
        self.base_url = base_url or "http://localhost:19088"  # 使用默认端口
        self.auto_inject = auto_inject  # 是否启用自动注入
        self.is_connected = False
        self.is_logged_in = False
        self.user_info = {}
        self.contacts = []
        self.session = None

        # 记录创建线程，用于线程安全检查
        self._creation_thread = QThread.currentThread()

        # 基础注入器（仅对wxhelper有效）
        self.injector = None
        # 注入器将在需要时动态创建，而不是在初始化时创建

        # API端点配置
        self.endpoints = self._get_endpoints()

    def _emit_signal_safe(self, signal, *args):
        """线程安全的信号发射"""
        try:
            if QThread.currentThread() == self._creation_thread:
                # 在创建线程中，直接发射信号
                signal.emit(*args)
            else:
                # 在其他线程中，暂时跳过信号发射，避免线程问题
                logger.debug(f"跳过跨线程信号发射: {signal}")
        except Exception as e:
            logger.debug(f"信号发射失败: {e}")

    def _get_endpoints(self) -> Dict[str, str]:
        """获取API端点配置"""
        if self.api_type == "wxhelper":
            # 新版wxhelper使用RESTful风格的API端点
            return {
                "check_login": "/api/checkLogin",
                "user_info": "/api/userInfo",
                "contact_list": "/api/getContactList",
                "send_text": "/api/sendTextMsg",
                "send_at": "/api/sendAtText",
                "send_image": "/api/sendImagesMsg",  # 修正：官方API是sendImagesMsg（带s）
                "send_file": "/api/sendFileMsg",
                "send_xml": "/api/sendXmlMsg",
                "hook_msg": "/api/hookSyncMsg",
                "unhook_msg": "/api/unhookSyncMsg",
                "hook_img": "/api/hookImageMsg",
                "unhook_img": "/api/unhookImageMsg",
                "del_friend": "/api/delFriend",
                "search_friend": "/api/searchFriend",
                "add_friend": "/api/addFriend",
                "get_room_members": "/api/getChatRoomMembers",
                "get_member_nickname": "/api/getMemberNickname",
                "del_member": "/api/delChatRoomMember",
                "add_member": "/api/addChatRoomMember",
                "modify_room_name": "/api/modifyChatRoomName",
                "get_db_handlers": "/api/getDbHandles",
                "query_db": "/api/execSql",
                "forward_msg": "/api/forwardMsg",
                "logout": "/api/logout",
                "confirm_receipt": "/api/confirmReceipt",
                "room_detail": "/api/getChatRoomDetail",
                "ocr": "/api/ocr",
                "pat": "/api/sendPatMsg",
                "top_msg": "/api/topMsg",
                "close_top_msg": "/api/closeTopMsg",
                "sns_first": "/api/getSNSFirstPage",
                "sns_next": "/api/getSNSNextPage",
                "query_nickname": "/api/queryNickname",
                "download_msg_attach": "/api/downloadAttach",
                "get_member_info": "/api/getMemberInfo",
            }
        elif self.api_type == "wechatapis":
            return {
                "check_login": "/api/checkLogin",
                "user_info": "/api/getUserInfo",
                "contact_list": "/api/getContactList",
                "send_text": "/api/sendTextMsg",
                "send_file": "/api/sendFileMsg",
                "send_image": "/api/sendImageMsg",
                "send_xml": "/api/sendXmlMsg",
            }
        else:
            # 自定义API端点
            return {
                "check_login": "/api/check_login",
                "user_info": "/api/user_info",
                "contact_list": "/api/contacts",
                "send_text": "/api/send_text",
                "send_file": "/api/send_file",
                "send_image": "/api/send_image",
                "send_xml": "/api/send_xml",
            }

    async def connect(self) -> Tuple[bool, str]:
        """
        连接到微信HTTP API服务

        Returns:
            (是否成功, 消息)
        """
        try:
            logger.info(f"正在连接微信HTTP API服务: {self.base_url}")

            # 在GUI模式下，不在连接时创建session
            # session将在每次异步调用时动态创建
            self.session = None

            # 检查API服务是否可用
            if not await self._check_api_service():
                # 如果API服务不可用，尝试注入
                if self.api_type == "wxhelper":
                    logger.info("API服务不可用，开始注入wxhelper...")
                    injection_success = await self._handle_injection()
                    if injection_success:
                        # 等待服务启动，增加重试次数
                        logger.info("注入成功，等待API服务启动...")
                        for retry in range(15):  # 最多等待15秒
                            await asyncio.sleep(1)
                            if await self._check_api_service():
                                logger.info("API服务启动成功")
                                break
                            elif retry % 3 == 0:  # 每3秒尝试激活一次
                                await self._try_activate_service()
                        else:
                            # 检查是否是端口问题
                            if await self._check_alternative_ports():
                                logger.warning("API服务在备用端口运行")
                            else:
                                return (
                                    False,
                                    "注入成功但API服务启动超时，请重启微信后重试",
                                )
                    else:
                        return False, "无法连接到微信API服务，注入失败"
                else:
                    return False, "无法连接到微信API服务，请确保wxhelper已注入"

            # 检查微信登录状态
            if not await self._check_login_status():
                return False, "微信未登录，请先登录微信客户端"

            self.is_connected = True
            self.is_logged_in = True

            # 获取用户信息
            user_info = await self._get_user_info()
            if user_info:
                self.user_info = user_info
                # 使用线程安全的方式发射信号
                self._emit_signal_safe(self.login_success, user_info)
                logger.info("微信HTTP API连接成功")
                return True, "连接微信成功"
            else:
                return False, "获取用户信息失败"

        except Exception as e:
            logger.error(f"连接微信失败: {e}")
            if self.session:
                await self.session.close()
                self.session = None
            return False, f"连接失败: {e}"

    async def _ensure_session(self):
        """确保session可用 - GUI模式下每次都重新创建"""
        try:
            # 在GUI应用中，每次异步调用都可能在新的事件循环中
            # 为了避免Event loop is closed错误，每次都重新创建session

            # 先关闭旧的session（如果存在）
            if self.session and not self.session.closed:
                try:
                    await self.session.close()
                except Exception as close_error:
                    logger.debug(f"关闭旧session时出错: {close_error}")

            # 创建新的session
            logger.debug("创建新的HTTP会话")
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10, connect=5)
            )

        except Exception as e:
            logger.error(f"确保session可用失败: {e}")
            # 强制重新创建session
            try:
                if self.session:
                    await self.session.close()
            except:
                pass

            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10, connect=5)
            )

    async def _handle_injection(self, force=False) -> bool:
        """处理注入（仅在用户主动连接时执行）"""
        try:
            # 动态创建注入器
            if not self.injector:
                try:
                    from core.auto_injector import AutoInjector

                    self.injector = AutoInjector()
                    logger.info("注入器已创建")
                except ImportError as e:
                    logger.error(f"无法导入注入器: {e}")
                    return False

            logger.info("使用注入器...")

            # 检查当前注入状态
            if not force:
                is_injected, status_msg = self.injector.check_injection_status()
                if is_injected:
                    logger.info(f"wxhelper已注入: {status_msg}")
                    return True
                else:
                    logger.info(f"wxhelper未注入: {status_msg}")

            # 执行注入
            logger.info("开始注入wxhelper...")

            # 在异步环境中运行同步的注入方法
            loop = asyncio.get_event_loop()
            success, message = await loop.run_in_executor(
                None, self.injector.auto_inject
            )

            if success:
                logger.info(f"注入成功: {message}")
                # 等待API服务启动
                await asyncio.sleep(2)
                return True
            else:
                logger.error(f"注入失败: {message}")
                return False

        except Exception as e:
            logger.error(f"注入处理异常: {e}")
            return False

    async def _try_activate_service(self):
        """尝试激活API服务"""
        try:
            async with aiohttp.ClientSession() as session:
                # 尝试发送一个简单的请求来激活服务
                async with session.get(
                    f"{self.base_url}/", timeout=aiohttp.ClientTimeout(total=1)
                ) as response:
                    pass
        except:
            pass

    async def _check_alternative_ports(self) -> bool:
        """检查备用端口的API服务"""
        alternative_ports = [19089, 19090, 19087]  # 使用固定的备用端口
        for port in alternative_ports:
            try:
                test_url = f"http://localhost:{port}/api/checkLogin"
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        test_url, json={}, timeout=aiohttp.ClientTimeout(total=2)
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            if "code" in data:
                                logger.info(f"在端口{port}找到API服务，更新base_url")
                                self.base_url = f"http://localhost:{port}"
                                return True
            except:
                continue
        return False

    async def _check_api_service(self) -> bool:
        """检查API服务是否可用"""
        session = None
        try:
            # 创建临时session
            session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=5, connect=2)
            )

            url = f"{self.base_url}{self.endpoints['check_login']}"
            # wxhelper API需要使用POST方法
            async with session.post(
                url, json={}, timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                if response.status == 200:
                    try:
                        data = await response.json()
                        # 检查返回的数据格式是否正确
                        if self.api_type == "wxhelper":
                            # wxhelper应该返回包含code字段的JSON
                            return "code" in data and isinstance(data.get("code"), int)
                        else:
                            return True
                    except Exception as json_error:
                        logger.debug(f"解析API响应JSON失败: {json_error}")
                        return False
                return False
        except Exception as e:
            logger.debug(f"API服务检查失败: {e}")
            return False
        finally:
            # 确保session被关闭
            if session and not session.closed:
                try:
                    await session.close()
                except Exception as close_error:
                    logger.debug(f"关闭session时出错: {close_error}")

    async def _check_login_status(self) -> bool:
        """检查微信登录状态"""
        session = None
        try:
            # 创建临时session
            session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=5, connect=2)
            )

            url = f"{self.base_url}{self.endpoints['check_login']}"
            # wxhelper API需要使用POST方法
            async with session.post(
                url, json={}, timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    # 不同API的返回格式可能不同
                    if self.api_type == "wxhelper":
                        return data.get("code") == 1
                    elif self.api_type == "wechatapis":
                        return data.get("success", False)
                    else:
                        return data.get("logged_in", False)
                return False
        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False
        finally:
            # 确保session被关闭
            if session and not session.closed:
                try:
                    await session.close()
                except Exception as close_error:
                    logger.debug(f"关闭session时出错: {close_error}")

    def check_connection_status(self) -> bool:
        """同步检查连接状态"""
        try:
            # 首先检查内存中的状态
            if hasattr(self, "is_connected") and self.is_connected:
                if hasattr(self, "is_logged_in") and self.is_logged_in:
                    logger.debug("内存中的连接状态正常")
                    return True

            # 如果内存状态为False，尝试重新检查
            logger.info("内存中的连接状态为False，尝试重新检查...")
            return False
        except Exception as e:
            logger.error(f"检查连接状态失败: {e}")
            return False

    def force_refresh_connection_status(self) -> bool:
        """强制刷新连接状态（通过API检查）"""
        try:
            import asyncio
            import aiohttp

            # 创建新的事件循环来执行异步检查
            async def check_status():
                try:
                    session = aiohttp.ClientSession(
                        timeout=aiohttp.ClientTimeout(total=3, connect=2)
                    )

                    url = f"{self.base_url}{self.endpoints['check_login']}"
                    async with session.post(
                        url, json={}, timeout=aiohttp.ClientTimeout(total=3)
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            if self.api_type == "wxhelper":
                                is_logged_in = data.get("code") == 1
                            elif self.api_type == "wechatapis":
                                is_logged_in = data.get("success", False)
                            else:
                                is_logged_in = data.get("logged_in", False)

                            # 更新内存中的状态
                            self.is_connected = is_logged_in
                            self.is_logged_in = is_logged_in

                            logger.info(f"强制刷新连接状态: {is_logged_in}")
                            return is_logged_in
                        return False
                except Exception as e:
                    logger.error(f"强制刷新连接状态失败: {e}")
                    return False
                finally:
                    if "session" in locals() and not session.closed:
                        await session.close()

            # 在新线程中运行异步检查
            import threading

            result = [False]

            def run_check():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result[0] = loop.run_until_complete(check_status())
                    loop.close()
                except Exception as e:
                    logger.error(f"运行连接检查失败: {e}")

            thread = threading.Thread(target=run_check)
            thread.start()
            thread.join(timeout=5)  # 最多等待5秒

            return result[0]

        except Exception as e:
            logger.error(f"强制刷新连接状态异常: {e}")
            return False

    async def _get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        session = None
        try:
            # 创建临时session
            session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=5, connect=2)
            )

            url = f"{self.base_url}{self.endpoints['user_info']}"
            # wxhelper API需要使用POST方法
            async with session.post(
                url, json={}, timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                if response.status == 200:
                    data = await response.json()

                    if self.api_type == "wxhelper":
                        if data.get("code") == 1:
                            user_data = data.get("data", {})
                            return {
                                "wxid": user_data.get("wxid", ""),
                                "name": user_data.get("name", ""),
                                "mobile": user_data.get("mobile", ""),
                                "avatar": user_data.get("headImage", ""),
                            }
                    elif self.api_type == "wechatapis":
                        if data.get("success"):
                            user_data = data.get("data", {})
                            return {
                                "wxid": user_data.get("wxid", ""),
                                "name": user_data.get("nickname", ""),
                                "mobile": user_data.get("phone", ""),
                                "avatar": user_data.get("avatar", ""),
                            }

                return None
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None
        finally:
            # 确保session被关闭
            if session and not session.closed:
                try:
                    await session.close()
                except Exception as close_error:
                    logger.debug(f"关闭session时出错: {close_error}")

    async def disconnect(self) -> Tuple[bool, str]:
        """
        断开连接

        Returns:
            (是否成功, 消息)
        """
        try:
            logger.info("正在断开微信连接...")

            if self.session and not self.session.closed:
                try:
                    await self.session.close()
                except Exception as e:
                    logger.warning(f"关闭session时出错: {e}")
                finally:
                    self.session = None

            self.is_connected = False
            self.is_logged_in = False
            self.user_info = {}
            self.contacts = []

            # 使用线程安全的方式发射信号
            self._emit_signal_safe(self.logout)

            logger.info("微信连接已断开")
            return True, "断开成功"

        except Exception as e:
            logger.error(f"断开微信连接失败: {e}")
            return False, str(e)

    async def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        获取当前登录用户信息

        Returns:
            用户信息字典
        """
        if not self.is_connected:
            return None

        return self.user_info

    async def get_self_info(self) -> Optional[Dict[str, Any]]:
        """
        获取自己的信息（get_user_info的别名）

        Returns:
            用户信息字典
        """
        return await self.get_user_info()

    async def get_contacts(self) -> List[Contact]:
        """
        获取联系人列表

        Returns:
            联系人列表
        """
        session = None
        try:
            if not self.is_logged_in:
                logger.warning("未登录，无法获取联系人")
                return []

            logger.info("开始获取微信联系人列表...")

            # 创建临时session
            session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10, connect=5)
            )

            url = f"{self.base_url}{self.endpoints['contact_list']}"
            # wxhelper API需要使用POST方法
            async with session.post(
                url, json={}, timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    contacts = []

                    if self.api_type == "wxhelper":
                        if data.get("code") == 1:
                            contact_list = data.get("data", [])
                            for contact_data in contact_list:
                                # wxhelper API返回的字段名是nickname，不是name
                                contact = Contact(
                                    wxid=contact_data.get("wxid", ""),
                                    name=contact_data.get(
                                        "nickname", contact_data.get("name", "")
                                    ),
                                    remark=contact_data.get("remark", ""),
                                    type=(
                                        "group"
                                        if contact_data.get("wxid", "").endswith(
                                            "@chatroom"
                                        )
                                        else "friend"
                                    ),
                                )
                                contacts.append(contact)

                    elif self.api_type == "wechatapis":
                        if data.get("success"):
                            contact_list = data.get("data", [])
                            for contact_data in contact_list:
                                contact = Contact(
                                    wxid=contact_data.get("wxid", ""),
                                    name=contact_data.get("nickname", ""),
                                    remark=contact_data.get("remark", ""),
                                    type=(
                                        "group"
                                        if contact_data.get("wxid", "").endswith(
                                            "@chatroom"
                                        )
                                        else "friend"
                                    ),
                                )
                                contacts.append(contact)

                    self.contacts = contacts

                    # 统计联系人类型
                    friend_count = len([c for c in contacts if c.type == "friend"])
                    group_count = len([c for c in contacts if c.type == "group"])

                    logger.info(
                        f"成功获取联系人列表: 好友 {friend_count} 个, 群聊 {group_count} 个, 总计 {len(contacts)} 个"
                    )

                    # 发送联系人更新信号（线程安全）
                    self._emit_signal_safe(self.contact_updated, contacts)

                    return contacts
                else:
                    logger.error(f"获取联系人失败，HTTP状态码: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"获取联系人失败: {e}")
            return []
        finally:
            # 确保session被关闭
            if session and not session.closed:
                try:
                    await session.close()
                except Exception as close_error:
                    logger.debug(f"关闭session时出错: {close_error}")

    async def send_text_message(self, wxid: str, content: str) -> bool:
        """
        发送文本消息

        Args:
            wxid: 接收者微信ID
            content: 消息内容

        Returns:
            是否发送成功
        """
        session = None
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 清理消息内容
            clean_content = self._thoroughly_clean_text(content)

            if not clean_content:
                logger.warning("清理后消息内容为空，跳过发送")
                return False

            logger.info(f"发送文本消息到 {wxid}: {clean_content[:50]}...")

            # 创建临时session
            session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10, connect=5)
            )

            url = f"{self.base_url}{self.endpoints['send_text']}"

            # 构造请求数据
            if self.api_type == "wxhelper":
                # wxhelper使用标准格式：{"wxid": "目标ID", "msg": "消息内容"}
                payload = {"wxid": wxid, "msg": clean_content}
            elif self.api_type == "wechatapis":
                payload = {"toUserName": wxid, "content": clean_content}
            else:
                payload = {"to": wxid, "message": clean_content}

            async with session.post(
                url, json=payload, timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    data = await response.json()

                    if self.api_type == "wxhelper":
                        # wxhelper API: code=0表示成功，或者msg='success'
                        success = (
                            data.get("code") == 0
                            or data.get("code") == 1
                            or data.get("msg") == "success"
                        )
                    elif self.api_type == "wechatapis":
                        success = data.get("success", False)
                    else:
                        success = data.get("success", False)

                    if success:
                        logger.info(f"成功发送文本消息到 {wxid}")
                        return True
                    else:
                        logger.error(f"发送文本消息失败: {data}")
                        return False
                else:
                    logger.error(f"发送文本消息失败，HTTP状态码: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"发送文本消息失败: {e}")
            return False
        finally:
            # 确保session被关闭
            if session and not session.closed:
                try:
                    await session.close()
                except Exception as close_error:
                    logger.debug(f"关闭session时出错: {close_error}")

    async def send_file_message(self, wxid: str, file_path: str) -> bool:
        """
        发送文件消息

        Args:
            wxid: 接收者微信ID
            file_path: 文件路径

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            import os

            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return False

            logger.info(f"发送文件到 {wxid}: {file_path}")

            url = f"{self.base_url}{self.endpoints['send_file']}"

            # 构造请求数据
            if self.api_type == "wxhelper":
                # wxhelper使用标准格式：{"wxid": "目标ID", "filePath": "文件路径"}
                payload = {"wxid": wxid, "filePath": file_path}
            elif self.api_type == "wechatapis":
                payload = {"toUserName": wxid, "filePath": file_path}
            else:
                payload = {"to": wxid, "file": file_path}

            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()

                    if self.api_type == "wxhelper":
                        success = data.get("code") == 1
                    elif self.api_type == "wechatapis":
                        success = data.get("success", False)
                    else:
                        success = data.get("success", False)

                    if success:
                        logger.info(f"成功发送文件到 {wxid}")
                        return True
                    else:
                        logger.error(f"发送文件失败: {data}")
                        return False
                else:
                    logger.error(f"发送文件失败，HTTP状态码: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"发送文件消息失败: {e}")
            return False

    async def send_at_message(
        self, chat_room_id: str, wxids: str, content: str
    ) -> bool:
        """
        发送@消息（群聊中@某人）

        Args:
            chat_room_id: 群聊ID
            wxids: 要@的用户ID，多个用逗号分隔，或使用"notify@all"@所有人
            content: 消息内容

        Returns:
            是否发送成功
        """
        session = None
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 清理消息内容
            clean_content = self._thoroughly_clean_text(content)

            if not clean_content:
                logger.warning("清理后消息内容为空，跳过发送")
                return False

            logger.info(
                f"发送@消息到群 {chat_room_id}: @{wxids} {clean_content[:50]}..."
            )

            # 创建临时session
            session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10, connect=5)
            )

            url = f"{self.base_url}{self.endpoints['send_at']}"

            # 构造请求数据
            if self.api_type == "wxhelper":
                # wxhelper使用标准格式：{"chatRoomId": "群ID", "wxids": "用户ID", "msg": "消息"}
                payload = {
                    "chatRoomId": chat_room_id,
                    "wxids": wxids,
                    "msg": clean_content,
                }
            else:
                # 其他API类型暂不支持@消息
                logger.error(f"API类型 {self.api_type} 不支持发送@消息")
                return False

            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()

                    if self.api_type == "wxhelper":
                        # wxhelper API: code=1表示成功
                        success = data.get("code") == 1
                        if success:
                            logger.info(f"@消息发送成功到群 {chat_room_id}")
                        else:
                            error_msg = data.get("msg", "未知错误")
                            logger.error(f"@消息发送失败: {error_msg}")
                    else:
                        success = False

                    return success
                else:
                    logger.error(f"@消息发送请求失败，状态码: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"发送@消息失败: {e}")
            return False
        finally:
            if session:
                try:
                    await session.close()
                except Exception as close_error:
                    logger.debug(f"关闭session时出错: {close_error}")

    async def send_image_message(self, wxid: str, image_path: str) -> bool:
        """
        发送图片消息

        Args:
            wxid: 接收者微信ID
            image_path: 图片文件路径

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 确保session可用
            await self._ensure_session()

            import os

            # 转换为绝对路径
            abs_image_path = os.path.abspath(image_path)

            if not os.path.exists(abs_image_path):
                logger.error(f"图片文件不存在: {abs_image_path} (原路径: {image_path})")
                return False

            # 获取文件大小
            file_size = os.path.getsize(abs_image_path)
            logger.info(f"图片文件大小: {file_size} 字节")

            # 检查是否是图片文件
            image_extensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}
            file_ext = os.path.splitext(image_path)[1].lower()
            if file_ext not in image_extensions:
                logger.warning(f"文件可能不是图片格式: {image_path}")

            logger.info(f"发送图片到 {wxid}: {abs_image_path}")

            url = f"{self.base_url}{self.endpoints['send_image']}"

            # 构造请求数据 - 根据wxhelper官方API文档
            if self.api_type == "wxhelper":
                # wxhelper官方API格式：{"wxid": "filehelper", "imagePath": "C:/123.png"}
                payload = {"wxid": wxid, "imagePath": abs_image_path}
            elif self.api_type == "wechatapis":
                payload = {"toUserName": wxid, "imagePath": abs_image_path}
            else:
                # 统一使用imagePath参数名
                payload = {"wxid": wxid, "imagePath": abs_image_path}

            # 根据wxhelper官方API，只支持JSON方式传递本地文件路径
            try:
                async with self.session.post(url, json=payload) as response:
                    if response.status == 200:
                        try:
                            data = await response.json()

                            if self.api_type == "wxhelper":
                                # wxhelper API: 根据实际测试，msg='success'是最可靠的成功标识
                                # code可能是1或者其他大数字，但msg='success'表示真正成功
                                success = data.get("msg") == "success"
                                if success:
                                    logger.info(f"图片发送成功 (wxhelper API): {data}")
                                else:
                                    logger.warning(
                                        f"图片发送失败 (wxhelper API): {data}"
                                    )
                            elif self.api_type == "wechatapis":
                                success = data.get("success", False)
                            else:
                                success = data.get("success", False)

                            if success:
                                logger.info(f"成功发送图片到 {wxid}")
                                return True
                            else:
                                logger.error(f"发送图片失败: {data}")
                                return False

                        except Exception as json_error:
                            # 如果JSON解析失败，尝试获取文本内容
                            try:
                                response_text = await response.text()
                                logger.error(
                                    f"发送图片失败，服务器返回非JSON格式: {response_text[:200]}..."
                                )
                            except:
                                logger.error(
                                    f"发送图片失败，JSON解析错误: {json_error}"
                                )
                            return False
                    else:
                        logger.error(f"发送图片失败，HTTP状态码: {response.status}")
                        return False
            except Exception as e:
                logger.error(f"发送图片异常: {e}")
                return False

        except Exception as e:
            logger.error(f"发送图片消息失败: {e}")
            return False

    async def send_rich_text_message(self, wxid: str, content: Dict) -> bool:
        """
        发送富文本消息（包含文字和图片的混合消息）

        Args:
            wxid: 接收者微信ID
            content: 富文本内容，包含text和images
                {
                    'text': '文字内容',
                    'images': ['图片路径1', '图片路径2', ...]
                }

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 确保session可用
            await self._ensure_session()

            logger.info(f"发送富文本消息到 {wxid}")

            # 1. 先发送文字部分
            text_content = content.get("text", "").strip()
            text_success = True

            if text_content:
                logger.info(f"发送富文本的文字部分: {text_content[:50]}...")
                text_success = await self.send_text_message(wxid, text_content)
                if not text_success:
                    logger.warning("富文本的文字部分发送失败")

            # 2. 再发送图片部分
            images = content.get("images", [])
            image_success = True
            image_count = len(images)

            if images:
                logger.info(f"发送富文本的图片部分: {image_count}张图片")

                for i, image_data in enumerate(images):
                    # 提取图片路径（支持字典格式和字符串格式）
                    if isinstance(image_data, dict):
                        image_path = image_data.get("path", "")
                    else:
                        image_path = str(image_data)

                    logger.info(f"发送第{i+1}/{image_count}张图片: {image_path}")
                    success = await self.send_image_message(wxid, image_path)
                    if not success:
                        logger.warning(f"第{i+1}张图片发送失败")
                        image_success = False

                    # 图片间添加短暂延迟，避免发送过快
                    if i < image_count - 1:
                        import asyncio

                        await asyncio.sleep(0.5)

            # 只要有一部分成功，就认为富文本发送成功
            return text_success or image_success

        except Exception as e:
            logger.error(f"发送富文本消息失败: {e}")
            return False

    async def send_combined_image_message(self, wxid: str, content: Dict) -> bool:
        """
        发送组合图片消息（将文字和图片合并为一张图片）

        Args:
            wxid: 接收者微信ID
            content: 富文本内容
                {
                    'text': '文字内容',
                    'images': ['图片路径1', '图片路径2', ...]
                }

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 确保session可用
            await self._ensure_session()

            text_content = content.get("text", "").strip()
            images = content.get("images", [])

            # 如果只有文字，直接发送文字消息
            if text_content and not images:
                return await self.send_text_message(wxid, text_content)

            # 如果只有图片，发送第一张图片
            if images and not text_content:
                first_image = images[0]
                if isinstance(first_image, dict):
                    image_path = first_image.get("path", "")
                else:
                    image_path = str(first_image)
                return await self.send_image_message(wxid, image_path)

            # 如果既有文字又有图片，创建组合图片
            if text_content and images:
                logger.info(
                    f"创建组合图片消息: 文字{len(text_content)}字, 图片{len(images)}张"
                )

                # 创建组合图片
                combined_image_path = await self._create_combined_image(
                    text_content, images
                )

                if combined_image_path:
                    # 发送组合图片
                    success = await self.send_image_message(wxid, combined_image_path)

                    # 清理临时文件
                    try:
                        os.remove(combined_image_path)
                    except:
                        pass

                    return success
                else:
                    # 如果组合失败，降级为分别发送
                    logger.warning("组合图片失败，降级为分别发送")
                    text_success = await self.send_text_message(wxid, text_content)

                    # 提取图片路径
                    first_image = images[0]
                    if isinstance(first_image, dict):
                        image_path = first_image.get("path", "")
                    else:
                        image_path = str(first_image)

                    image_success = await self.send_image_message(wxid, image_path)
                    return text_success or image_success

            return False

        except Exception as e:
            logger.error(f"发送组合图片消息失败: {e}")
            return False

    async def _create_combined_image(self, text: str, images: list) -> str:
        """
        创建组合图片（文字+图片）

        Args:
            text: 文字内容
            images: 图片路径列表

        Returns:
            组合图片的路径，失败返回None
        """
        try:
            from PIL import Image, ImageDraw, ImageFont
            import textwrap

            # 设置图片参数
            img_width = 600
            padding = 20
            line_height = 30
            font_size = 20

            # 尝试加载字体
            try:
                font = ImageFont.truetype("msyh.ttc", font_size)  # 微软雅黑
            except:
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    font = ImageFont.load_default()

            # 计算文字区域高度
            lines = textwrap.wrap(text, width=25)  # 每行约25个字符
            text_height = len(lines) * line_height + padding * 2

            # 加载第一张图片
            original_img = None
            if images:
                try:
                    # 提取图片路径（支持字典格式和字符串格式）
                    first_image = images[0]
                    if isinstance(first_image, dict):
                        image_path = first_image.get("path", "")
                    else:
                        image_path = str(first_image)

                    original_img = Image.open(image_path)
                    # 调整图片大小
                    img_ratio = original_img.width / original_img.height
                    if original_img.width > img_width - padding * 2:
                        new_width = img_width - padding * 2
                        new_height = int(new_width / img_ratio)
                        original_img = original_img.resize(
                            (new_width, new_height), Image.Resampling.LANCZOS
                        )
                except Exception as e:
                    logger.warning(f"加载图片失败: {e}")
                    original_img = None

            # 计算总高度
            img_height = 0
            if original_img:
                img_height = original_img.height + padding

            total_height = text_height + img_height + padding

            # 创建新图片
            combined_img = Image.new("RGB", (img_width, total_height), "white")
            draw = ImageDraw.Draw(combined_img)

            # 绘制文字
            y_offset = padding
            for line in lines:
                draw.text((padding, y_offset), line, font=font, fill="black")
                y_offset += line_height

            # 添加图片
            if original_img:
                x_offset = (img_width - original_img.width) // 2
                y_offset = text_height + padding
                combined_img.paste(original_img, (x_offset, y_offset))

            # 保存组合图片
            import tempfile
            import time

            temp_dir = "temp_images"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            timestamp = int(time.time())
            combined_path = os.path.join(temp_dir, f"combined_{timestamp}.png")
            combined_img.save(combined_path, "PNG")

            logger.info(f"组合图片创建成功: {combined_path}")
            return combined_path

        except Exception as e:
            logger.error(f"创建组合图片失败: {e}")
            return None

    async def send_xml_rich_message(self, wxid: str, content: Dict) -> bool:
        """
        发送XML格式的富文本消息

        Args:
            wxid: 接收者微信ID
            content: 富文本内容
                {
                    'text': '文字内容',
                    'images': ['图片路径1', '图片路径2', ...]
                }

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 确保session可用
            await self._ensure_session()

            text_content = content.get("text", "").strip()
            images = content.get("images", [])

            # 构建XML富文本消息
            xml_content = self._build_rich_xml_message(text_content, images)

            if not xml_content:
                logger.error("构建XML富文本消息失败")
                return False

            logger.info(f"发送XML富文本消息到 {wxid}")

            # 使用发送XML消息的API
            url = f"{self.base_url}{self.endpoints['send_xml']}"

            # 构造请求数据
            if self.api_type == "wxhelper":
                payload = {"wxid": wxid, "xml": xml_content}
            elif self.api_type == "wechatapis":
                payload = {"toUserName": wxid, "xml": xml_content}
            else:
                payload = {"to": wxid, "xml": xml_content}

            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()

                    if self.api_type == "wxhelper":
                        success = (
                            data.get("code") == 0
                            or data.get("code") == 1
                            or data.get("msg") == "success"
                        )
                    elif self.api_type == "wechatapis":
                        success = data.get("success", False)
                    else:
                        success = data.get("success", False)

                    if success:
                        logger.info(f"成功发送XML富文本消息到 {wxid}")
                        return True
                    else:
                        logger.warning(f"发送XML富文本消息失败: {data}")
                        # 如果XML发送失败，尝试降级为分别发送
                        return await self._fallback_send_rich_content(wxid, content)
                else:
                    logger.warning(
                        f"发送XML富文本消息失败，HTTP状态码: {response.status}"
                    )
                    # 如果XML发送失败，尝试降级为分别发送
                    return await self._fallback_send_rich_content(wxid, content)

        except Exception as e:
            logger.warning(f"发送XML富文本消息失败: {e}")
            # 如果XML发送失败，尝试降级为分别发送
            return await self._fallback_send_rich_content(wxid, content)

    async def _fallback_send_rich_content(self, wxid: str, content: Dict) -> bool:
        """
        降级发送富文本内容（分别发送文字和图片）

        Args:
            wxid: 接收者微信ID
            content: 富文本内容

        Returns:
            是否发送成功
        """
        try:
            logger.info(f"使用降级方案发送富文本内容到 {wxid}")

            text_content = content.get("text", "").strip()
            images = content.get("images", [])

            success_count = 0
            total_count = 0

            # 发送文字部分
            if text_content:
                total_count += 1
                if await self.send_text_message(wxid, text_content):
                    success_count += 1
                    logger.info("文字部分发送成功")
                else:
                    logger.warning("文字部分发送失败")

                # 文字和图片之间添加间隔
                if images:
                    import asyncio

                    await asyncio.sleep(1.0)

            # 发送图片部分
            for i, image_path in enumerate(images):
                total_count += 1
                logger.info(f"发送第{i+1}/{len(images)}张图片: {image_path}")

                if await self.send_image_message(wxid, image_path):
                    success_count += 1
                    logger.info(f"第{i+1}张图片发送成功")
                else:
                    logger.warning(f"第{i+1}张图片发送失败")

                # 图片间添加间隔
                if i < len(images) - 1:
                    import asyncio

                    await asyncio.sleep(0.5)

            # 只要有一部分成功就认为成功
            success = success_count > 0
            logger.info(f"降级发送完成: {success_count}/{total_count} 成功")

            return success

        except Exception as e:
            logger.error(f"降级发送富文本内容失败: {e}")
            return False

    async def send_markdown_rich_message(self, wxid: str, content: Dict) -> bool:
        """
        发送Markdown格式的富文本消息

        Args:
            wxid: 接收者微信ID
            content: 富文本内容
                {
                    'text': '文字内容',
                    'images': ['图片路径1', '图片路径2', ...]
                }

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 确保session可用
            await self._ensure_session()

            text_content = content.get("text", "").strip()
            images = content.get("images", [])

            # 构建Markdown格式的消息
            markdown_content = self._build_markdown_message(text_content, images)

            if not markdown_content:
                logger.error("构建Markdown消息失败")
                return False

            logger.info(f"发送Markdown富文本消息到 {wxid}")
            logger.info(f"Markdown内容预览: {markdown_content[:200]}...")

            # 使用文本消息API发送Markdown内容
            success = await self.send_text_message(wxid, markdown_content)

            if success:
                logger.info(f"成功发送Markdown富文本消息到 {wxid}")
            else:
                logger.error(f"发送Markdown富文本消息失败")

            return success

        except Exception as e:
            logger.error(f"发送Markdown富文本消息失败: {e}")
            return False

    def _build_markdown_message(self, text: str, images: list) -> str:
        """
        构建Markdown格式的消息

        Args:
            text: 文字内容
            images: 图片路径列表

        Returns:
            Markdown格式的字符串
        """
        try:
            markdown_parts = []

            # 添加标题
            markdown_parts.append("📝 **富文本消息**")
            markdown_parts.append("")  # 空行

            # 添加文字内容
            if text:
                # 处理文字内容，保持段落格式
                text_lines = text.split("\n")
                for line in text_lines:
                    line = line.strip()
                    if line:
                        markdown_parts.append(line)
                    else:
                        markdown_parts.append("")  # 保持空行

                if images:
                    markdown_parts.append("")  # 文字和图片之间的分隔

            # 添加图片信息
            if images:
                markdown_parts.append("🖼️ **包含图片:**")
                for i, image_path in enumerate(images, 1):
                    # 获取图片文件名
                    import os

                    image_name = os.path.basename(image_path)
                    markdown_parts.append(f"{i}. 📷 {image_name}")

                markdown_parts.append("")
                markdown_parts.append("💡 *图片将单独发送*")

            # 添加时间戳
            import time

            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            markdown_parts.append("")
            markdown_parts.append(f"⏰ {timestamp}")

            # 组合所有部分
            markdown_content = "\n".join(markdown_parts)

            logger.info(f"构建的Markdown消息长度: {len(markdown_content)} 字符")
            return markdown_content

        except Exception as e:
            logger.error(f"构建Markdown消息失败: {e}")
            return None

    async def send_markdown_with_images(self, wxid: str, content: Dict) -> bool:
        """
        发送Markdown消息并附带图片

        Args:
            wxid: 接收者微信ID
            content: 富文本内容

        Returns:
            是否发送成功
        """
        try:
            text_content = content.get("text", "").strip()
            images = content.get("images", [])

            success_count = 0
            total_count = 0

            # 1. 先发送Markdown格式的文字消息
            if text_content or images:
                total_count += 1
                if await self.send_markdown_rich_message(wxid, content):
                    success_count += 1
                    logger.info("Markdown消息发送成功")
                else:
                    logger.warning("Markdown消息发送失败")

                # Markdown消息和图片之间添加间隔
                if images:
                    import asyncio

                    await asyncio.sleep(1.5)

            # 2. 再依次发送图片
            for i, image_path in enumerate(images):
                total_count += 1
                logger.info(f"发送第{i+1}/{len(images)}张图片: {image_path}")

                if await self.send_image_message(wxid, image_path):
                    success_count += 1
                    logger.info(f"第{i+1}张图片发送成功")
                else:
                    logger.warning(f"第{i+1}张图片发送失败")

                # 图片间添加间隔
                if i < len(images) - 1:
                    import asyncio

                    await asyncio.sleep(0.8)

            # 只要有一部分成功就认为成功
            success = success_count > 0
            logger.info(f"Markdown富文本发送完成: {success_count}/{total_count} 成功")

            return success

        except Exception as e:
            logger.error(f"发送Markdown富文本消息失败: {e}")
            return False

    async def send_native_rich_message(self, wxid: str, content: Dict) -> bool:
        """
        发送原生富文本消息（模拟微信客户端的发送方式）

        Args:
            wxid: 接收者微信ID
            content: 富文本内容
                {
                    'text': '文字内容',
                    'images': ['图片路径1', '图片路径2', ...]
                }

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 确保session可用
            await self._ensure_session()

            text_content = content.get("text", "").strip()
            images = content.get("images", [])

            # 如果只有文字，直接发送文字消息
            if text_content and not images:
                logger.info("发送纯文字消息")
                return await self.send_text_message(wxid, text_content)

            # 如果只有图片，发送图片消息
            if images and not text_content:
                logger.info("发送纯图片消息")
                if len(images) == 1:
                    return await self.send_image_message(wxid, images[0])
                else:
                    # 多张图片，依次发送
                    success_count = 0
                    for i, image_path in enumerate(images):
                        if await self.send_image_message(wxid, image_path):
                            success_count += 1
                        if i < len(images) - 1:
                            import asyncio

                            await asyncio.sleep(0.3)
                    return success_count > 0

            # 如果既有文字又有图片，尝试发送富文本消息
            if text_content and images:
                logger.info(
                    f"发送图文混合消息: 文字{len(text_content)}字, 图片{len(images)}张"
                )

                # 方案1: 尝试发送HTML格式的富文本消息
                html_success = await self._try_send_html_message(
                    wxid, text_content, images
                )
                if html_success:
                    return True

                # 方案2: 尝试发送带图片的文字消息
                text_with_images_success = await self._try_send_text_with_images(
                    wxid, text_content, images
                )
                if text_with_images_success:
                    return True

                # 方案3: 降级为先发文字再发图片
                logger.info("使用降级方案：先发文字再发图片")
                success_count = 0
                total_count = 0

                # 发送文字
                total_count += 1
                if await self.send_text_message(wxid, text_content):
                    success_count += 1
                    logger.info("文字部分发送成功")

                # 短暂间隔
                import asyncio

                await asyncio.sleep(0.5)

                # 发送图片
                for i, image_path in enumerate(images):
                    total_count += 1
                    if await self.send_image_message(wxid, image_path):
                        success_count += 1
                        logger.info(f"第{i+1}张图片发送成功")

                    if i < len(images) - 1:
                        await asyncio.sleep(0.3)

                return success_count > 0

            return False

        except Exception as e:
            logger.error(f"发送原生富文本消息失败: {e}")
            return False

    async def _try_send_html_message(self, wxid: str, text: str, images: list) -> bool:
        """
        尝试发送HTML格式的富文本消息
        """
        try:
            # 构建HTML内容
            html_content = self._build_html_content(text, images)

            # 尝试使用HTML消息API
            url = f"{self.base_url}/api/sendHtmlMsg"

            payload = {"wxid": wxid, "html": html_content}

            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    success = (
                        data.get("code") == 0
                        or data.get("code") == 1
                        or data.get("msg") == "success"
                    )

                    if success:
                        logger.info("HTML富文本消息发送成功")
                        return True
                    else:
                        logger.debug(f"HTML消息发送失败: {data}")
                        return False
                else:
                    logger.debug(f"HTML消息API不可用，状态码: {response.status}")
                    return False

        except Exception as e:
            logger.debug(f"HTML消息发送异常: {e}")
            return False

    async def _try_send_text_with_images(
        self, wxid: str, text: str, images: list
    ) -> bool:
        """
        尝试发送带图片的文字消息（内联图片）
        """
        try:
            # 构建包含图片引用的文字内容
            content_with_images = self._build_text_with_image_refs(text, images)

            # 尝试使用富文本消息API
            url = f"{self.base_url}/api/sendRichTextMsg"

            payload = {"wxid": wxid, "content": content_with_images, "images": images}

            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    success = (
                        data.get("code") == 0
                        or data.get("code") == 1
                        or data.get("msg") == "success"
                    )

                    if success:
                        logger.info("富文本消息发送成功")
                        return True
                    else:
                        logger.debug(f"富文本消息发送失败: {data}")
                        return False
                else:
                    logger.debug(f"富文本消息API不可用，状态码: {response.status}")
                    return False

        except Exception as e:
            logger.debug(f"富文本消息发送异常: {e}")
            return False

    def _build_html_content(self, text: str, images: list) -> str:
        """构建HTML内容"""
        try:
            import base64

            html_parts = []
            html_parts.append(
                '<div style="font-family: Arial, sans-serif; line-height: 1.4;">'
            )

            # 添加文字内容
            if text:
                # 处理换行
                text_lines = text.split("\n")
                for line in text_lines:
                    if line.strip():
                        html_parts.append(f"<p>{line}</p>")
                    else:
                        html_parts.append("<br>")

            # 添加图片
            for i, image_path in enumerate(images):
                try:
                    with open(image_path, "rb") as f:
                        image_data = f.read()
                        # 限制图片大小
                        if len(image_data) < 500 * 1024:  # 小于500KB
                            image_base64 = base64.b64encode(image_data).decode("utf-8")
                            # 检测图片格式
                            if image_path.lower().endswith(".png"):
                                mime_type = "image/png"
                            elif image_path.lower().endswith(
                                ".jpg"
                            ) or image_path.lower().endswith(".jpeg"):
                                mime_type = "image/jpeg"
                            else:
                                mime_type = "image/png"

                            html_parts.append(
                                f'<img src="data:{mime_type};base64,{image_base64}" style="max-width: 100%; height: auto; margin: 5px 0;" />'
                            )
                        else:
                            html_parts.append(f"<p>[图片{i+1}: 文件过大]</p>")
                except Exception as e:
                    logger.warning(f"处理图片{i+1}失败: {e}")
                    html_parts.append(f"<p>[图片{i+1}: 加载失败]</p>")

            html_parts.append("</div>")

            return "".join(html_parts)

        except Exception as e:
            logger.error(f"构建HTML内容失败: {e}")
            return text

    def _build_text_with_image_refs(self, text: str, images: list) -> str:
        """构建包含图片引用的文字内容"""
        try:
            content_parts = []

            if text:
                content_parts.append(text)

            if images:
                content_parts.append("")  # 空行分隔
                for i, image_path in enumerate(images, 1):
                    import os

                    image_name = os.path.basename(image_path)
                    content_parts.append(f"[图片{i}: {image_name}]")

            return "\n".join(content_parts)

        except Exception as e:
            logger.error(f"构建图片引用文字失败: {e}")
            return text

    async def send_formatted_rich_message(self, wxid: str, content: Dict) -> bool:
        """
        发送格式化的富文本消息（保持编辑器格式）

        Args:
            wxid: 接收者微信ID
            content: 富文本内容
                {
                    'html': 'HTML内容',
                    'plain_text': '纯文本内容',
                    'images': ['图片路径1', '图片路径2', ...]
                }

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 确保session可用
            await self._ensure_session()

            html_content = content.get("html", "")
            text_content = content.get("plain_text", "").strip()
            images = content.get("images", [])

            logger.info(f"发送格式化富文本消息到 {wxid}")
            logger.info(f"内容: 文字{len(text_content)}字, 图片{len(images)}张")

            # 方案1: 尝试发送微信富文本消息（文字+图片分离但作为一条消息）
            rich_success = await self._try_send_wechat_rich_message(
                wxid, text_content, images
            )
            if rich_success:
                logger.info("微信富文本消息发送成功")
                return True

            # 方案2: 尝试发送HTML富文本消息
            if html_content:
                html_success = await self._try_send_formatted_html(
                    wxid, html_content, images
                )
                if html_success:
                    logger.info("HTML富文本消息发送成功")
                    return True

            # 方案3: 降级为原生富文本发送
            logger.info("使用原生富文本发送方案")
            return await self.send_native_rich_message(
                wxid, {"text": text_content, "images": images}
            )

        except Exception as e:
            logger.error(f"发送格式化富文本消息失败: {e}")
            return False

    async def _try_send_wechat_rich_message(
        self, wxid: str, text: str, images: list
    ) -> bool:
        """
        尝试发送微信富文本消息（文字和图片作为一条消息）
        """
        try:
            # 彻底清理文字内容，移除所有图片占位符
            clean_text = self._thoroughly_clean_text(text)

            if not clean_text and not images:
                logger.warning("清理后没有可发送的内容")
                return False

            # 构建富文本消息数据
            rich_data = {
                "wxid": wxid,
                "type": "rich",
                "content": {"text": clean_text, "images": []},
            }

            # 处理图片数据
            import base64

            for i, image_path in enumerate(images):
                try:
                    if isinstance(image_path, dict):
                        actual_path = image_path.get("path", "")
                    else:
                        actual_path = str(image_path)

                    if actual_path:
                        with open(actual_path, "rb") as f:
                            image_data = f.read()

                        # 限制图片大小
                        if len(image_data) < 2 * 1024 * 1024:  # 小于2MB
                            image_base64 = base64.b64encode(image_data).decode("utf-8")

                            # 获取文件名和扩展名
                            import os

                            filename = os.path.basename(actual_path)

                            rich_data["content"]["images"].append(
                                {
                                    "name": filename,
                                    "data": image_base64,
                                    "size": len(image_data),
                                }
                            )

                            logger.debug(
                                f"添加图片到富文本: {filename} ({len(image_data)} 字节)"
                            )
                        else:
                            logger.warning(f"图片过大，跳过: {actual_path}")

                except Exception as e:
                    logger.warning(f"处理图片失败: {image_path}, {e}")
                    continue

            # 尝试多种富文本API端点
            api_endpoints = [
                "/api/sendRichTextMsg",
                "/api/sendMixedMsg",
                "/api/sendMultiMediaMsg",
            ]

            for endpoint in api_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"

                    async with self.session.post(url, json=rich_data) as response:
                        if response.status == 200:
                            data = await response.json()
                            success = (
                                data.get("code") == 0
                                or data.get("code") == 1
                                or data.get("msg") == "success"
                            )

                            if success:
                                logger.info(f"微信富文本消息发送成功 (使用 {endpoint})")
                                return True
                            else:
                                logger.debug(f"{endpoint} 发送失败: {data}")
                        else:
                            logger.debug(
                                f"{endpoint} 不可用，状态码: {response.status}"
                            )

                except Exception as e:
                    logger.debug(f"{endpoint} 发送异常: {e}")
                    continue

            return False

        except Exception as e:
            logger.debug(f"微信富文本消息发送异常: {e}")
            return False

    def _thoroughly_clean_text(self, text: str) -> str:
        """
        彻底清理文字内容，移除所有图片占位符和无效字符
        """
        try:
            if not text:
                return ""

            # 移除各种可能的图片占位符
            cleaned = text

            # Unicode对象替换字符
            cleaned = cleaned.replace("\ufffc", "")

            # 其他可能的占位符
            cleaned = cleaned.replace("\ufeff", "")  # 零宽度无断空格
            cleaned = cleaned.replace("\u200b", "")  # 零宽度空格
            cleaned = cleaned.replace("\u200c", "")  # 零宽度非连接符
            cleaned = cleaned.replace("\u200d", "")  # 零宽度连接符

            # 移除多余的空白字符
            import re

            cleaned = re.sub(r"\s+", " ", cleaned)  # 多个空格合并为一个
            cleaned = re.sub(r"\n\s*\n", "\n", cleaned)  # 多个换行合并

            # 去除首尾空白
            cleaned = cleaned.strip()

            logger.debug(f"文字清理: '{text[:50]}...' -> '{cleaned[:50]}...'")
            return cleaned

        except Exception as e:
            logger.error(f"清理文字失败: {e}")
            return text.replace("\ufffc", "").strip() if text else ""

    async def send_itchat_style_rich_message(self, wxid: str, content: Dict) -> bool:
        """
        使用itchat风格发送富文本消息（严格按照用户编辑器中的顺序发送）

        Args:
            wxid: 接收者微信ID
            content: 富文本内容
                {
                    'html': 'HTML内容',
                    'plain_text': '纯文本内容',
                    'images': ['图片路径1', '图片路径2', ...],
                    'send_parts': [(文字内容, 图片路径), ...] # 可选，按顺序的发送部分
                }

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            # 确保session可用
            await self._ensure_session()

            # 优先使用send_parts（按顺序的发送部分）
            send_parts = content.get("send_parts", [])

            if not send_parts:
                # 回退到原有逻辑
                text_content = content.get("plain_text", "").strip()
                images = content.get("images", [])
                clean_text = self._thoroughly_clean_text(text_content)

                # 构建简单的发送部分
                send_parts = []
                if clean_text:
                    send_parts.append((clean_text, None))
                for image_info in images:
                    if isinstance(image_info, dict):
                        image_path = image_info.get("path", "")
                    else:
                        image_path = str(image_info)
                    if image_path:
                        send_parts.append((None, image_path))

            logger.info(f"使用itchat风格发送富文本消息到 {wxid}")
            logger.info(f"共{len(send_parts)}个发送部分，严格按照用户编辑器中的顺序")

            success_count = 0
            total_count = len(send_parts)

            # 按顺序发送每个部分
            for i, (text_content, image_path) in enumerate(send_parts):
                part_num = i + 1

                if text_content:
                    # 发送文字内容
                    logger.info(
                        f"第{part_num}/{total_count}部分: 发送文字内容 ({len(text_content)}字)"
                    )
                    logger.debug(f"文字内容: {text_content[:100]}...")

                    if await self.send_text_message(wxid, text_content):
                        success_count += 1
                        logger.info(f"第{part_num}部分文字发送成功")
                    else:
                        logger.warning(f"第{part_num}部分文字发送失败")

                elif image_path:
                    # 发送图片
                    image_name = (
                        os.path.basename(image_path) if image_path else "unknown"
                    )
                    logger.info(
                        f"第{part_num}/{total_count}部分: 发送图片 ({image_name})"
                    )

                    if await self.send_image_message(wxid, image_path):
                        success_count += 1
                        logger.info(f"第{part_num}部分图片发送成功")
                    else:
                        logger.warning(f"第{part_num}部分图片发送失败")

                # 部分之间的短暂间隔（模拟用户手动发送的节奏）
                if i < len(send_parts) - 1:
                    import asyncio

                    await asyncio.sleep(0.3)

            # 计算成功率
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0
            logger.info(
                f"按顺序富文本发送完成: {success_count}/{total_count} 成功 ({success_rate:.1f}%)"
            )

            # 只要有一部分成功就认为整体成功
            return success_count > 0

        except Exception as e:
            logger.error(f"按顺序富文本发送失败: {e}")
            return False

    async def _try_send_formatted_html(
        self, wxid: str, html_content: str, images: list
    ) -> bool:
        """
        尝试发送格式化的HTML消息
        """
        try:
            # 处理HTML内容，嵌入图片
            processed_html = self._process_html_with_images(html_content, images)

            # 尝试多种HTML消息API
            api_endpoints = [
                "/api/sendHtmlMsg",
                "/api/sendRichMsg",
                "/api/sendFormattedMsg",
            ]

            for endpoint in api_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"

                    payload = {"wxid": wxid, "content": processed_html, "type": "html"}

                    async with self.session.post(url, json=payload) as response:
                        if response.status == 200:
                            data = await response.json()
                            success = (
                                data.get("code") == 0
                                or data.get("code") == 1
                                or data.get("msg") == "success"
                            )

                            if success:
                                logger.info(f"HTML消息发送成功 (使用 {endpoint})")
                                return True
                            else:
                                logger.debug(f"{endpoint} 发送失败: {data}")
                        else:
                            logger.debug(
                                f"{endpoint} 不可用，状态码: {response.status}"
                            )

                except Exception as e:
                    logger.debug(f"{endpoint} 发送异常: {e}")
                    continue

            return False

        except Exception as e:
            logger.debug(f"格式化HTML发送异常: {e}")
            return False

    async def _try_send_rich_card(self, wxid: str, text: str, images: list) -> bool:
        """
        尝试发送富文本卡片消息
        """
        try:
            # 构建富文本卡片XML
            card_xml = self._build_rich_card_xml(text, images)

            # 尝试发送卡片消息
            url = f"{self.base_url}/api/sendCardMsg"

            payload = {"wxid": wxid, "xml": card_xml}

            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    success = (
                        data.get("code") == 0
                        or data.get("code") == 1
                        or data.get("msg") == "success"
                    )

                    if success:
                        logger.info("富文本卡片消息发送成功")
                        return True
                    else:
                        logger.debug(f"卡片消息发送失败: {data}")
                        return False
                else:
                    logger.debug(f"卡片消息API不可用，状态码: {response.status}")
                    return False

        except Exception as e:
            logger.debug(f"富文本卡片发送异常: {e}")
            return False

    def _process_html_with_images(self, html_content: str, images: list) -> str:
        """
        处理HTML内容，移除图片占位符并准备图片数据
        """
        try:
            import re

            # 移除图片占位符字符
            processed_html = html_content.replace("\ufffc", "")

            # 移除img标签，因为微信可能不支持内联图片
            img_pattern = r"<img[^>]*>"
            processed_html = re.sub(img_pattern, "", processed_html)

            # 清理空的段落标签
            processed_html = re.sub(r"<p>\s*</p>", "", processed_html)
            processed_html = re.sub(r"<div>\s*</div>", "", processed_html)

            # 如果有图片，在文字后添加图片提示
            if images:
                processed_html += f"<br><small>📷 包含 {len(images)} 张图片</small>"

            logger.debug(f"处理后的HTML: {processed_html[:200]}...")
            return processed_html

        except Exception as e:
            logger.error(f"处理HTML图片失败: {e}")
            return html_content.replace("\ufffc", "")

    def _build_rich_card_xml(self, text: str, images: list) -> str:
        """
        构建富文本卡片XML
        """
        try:
            import xml.etree.ElementTree as ET
            import base64
            import time

            # 创建根元素
            msg = ET.Element("msg")

            # 创建appmsg元素
            appmsg = ET.SubElement(msg, "appmsg")
            appmsg.set("appid", "")
            appmsg.set("sdkver", "0")

            # 添加标题
            title = ET.SubElement(appmsg, "title")
            title.text = "富文本消息"

            # 添加描述
            des = ET.SubElement(appmsg, "des")
            des.text = text[:100] + "..." if len(text) > 100 else text

            # 添加类型 (5=链接消息, 可以包含富文本)
            type_elem = ET.SubElement(appmsg, "type")
            type_elem.text = "5"

            # 添加内容 (HTML格式)
            content = ET.SubElement(appmsg, "content")
            html_content = f"""
            <div style="font-family: Arial, sans-serif; line-height: 1.4; padding: 10px;">
                <div style="margin-bottom: 10px;">{text}</div>
            """

            # 添加图片
            for i, image_path in enumerate(images):
                try:
                    if isinstance(image_path, dict):
                        actual_path = image_path.get("path", "")
                    else:
                        actual_path = str(image_path)

                    if actual_path:
                        with open(actual_path, "rb") as f:
                            image_data = f.read()

                        if len(image_data) < 500 * 1024:  # 小于500KB
                            image_base64 = base64.b64encode(image_data).decode("utf-8")

                            if actual_path.lower().endswith(".png"):
                                mime_type = "image/png"
                            elif actual_path.lower().endswith((".jpg", ".jpeg")):
                                mime_type = "image/jpeg"
                            else:
                                mime_type = "image/png"

                            html_content += f'<img src="data:{mime_type};base64,{image_base64}" style="max-width: 100%; height: auto; margin: 5px 0;" />'
                        else:
                            html_content += f"<p>[图片{i+1}: 文件过大]</p>"

                except Exception as e:
                    logger.warning(f"处理卡片图片{i+1}失败: {e}")
                    html_content += f"<p>[图片{i+1}: 加载失败]</p>"

            html_content += "</div>"
            content.text = f"<![CDATA[{html_content}]]>"

            # 添加其他必要元素
            url_elem = ET.SubElement(appmsg, "url")
            url_elem.text = ""

            # 添加缩略图
            if images:
                try:
                    first_image = images[0]
                    if isinstance(first_image, dict):
                        first_path = first_image.get("path", "")
                    else:
                        first_path = str(first_image)

                    if first_path:
                        with open(first_path, "rb") as f:
                            thumb_data = f.read()

                        if len(thumb_data) < 32 * 1024:  # 小于32KB
                            thumb_base64 = base64.b64encode(thumb_data).decode("utf-8")
                            thumbdata = ET.SubElement(appmsg, "thumbdata")
                            thumbdata.text = thumb_base64

                except Exception as e:
                    logger.warning(f"处理缩略图失败: {e}")

            # 转换为字符串
            xml_str = ET.tostring(msg, encoding="unicode")

            logger.debug(f"构建的富文本卡片XML: {xml_str[:200]}...")
            return xml_str

        except Exception as e:
            logger.error(f"构建富文本卡片XML失败: {e}")
            return ""

    def _build_rich_xml_message(self, text: str, images: list) -> str:
        """
        构建富文本XML消息

        Args:
            text: 文字内容
            images: 图片路径列表

        Returns:
            XML字符串
        """
        try:
            import xml.etree.ElementTree as ET
            import base64
            import time

            # 创建根元素
            msg = ET.Element("msg")

            # 添加基本属性
            msg.set("bigheadimgurl", "")
            msg.set("smallheadimgurl", "")
            msg.set("username", "")
            msg.set("nickname", "")
            msg.set("shortpy", "")
            msg.set("imagestatus", "3")
            msg.set("scene", "17")
            msg.set("country", "CN")
            msg.set("province", "")
            msg.set("city", "")
            msg.set("sign", "")
            msg.set("sex", "1")
            msg.set("certflag", "0")
            msg.set("certinfo", "")
            msg.set("brandIconUrl", "")
            msg.set("brandHomeUrl", "")
            msg.set("brandSubscriptConfigUrl", "")
            msg.set("brandFlags", "0")
            msg.set("regionCode", "")

            # 创建appmsg元素
            appmsg = ET.SubElement(msg, "appmsg")
            appmsg.set("appid", "")
            appmsg.set("sdkver", "0")

            # 添加标题和描述
            title = ET.SubElement(appmsg, "title")
            title.text = "富文本消息"

            des = ET.SubElement(appmsg, "des")
            des.text = text if text else "图片消息"

            # 添加动作和类型
            action = ET.SubElement(appmsg, "action")
            action.text = "view"

            type_elem = ET.SubElement(appmsg, "type")
            type_elem.text = "5"  # 类型5表示链接消息

            # 添加显示应用信息
            showtype = ET.SubElement(appmsg, "showtype")
            showtype.text = "0"

            # 添加内容
            content = ET.SubElement(appmsg, "content")
            content.text = ""

            # 添加URL
            url_elem = ET.SubElement(appmsg, "url")
            url_elem.text = ""

            # 添加缩略图
            if images:
                thumburl = ET.SubElement(appmsg, "thumburl")
                thumburl.text = ""

                # 尝试读取第一张图片作为缩略图
                try:
                    with open(images[0], "rb") as f:
                        image_data = f.read()
                        # 限制缩略图大小
                        if len(image_data) < 32 * 1024:  # 小于32KB
                            thumb_base64 = base64.b64encode(image_data).decode("utf-8")

                            thumbdata = ET.SubElement(appmsg, "thumbdata")
                            thumbdata.text = thumb_base64
                except Exception as e:
                    logger.warning(f"读取缩略图失败: {e}")

            # 添加其他必要元素
            lowurl = ET.SubElement(appmsg, "lowurl")
            lowurl.text = ""

            dataurl = ET.SubElement(appmsg, "dataurl")
            dataurl.text = ""

            lowdataurl = ET.SubElement(appmsg, "lowdataurl")
            lowdataurl.text = ""

            # 添加应用信息
            appattach = ET.SubElement(appmsg, "appattach")
            totallen = ET.SubElement(appattach, "totallen")
            totallen.text = "0"

            attachid = ET.SubElement(appattach, "attachid")
            attachid.text = ""

            emoticonmd5 = ET.SubElement(appattach, "emoticonmd5")
            emoticonmd5.text = ""

            fileext = ET.SubElement(appattach, "fileext")
            fileext.text = ""

            # 添加来源显示名称
            sourcedisplayname = ET.SubElement(appmsg, "sourcedisplayname")
            sourcedisplayname.text = ""

            # 添加评论URL
            commenturl = ET.SubElement(appmsg, "commenturl")
            commenturl.text = ""

            # 添加来源图标和URL
            sourceusername = ET.SubElement(appmsg, "sourceusername")
            sourceusername.text = ""

            sourceiconurl = ET.SubElement(appmsg, "sourceiconurl")
            sourceiconurl.text = ""

            # 添加MD5
            md5_elem = ET.SubElement(appmsg, "md5")
            md5_elem.text = ""

            # 添加微信版本
            weappinfo = ET.SubElement(appmsg, "weappinfo")
            pagepath = ET.SubElement(weappinfo, "pagepath")
            pagepath.text = ""

            username = ET.SubElement(weappinfo, "username")
            username.text = ""

            appid = ET.SubElement(weappinfo, "appid")
            appid.text = ""

            # 转换为字符串
            xml_str = ET.tostring(msg, encoding="unicode")

            logger.info(f"构建的XML消息: {xml_str[:200]}...")
            return xml_str

        except Exception as e:
            logger.error(f"构建XML富文本消息失败: {e}")
            return None
