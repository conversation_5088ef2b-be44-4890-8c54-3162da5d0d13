#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控面板

提供实时性能监控、资源使用情况显示、优化建议等功能。
"""

import time
from typing import Dict, Any

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QProgressBar,
    QGroupBox,
    QGridLayout,
    QTextEdit,
    QTabWidget,
)
from PyQt6.QtCore import QTimer, pyqtSignal
from PyQt6.QtGui import QFont

from utils.logger import setup_logger
from utils.performance_optimizer import performance_optimizer
from ui.ui_optimizer import ui_optimizer

logger = setup_logger("performance_panel")


class PerformancePanel(QWidget):
    """性能监控面板"""

    # 信号定义
    optimization_requested = pyqtSignal(str)  # 优化请求

    def __init__(self):
        super().__init__()
        self.setWindowTitle("性能监控")
        self.setMinimumSize(600, 400)

        # 初始化UI
        self.init_ui()

        # 启动监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_performance_data)
        self.monitor_timer.start(2000)  # 每2秒更新一次

        logger.info("性能监控面板初始化完成")

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 实时监控标签页
        self.create_realtime_tab()

        # 统计信息标签页
        self.create_statistics_tab()

        # 优化建议标签页
        self.create_optimization_tab()

        # 控制按钮
        self.create_control_buttons(layout)

    def create_realtime_tab(self):
        """创建实时监控标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 内存使用组
        memory_group = QGroupBox("内存使用")
        memory_layout = QGridLayout(memory_group)

        # 内存使用进度条
        self.memory_progress = QProgressBar()
        self.memory_progress.setMaximum(100)
        self.memory_label = QLabel("内存: 0 MB")

        memory_layout.addWidget(QLabel("当前使用:"), 0, 0)
        memory_layout.addWidget(self.memory_progress, 0, 1)
        memory_layout.addWidget(self.memory_label, 0, 2)

        # 内存详细信息
        self.memory_details = QLabel("RSS: 0 MB | VMS: 0 MB | 可用: 0 MB")
        memory_layout.addWidget(self.memory_details, 1, 0, 1, 3)

        layout.addWidget(memory_group)

        # CPU使用组
        cpu_group = QGroupBox("CPU使用")
        cpu_layout = QGridLayout(cpu_group)

        # CPU使用进度条
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setMaximum(100)
        self.cpu_label = QLabel("CPU: 0%")

        cpu_layout.addWidget(QLabel("进程CPU:"), 0, 0)
        cpu_layout.addWidget(self.cpu_progress, 0, 1)
        cpu_layout.addWidget(self.cpu_label, 0, 2)

        # 系统CPU
        self.system_cpu_progress = QProgressBar()
        self.system_cpu_progress.setMaximum(100)
        self.system_cpu_label = QLabel("系统CPU: 0%")

        cpu_layout.addWidget(QLabel("系统CPU:"), 1, 0)
        cpu_layout.addWidget(self.system_cpu_progress, 1, 1)
        cpu_layout.addWidget(self.system_cpu_label, 1, 2)

        layout.addWidget(cpu_group)

        # 响应性监控组
        response_group = QGroupBox("响应性监控")
        response_layout = QGridLayout(response_group)

        self.ui_freeze_count = QLabel("UI冻结次数: 0")
        self.last_freeze_time = QLabel("最后冻结: 无")
        self.avg_response_time = QLabel("平均响应时间: 0ms")

        response_layout.addWidget(self.ui_freeze_count, 0, 0)
        response_layout.addWidget(self.last_freeze_time, 0, 1)
        response_layout.addWidget(self.avg_response_time, 1, 0, 1, 2)

        layout.addWidget(response_group)

        self.tab_widget.addTab(tab, "实时监控")

    def create_statistics_tab(self):
        """创建统计信息标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 性能统计组
        stats_group = QGroupBox("性能统计")
        stats_layout = QGridLayout(stats_group)

        self.startup_time_label = QLabel("启动时间: 0s")
        self.memory_peak_label = QLabel("内存峰值: 0 MB")
        self.cpu_peak_label = QLabel("CPU峰值: 0%")
        self.total_operations_label = QLabel("总操作数: 0")

        stats_layout.addWidget(self.startup_time_label, 0, 0)
        stats_layout.addWidget(self.memory_peak_label, 0, 1)
        stats_layout.addWidget(self.cpu_peak_label, 1, 0)
        stats_layout.addWidget(self.total_operations_label, 1, 1)

        layout.addWidget(stats_group)

        # 发送统计组
        send_group = QGroupBox("发送统计")
        send_layout = QGridLayout(send_group)

        self.total_sent_label = QLabel("总发送数: 0")
        self.send_speed_label = QLabel("发送速度: 0条/秒")
        self.success_rate_label = QLabel("成功率: 0%")

        send_layout.addWidget(self.total_sent_label, 0, 0)
        send_layout.addWidget(self.send_speed_label, 0, 1)
        send_layout.addWidget(self.success_rate_label, 1, 0, 1, 2)

        layout.addWidget(send_group)

        # 详细日志
        log_group = QGroupBox("性能日志")
        log_layout = QVBoxLayout(log_group)

        self.performance_log = QTextEdit()
        self.performance_log.setMaximumHeight(150)
        self.performance_log.setReadOnly(True)

        log_layout.addWidget(self.performance_log)
        layout.addWidget(log_group)

        self.tab_widget.addTab(tab, "统计信息")

    def create_optimization_tab(self):
        """创建优化建议标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 优化建议组
        advice_group = QGroupBox("优化建议")
        advice_layout = QVBoxLayout(advice_group)

        self.optimization_advice = QTextEdit()
        self.optimization_advice.setReadOnly(True)
        advice_layout.addWidget(self.optimization_advice)

        layout.addWidget(advice_group)

        # 快速优化按钮
        quick_group = QGroupBox("快速优化")
        quick_layout = QGridLayout(quick_group)

        self.memory_cleanup_btn = QPushButton("清理内存")
        self.memory_cleanup_btn.clicked.connect(
            lambda: self.optimization_requested.emit("memory_cleanup")
        )

        self.cache_clear_btn = QPushButton("清理缓存")
        self.cache_clear_btn.clicked.connect(
            lambda: self.optimization_requested.emit("cache_clear")
        )

        self.gc_force_btn = QPushButton("强制垃圾回收")
        self.gc_force_btn.clicked.connect(
            lambda: self.optimization_requested.emit("force_gc")
        )

        quick_layout.addWidget(self.memory_cleanup_btn, 0, 0)
        quick_layout.addWidget(self.cache_clear_btn, 0, 1)
        quick_layout.addWidget(self.gc_force_btn, 1, 0, 1, 2)

        layout.addWidget(quick_group)

        self.tab_widget.addTab(tab, "优化建议")

    def create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.update_performance_data)

        self.export_btn = QPushButton("导出报告")
        self.export_btn.clicked.connect(self.export_performance_report)

        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)

        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def update_performance_data(self):
        """更新性能数据"""
        try:
            # 获取内存信息
            memory_info = performance_optimizer.memory_manager.get_memory_usage()
            if memory_info:
                rss_mb = memory_info.get("rss", 0)
                vms_mb = memory_info.get("vms", 0)
                available_mb = memory_info.get("available", 0)
                percent = memory_info.get("percent", 0)

                self.memory_progress.setValue(int(percent))
                self.memory_label.setText(f"内存: {rss_mb:.1f} MB")
                self.memory_details.setText(
                    f"RSS: {rss_mb:.1f} MB | VMS: {vms_mb:.1f} MB | 可用: {available_mb:.1f} MB"
                )

            # 获取CPU信息
            cpu_info = performance_optimizer.cpu_optimizer.get_cpu_usage()
            if cpu_info:
                process_cpu = cpu_info.get("process_cpu", 0)
                system_cpu = cpu_info.get("system_cpu", 0)

                self.cpu_progress.setValue(int(process_cpu))
                self.cpu_label.setText(f"CPU: {process_cpu:.1f}%")

                self.system_cpu_progress.setValue(int(system_cpu))
                self.system_cpu_label.setText(f"系统CPU: {system_cpu:.1f}%")

            # 获取性能统计
            stats = performance_optimizer.get_performance_stats()
            if stats:
                self.memory_peak_label.setText(
                    f"内存峰值: {stats.get('memory_peak', 0):.1f} MB"
                )
                self.cpu_peak_label.setText(f"CPU峰值: {stats.get('cpu_peak', 0):.1f}%")

            # 更新优化建议
            self.update_optimization_advice()

        except Exception as e:
            logger.error(f"更新性能数据失败: {e}")

    def update_optimization_advice(self):
        """更新优化建议"""
        try:
            advice = []

            # 检查内存使用
            memory_info = performance_optimizer.memory_manager.get_memory_usage()
            if memory_info:
                rss_mb = memory_info.get("rss", 0)
                if rss_mb > 700:
                    advice.append("⚠️ 内存使用过高，建议清理内存或重启程序")
                elif rss_mb > 500:
                    advice.append("💡 内存使用较高，建议定期清理缓存")

            # 检查CPU使用
            cpu_info = performance_optimizer.cpu_optimizer.get_cpu_usage()
            if cpu_info:
                process_cpu = cpu_info.get("process_cpu", 0)
                if process_cpu > 50:
                    advice.append("⚠️ CPU使用过高，建议减少并发操作")
                elif process_cpu > 30:
                    advice.append("💡 CPU使用较高，建议优化发送频率")

            # 通用建议
            if not advice:
                advice.append("✅ 当前性能状态良好")
                advice.append("💡 建议定期清理内存以保持最佳性能")
                advice.append("💡 大批量发送时建议使用批量模式")

            self.optimization_advice.setText("\n".join(advice))

        except Exception as e:
            logger.error(f"更新优化建议失败: {e}")

    def export_performance_report(self):
        """导出性能报告"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.txt"

            # 收集性能数据
            memory_info = performance_optimizer.memory_manager.get_memory_usage()
            cpu_info = performance_optimizer.cpu_optimizer.get_cpu_usage()
            stats = performance_optimizer.get_performance_stats()

            # 生成报告
            report = f"""
性能监控报告
生成时间: {time.strftime("%Y-%m-%d %H:%M:%S")}

=== 内存使用 ===
RSS: {memory_info.get('rss', 0):.1f} MB
VMS: {memory_info.get('vms', 0):.1f} MB
使用率: {memory_info.get('percent', 0):.1f}%
可用内存: {memory_info.get('available', 0):.1f} MB

=== CPU使用 ===
进程CPU: {cpu_info.get('process_cpu', 0):.1f}%
系统CPU: {cpu_info.get('system_cpu', 0):.1f}%
CPU核心数: {cpu_info.get('cpu_count', 0)}

=== 性能统计 ===
内存峰值: {stats.get('memory_peak', 0):.1f} MB
CPU峰值: {stats.get('cpu_peak', 0):.1f}%
UI冻结次数: {stats.get('ui_freezes', 0)}

=== 优化建议 ===
{self.optimization_advice.toPlainText()}
"""

            # 保存报告
            with open(filename, "w", encoding="utf-8") as f:
                f.write(report)

            logger.info(f"性能报告已导出: {filename}")

        except Exception as e:
            logger.error(f"导出性能报告失败: {e}")


# 全局性能面板实例
performance_panel = None


def show_performance_panel():
    """显示性能监控面板"""
    global performance_panel

    if performance_panel is None:
        performance_panel = PerformancePanel()

    performance_panel.show()
    performance_panel.raise_()
    performance_panel.activateWindow()

    return performance_panel
