#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能调度功能
"""

import sys
import time
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_timing_scheduler():
    """测试定时发送的智能调度"""
    print("🕐 测试定时发送智能调度")
    print("=" * 50)
    
    try:
        from core.timing_sender import timing_sender
        
        # 创建测试任务
        print("创建测试任务...")
        
        # 任务1: 30秒后执行
        future_time_1 = datetime.now() + timedelta(seconds=30)
        task1 = timing_sender.create_task(
            group_id="test_group_1",
            scheduled_time=future_time_1,
            message_content="测试消息1 - 30秒后执行",
            message_type="text"
        )
        
        # 任务2: 2分钟后执行
        future_time_2 = datetime.now() + timedelta(minutes=2)
        task2 = timing_sender.create_task(
            group_id="test_group_2", 
            scheduled_time=future_time_2,
            message_content="测试消息2 - 2分钟后执行",
            message_type="text"
        )
        
        # 任务3: 10分钟后执行
        future_time_3 = datetime.now() + timedelta(minutes=10)
        task3 = timing_sender.create_task(
            group_id="test_group_3",
            scheduled_time=future_time_3, 
            message_content="测试消息3 - 10分钟后执行",
            message_type="text"
        )
        
        if task1 and task2 and task3:
            print("✅ 测试任务创建成功")
            print(f"   任务1: {task1.task_id} - {future_time_1}")
            print(f"   任务2: {task2.task_id} - {future_time_2}")
            print(f"   任务3: {task3.task_id} - {future_time_3}")
            
            # 初始化定时器
            timing_sender.init_timer()
            
            print("\n观察智能调度日志...")
            print("程序将根据最近任务时间智能调整检查间隔")
            print("按 Ctrl+C 停止测试")
            
            # 等待并观察调度
            try:
                for i in range(60):  # 观察1分钟
                    time.sleep(1)
                    if i % 10 == 0:
                        print(f"已观察 {i} 秒...")
            except KeyboardInterrupt:
                print("\n用户停止测试")
            
            # 清理测试任务
            print("\n清理测试任务...")
            timing_sender.cancel_task(task1.task_id)
            timing_sender.cancel_task(task2.task_id) 
            timing_sender.cancel_task(task3.task_id)
            print("✅ 测试任务已清理")
            
        else:
            print("❌ 测试任务创建失败")
            
    except Exception as e:
        print(f"❌ 定时发送调度测试失败: {e}")

def test_loop_scheduler():
    """测试循环发送的智能调度"""
    print("\n🔄 测试循环发送智能调度")
    print("=" * 50)
    
    try:
        from core.loop_sender import loop_sender
        
        # 创建测试任务
        print("创建循环测试任务...")
        
        # 循环任务1: 每1分钟执行一次
        task1 = loop_sender.create_task(
            group_id="test_loop_group_1",
            interval_minutes=1,
            message_content="循环测试消息1 - 每1分钟",
            message_type="text"
        )
        
        # 循环任务2: 每5分钟执行一次
        task2 = loop_sender.create_task(
            group_id="test_loop_group_2",
            interval_minutes=5,
            message_content="循环测试消息2 - 每5分钟",
            message_type="text"
        )
        
        if task1 and task2:
            print("✅ 循环测试任务创建成功")
            print(f"   任务1: {task1.task_id} - 每{task1.interval_minutes}分钟")
            print(f"   任务2: {task2.task_id} - 每{task2.interval_minutes}分钟")
            
            # 启动任务
            loop_sender.start_task(task1.task_id)
            loop_sender.start_task(task2.task_id)
            
            # 初始化定时器
            loop_sender.init_timer()
            
            print("\n观察循环任务智能调度日志...")
            print("程序将根据最近发送时间智能调整检查间隔")
            print("按 Ctrl+C 停止测试")
            
            # 等待并观察调度
            try:
                for i in range(60):  # 观察1分钟
                    time.sleep(1)
                    if i % 10 == 0:
                        print(f"已观察 {i} 秒...")
            except KeyboardInterrupt:
                print("\n用户停止测试")
            
            # 清理测试任务
            print("\n清理循环测试任务...")
            loop_sender.stop_task(task1.task_id)
            loop_sender.stop_task(task2.task_id)
            loop_sender.delete_task(task1.task_id)
            loop_sender.delete_task(task2.task_id)
            print("✅ 循环测试任务已清理")
            
        else:
            print("❌ 循环测试任务创建失败")
            
    except Exception as e:
        print(f"❌ 循环发送调度测试失败: {e}")

def show_scheduling_comparison():
    """显示调度方式对比"""
    print("\n📊 智能调度 vs 固定间隔对比")
    print("=" * 60)
    
    print("🔴 原来的固定间隔方式:")
    print("   - 定时发送: 每10秒检查一次")
    print("   - 循环发送: 每30秒检查一次")
    print("   - 问题: 浪费资源，检查频率不合理")
    
    print("\n🟢 新的智能调度方式:")
    print("   - 根据最近任务时间动态调整")
    print("   - 任务快到时提前检查")
    print("   - 无任务时延长检查间隔")
    
    print("\n📋 智能调度规则:")
    print("   定时发送:")
    print("     • 1分钟内: 提前20%检查")
    print("     • 5分钟内: 30秒检查一次")
    print("     • 1小时内: 1分钟检查一次")
    print("     • 1小时以上: 5分钟检查一次")
    print("     • 无任务: 60秒检查一次")
    
    print("\n   循环发送:")
    print("     • 1分钟内: 提前10%检查")
    print("     • 5分钟内: 30秒检查一次")
    print("     • 30分钟内: 1分钟检查一次")
    print("     • 30分钟以上: 5分钟检查一次")
    print("     • 无任务: 2分钟检查一次")

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 智能调度测试")
    print("=" * 70)
    
    # 显示调度方式对比
    show_scheduling_comparison()
    
    # 询问用户是否要进行实际测试
    print("\n" + "=" * 70)
    choice = input("是否要进行实际测试？(y/n): ").lower().strip()
    
    if choice == 'y':
        print("\n开始智能调度测试...")
        
        # 测试定时发送调度
        test_timing_scheduler()
        
        # 测试循环发送调度
        test_loop_scheduler()
        
        print("\n🎉 智能调度测试完成！")
        print("✨ 优势:")
        print("  - 任务快到时及时检查")
        print("  - 无任务时减少检查频率")
        print("  - 节省系统资源")
        print("  - 提高响应精度")
        
    else:
        print("\n跳过实际测试")
    
    print("\n📋 智能调度已启用:")
    print("  ✅ 定时发送: 根据最近任务时间智能调度")
    print("  ✅ 循环发送: 根据最近发送时间智能调度")
    print("  ✅ 日志会显示具体的调度决策")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
