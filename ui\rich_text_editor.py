#!/usr/bin/env python3
"""
富文本消息编辑器
支持文字图片混合输入和粘贴功能
"""

import os
import time
import uuid
from ui.themed_message_box import ThemedMessageBoxHelper
from ui.themed_dialog_base import ThemedDialogBase
from typing import List, Dict, Tuple, Optional, Any

from PyQt6.QtWidgets import (
    QTextEdit,
    QApplication,
    QMessageBox,
    QVBoxLayout,
    QHBoxLayout,
    QPushButton,
    QLabel,
    QDialog,
    QWidget,
)
from PyQt6.QtGui import (
    QTextCursor,
    QTextDocument,
    QPixmap,
    QClipboard,
    QTextImageFormat,
    QDragEnterEvent,
    QDropEvent,
    QKeyEvent,
    QTextCharFormat,
)
from PyQt6.QtCore import Qt, QUrl, pyqtSignal, QTimer

from utils.logger import setup_logger
from utils.error_handler import handle_rich_text_error, safe_call

logger = setup_logger("rich_text_editor")


class RichTextMessageEditor(QTextEdit):
    """富文本消息编辑器"""

    # 信号定义
    content_changed = pyqtSignal()  # 内容变化信号
    image_inserted = pyqtSignal(str)  # 图片插入信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptRichText(True)
        self.setPlaceholderText(
            "输入消息内容，支持文字和图片混合...\n\n"
            "快捷操作：\n"
            "• Ctrl+V 粘贴图片或文字\n"
            "• 拖拽图片文件到此处\n"
            "• 支持 PNG、JPG、GIF 等格式"
        )

        # 临时图片存储目录
        self.temp_image_dir = "temp_images"
        if not os.path.exists(self.temp_image_dir):
            os.makedirs(self.temp_image_dir)

        # 存储插入的图片路径
        self.inserted_images: List[str] = []

        # 设置拖拽支持
        self.setAcceptDrops(True)

        # 连接文本变化信号
        self.textChanged.connect(self.on_text_changed)

        # 连接文档内容变化信号
        self.document().contentsChanged.connect(self.on_document_changed)

        # 标准化图片记录格式
        self.normalize_image_records()

        logger.info("富文本编辑器初始化完成")

    def keyPressEvent(self, event: QKeyEvent):
        """处理键盘事件"""
        # Ctrl+V 粘贴
        if (
            event.key() == Qt.Key.Key_V
            and event.modifiers() == Qt.KeyboardModifier.ControlModifier
        ):
            self.paste_from_clipboard()
        else:
            # 记录删除操作前的图片数量
            old_image_count = len(self.inserted_images)

            # 执行默认的键盘事件处理
            super().keyPressEvent(event)

            # 如果是删除键，检查图片是否被删除
            if event.key() in [Qt.Key.Key_Delete, Qt.Key.Key_Backspace]:
                # 延迟检查图片列表，确保DOM更新完成
                QTimer.singleShot(100, self.check_image_deletion)

    def paste_from_clipboard(self):
        """从剪贴板粘贴内容"""
        try:
            clipboard = QApplication.clipboard()
            mime_data = clipboard.mimeData()

            if mime_data.hasImage():
                # 粘贴图片
                self.insert_image_from_clipboard()
            elif mime_data.hasText():
                # 粘贴文本
                self.insertPlainText(mime_data.text())
                logger.info("从剪贴板粘贴文本")
            else:
                logger.warning("剪贴板中没有支持的内容")

        except Exception as e:
            logger.error(f"从剪贴板粘贴失败: {e}")
            ThemedMessageBoxHelper.show_warning(
                self, "粘贴失败", f"从剪贴板粘贴内容失败:\n{e}"
            )

    def insert_image_from_clipboard(self):
        """从剪贴板插入图片"""
        try:
            clipboard = QApplication.clipboard()
            image = clipboard.image()

            if not image.isNull():
                # 生成临时文件名
                timestamp = int(time.time())
                image_id = str(uuid.uuid4())[:8]
                temp_filename = f"clipboard_image_{timestamp}_{image_id}.png"
                temp_path = os.path.join(self.temp_image_dir, temp_filename)

                # 保存图片
                if image.save(temp_path):
                    self.insert_image_file(temp_path)
                    logger.info(f"成功粘贴图片: {temp_path}")
                else:
                    logger.error("保存剪贴板图片失败")
                    ThemedMessageBoxHelper.show_warning(
                        self, "粘贴失败", "保存剪贴板图片失败"
                    )
            else:
                logger.warning("剪贴板中的图片无效")
                ThemedMessageBoxHelper.show_warning(
                    self, "粘贴失败", "剪贴板中没有有效的图片"
                )

        except Exception as e:
            logger.error(f"从剪贴板插入图片失败: {e}")
            ThemedMessageBoxHelper.show_warning(
                self, "插入失败", f"从剪贴板插入图片失败:\n{e}"
            )

    def insert_image_file(self, image_path: str):
        """插入图片文件"""
        try:
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                ThemedMessageBoxHelper.show_warning(
                    self, "插入失败", f"图片文件不存在:\n{image_path}"
                )
                return

            # 创建图片格式
            image_format = QTextImageFormat()
            image_format.setName(image_path)

            # 设置图片大小
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # 限制图片显示大小
                max_width = 300
                max_height = 200

                if pixmap.width() > max_width or pixmap.height() > max_height:
                    scaled_pixmap = pixmap.scaled(
                        max_width,
                        max_height,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation,
                    )
                    image_format.setWidth(scaled_pixmap.width())
                    image_format.setHeight(scaled_pixmap.height())
                else:
                    image_format.setWidth(pixmap.width())
                    image_format.setHeight(pixmap.height())

            # 插入图片
            cursor = self.textCursor()
            cursor.insertImage(image_format)

            # 记录插入的图片
            image_info = {
                "path": image_path,
                "name": os.path.basename(image_path),
                "timestamp": int(time.time()),
            }

            # 检查是否已存在（避免重复）
            path_exists = False
            for existing_image in self.inserted_images:
                if (
                    isinstance(existing_image, dict)
                    and existing_image.get("path") == image_path
                ):
                    path_exists = True
                    break
                elif isinstance(existing_image, str) and existing_image == image_path:
                    path_exists = True
                    break

            if not path_exists:
                self.inserted_images.append(image_info)

            # 在图片后添加换行
            cursor.insertText("\n")

            # 发送信号
            self.image_inserted.emit(image_path)

            logger.info(f"成功插入图片: {image_path}")

        except Exception as e:
            logger.error(f"插入图片文件失败: {e}")
            ThemedMessageBoxHelper.show_warning(
                self, "插入失败", f"插入图片文件失败:\n{e}"
            )

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        try:
            if event.mimeData().hasUrls():
                # 检查是否包含图片文件
                for url in event.mimeData().urls():
                    file_path = url.toLocalFile()
                    if self.is_image_file(file_path):
                        event.acceptProposedAction()
                        return
            event.ignore()
        except Exception as e:
            logger.error(f"处理拖拽进入事件失败: {e}")
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        try:
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if self.is_image_file(file_path):
                    self.insert_image_file(file_path)
            event.acceptProposedAction()
        except Exception as e:
            logger.error(f"处理拖拽放下事件失败: {e}")

    def is_image_file(self, file_path: str) -> bool:
        """检查是否为图片文件"""
        image_extensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp", ".tiff"]
        return any(file_path.lower().endswith(ext) for ext in image_extensions)

    def on_text_changed(self):
        """文本变化时的处理"""
        # 检查并更新图片列表
        self.update_image_list()
        self.content_changed.emit()

    def on_document_changed(self):
        """文档内容变化时的处理"""
        # 延迟检查图片列表，确保DOM更新完成
        QTimer.singleShot(50, self.check_and_update_images)

    def update_image_list(self):
        """更新图片列表，移除已删除的图片"""
        try:
            # 获取当前HTML内容
            html_content = self.toHtml()

            # 使用更精确的方法检查图片
            existing_images = []
            for image_info in self.inserted_images:
                # 确保image_info是字典类型
                if not isinstance(image_info, dict):
                    logger.warning(f"跳过非字典类型的图片信息: {image_info}")
                    continue

                image_path = image_info.get("path", "")
                image_name = image_info.get("name", "")

                # 检查图片是否还在HTML中
                image_exists = False

                # 方法1: 检查完整路径
                if image_path and image_path in html_content:
                    image_exists = True

                # 方法2: 检查文件名
                if not image_exists and image_name and image_name in html_content:
                    image_exists = True

                # 方法3: 检查src属性中的路径
                if not image_exists and image_path:
                    import re

                    # 查找所有img标签的src属性
                    img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
                    img_matches = re.findall(img_pattern, html_content)
                    for src in img_matches:
                        if image_path in src or (image_name and image_name in src):
                            image_exists = True
                            break

                if image_exists:
                    existing_images.append(image_info)
                else:
                    logger.info(f"检测到图片被删除: {image_path or image_name}")

            # 更新图片列表
            old_count = len(self.inserted_images)
            new_count = len(existing_images)

            if old_count != new_count:
                self.inserted_images = existing_images
                logger.info(f"图片列表已更新: {old_count} -> {new_count}")
                return True  # 返回True表示有变化

            return False  # 返回False表示无变化

        except Exception as e:
            logger.error(f"更新图片列表失败: {e}")
            return False

    def check_image_deletion(self):
        """检查图片删除（延迟调用）"""
        try:
            old_count = len(self.inserted_images)
            self.update_image_list()
            new_count = len(self.inserted_images)

            if old_count != new_count:
                logger.info(f"图片数量变化: {old_count} -> {new_count}")
                # 强制触发内容变化信号
                self.content_changed.emit()

        except Exception as e:
            logger.error(f"检查图片删除失败: {e}")

    def check_and_update_images(self):
        """检查并更新图片列表"""
        try:
            # 使用返回值判断是否有变化
            has_changes = self.update_image_list()

            if has_changes:
                logger.info(f"图片列表已更新，当前数量: {len(self.inserted_images)}")
                # 强制触发内容变化信号
                self.content_changed.emit()

        except Exception as e:
            logger.error(f"检查和更新图片失败: {e}")

    def normalize_image_records(self):
        """标准化图片记录格式"""
        try:
            normalized_images = []
            for image_info in self.inserted_images:
                if isinstance(image_info, str):
                    # 将字符串转换为字典格式
                    normalized_images.append(
                        {
                            "path": image_info,
                            "name": os.path.basename(image_info),
                            "timestamp": int(time.time()),
                        }
                    )
                elif isinstance(image_info, dict):
                    # 确保字典包含必要的键
                    normalized_info = {
                        "path": image_info.get("path", ""),
                        "name": image_info.get("name", ""),
                        "timestamp": image_info.get("timestamp", int(time.time())),
                    }
                    normalized_images.append(normalized_info)

            self.inserted_images = normalized_images
            logger.debug(f"标准化了 {len(normalized_images)} 个图片记录")

        except Exception as e:
            logger.error(f"标准化图片记录失败: {e}")

    @handle_rich_text_error
    def get_message_content(self) -> Dict:
        """获取消息内容"""
        # 获取原始内容
        raw_html = self.toHtml()
        raw_text = self.toPlainText()

        # 清理图片占位符
        clean_text = raw_text.replace("\ufffc", "").strip()
        clean_html = self._clean_html_content(raw_html)

        return {
            "type": "rich_text",
            "html": clean_html,
            "plain_text": clean_text,
            "images": self.inserted_images.copy(),
        }

    def get_template_data(self) -> Dict:
        """获取模板数据（用于保存模板）"""
        try:
            content = self.get_message_content()

            # 构建模板数据
            template_data = {
                "name": "",  # 模板名称，由调用者设置
                "content": content["plain_text"],
                "html": content["html"],
                "images": [],
            }

            # 处理图片数据
            for image_info in content["images"]:
                if isinstance(image_info, dict):
                    image_path = image_info.get("path", "")
                    image_name = image_info.get("name", "")
                else:
                    image_path = str(image_info)
                    import os

                    image_name = os.path.basename(image_path) if image_path else ""

                if image_path:
                    template_data["images"].append(
                        {"path": image_path, "name": image_name}
                    )

            logger.debug(
                f"获取模板数据: 文字{len(template_data['content'])}字, 图片{len(template_data['images'])}张"
            )
            return template_data

        except Exception as e:
            logger.error(f"获取模板数据失败: {e}")
            return {"name": "", "content": "", "html": "", "images": []}

    def _clean_html_content(self, html_content: str) -> str:
        """清理HTML内容中的图片占位符"""
        try:
            import re

            # 移除图片占位符字符
            cleaned = html_content.replace("\ufffc", "")

            # 移除空的img标签或无效的img标签
            cleaned = re.sub(r"<img[^>]*>", "", cleaned)

            # 清理空的段落
            cleaned = re.sub(r"<p>\s*</p>", "", cleaned)
            cleaned = re.sub(r"<p></p>", "", cleaned)

            # 清理多余的空白
            cleaned = re.sub(r"\n\s*\n", "\n", cleaned)

            logger.debug(f"HTML清理完成: {len(html_content)} -> {len(cleaned)} 字符")
            return cleaned

        except Exception as e:
            logger.error(f"清理HTML内容失败: {e}")
            return html_content.replace("\ufffc", "")

    def clear_content(self):
        """清空内容"""
        self.clear()
        self.inserted_images.clear()
        logger.info("清空编辑器内容")

    def get_content_summary(self) -> str:
        """获取内容摘要"""
        text_length = len(self.toPlainText().strip())
        image_count = len(self.inserted_images)

        if text_length > 0 and image_count > 0:
            return f"混合消息 (文字{text_length}字, 图片{image_count}张)"
        elif text_length > 0:
            return f"文字消息 ({text_length}字)"
        elif image_count > 0:
            return f"图片消息 ({image_count}张)"
        else:
            return "空消息"

    @handle_rich_text_error
    def has_content(self) -> bool:
        """检查是否有内容"""
        return len(self.toPlainText().strip()) > 0 or len(self.inserted_images) > 0

    def load_template_content(self, template_data: Dict):
        """
        加载模板内容到编辑器

        Args:
            template_data: 模板数据字典，支持以下格式：
                - content: 纯文字内容
                - html: HTML内容（富文本）
                - plain_text: 纯文本内容（富文本格式）
                - images: 图片列表
        """
        if not template_data:
            logger.warning("模板数据为空，跳过加载")
            return

        try:
            # 清空当前内容
            self.clear_content()

            # 统计加载信息
            loaded_items = []

            # 优先级1: 加载HTML内容（富文本）
            if template_data.get("html"):
                self.setHtml(template_data["html"])
                loaded_items.append(f"HTML内容({len(template_data['html'])}字符)")

            # 优先级2: 加载纯文本内容（富文本格式）
            elif template_data.get("plain_text"):
                self.setPlainText(template_data["plain_text"])
                loaded_items.append(f"纯文本({len(template_data['plain_text'])}字符)")

            # 优先级3: 加载传统content字段
            elif template_data.get("content"):
                self.setPlainText(template_data["content"])
                loaded_items.append(f"文字内容({len(template_data['content'])}字符)")

            # 加载图片（如果有）
            images = template_data.get("images", [])
            if images:
                loaded_images = 0
                for image_info in images:
                    try:
                        image_path = self._extract_image_path(image_info)
                        if image_path and os.path.exists(image_path):
                            self.insert_image_file(image_path)
                            loaded_images += 1
                            logger.debug(f"加载模板图片: {os.path.basename(image_path)}")
                        else:
                            logger.warning(f"图片文件不存在或路径无效: {image_path}")
                    except Exception as img_error:
                        logger.error(f"加载单个图片失败: {img_error}")

                if loaded_images > 0:
                    loaded_items.append(f"图片({loaded_images}/{len(images)}张)")

            # 记录加载结果
            if loaded_items:
                logger.info(f"模板内容加载完成: {', '.join(loaded_items)}")
            else:
                logger.warning("模板内容为空，没有加载任何内容")

        except Exception as e:
            logger.error(f"加载模板内容失败: {e}")
            # 降级处理：尝试加载基本文字内容
            self._fallback_load_content(template_data)

    def _extract_image_path(self, image_info) -> str:
        """
        从图片信息中提取图片路径

        Args:
            image_info: 图片信息，可能是字符串或字典

        Returns:
            图片路径字符串
        """
        if isinstance(image_info, dict):
            return image_info.get("path", "")
        elif isinstance(image_info, str):
            return image_info
        else:
            logger.warning(f"不支持的图片信息格式: {type(image_info)}")
            return ""

    def _fallback_load_content(self, template_data: Dict):
        """
        降级加载内容（仅文字）

        Args:
            template_data: 模板数据
        """
        try:
            # 尝试加载任何可用的文字内容
            content_candidates = [
                template_data.get("content", ""),
                template_data.get("plain_text", ""),
                str(template_data.get("html", ""))
            ]

            for content in content_candidates:
                if content and content.strip():
                    self.setPlainText(content)
                    logger.info(f"降级加载文字内容成功: {len(content)}字符")
                    break
            else:
                logger.warning("降级加载失败：没有找到有效的文字内容")

        except Exception as e:
            logger.error(f"降级加载内容失败: {e}")

    def get_send_parts(self) -> List[Tuple[str, Optional[str]]]:
        """
        获取发送部分列表，严格按照用户在编辑器中的排列顺序
        返回: [(文字内容, 图片路径), ...]
        """
        try:
            # 使用新的方法按顺序解析内容
            return self._parse_content_in_order()

        except Exception as e:
            logger.error(f"获取发送部分失败: {e}")
            return []

    def _parse_content_in_order(self) -> List[Tuple[str, Optional[str]]]:
        """
        按照用户在富文本编辑器中的实际顺序解析内容
        返回: [(文字内容, 图片路径), ...]
        """
        try:
            parts = []

            # 获取QTextDocument
            document = self.document()

            # 遍历文档的所有块（段落）
            block = document.begin()
            current_text_parts = []

            while block.isValid():
                # 获取块的迭代器
                block_iterator = block.begin()

                while not block_iterator.atEnd():
                    fragment = block_iterator.fragment()

                    if fragment.isValid():
                        # 检查是否是图片
                        char_format = fragment.charFormat()

                        if char_format.isImageFormat():
                            # 这是一个图片
                            image_format = char_format.toImageFormat()
                            image_name = image_format.name()

                            # 如果之前有文字内容，先添加文字部分
                            if current_text_parts:
                                text_content = "".join(current_text_parts).strip()
                                if text_content:
                                    parts.append((text_content, None))
                                    logger.debug(
                                        f"添加文字部分: {text_content[:50]}..."
                                    )
                                current_text_parts = []

                            # 查找对应的图片路径
                            image_path = self._find_image_path_by_name(image_name)
                            if image_path:
                                parts.append((None, image_path))
                                logger.debug(f"添加图片部分: {image_path}")
                            else:
                                logger.warning(f"未找到图片路径: {image_name}")
                        else:
                            # 这是文字内容
                            text = fragment.text()
                            # 过滤掉图片占位符
                            if text != "\ufffc":
                                current_text_parts.append(text)

                    block_iterator += 1

                # 处理段落结束
                if current_text_parts and block.text().strip():
                    current_text_parts.append("\n")

                block = block.next()

            # 处理最后剩余的文字内容
            if current_text_parts:
                text_content = "".join(current_text_parts).strip()
                if text_content:
                    parts.append((text_content, None))
                    logger.debug(f"添加最后的文字部分: {text_content[:50]}...")

            logger.info(f"按顺序解析完成，共{len(parts)}个部分")
            for i, (text, image) in enumerate(parts):
                if text:
                    logger.info(f"  第{i+1}部分: 文字 ({len(text)}字)")
                else:
                    logger.info(
                        f"  第{i+1}部分: 图片 ({os.path.basename(image) if image else 'unknown'})"
                    )

            return parts

        except Exception as e:
            logger.error(f"按顺序解析内容失败: {e}")
            # 回退到简化方法
            return self._get_send_parts_fallback()

    def _find_image_path_by_name(self, image_name: str) -> Optional[str]:
        """
        根据图片名称查找对应的图片路径
        """
        try:
            for image_info in self.inserted_images:
                if isinstance(image_info, dict):
                    path = image_info.get("path", "")
                    name = image_info.get("name", "")

                    # 检查文件名是否匹配
                    if name and name in image_name:
                        return path

                    # 检查完整路径是否匹配
                    if path and os.path.basename(path) in image_name:
                        return path

                    # 检查路径是否直接包含在image_name中
                    if path and path in image_name:
                        return path

                elif isinstance(image_info, str):
                    # 字符串格式的图片信息
                    if os.path.basename(image_info) in image_name:
                        return image_info
                    if image_info in image_name:
                        return image_info

            logger.warning(f"未找到匹配的图片路径: {image_name}")
            return None

        except Exception as e:
            logger.error(f"查找图片路径失败: {e}")
            return None

    def _get_send_parts_fallback(self) -> List[Tuple[str, Optional[str]]]:
        """
        回退方法：简化处理（先发送所有文字，再发送所有图片）
        """
        try:
            parts = []

            # 添加文字部分
            text_content = self.toPlainText().strip()
            # 移除图片占位符
            text_content = text_content.replace("\ufffc", "").strip()
            if text_content:
                parts.append((text_content, None))

            # 添加图片部分
            for image_info in self.inserted_images:
                # 确保获取正确的图片路径
                if isinstance(image_info, dict):
                    image_path = image_info.get("path", "")
                else:
                    image_path = str(image_info)

                if image_path:
                    parts.append((None, image_path))

            logger.warning("使用回退方法解析发送部分")
            return parts

        except Exception as e:
            logger.error(f"回退方法也失败: {e}")
            return []

    def has_content_detailed(self) -> Dict[str, Any]:
        """
        详细检查内容状态

        Returns:
            包含详细状态的字典
        """
        try:
            content = self.get_message_content()

            # 使用新的验证工具
            from utils.config_validator import message_content_validator
            is_valid, detail_msg, stats = message_content_validator.validate_rich_text_content(content)

            # 兼容旧接口，同时提供更详细的信息
            result = {
                "has_content": stats.get("has_content", False),
                "has_text": stats.get("has_text", False),
                "has_images": stats.get("has_images", False),
                "text_length": stats.get("text_length", 0),
                "image_count": stats.get("image_count", 0),
                "valid_images": stats.get("valid_images", 0),
                "detail_message": detail_msg,
                "is_valid": is_valid
            }

            logger.debug(f"内容详情检查: {detail_msg}")
            return result

        except Exception as e:
            logger.error(f"检查内容详情失败: {e}")
            return {
                "has_content": False,
                "has_text": False,
                "has_images": False,
                "text_length": 0,
                "image_count": 0,
                "valid_images": 0,
                "detail_message": f"检查失败: {e}",
                "is_valid": False
            }


class MessagePreviewDialog(ThemedDialogBase):
    """消息预览对话框"""

    def __init__(self, content: Dict, parent=None):
        super().__init__(parent)
        self.setWindowTitle("消息预览")
        self.setModal(True)
        self.resize(500, 400)

        self.setup_ui(content)

    def setup_ui(self, content: Dict):
        """设置界面"""
        layout = QVBoxLayout(self)

        # 预览标签
        preview_label = QLabel("发送预览:")
        layout.addWidget(preview_label)

        # 预览内容
        preview_text = QTextEdit()
        preview_text.setReadOnly(True)

        if content["type"] == "rich_text":
            preview_text.setHtml(content["html"])
        else:
            preview_text.setPlainText(content.get("plain_text", ""))

        layout.addWidget(preview_text)

        # 内容信息
        info_label = QLabel(f"内容摘要: {self.get_content_info(content)}")
        layout.addWidget(info_label)

        # 按钮
        button_layout = QHBoxLayout()

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def get_content_info(self, content: Dict) -> str:
        """获取内容信息"""
        try:
            if content["type"] == "rich_text":
                text_length = len(content["plain_text"].strip())
                image_count = len(content["images"])

                if text_length > 0 and image_count > 0:
                    return f"混合消息 (文字{text_length}字, 图片{image_count}张)"
                elif text_length > 0:
                    return f"文字消息 ({text_length}字)"
                elif image_count > 0:
                    return f"图片消息 ({image_count}张)"

        except Exception as e:
            logger.error(f"获取内容信息失败: {e}")
            return "消息内容"

        return "空消息"

    @staticmethod
    def format_rich_text_for_display(content) -> str:
        """
        将富文本内容格式化为用户友好的显示格式

        Args:
            content: 富文本内容，可以是字典或JSON字符串

        Returns:
            用户友好的显示字符串
        """
        try:
            # 如果是字符串，尝试解析为JSON
            if isinstance(content, str):
                import json
                try:
                    content_data = json.loads(content)
                except json.JSONDecodeError:
                    # 如果不是JSON，直接返回字符串
                    return content[:100] + "..." if len(content) > 100 else content
            else:
                content_data = content

            # 如果不是富文本格式，直接返回
            if not isinstance(content_data, dict) or content_data.get("type") != "rich_text":
                return str(content)[:100] + "..." if len(str(content)) > 100 else str(content)

            # 提取关键信息
            plain_text = content_data.get("plain_text", "").strip()
            images = content_data.get("images", [])

            # 构建用户友好的显示
            parts = []

            if plain_text:
                # 限制文字长度
                if len(plain_text) > 50:
                    text_preview = plain_text[:50] + "..."
                else:
                    text_preview = plain_text
                parts.append(f"文字: {text_preview}")

            if images:
                image_names = []
                for img in images[:3]:  # 最多显示3个图片名
                    if isinstance(img, dict):
                        name = img.get("name", "未知图片")
                    else:
                        name = str(img).split("\\")[-1] if "\\" in str(img) else str(img)
                    image_names.append(name)

                if len(images) > 3:
                    image_names.append(f"等{len(images)}张图片")

                parts.append(f"图片: {', '.join(image_names)}")

            if parts:
                return " | ".join(parts)
            else:
                return "空消息"

        except Exception as e:
            logger.error(f"格式化富文本显示失败: {e}")
            return "富文本消息"

    def get_template_data_v2(self) -> Dict:
        """
        获取模板数据（备用方法）

        Returns:
            模板数据字典
        """
        try:
            content = self.get_message_content()
            if not content:
                logger.warning("获取的内容为空")
                return {"html": "", "plain_text": "", "images": []}

            return {
                "html": content.get("html", ""),
                "plain_text": content.get("plain_text", ""),
                "images": content.get("images", []),
            }
        except Exception as e:
            logger.error(f"获取模板数据失败: {e}")
            import traceback

            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {"html": "", "plain_text": "", "images": []}



    def get_text_count(self) -> int:
        """
        获取文字字数

        Returns:
            文字字数
        """
        try:
            content = self.get_message_content()
            return len(content["plain_text"].strip())
        except Exception as e:
            logger.error(f"获取文字字数失败: {e}")
            return 0

    def get_image_count(self) -> int:
        """
        获取图片数量

        Returns:
            图片数量
        """
        try:
            content = self.get_message_content()
            return len(content["images"])
        except Exception as e:
            logger.error(f"获取图片数量失败: {e}")
            return 0
