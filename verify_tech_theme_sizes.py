#!/usr/bin/env python3
"""
科技主题尺寸验证工具
验证科技主题是否达到目标尺寸
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QLabel, QGroupBox, QSpinBox, QComboBox, QPushButton,
                            QCheckBox, QRadioButton)
from PyQt6.QtCore import Qt, QTimer
from ui.modern_theme_manager import theme_manager
from ui.themed_dialog_base import ThemedDialogBase


class SizeVerificationDialog(ThemedDialogBase):
    """尺寸验证对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("科技主题尺寸验证")
        self.setFixedSize(800, 600)
        
        # 目标尺寸（默认主题的标准尺寸）
        self.target_sizes = {
            "SpinBox": (77, 23),
            "ComboBox": (86, 23),
            "CheckBox1": (57, 19),
            "CheckBox2": (57, 19),
            "RadioButton1": (58, 19),
            "RadioButton2": (58, 19),
        }
        
        self.setup_ui()
        
        # 延迟检查尺寸，确保控件完全渲染
        QTimer.singleShot(500, self.auto_check_sizes)
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("科技主题尺寸验证 - 目标尺寸对比")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 目标尺寸显示
        target_group = QGroupBox("目标尺寸（默认主题标准）")
        target_layout = QVBoxLayout(target_group)
        
        target_text = []
        for control_name, (width, height) in self.target_sizes.items():
            target_text.append(f"  {control_name}: {width} x {height} px")
        
        target_label = QLabel("\n".join(target_text))
        target_layout.addWidget(target_label)
        layout.addWidget(target_group)
        
        # 测试控件组
        controls_group = QGroupBox("测试控件")
        controls_layout = QVBoxLayout(controls_group)
        
        # SpinBox测试
        spinbox_row = QHBoxLayout()
        spinbox_row.addWidget(QLabel("SpinBox:"))
        self.spinbox = QSpinBox()
        self.spinbox.setRange(0, 100)
        self.spinbox.setValue(50)
        spinbox_row.addWidget(self.spinbox)
        spinbox_row.addStretch()
        controls_layout.addLayout(spinbox_row)
        
        # ComboBox测试
        combobox_row = QHBoxLayout()
        combobox_row.addWidget(QLabel("ComboBox:"))
        self.combobox = QComboBox()
        self.combobox.addItems(["选项1", "选项2", "选项3"])
        combobox_row.addWidget(self.combobox)
        combobox_row.addStretch()
        controls_layout.addLayout(combobox_row)
        
        # CheckBox测试
        checkbox_row = QHBoxLayout()
        checkbox_row.addWidget(QLabel("CheckBox:"))
        self.checkbox1 = QCheckBox("选项1")
        self.checkbox2 = QCheckBox("选项2")
        checkbox_row.addWidget(self.checkbox1)
        checkbox_row.addWidget(self.checkbox2)
        checkbox_row.addStretch()
        controls_layout.addLayout(checkbox_row)
        
        # RadioButton测试
        radio_row = QHBoxLayout()
        radio_row.addWidget(QLabel("RadioButton:"))
        self.radio1 = QRadioButton("选项A")
        self.radio2 = QRadioButton("选项B")
        self.radio1.setChecked(True)
        radio_row.addWidget(self.radio1)
        radio_row.addWidget(self.radio2)
        radio_row.addStretch()
        controls_layout.addLayout(radio_row)
        
        layout.addWidget(controls_group)
        
        # 实际尺寸显示
        result_group = QGroupBox("实际尺寸检查结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_label = QLabel("正在检查尺寸...")
        result_layout.addWidget(self.result_label)
        
        layout.addWidget(result_group)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        check_btn = QPushButton("重新检查")
        check_btn.clicked.connect(self.check_sizes)
        
        switch_default_btn = QPushButton("切换到默认主题对比")
        switch_default_btn.clicked.connect(self.switch_to_default)
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(check_btn)
        button_layout.addWidget(switch_default_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def auto_check_sizes(self):
        """自动检查尺寸"""
        self.check_sizes()
        
    def check_sizes(self):
        """检查控件尺寸"""
        current_theme = theme_manager.get_current_theme()
        
        # 获取实际尺寸
        actual_sizes = {
            "SpinBox": (self.spinbox.size().width(), self.spinbox.size().height()),
            "ComboBox": (self.combobox.size().width(), self.combobox.size().height()),
            "CheckBox1": (self.checkbox1.size().width(), self.checkbox1.size().height()),
            "CheckBox2": (self.checkbox2.size().width(), self.checkbox2.size().height()),
            "RadioButton1": (self.radio1.size().width(), self.radio1.size().height()),
            "RadioButton2": (self.radio2.size().width(), self.radio2.size().height()),
        }
        
        # 对比结果
        result_lines = [f"📊 当前主题: {current_theme}", ""]
        
        all_match = True
        for control_name in self.target_sizes:
            target_w, target_h = self.target_sizes[control_name]
            actual_w, actual_h = actual_sizes[control_name]
            
            # 允许1-2像素的误差
            width_match = abs(actual_w - target_w) <= 2
            height_match = abs(actual_h - target_h) <= 2
            
            if width_match and height_match:
                status = "✅"
            else:
                status = "❌"
                all_match = False
            
            result_lines.append(
                f"{status} {control_name}:"
            )
            result_lines.append(
                f"    目标: {target_w} x {target_h} px"
            )
            result_lines.append(
                f"    实际: {actual_w} x {actual_h} px"
            )
            
            if not width_match:
                diff_w = actual_w - target_w
                result_lines.append(f"    宽度差异: {diff_w:+d} px")
            if not height_match:
                diff_h = actual_h - target_h
                result_lines.append(f"    高度差异: {diff_h:+d} px")
            
            result_lines.append("")
        
        # 总体评估
        if all_match:
            result_lines.append("🎉 所有控件尺寸都符合目标标准！")
            result_lines.append("✅ 科技主题尺寸修复成功")
        else:
            result_lines.append("⚠️  部分控件尺寸不符合目标标准")
            result_lines.append("❌ 科技主题可能需要进一步调整")
        
        # 更新显示
        self.result_label.setText("\n".join(result_lines))
        
        # 同时在控制台输出
        print("\n" + "="*60)
        for line in result_lines:
            print(line)
        print("="*60)
        
        return all_match
        
    def switch_to_default(self):
        """切换到默认主题进行对比"""
        app = QApplication.instance()
        theme_manager.set_theme(app, "默认主题")
        self.setWindowTitle("默认主题尺寸验证")
        
        # 延迟检查，确保主题切换完成
        QTimer.singleShot(200, self.check_sizes)


def test_tech_theme_sizes():
    """测试科技主题尺寸"""
    app = QApplication(sys.argv)
    
    print("🔧 科技主题尺寸验证工具")
    print("=" * 60)
    
    print("📋 目标尺寸（默认主题标准）:")
    target_sizes = {
        "SpinBox": (77, 23),
        "ComboBox": (86, 23),
        "CheckBox1": (57, 19),
        "CheckBox2": (57, 19),
        "RadioButton1": (58, 19),
        "RadioButton2": (58, 19),
    }
    
    for control_name, (width, height) in target_sizes.items():
        print(f"  {control_name}: {width} x {height} px")
    
    print("\n🎨 正在应用科技主题...")
    theme_manager.set_theme(app, "科技主题")
    print("✅ 科技主题已应用")
    
    print("\n📋 测试说明:")
    print("1. 对话框将显示科技主题下的控件")
    print("2. 自动检查各控件的实际尺寸")
    print("3. 与目标尺寸进行对比")
    print("4. 可以切换到默认主题进行对比验证")
    
    dialog = SizeVerificationDialog()
    dialog.show()
    
    result = dialog.exec()
    if result == QDialog.DialogCode.Accepted:
        print("✅ 尺寸验证完成")
    
    sys.exit(0)


def main():
    """主函数"""
    print("🚀 科技主题尺寸验证工具")
    print("=" * 60)
    print("\n📋 验证目标:")
    print("  确认科技主题的控件尺寸是否符合以下标准:")
    print("  - SpinBox: 77 x 23 px")
    print("  - ComboBox: 86 x 23 px") 
    print("  - CheckBox: 57 x 19 px")
    print("  - RadioButton: 58 x 19 px")
    
    print("\n✅ 开始验证测试:")
    print("python verify_tech_theme_sizes.py --test")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_tech_theme_sizes()


if __name__ == "__main__":
    main()
