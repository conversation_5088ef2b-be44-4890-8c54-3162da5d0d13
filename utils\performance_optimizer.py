#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化器

提供内存管理、CPU优化、响应性提升等功能。
"""

import gc
import sys
import time
import psutil
import threading
from typing import Dict, Any, Optional, Callable
from functools import wraps
from PyQt6.QtCore import QTimer, QThread, QObject, pyqtSignal
from PyQt6.QtWidgets import QApplication

from utils.logger import setup_logger

logger = setup_logger("performance_optimizer")


class PerformanceConfig:
    """性能配置类"""

    def __init__(self):
        # 状态更新配置
        self.status_update_interval = 5000  # 状态更新间隔(ms)
        self.status_update_skip_ratio = 3  # 每N次更新执行一次

        # 性能监控配置
        self.performance_warning_threshold = {
            "update_status": 300,  # 状态更新方法阈值(ms)
            "update": 200,  # 其他更新方法阈值(ms)
            "default": 100,  # 默认阈值(ms)
        }

        # 事件处理配置
        self.max_event_blocking_time = 250  # 事件处理最大阻塞时间(ms)
        self.warning_interval = 10  # 警告间隔(秒)

        # 异步任务配置
        self.max_concurrent_tasks = 3  # 最大并发任务数

        # 内存管理配置
        self.memory_warning_threshold = 10  # 内存增长警告阈值(MB)

    def get_warning_threshold(self, func_name: str) -> int:
        """获取函数的警告阈值"""
        for key, threshold in self.performance_warning_threshold.items():
            if key in func_name:
                return threshold
        return self.performance_warning_threshold["default"]


# 全局性能配置实例
perf_config = PerformanceConfig()


class MemoryManager:
    """内存管理器"""

    def __init__(self):
        self.memory_threshold = 500 * 1024 * 1024  # 500MB阈值
        self.cleanup_timer = None
        # 延迟初始化定时器
        QTimer.singleShot(0, self._init_timer)

    def _init_timer(self):
        """在主线程中初始化定时器"""
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app and QThread.currentThread() != app.thread():
            logger.warning("内存管理器定时器应该在主线程中初始化")
            # 重新调度到主线程
            QTimer.singleShot(100, self._init_timer)
            return

        if self.cleanup_timer is None:
            self.cleanup_timer = QTimer()
            self.cleanup_timer.timeout.connect(self.auto_cleanup)
            self.cleanup_timer.start(30000)  # 每30秒检查一次
            logger.debug("内存管理器定时器初始化完成")

    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                "rss": memory_info.rss / 1024 / 1024,  # MB
                "vms": memory_info.vms / 1024 / 1024,  # MB
                "percent": process.memory_percent(),
                "available": psutil.virtual_memory().available / 1024 / 1024,  # MB
            }
        except Exception as e:
            logger.error(f"获取内存使用情况失败: {e}")
            return {}

    def auto_cleanup(self):
        """自动内存清理"""
        try:
            memory_info = self.get_memory_usage()
            if memory_info.get("rss", 0) > self.memory_threshold / 1024 / 1024:
                self.force_cleanup()
                logger.info(
                    f"自动内存清理完成，当前使用: {memory_info.get('rss', 0):.1f}MB"
                )
        except Exception as e:
            logger.error(f"自动内存清理失败: {e}")

    def force_cleanup(self):
        """强制内存清理"""
        try:
            # 强制垃圾回收
            collected = gc.collect()

            # 清理Qt对象缓存
            QApplication.processEvents()

            logger.debug(f"垃圾回收清理了 {collected} 个对象")

        except Exception as e:
            logger.error(f"强制内存清理失败: {e}")


class CPUOptimizer:
    """CPU优化器"""

    def __init__(self):
        self.cpu_threshold = 80.0  # CPU使用率阈值
        self.monitor_timer = None
        # 延迟初始化定时器
        QTimer.singleShot(0, self._init_timer)

    def _init_timer(self):
        """在主线程中初始化定时器"""
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app and QThread.currentThread() != app.thread():
            logger.warning("CPU优化器定时器应该在主线程中初始化")
            # 重新调度到主线程
            QTimer.singleShot(100, self._init_timer)
            return

        if self.monitor_timer is None:
            self.monitor_timer = QTimer()
            self.monitor_timer.timeout.connect(self.monitor_cpu)
            self.monitor_timer.start(5000)  # 每5秒监控一次
            logger.debug("CPU优化器定时器初始化完成")

    def get_cpu_usage(self) -> Dict[str, float]:
        """获取CPU使用情况"""
        try:
            process = psutil.Process()
            return {
                "process_cpu": process.cpu_percent(),
                "system_cpu": psutil.cpu_percent(),
                "cpu_count": psutil.cpu_count(),
            }
        except Exception as e:
            logger.error(f"获取CPU使用情况失败: {e}")
            return {}

    def monitor_cpu(self):
        """监控CPU使用率"""
        try:
            cpu_info = self.get_cpu_usage()
            process_cpu = cpu_info.get("process_cpu", 0)

            if process_cpu > self.cpu_threshold:
                logger.warning(f"CPU使用率过高: {process_cpu:.1f}%")
                self.optimize_cpu_usage()

        except Exception as e:
            logger.error(f"CPU监控失败: {e}")

    def optimize_cpu_usage(self):
        """优化CPU使用"""
        try:
            # 降低定时器频率
            app = QApplication.instance()
            if app:
                # 处理待处理的事件
                app.processEvents()

                # 短暂休眠
                time.sleep(0.01)

            logger.debug("CPU优化处理完成")

        except Exception as e:
            logger.error(f"CPU优化失败: {e}")


class ResponsivenessManager:
    """响应性管理器"""

    def __init__(self):
        self.max_blocking_time = perf_config.max_event_blocking_time
        self.event_queue_size = 0
        self.last_warning_time = 0  # 上次警告时间
        self.warning_interval = perf_config.warning_interval

    def process_events_safely(self):
        """安全地处理事件队列"""
        try:
            app = QApplication.instance()
            if app:
                start_time = time.time()
                app.processEvents()

                # 检查处理时间
                elapsed = (time.time() - start_time) * 1000
                current_time = time.time()

                # 只有超过阈值且距离上次警告超过间隔时间才记录警告
                if (
                    elapsed > self.max_blocking_time
                    and current_time - self.last_warning_time > self.warning_interval
                ):
                    logger.warning(f"事件处理耗时过长: {elapsed:.1f}ms")
                    self.last_warning_time = current_time

        except Exception as e:
            logger.error(f"事件处理失败: {e}")

    def yield_control(self):
        """主动让出控制权"""
        try:
            self.process_events_safely()
            time.sleep(0.001)  # 1ms休眠
        except Exception as e:
            logger.error(f"让出控制权失败: {e}")


class AsyncTaskManager(QObject):
    """异步任务管理器"""

    task_completed = pyqtSignal(str, object)  # 任务完成信号
    task_error = pyqtSignal(str, str)  # 任务错误信号

    def __init__(self):
        super().__init__()
        self.running_tasks = {}
        self.task_queue = []
        self.max_concurrent_tasks = perf_config.max_concurrent_tasks

    def submit_task(self, task_id: str, func: Callable, *args, **kwargs):
        """提交异步任务"""
        try:
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                self.task_queue.append((task_id, func, args, kwargs))
                logger.debug(f"任务 {task_id} 已加入队列")
                return

            thread = TaskThread(task_id, func, *args, **kwargs)
            thread.finished.connect(lambda tid=task_id: self._on_task_finished(tid))
            thread.error.connect(lambda tid, err: self.task_error.emit(tid, err))
            thread.result.connect(lambda tid, res: self.task_completed.emit(tid, res))

            self.running_tasks[task_id] = thread
            thread.start()

            logger.debug(f"任务 {task_id} 已启动")

        except Exception as e:
            logger.error(f"提交任务失败: {e}")
            self.task_error.emit(task_id, str(e))

    def has_running_tasks(self) -> bool:
        """检查是否有正在运行的任务"""
        return len(self.running_tasks) > 0

    def get_running_task_count(self) -> int:
        """获取正在运行的任务数量"""
        return len(self.running_tasks)

    def get_queued_task_count(self) -> int:
        """获取队列中的任务数量"""
        return len(self.task_queue)

    def _on_task_finished(self, task_id: str):
        """任务完成处理"""
        try:
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

            # 处理队列中的下一个任务
            if self.task_queue:
                next_task = self.task_queue.pop(0)
                self.submit_task(*next_task)

        except Exception as e:
            logger.error(f"任务完成处理失败: {e}")

    def cancel_task(self, task_id: str):
        """取消任务"""
        try:
            if task_id in self.running_tasks:
                thread = self.running_tasks[task_id]
                thread.terminate()
                del self.running_tasks[task_id]
                logger.debug(f"任务 {task_id} 已取消")

        except Exception as e:
            logger.error(f"取消任务失败: {e}")


class TaskThread(QThread):
    """任务线程"""

    result = pyqtSignal(str, object)
    error = pyqtSignal(str, str)
    finished = pyqtSignal(str)

    def __init__(self, task_id: str, func: Callable, *args, **kwargs):
        super().__init__()
        self.task_id = task_id
        self.func = func
        self.args = args
        self.kwargs = kwargs

    def run(self):
        """运行任务"""
        try:
            result = self.func(*self.args, **self.kwargs)
            self.result.emit(self.task_id, result)
        except Exception as e:
            self.error.emit(self.task_id, str(e))
        finally:
            self.finished.emit(self.task_id)


def performance_monitor(func):
    """性能监控装饰器"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss

        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss

            elapsed_time = (end_time - start_time) * 1000
            memory_diff = (end_memory - start_memory) / 1024 / 1024

            # 使用配置的阈值
            warning_threshold = perf_config.get_warning_threshold(func.__name__)

            if elapsed_time > warning_threshold:
                logger.warning(f"{func.__name__} 执行耗时: {elapsed_time:.1f}ms")

            if memory_diff > perf_config.memory_warning_threshold:
                logger.warning(f"{func.__name__} 内存增长: {memory_diff:.1f}MB")

    return wrapper


def async_task(task_manager: AsyncTaskManager, task_id: str = None):
    """异步任务装饰器"""

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal task_id
            if task_id is None:
                task_id = f"{func.__name__}_{int(time.time())}"

            task_manager.submit_task(task_id, func, *args, **kwargs)
            return task_id

        return wrapper

    return decorator


class PerformanceOptimizer:
    """性能优化器主类"""

    def __init__(self):
        self.memory_manager = MemoryManager()
        self.cpu_optimizer = CPUOptimizer()
        self.responsiveness_manager = ResponsivenessManager()
        self.async_task_manager = AsyncTaskManager()

        # 性能统计
        self.stats = {
            "startup_time": 0,
            "memory_peak": 0,
            "cpu_peak": 0,
            "ui_freezes": 0,
        }

        logger.info("性能优化器初始化完成")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        try:
            memory_info = self.memory_manager.get_memory_usage()
            cpu_info = self.cpu_optimizer.get_cpu_usage()

            # 更新峰值
            current_memory = memory_info.get("rss", 0)
            current_cpu = cpu_info.get("process_cpu", 0)

            if current_memory > self.stats["memory_peak"]:
                self.stats["memory_peak"] = current_memory

            if current_cpu > self.stats["cpu_peak"]:
                self.stats["cpu_peak"] = current_cpu

            return {
                "current_memory": current_memory,
                "current_cpu": current_cpu,
                "memory_peak": self.stats["memory_peak"],
                "cpu_peak": self.stats["cpu_peak"],
                "ui_freezes": self.stats["ui_freezes"],
            }

        except Exception as e:
            logger.error(f"获取性能统计失败: {e}")
            return {}

    def optimize_startup(self):
        """优化启动性能"""
        try:
            # 延迟导入非关键模块
            self._delay_imports()

            # 预分配内存
            self._preallocate_memory()

            # 设置进程优先级
            self._set_process_priority()

            logger.info("启动性能优化完成")

        except Exception as e:
            logger.error(f"启动性能优化失败: {e}")

    def _delay_imports(self):
        """延迟导入"""
        # 这里可以添加延迟导入的逻辑
        pass

    def _preallocate_memory(self):
        """预分配内存"""
        try:
            # 预分配一些内存以减少后续分配开销
            dummy_data = bytearray(1024 * 1024)  # 1MB
            del dummy_data
        except Exception as e:
            logger.error(f"预分配内存失败: {e}")

    def _set_process_priority(self):
        """设置进程优先级"""
        try:
            process = psutil.Process()
            # 设置为正常优先级
            process.nice(
                psutil.NORMAL_PRIORITY_CLASS
                if hasattr(psutil, "NORMAL_PRIORITY_CLASS")
                else 0
            )
        except Exception as e:
            logger.error(f"设置进程优先级失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            self.memory_manager.cleanup_timer.stop()
            self.cpu_optimizer.monitor_timer.stop()

            # 取消所有运行中的任务
            for task_id in list(self.async_task_manager.running_tasks.keys()):
                self.async_task_manager.cancel_task(task_id)

            logger.info("性能优化器清理完成")

        except Exception as e:
            logger.error(f"性能优化器清理失败: {e}")


# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()
