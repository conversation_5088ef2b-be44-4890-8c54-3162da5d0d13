"""
按钮状态管理器

为定时发送和循环发送页面提供统一的按钮状态管理逻辑
"""

from ui.themed_message_box import ThemedMessageBoxHelper
from typing import Dict, List, Optional, Callable
from enum import Enum

from PyQt6.QtWidgets import QPushButton, QMessageBox
from utils.logger import setup_logger

logger = setup_logger("button_state_manager")


class TaskStatus(Enum):
    """任务状态枚举"""

    IDLE = "idle"  # 空闲状态（无任务）
    CREATED = "created"  # 任务已创建但未启动
    RUNNING = "running"  # 任务运行中
    PAUSED = "paused"  # 任务已暂停
    STOPPED = "stopped"  # 任务已停止
    CANCELLED = "cancelled"  # 任务已取消
    COMPLETED = "completed"  # 任务已完成
    FAILED = "failed"  # 任务失败


class ButtonType(Enum):
    """按钮类型枚举"""

    PRIMARY = "primary"  # 主要按钮（创建/发送）
    PAUSE = "pause"  # 暂停/继续按钮
    STOP = "stop"  # 停止按钮
    CANCEL = "cancel"  # 取消按钮


class ButtonConfig:
    """按钮配置"""

    def __init__(self, text: str, enabled: bool = True, style: str = ""):
        self.text = text
        self.enabled = enabled
        self.style = style


class ButtonStateManager:
    """统一的按钮状态管理器"""

    def __init__(self, page_type: str = "generic"):
        self.page_type = page_type  # "timing" 或 "loop"
        self.buttons: Dict[ButtonType, QPushButton] = {}
        self.current_status = TaskStatus.IDLE
        self.has_selected_group = False

        # 状态变化回调
        self.status_change_callbacks: List[Callable[[TaskStatus], None]] = []

        # 定义按钮状态配置
        self._init_button_configs()

    def _init_button_configs(self):
        """初始化按钮状态配置"""
        self.button_configs = {
            # 定时发送页面配置（只有保存设置按钮，任务控制通过分组卡片完成）
            "timing": {
                TaskStatus.IDLE: {
                    ButtonType.PRIMARY: ButtonConfig("💾 保存设置", True),
                },
                TaskStatus.RUNNING: {
                    ButtonType.PRIMARY: ButtonConfig("💾 保存设置", False),
                },
                TaskStatus.PAUSED: {
                    ButtonType.PRIMARY: ButtonConfig("💾 保存设置", True),
                },
                TaskStatus.COMPLETED: {
                    ButtonType.PRIMARY: ButtonConfig("💾 保存设置", True),
                },
                TaskStatus.STOPPED: {
                    ButtonType.PRIMARY: ButtonConfig("💾 保存设置", True),
                },
                TaskStatus.CANCELLED: {
                    ButtonType.PRIMARY: ButtonConfig("💾 保存设置", True),
                },
                TaskStatus.FAILED: {
                    ButtonType.PRIMARY: ButtonConfig("💾 保存设置", True),
                },
            },
            # 循环发送页面配置
            "loop": {
                TaskStatus.IDLE: {
                    ButtonType.PRIMARY: ButtonConfig("🔄 创建循环任务", True),
                    ButtonType.PAUSE: ButtonConfig("⏸️ 暂停", False),
                    ButtonType.STOP: ButtonConfig("⏹️ 停止", False),
                    ButtonType.CANCEL: ButtonConfig("❌ 取消", False),
                },
                TaskStatus.CREATED: {
                    ButtonType.PRIMARY: ButtonConfig("🔄 任务已创建", False),
                    ButtonType.PAUSE: ButtonConfig("⏸️ 暂停", False),
                    ButtonType.STOP: ButtonConfig("⏹️ 停止", True),
                    ButtonType.CANCEL: ButtonConfig("❌ 取消", True),
                },
                TaskStatus.RUNNING: {
                    ButtonType.PRIMARY: ButtonConfig("🔄 任务运行中", False),
                    ButtonType.PAUSE: ButtonConfig("⏸️ 暂停", True),
                    ButtonType.STOP: ButtonConfig("⏹️ 停止", True),
                    ButtonType.CANCEL: ButtonConfig("❌ 取消", True),
                },
                TaskStatus.PAUSED: {
                    ButtonType.PRIMARY: ButtonConfig("🔄 任务已暂停", False),
                    ButtonType.PAUSE: ButtonConfig("▶️ 继续", True),
                    ButtonType.STOP: ButtonConfig("⏹️ 停止", True),
                    ButtonType.CANCEL: ButtonConfig("❌ 取消", True),
                },
                TaskStatus.STOPPED: {
                    ButtonType.PRIMARY: ButtonConfig("🔄 创建循环任务", True),
                    ButtonType.PAUSE: ButtonConfig("⏸️ 暂停", False),
                    ButtonType.STOP: ButtonConfig("⏹️ 停止", False),
                    ButtonType.CANCEL: ButtonConfig("❌ 取消", False),
                },
                TaskStatus.CANCELLED: {
                    ButtonType.PRIMARY: ButtonConfig("🔄 创建循环任务", True),
                    ButtonType.PAUSE: ButtonConfig("⏸️ 暂停", False),
                    ButtonType.STOP: ButtonConfig("⏹️ 停止", False),
                    ButtonType.CANCEL: ButtonConfig("❌ 取消", False),
                },
                TaskStatus.COMPLETED: {
                    ButtonType.PRIMARY: ButtonConfig("🔄 创建循环任务", True),
                    ButtonType.PAUSE: ButtonConfig("⏸️ 暂停", False),
                    ButtonType.STOP: ButtonConfig("⏹️ 停止", False),
                    ButtonType.CANCEL: ButtonConfig("❌ 取消", False),
                },
            },
        }

    def register_button(self, button_type: ButtonType, button: QPushButton):
        """注册按钮"""
        self.buttons[button_type] = button
        logger.debug(f"注册按钮: {button_type.value} -> {button.text()}")

    def set_selected_group(self, has_group: bool):
        """设置是否有选中的分组"""
        self.has_selected_group = has_group
        self.update_buttons()

    def set_task_status(self, status: TaskStatus):
        """设置任务状态"""
        if self.current_status != status:
            old_status = self.current_status
            self.current_status = status
            logger.info(f"任务状态变化: {old_status.value} -> {status.value}")

            # 更新按钮状态
            self.update_buttons()

            # 触发回调
            for callback in self.status_change_callbacks:
                try:
                    callback(status)
                except Exception as e:
                    logger.error(f"状态变化回调异常: {e}")

    def add_status_change_callback(self, callback: Callable[[TaskStatus], None]):
        """添加状态变化回调"""
        self.status_change_callbacks.append(callback)

    def update_buttons(self):
        """更新所有按钮状态"""
        try:
            # 获取当前页面类型的配置
            page_configs = self.button_configs.get(self.page_type, {})
            status_config = page_configs.get(self.current_status, {})

            # 更新每个按钮
            for button_type, button in self.buttons.items():
                if button_type in status_config:
                    config = status_config[button_type]

                    # 设置按钮文本
                    button.setText(config.text)

                    # 设置按钮启用状态
                    if button_type == ButtonType.PRIMARY:
                        # 主按钮需要考虑是否有选中的分组
                        button.setEnabled(config.enabled and self.has_selected_group)
                    else:
                        button.setEnabled(config.enabled)

                    # 设置按钮样式
                    if config.style:
                        button.setStyleSheet(config.style)

                    logger.debug(
                        f"更新按钮 {button_type.value}: {config.text}, enabled={button.isEnabled()}"
                    )

        except Exception as e:
            logger.error(f"更新按钮状态失败: {e}")

    def get_current_status(self) -> TaskStatus:
        """获取当前任务状态"""
        return self.current_status

    def is_button_enabled(self, button_type: ButtonType) -> bool:
        """检查按钮是否启用"""
        if button_type in self.buttons:
            return self.buttons[button_type].isEnabled()
        return False

    def get_button_config(self, button_type: ButtonType, status: TaskStatus = None) -> Optional[ButtonConfig]:
        """获取指定按钮在指定状态下的配置"""
        if status is None:
            status = self.current_status

        page_config = self.button_configs.get(self.page_type, {})
        status_config = page_config.get(status, {})
        return status_config.get(button_type)

    def show_confirmation_dialog(self, parent, title: str, message: str) -> bool:
        """显示确认对话框（统一样式）"""
        reply = ThemedMessageBoxHelper.show_question(parent, title, message)
        return reply

    def show_warning_dialog(self, parent, title: str, message: str):
        """显示警告对话框（统一样式）"""
        ThemedMessageBoxHelper.show_warning(parent, title, message)

    def show_error_dialog(self, parent, title: str, message: str):
        """显示错误对话框（统一样式）"""
        ThemedMessageBoxHelper.show_error(parent, title, message)

    def show_info_dialog(self, parent, title: str, message: str):
        """显示信息对话框（统一样式）"""
        ThemedMessageBoxHelper.show_information(parent, title, message)


def create_timing_button_manager() -> ButtonStateManager:
    """创建定时发送页面的按钮管理器"""
    return ButtonStateManager("timing")


def create_loop_button_manager() -> ButtonStateManager:
    """创建循环发送页面的按钮管理器"""
    return ButtonStateManager("loop")


# 任务状态映射函数
def map_task_status_to_enum(status_str: str) -> TaskStatus:
    """将字符串状态映射到枚举"""
    status_map = {
        "idle": TaskStatus.IDLE,
        "created": TaskStatus.CREATED,
        "running": TaskStatus.RUNNING,
        "paused": TaskStatus.PAUSED,
        "stopped": TaskStatus.STOPPED,
        "cancelled": TaskStatus.CANCELLED,
        "completed": TaskStatus.COMPLETED,
        "failed": TaskStatus.FAILED,
        # 定时发送特有状态
        "pending": TaskStatus.CREATED,
        "executing": TaskStatus.RUNNING,
        # 其他可能的状态
        "no_task": TaskStatus.IDLE,
        "finished": TaskStatus.COMPLETED,
    }

    return status_map.get(status_str.lower(), TaskStatus.IDLE)
