#!/usr/bin/env python3
"""
自动注入器 - 基于Injector.exe的唯一注入方式
"""

from typing import Tuple
from core.injector_tool import InjectorTool
from utils.logger import setup_logger

logger = setup_logger("auto_injector")


class AutoInjector:
    """自动注入器 - 唯一支持：Injector.exe + wxhelper.dll"""

    def __init__(self, auto_elevate: bool = True):
        # 唯一的注入实现：基于Injector.exe + wxhelper.dll
        self.injector_tool = InjectorTool(auto_elevate=auto_elevate)
        logger.info("AutoInjector初始化 - 唯一支持：Injector.exe + wxhelper.dll")
        logger.info(f"自动权限提升: {'启用' if auto_elevate else '禁用'}")

    def check_admin_privileges(self) -> bool:
        """检查是否具有管理员权限"""
        return self.injector_tool.check_admin_privileges()

    def find_wechat_process(self):
        """查找微信进程"""
        return self.injector_tool.find_wechat_process()

    def check_api_service(self, timeout: int = 3) -> bool:
        """检查wxhelper API服务是否正常"""
        return self.injector_tool.check_api_service()

    def check_dll_injected(self, process) -> bool:
        """检查DLL是否已注入到进程"""
        return self.injector_tool.check_dll_injected(process)

    def check_injection_status(self) -> Tuple[bool, str]:
        """检查注入状态"""
        return self.injector_tool.check_injection_status()

    def auto_inject(self) -> Tuple[bool, str]:
        """自动注入DLL"""
        return self.injector_tool.inject_dll()
