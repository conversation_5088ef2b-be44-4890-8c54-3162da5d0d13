#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试定时任务修复
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_timing_task_basic():
    """测试定时任务基本功能"""
    print("🔧 测试定时任务基本功能...")
    
    try:
        from core.timing_sender import TimingTask
        
        # 创建测试任务
        task = TimingTask(
            task_id="test_basic_001",
            group_id="test_group",
            group_name="测试分组",
            execute_date="2025-08-07",
            execute_time="14:00",
            message_content="测试消息",
            message_type="text"
        )
        
        print(f"✅ 任务创建成功: {task.task_id}")
        
        # 测试执行时间获取
        execute_datetime = task.get_execute_datetime()
        print(f"✅ 执行时间获取成功: {execute_datetime}")
        
        # 测试执行准备检查
        is_ready = task.is_ready_to_execute()
        print(f"✅ 执行准备检查成功: {is_ready}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_schedule_without_timer():
    """测试无定时器的调度"""
    print("\n🔧 测试无定时器的调度...")
    
    try:
        from core.timing_sender import TimingSender
        
        # 创建发送器实例（不初始化定时器）
        sender = TimingSender()
        
        # 确保定时器未初始化
        sender.check_timer = None
        
        # 测试调度（应该不会崩溃）
        try:
            sender._schedule_next_check()
            print("✅ 无定时器调度成功（不会崩溃）")
        except Exception as e:
            print(f"❌ 无定时器调度失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 定时任务简单修复测试")
    print("=" * 60)
    
    tests = [
        ("定时任务基本功能", test_timing_task_basic),
        ("无定时器调度测试", test_schedule_without_timer)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 定时任务修复成功！")
        print("\n✨ 修复内容:")
        print("  🔧 修复了 scheduled_time 属性错误")
        print("  ⏰ 修复了定时器空指针问题")
        print("  🛡️  添加了定时器空值检查")
        
        print("\n📋 现在定时发送功能应该正常工作了！")
    else:
        print("\n❌ 部分测试失败")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
