#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断注入问题的测试脚本
"""

import os
import sys
import tempfile
from pathlib import Path

def test_resource_extraction():
    """测试资源文件提取"""
    print("🔍 测试资源文件提取...")
    
    try:
        # 模拟打包环境的资源提取
        def get_resource_path(relative_path: str) -> Path:
            try:
                # PyInstaller打包后的路径
                base_path = Path(sys._MEIPASS)
                print(f"  使用打包环境路径: {base_path}")
            except AttributeError:
                # 开发环境路径
                base_path = Path(__file__).parent
                print(f"  使用开发环境路径: {base_path}")
            
            full_path = base_path / relative_path
            return full_path
        
        def extract_resource_to_temp(relative_path: str) -> Path:
            import shutil
            
            # 获取源文件路径
            source_path = get_resource_path(relative_path)
            print(f"  源文件路径: {source_path}")
            
            # 创建临时目录
            temp_dir = Path(tempfile.gettempdir()) / "MeetSpaceWeChatSender"
            temp_dir.mkdir(exist_ok=True)
            print(f"  临时目录: {temp_dir}")
            
            # 目标文件路径
            target_path = temp_dir / Path(relative_path).name
            print(f"  目标文件路径: {target_path}")
            
            if source_path.exists():
                # 复制文件
                shutil.copy2(source_path, target_path)
                print(f"  ✅ 文件提取成功: {target_path}")
                print(f"  文件大小: {target_path.stat().st_size} bytes")
                return target_path
            else:
                print(f"  ❌ 源文件不存在: {source_path}")
                return source_path
        
        # 测试关键文件
        test_files = [
            "tools/Injector.exe",
            "wxhelper_files/wxhelper.dll",
            "tools/x64/Injector.exe",
            "tools/Win32/Injector.exe"
        ]
        
        extracted_files = {}
        for file_path in test_files:
            print(f"\n测试文件: {file_path}")
            try:
                extracted_path = extract_resource_to_temp(file_path)
                extracted_files[file_path] = extracted_path
            except Exception as e:
                print(f"  ❌ 提取失败: {e}")
        
        return extracted_files
        
    except Exception as e:
        print(f"❌ 资源提取测试失败: {e}")
        return {}

def test_injector_execution():
    """测试注入器执行"""
    print("\n🔧 测试注入器执行...")
    
    try:
        # 提取文件
        extracted_files = test_resource_extraction()
        
        if not extracted_files:
            print("❌ 没有成功提取的文件")
            return False
        
        # 测试注入器
        injector_path = extracted_files.get("tools/Injector.exe")
        if not injector_path or not injector_path.exists():
            print("❌ 注入器文件不存在")
            return False
        
        print(f"注入器路径: {injector_path}")
        
        # 测试注入器是否可执行
        import subprocess
        try:
            result = subprocess.run(
                [str(injector_path), "--help"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            print(f"注入器返回码: {result.returncode}")
            if result.stdout:
                print(f"标准输出: {result.stdout[:200]}...")
            if result.stderr:
                print(f"错误输出: {result.stderr[:200]}...")
            
            if result.returncode == 0 or "usage" in result.stdout.lower() or "help" in result.stdout.lower():
                print("✅ 注入器可以正常执行")
                return True
            else:
                print("⚠️  注入器执行异常但可能正常")
                return True
                
        except subprocess.TimeoutExpired:
            print("⚠️  注入器执行超时")
            return False
        except Exception as e:
            print(f"❌ 注入器执行失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 注入器测试失败: {e}")
        return False

def test_wechat_process():
    """测试微信进程检测"""
    print("\n🔍 测试微信进程检测...")
    
    try:
        import psutil
        
        # 查找微信进程
        wechat_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                    wechat_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if wechat_processes:
            print("✅ 找到微信进程:")
            for proc in wechat_processes:
                print(f"  PID: {proc['pid']}, 名称: {proc['name']}, 路径: {proc.get('exe', 'N/A')}")
            return True
        else:
            print("❌ 未找到微信进程")
            print("请确保微信PC版已启动并登录")
            return False
            
    except ImportError:
        print("❌ psutil库未安装")
        return False
    except Exception as e:
        print(f"❌ 进程检测失败: {e}")
        return False

def test_api_service():
    """测试API服务"""
    print("\n🌐 测试API服务...")
    
    try:
        import requests
        
        api_url = "http://localhost:19088"
        test_endpoints = [
            "/api/checkLogin",
            "/api/userInfo",
            "/api/getContacts"
        ]
        
        for endpoint in test_endpoints:
            try:
                response = requests.get(f"{api_url}{endpoint}", timeout=3)
                print(f"  {endpoint}: {response.status_code}")
                if response.status_code == 200:
                    print("    ✅ API服务响应正常")
                    return True
            except requests.exceptions.ConnectionError:
                print(f"  {endpoint}: 连接失败")
            except requests.exceptions.Timeout:
                print(f"  {endpoint}: 超时")
            except Exception as e:
                print(f"  {endpoint}: 异常 - {e}")
        
        print("❌ API服务未响应")
        return False
        
    except ImportError:
        print("❌ requests库未安装")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 注入问题诊断")
    print("=" * 60)
    
    # 检查运行环境
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本路径: {__file__}")
    
    try:
        print(f"打包环境路径: {sys._MEIPASS}")
    except AttributeError:
        print("开发环境（非打包）")
    
    print("\n" + "=" * 60)
    
    # 运行诊断测试
    tests = [
        ("资源文件提取", test_resource_extraction),
        ("注入器执行", test_injector_execution),
        ("微信进程检测", test_wechat_process),
        ("API服务", test_api_service)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 诊断结果总结:")
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    # 建议
    print("\n💡 建议:")
    if not results.get("微信进程检测", False):
        print("  1. 请启动微信PC版并登录")
    
    if not results.get("注入器执行", False):
        print("  2. 检查注入器文件是否正确打包")
        print("  3. 尝试以管理员身份运行程序")
    
    if not results.get("API服务", False):
        print("  4. 检查是否需要先执行注入")
        print("  5. 检查防火墙是否阻止了本地连接")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
