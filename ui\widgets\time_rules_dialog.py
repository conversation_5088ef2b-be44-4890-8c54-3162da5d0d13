"""
时间规则配置对话框

为循环发送提供时间控制配置界面
"""

from datetime import time, date, timedelta
from typing import Optional, List

from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QFormLayout,
    QGroupBox,
    QCheckBox,
    QComboBox,
    QTimeEdit,
    QDateEdit,
    QListWidget,
    QListWidgetItem,
    QPushButton,
    QLabel,
    QSpinBox,
    QMessageBox,
    QScrollArea,
    QWidget,
)
from PyQt6.QtCore import Qt, QTime, QDate, pyqtSignal
from PyQt6.QtGui import QFont

from core.time_rules import (
    TimeRules,
    TimeRange,
    DateRange,
    WeekdayType,
    create_work_hours_rule,
    create_unlimited_rule,
)
from ui.themed_dialog_base import ThemedDialogBase
from ui.themed_message_box import ThemedMessageBoxHelper
from utils.logger import setup_logger

logger = setup_logger("time_rules_dialog")


class TimeRulesDialog(ThemedDialogBase):
    """时间规则配置对话框"""

    rules_changed = pyqtSignal(TimeRules)

    def __init__(self, initial_rules: Optional[TimeRules] = None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("时间规则配置")
        self.setModal(True)
        self.resize(500, 600)

        self.time_rules = initial_rules or create_unlimited_rule()

        # 主题支持已由 ThemedDialogBase 自动设置

        self.init_ui()
        self.load_rules()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 启用时间规则
        self.enable_checkbox = QCheckBox("启用时间规则")
        self.enable_checkbox.stateChanged.connect(self.on_enable_changed)
        scroll_layout.addWidget(self.enable_checkbox)

        # 日期范围配置
        self.date_group = self.create_date_range_group()
        scroll_layout.addWidget(self.date_group)

        # 工作日配置
        self.weekday_group = self.create_weekday_group()
        scroll_layout.addWidget(self.weekday_group)

        # 时间段配置
        self.time_group = self.create_time_range_group()
        scroll_layout.addWidget(self.time_group)

        # 节假日配置
        self.holiday_group = self.create_holiday_group()
        scroll_layout.addWidget(self.holiday_group)

        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)

        # 预设按钮
        preset_layout = QHBoxLayout()

        work_hours_btn = QPushButton("工作时间预设")
        work_hours_btn.clicked.connect(self.apply_work_hours_preset)
        preset_layout.addWidget(work_hours_btn)

        unlimited_btn = QPushButton("无限制预设")
        unlimited_btn.clicked.connect(self.apply_unlimited_preset)
        preset_layout.addWidget(unlimited_btn)

        layout.addLayout(preset_layout)

        # 按钮
        button_layout = QHBoxLayout()

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

    def create_date_range_group(self) -> QGroupBox:
        """创建日期范围配置组"""
        group = QGroupBox("日期范围")
        layout = QFormLayout(group)

        self.start_date_checkbox = QCheckBox("设置开始日期")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate())
        self.start_date_edit.setEnabled(False)
        self.start_date_checkbox.toggled.connect(self.start_date_edit.setEnabled)

        start_layout = QHBoxLayout()
        start_layout.addWidget(self.start_date_checkbox)
        start_layout.addWidget(self.start_date_edit)
        layout.addRow("开始日期:", start_layout)

        self.end_date_checkbox = QCheckBox("设置结束日期")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate().addDays(30))
        self.end_date_edit.setEnabled(False)
        self.end_date_checkbox.toggled.connect(self.end_date_edit.setEnabled)

        end_layout = QHBoxLayout()
        end_layout.addWidget(self.end_date_checkbox)
        end_layout.addWidget(self.end_date_edit)
        layout.addRow("结束日期:", end_layout)

        return group

    def create_weekday_group(self) -> QGroupBox:
        """创建工作日配置组"""
        group = QGroupBox("工作日设置")
        layout = QVBoxLayout(group)

        self.weekday_combo = QComboBox()
        self.weekday_combo.addItems(
            ["所有日期", "工作日(周一至周五)", "周末(周六、周日)", "自定义"]
        )
        self.weekday_combo.currentTextChanged.connect(self.on_weekday_type_changed)
        layout.addWidget(self.weekday_combo)

        # 自定义工作日选择
        self.custom_weekday_widget = QWidget()
        custom_layout = QHBoxLayout(self.custom_weekday_widget)
        custom_layout.setContentsMargins(0, 0, 0, 0)

        self.weekday_checkboxes = []
        weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        for i, name in enumerate(weekday_names):
            checkbox = QCheckBox(name)
            checkbox.setProperty("weekday", i)
            self.weekday_checkboxes.append(checkbox)
            custom_layout.addWidget(checkbox)

        self.custom_weekday_widget.setVisible(False)
        layout.addWidget(self.custom_weekday_widget)

        return group

    def create_time_range_group(self) -> QGroupBox:
        """创建时间段配置组"""
        group = QGroupBox("时间段设置")
        layout = QVBoxLayout(group)

        # 时间段列表
        self.time_range_list = QListWidget()
        layout.addWidget(self.time_range_list)

        # 添加时间段
        add_layout = QHBoxLayout()

        self.start_time_edit = QTimeEdit()
        self.start_time_edit.setTime(QTime(9, 0))
        add_layout.addWidget(QLabel("从:"))
        add_layout.addWidget(self.start_time_edit)

        self.end_time_edit = QTimeEdit()
        self.end_time_edit.setTime(QTime(18, 0))
        add_layout.addWidget(QLabel("到:"))
        add_layout.addWidget(self.end_time_edit)

        add_btn = QPushButton("添加")
        add_btn.clicked.connect(self.add_time_range)
        add_layout.addWidget(add_btn)

        remove_btn = QPushButton("删除")
        remove_btn.clicked.connect(self.remove_time_range)
        add_layout.addWidget(remove_btn)

        layout.addLayout(add_layout)

        return group

    def create_holiday_group(self) -> QGroupBox:
        """创建节假日配置组"""
        group = QGroupBox("节假日设置")
        layout = QVBoxLayout(group)

        self.exclude_holidays_checkbox = QCheckBox("排除节假日")
        layout.addWidget(self.exclude_holidays_checkbox)

        # 自定义节假日列表
        self.holiday_list = QListWidget()
        layout.addWidget(self.holiday_list)

        # 添加节假日
        add_holiday_layout = QHBoxLayout()

        self.holiday_date_edit = QDateEdit()
        self.holiday_date_edit.setDate(QDate.currentDate())
        add_holiday_layout.addWidget(self.holiday_date_edit)

        add_holiday_btn = QPushButton("添加节假日")
        add_holiday_btn.clicked.connect(self.add_holiday)
        add_holiday_layout.addWidget(add_holiday_btn)

        remove_holiday_btn = QPushButton("删除节假日")
        remove_holiday_btn.clicked.connect(self.remove_holiday)
        add_holiday_layout.addWidget(remove_holiday_btn)

        layout.addLayout(add_holiday_layout)

        return group

    def on_enable_changed(self, state):
        """启用状态改变"""
        enabled = state == Qt.CheckState.Checked.value
        self.date_group.setEnabled(enabled)
        self.weekday_group.setEnabled(enabled)
        self.time_group.setEnabled(enabled)
        self.holiday_group.setEnabled(enabled)

    def on_weekday_type_changed(self, text):
        """工作日类型改变"""
        self.custom_weekday_widget.setVisible(text == "自定义")

    def add_time_range(self):
        """添加时间段"""
        start_time = self.start_time_edit.time().toPython()
        end_time = self.end_time_edit.time().toPython()

        if start_time >= end_time:
            ThemedMessageBoxHelper.show_warning(
                self, "警告", "结束时间必须晚于开始时间"
            )
            return

        time_range_text = (
            f"{start_time.strftime('%H:%M')} - {end_time.strftime('%H:%M')}"
        )

        # 检查是否已存在
        for i in range(self.time_range_list.count()):
            if self.time_range_list.item(i).text() == time_range_text:
                ThemedMessageBoxHelper.show_warning(self, "警告", "该时间段已存在")
                return

        item = QListWidgetItem(time_range_text)
        item.setData(Qt.ItemDataRole.UserRole, (start_time, end_time))
        self.time_range_list.addItem(item)

    def remove_time_range(self):
        """删除时间段"""
        current_item = self.time_range_list.currentItem()
        if current_item:
            self.time_range_list.takeItem(self.time_range_list.row(current_item))

    def add_holiday(self):
        """添加节假日"""
        holiday_date = self.holiday_date_edit.date().toPython()
        date_text = holiday_date.strftime("%Y-%m-%d")

        # 检查是否已存在
        for i in range(self.holiday_list.count()):
            if self.holiday_list.item(i).text() == date_text:
                ThemedMessageBoxHelper.show_warning(self, "警告", "该节假日已存在")
                return

        item = QListWidgetItem(date_text)
        item.setData(Qt.ItemDataRole.UserRole, holiday_date)
        self.holiday_list.addItem(item)

    def remove_holiday(self):
        """删除节假日"""
        current_item = self.holiday_list.currentItem()
        if current_item:
            self.holiday_list.takeItem(self.holiday_list.row(current_item))

    def apply_work_hours_preset(self):
        """应用工作时间预设"""
        self.time_rules = create_work_hours_rule()
        self.load_rules()

    def apply_unlimited_preset(self):
        """应用无限制预设"""
        self.time_rules = create_unlimited_rule()
        self.load_rules()

    def load_rules(self):
        """加载规则到UI"""
        # 启用状态
        self.enable_checkbox.setChecked(self.time_rules.enabled)
        self.on_enable_changed(
            Qt.CheckState.Checked.value
            if self.time_rules.enabled
            else Qt.CheckState.Unchecked.value
        )

        # 日期范围
        if self.time_rules.date_range.start_date:
            self.start_date_checkbox.setChecked(True)
            self.start_date_edit.setDate(QDate(self.time_rules.date_range.start_date))
        else:
            self.start_date_checkbox.setChecked(False)

        if self.time_rules.date_range.end_date:
            self.end_date_checkbox.setChecked(True)
            self.end_date_edit.setDate(QDate(self.time_rules.date_range.end_date))
        else:
            self.end_date_checkbox.setChecked(False)

        # 工作日类型
        weekday_map = {
            WeekdayType.ALL_DAYS: 0,
            WeekdayType.WEEKDAYS: 1,
            WeekdayType.WEEKENDS: 2,
            WeekdayType.CUSTOM: 3,
        }
        self.weekday_combo.setCurrentIndex(
            weekday_map.get(self.time_rules.weekday_type, 0)
        )

        # 自定义工作日
        for checkbox in self.weekday_checkboxes:
            weekday = checkbox.property("weekday")
            checkbox.setChecked(weekday in self.time_rules.custom_weekdays)

        # 时间段
        self.time_range_list.clear()
        for time_range in self.time_rules.time_ranges:
            time_range_text = f"{time_range.start_time.strftime('%H:%M')} - {time_range.end_time.strftime('%H:%M')}"
            item = QListWidgetItem(time_range_text)
            item.setData(
                Qt.ItemDataRole.UserRole, (time_range.start_time, time_range.end_time)
            )
            self.time_range_list.addItem(item)

        # 节假日
        self.exclude_holidays_checkbox.setChecked(self.time_rules.exclude_holidays)
        self.holiday_list.clear()
        for holiday_date in self.time_rules.custom_holidays:
            date_text = holiday_date.strftime("%Y-%m-%d")
            item = QListWidgetItem(date_text)
            item.setData(Qt.ItemDataRole.UserRole, holiday_date)
            self.holiday_list.addItem(item)

    def get_rules(self) -> TimeRules:
        """从UI获取规则"""
        # 基础设置
        enabled = self.enable_checkbox.isChecked()

        # 日期范围
        start_date = None
        if self.start_date_checkbox.isChecked():
            start_date = self.start_date_edit.date().toPython()

        end_date = None
        if self.end_date_checkbox.isChecked():
            end_date = self.end_date_edit.date().toPython()

        date_range = DateRange(start_date, end_date)

        # 工作日类型
        weekday_types = [
            WeekdayType.ALL_DAYS,
            WeekdayType.WEEKDAYS,
            WeekdayType.WEEKENDS,
            WeekdayType.CUSTOM,
        ]
        weekday_type = weekday_types[self.weekday_combo.currentIndex()]

        # 自定义工作日
        custom_weekdays = []
        if weekday_type == WeekdayType.CUSTOM:
            for checkbox in self.weekday_checkboxes:
                if checkbox.isChecked():
                    custom_weekdays.append(checkbox.property("weekday"))

        # 时间段
        time_ranges = []
        for i in range(self.time_range_list.count()):
            item = self.time_range_list.item(i)
            start_time, end_time = item.data(Qt.ItemDataRole.UserRole)
            time_ranges.append(TimeRange(start_time, end_time))

        # 节假日
        exclude_holidays = self.exclude_holidays_checkbox.isChecked()
        custom_holidays = []
        for i in range(self.holiday_list.count()):
            item = self.holiday_list.item(i)
            holiday_date = item.data(Qt.ItemDataRole.UserRole)
            custom_holidays.append(holiday_date)

        return TimeRules(
            enabled=enabled,
            date_range=date_range,
            weekday_type=weekday_type,
            custom_weekdays=custom_weekdays,
            time_ranges=time_ranges,
            exclude_holidays=exclude_holidays,
            custom_holidays=custom_holidays,
        )

    def accept(self):
        """确定按钮"""
        try:
            rules = self.get_rules()
            self.rules_changed.emit(rules)
            super().accept()
        except Exception as e:
            ThemedMessageBoxHelper.show_error(self, "错误", f"配置错误: {e}")
            logger.error(f"时间规则配置错误: {e}")
