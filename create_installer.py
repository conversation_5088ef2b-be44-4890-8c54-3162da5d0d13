#!/usr/bin/env python3
"""
创建安装程序 - Meet space 微信群发助手
自动下载Inno Setup并创建exe安装文件
"""

import os
import sys
import subprocess
import urllib.request
import time
from pathlib import Path
import tempfile
import shutil


class InstallerBuilder:
    """安装程序构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.temp_dir = Path(tempfile.gettempdir()) / "wx_installer_build"
        self.inno_setup_path = None
        
    def log(self, message: str, level: str = "INFO") -> None:
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "STEP": "🔄"
        }.get(level, "ℹ️")
        
        print(f"[{timestamp}] {prefix} {message}")
        
    def check_inno_setup(self) -> bool:
        """检查Inno Setup是否已安装"""
        self.log("检查Inno Setup安装状态...", "STEP")
        
        # 常见的Inno Setup安装路径
        possible_paths = [
            r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
            r"C:\Program Files\Inno Setup 6\ISCC.exe",
            r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
            r"C:\Program Files\Inno Setup 5\ISCC.exe",
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                self.inno_setup_path = path
                self.log(f"找到Inno Setup: {path}", "SUCCESS")
                return True
                
        self.log("未找到Inno Setup", "WARNING")
        return False
        
    def download_inno_setup(self) -> bool:
        """下载Inno Setup"""
        self.log("下载Inno Setup...", "STEP")
        
        try:
            # 创建临时目录
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            
            # 下载URL
            download_url = "https://jrsoftware.org/download.php/is.exe"
            installer_path = self.temp_dir / "innosetup-installer.exe"
            
            self.log(f"正在下载: {download_url}", "INFO")
            
            # 下载文件
            urllib.request.urlretrieve(download_url, installer_path)
            
            if installer_path.exists():
                file_size = installer_path.stat().st_size / (1024 * 1024)
                self.log(f"下载完成: {file_size:.1f} MB", "SUCCESS")
                return True
            else:
                self.log("下载失败", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"下载Inno Setup失败: {e}", "ERROR")
            return False
            
    def install_inno_setup(self) -> bool:
        """安装Inno Setup"""
        self.log("安装Inno Setup...", "STEP")
        
        try:
            installer_path = self.temp_dir / "innosetup-installer.exe"
            
            if not installer_path.exists():
                self.log("安装文件不存在", "ERROR")
                return False
                
            # 静默安装Inno Setup
            cmd = [str(installer_path), "/SILENT", "/NORESTART"]
            
            self.log("正在安装Inno Setup（静默模式）...", "INFO")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log("Inno Setup安装完成", "SUCCESS")
                
                # 等待安装完成
                time.sleep(3)
                
                # 重新检查安装
                if self.check_inno_setup():
                    return True
                else:
                    self.log("安装后未找到Inno Setup", "ERROR")
                    return False
            else:
                self.log(f"安装失败: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"安装Inno Setup失败: {e}", "ERROR")
            return False
            
    def ensure_inno_setup(self) -> bool:
        """确保Inno Setup可用"""
        if self.check_inno_setup():
            return True
            
        self.log("需要安装Inno Setup", "INFO")
        
        if not self.download_inno_setup():
            return False
            
        if not self.install_inno_setup():
            return False
            
        return True
        
    def build_executable(self) -> bool:
        """构建可执行文件"""
        self.log("检查可执行文件...", "STEP")
        
        dist_dir = self.project_root / "dist" / "MeetSpaceWeChatSender"
        exe_file = dist_dir / "MeetSpaceWeChatSender.exe"
        
        if exe_file.exists():
            self.log("可执行文件已存在", "SUCCESS")
            return True
            
        self.log("可执行文件不存在，开始构建...", "INFO")
        
        try:
            # 运行自动构建脚本
            result = subprocess.run([
                sys.executable, "auto_build.py"
            ], cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0 and exe_file.exists():
                self.log("可执行文件构建成功", "SUCCESS")
                return True
            else:
                self.log("可执行文件构建失败", "ERROR")
                if result.stderr:
                    self.log(result.stderr, "ERROR")
                return False
                
        except Exception as e:
            self.log(f"构建可执行文件失败: {e}", "ERROR")
            return False
            
    def create_installer(self) -> bool:
        """创建安装程序"""
        self.log("创建安装程序...", "STEP")
        
        if not self.inno_setup_path:
            self.log("Inno Setup编译器不可用", "ERROR")
            return False
            
        setup_script = self.project_root / "installer" / "setup.iss"
        if not setup_script.exists():
            self.log("setup.iss文件不存在", "ERROR")
            return False
            
        try:
            # 确保输出目录存在
            output_dir = self.project_root / "installer" / "Output"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 运行Inno Setup编译器
            cmd = [self.inno_setup_path, str(setup_script)]
            
            self.log(f"执行命令: {' '.join(cmd)}", "INFO")
            
            result = subprocess.run(
                cmd, 
                cwd=self.project_root / "installer",
                capture_output=True, 
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                self.log("安装程序创建成功", "SUCCESS")
                
                # 查找生成的安装程序
                setup_files = list(output_dir.glob("*.exe"))
                if setup_files:
                    setup_file = setup_files[0]
                    file_size = setup_file.stat().st_size / (1024 * 1024)
                    self.log(f"安装程序: {setup_file.name} ({file_size:.1f} MB)", "SUCCESS")
                    
                    # 移动到项目根目录
                    final_path = self.project_root / setup_file.name
                    if final_path.exists():
                        final_path.unlink()
                    shutil.move(str(setup_file), str(final_path))
                    self.log(f"安装程序已移动到: {final_path}", "SUCCESS")
                    
                return True
            else:
                self.log("安装程序创建失败", "ERROR")
                if result.stdout:
                    self.log(result.stdout, "ERROR")
                if result.stderr:
                    self.log(result.stderr, "ERROR")
                return False
                
        except Exception as e:
            self.log(f"创建安装程序失败: {e}", "ERROR")
            return False
            
    def cleanup(self) -> None:
        """清理临时文件"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                self.log("临时文件已清理", "INFO")
        except Exception as e:
            self.log(f"清理临时文件失败: {e}", "WARNING")
            
    def build(self) -> bool:
        """执行完整的构建流程"""
        self.log("=== 开始创建安装程序 ===", "STEP")
        start_time = time.time()
        
        try:
            # 1. 确保Inno Setup可用
            if not self.ensure_inno_setup():
                return False
                
            # 2. 构建可执行文件
            if not self.build_executable():
                return False
                
            # 3. 创建安装程序
            if not self.create_installer():
                return False
                
            # 构建完成
            elapsed_time = time.time() - start_time
            self.log(f"=== 安装程序创建完成 (耗时: {elapsed_time:.1f}秒) ===", "SUCCESS")
            
            return True
            
        except KeyboardInterrupt:
            self.log("构建被用户中断", "WARNING")
            return False
        except Exception as e:
            self.log(f"构建过程出错: {e}", "ERROR")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    print("🚀 Meet space 微信群发助手 - 安装程序创建工具")
    print("=" * 60)
    
    builder = InstallerBuilder()
    success = builder.build()
    
    if success:
        print("\n🎉 安装程序创建成功！")
        
        # 显示结果文件
        setup_files = list(Path(".").glob("MeetSpace_WeChatSender_Setup_*.exe"))
        if setup_files:
            setup_file = setup_files[0]
            file_size = setup_file.stat().st_size / (1024 * 1024)
            print(f"📦 安装程序: {setup_file.name} ({file_size:.1f} MB)")
            
        # 询问是否打开文件夹
        try:
            choice = input("\n是否打开输出文件夹？(y/n): ").strip().lower()
            if choice == 'y':
                subprocess.run(['explorer', str(Path(__file__).parent)], check=False)
        except KeyboardInterrupt:
            pass
            
        sys.exit(0)
    else:
        print("\n💥 安装程序创建失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
