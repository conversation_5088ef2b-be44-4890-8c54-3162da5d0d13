#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的打包脚本

确保包含所有必要的工具文件和依赖。
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_required_files():
    """检查必需的文件是否存在"""
    print("🔍 检查必需文件...")
    
    required_files = [
        "main.py",
        "resources/icons/app.ico",
        "tools/Injector.exe",
        "tools/x64/Injector.exe",
        "tools/Win32/Injector.exe",
        "tools/ARM64/Injector.exe",
        "wxhelper_files/wxhelper.dll",
        "README.md",
        "LICENSE.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必需文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def clean_old_build():
    """清理旧的构建文件"""
    print("🧹 清理旧的构建文件...")
    
    paths_to_clean = ["dist", "build"]
    for path in paths_to_clean:
        if Path(path).exists():
            try:
                shutil.rmtree(path)
                print(f"   - 已删除: {path}")
            except Exception as e:
                print(f"   - 删除失败 {path}: {e}")
    
    # 删除spec文件
    for spec_file in Path(".").glob("*.spec"):
        try:
            spec_file.unlink()
            print(f"   - 已删除: {spec_file}")
        except Exception as e:
            print(f"   - 删除失败 {spec_file}: {e}")

def build_application():
    """构建应用程序"""
    print("🚀 开始构建应用程序...")
    
    # PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--noconfirm",
        "--onedir",
        "--windowed",
        "--name=MeetSpaceWeChatSender",
        "--icon=resources/icons/app.ico",
        
        # 数据文件 - 使用递归方式确保包含所有子目录
        "--add-data=resources;resources",
        "--add-data=config;config_templates",
        "--add-data=tools;tools",
        "--add-data=wxhelper_files;wxhelper_files",
        "--add-data=README.md;.",
        "--add-data=LICENSE.txt;.",
        
        # 核心隐藏导入
        "--hidden-import=dis",
        "--hidden-import=inspect",
        "--hidden-import=types",
        "--hidden-import=collections.abc",
        "--hidden-import=opcode",
        "--hidden-import=keyword",
        "--hidden-import=token",
        "--hidden-import=tokenize",
        
        # PyQt6相关
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=PyQt6.QtNetwork",
        
        # 微信相关
        "--hidden-import=wcferry",
        "--hidden-import=wcferry.wcf",
        
        # Windows API
        "--hidden-import=win32api",
        "--hidden-import=win32con",
        "--hidden-import=win32gui",
        "--hidden-import=win32process",
        "--hidden-import=psutil",
        
        # 网络和加密
        "--hidden-import=requests",
        "--hidden-import=websocket",
        "--hidden-import=cryptography",
        "--hidden-import=ssl",
        "--hidden-import=socket",
        
        # 数据处理
        "--hidden-import=sqlite3",
        "--hidden-import=json",
        "--hidden-import=xml.etree.ElementTree",
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        
        # 邮件相关
        "--hidden-import=email.mime.text",
        "--hidden-import=email.mime.multipart",
        "--hidden-import=email.mime.base",
        
        # HTTP相关
        "--hidden-import=urllib3",
        "--hidden-import=certifi",
        "--hidden-import=charset_normalizer",
        "--hidden-import=idna",
        
        # 异步相关
        "--hidden-import=asyncio",
        "--hidden-import=aiohttp",
        
        # 排除不需要的模块以减小体积
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=pandas",
        "--exclude-module=scipy",
        "--exclude-module=jupyter",
        "--exclude-module=IPython",
        "--exclude-module=notebook",
        "--exclude-module=pytest",
        "--exclude-module=unittest",
        "--exclude-module=test",
        
        # 主脚本
        "main.py"
    ]
    
    print(f"执行命令: PyInstaller (共{len(cmd)}个参数)")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        print("✅ PyInstaller构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller构建失败: {e}")
        print("错误输出:")
        print(e.stderr)
        return False

def verify_build():
    """验证构建结果"""
    print("🔍 验证构建结果...")
    
    exe_path = Path("dist/MeetSpaceWeChatSender/MeetSpaceWeChatSender.exe")
    if not exe_path.exists():
        print("❌ 可执行文件不存在")
        return False
    
    # 检查关键工具文件
    critical_files = [
        "dist/MeetSpaceWeChatSender/_internal/tools/Injector.exe",
        "dist/MeetSpaceWeChatSender/_internal/tools/x64/Injector.exe",
        "dist/MeetSpaceWeChatSender/_internal/tools/Win32/Injector.exe",
        "dist/MeetSpaceWeChatSender/_internal/tools/ARM64/Injector.exe",
        "dist/MeetSpaceWeChatSender/_internal/wxhelper_files/wxhelper.dll",
        "dist/MeetSpaceWeChatSender/_internal/resources/icons/app.ico"
    ]
    
    missing_critical = []
    for file_path in critical_files:
        if not Path(file_path).exists():
            missing_critical.append(file_path)
    
    if missing_critical:
        print("❌ 缺少关键文件:")
        for file_path in missing_critical:
            print(f"   - {file_path}")
        return False
    
    # 显示构建信息
    size_mb = exe_path.stat().st_size / (1024 * 1024)
    print(f"✅ 可执行文件: {exe_path}")
    print(f"📊 文件大小: {size_mb:.1f} MB")
    
    # 统计文件数量
    dist_dir = Path("dist/MeetSpaceWeChatSender")
    file_count = len(list(dist_dir.rglob("*")))
    print(f"📁 总文件数: {file_count}")
    
    print("✅ 所有关键文件都存在")
    return True

def main():
    """主函数"""
    print("🏗️ Meet space 微信群发助手 - 完整构建脚本")
    print("=" * 60)
    
    # 设置工作目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 检查必需文件
    if not check_required_files():
        print("\n💥 构建失败：缺少必需文件")
        return False
    
    # 清理旧文件
    clean_old_build()
    
    # 构建应用程序
    if not build_application():
        print("\n💥 构建失败：PyInstaller执行失败")
        return False
    
    # 验证构建结果
    if not verify_build():
        print("\n💥 构建失败：验证失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 构建成功！")
    print("\n📦 构建结果:")
    print("   - 可执行文件: dist/MeetSpaceWeChatSender/MeetSpaceWeChatSender.exe")
    print("   - 分发目录: dist/MeetSpaceWeChatSender/")
    print("\n💡 使用说明:")
    print("   - 可以直接运行可执行文件")
    print("   - 也可以将整个MeetSpaceWeChatSender文件夹分发给用户")
    print("   - 用户无需安装Python或其他依赖")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
