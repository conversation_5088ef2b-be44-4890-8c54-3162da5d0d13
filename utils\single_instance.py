#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单实例管理器

确保程序在系统上只能启动一个实例。
支持Windows、Linux、macOS多平台。
"""

import os
import sys
import time
import socket
import tempfile
import platform
from pathlib import Path
from typing import Optional

from utils.logger import setup_logger

logger = setup_logger("single_instance")


class SingleInstanceManager:
    """单实例管理器"""
    
    def __init__(self, app_name: str = "wx_group_sender"):
        self.app_name = app_name
        self.system = platform.system().lower()
        self.lock_file: Optional[str] = None
        self.socket_server: Optional[socket.socket] = None
        self.port: int = 0
        
        # 根据系统选择锁定方式
        if self.system == "windows":
            self._init_windows_lock()
        else:
            self._init_unix_lock()
    
    def _init_windows_lock(self):
        """初始化Windows锁定方式（使用端口锁定）"""
        # Windows使用端口锁定，更可靠
        self.port = self._get_app_port()
        logger.debug(f"Windows系统，使用端口锁定: {self.port}")
    
    def _init_unix_lock(self):
        """初始化Unix/Linux锁定方式（使用文件锁定）"""
        # Unix/Linux使用文件锁定
        if self.system == "darwin":  # macOS
            lock_dir = Path.home() / "Library" / "Application Support" / self.app_name
        else:  # Linux
            lock_dir = Path.home() / ".local" / "share" / self.app_name
        
        lock_dir.mkdir(parents=True, exist_ok=True)
        self.lock_file = str(lock_dir / f"{self.app_name}.lock")
        logger.debug(f"Unix系统，使用文件锁定: {self.lock_file}")
    
    def _get_app_port(self) -> int:
        """获取应用专用端口"""
        # 为特定应用名使用固定端口号
        if self.app_name == "wx_group_sender":
            return 58888  # 微信群发助手专用端口
        else:
            # 其他应用基于名称生成端口
            port_base = 50000
            app_hash = abs(hash(self.app_name)) % 10000
            return port_base + app_hash
    
    def is_running(self) -> bool:
        """检查是否已有实例在运行"""
        try:
            if self.system == "windows":
                return self._check_windows_instance()
            else:
                return self._check_unix_instance()
        except Exception as e:
            logger.error(f"检查实例状态失败: {e}")
            return False
    
    def _check_windows_instance(self) -> bool:
        """检查Windows实例（端口方式）"""
        try:
            # 尝试绑定端口（不使用SO_REUSEADDR）
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.bind(("127.0.0.1", self.port))
            test_socket.close()
            return False  # 端口可用，没有实例运行
        except OSError:
            return True  # 端口被占用，有实例运行
    
    def _check_unix_instance(self) -> bool:
        """检查Unix实例（文件锁方式）"""
        if not os.path.exists(self.lock_file):
            return False
        
        try:
            # 读取锁文件中的PID
            with open(self.lock_file, 'r') as f:
                pid_str = f.read().strip()
            
            if not pid_str.isdigit():
                return False
            
            pid = int(pid_str)
            
            # 检查进程是否存在
            try:
                os.kill(pid, 0)  # 发送信号0检查进程是否存在
                return True  # 进程存在
            except OSError:
                # 进程不存在，删除过期的锁文件
                os.remove(self.lock_file)
                return False
                
        except Exception as e:
            logger.warning(f"检查锁文件失败: {e}")
            return False
    
    def acquire_lock(self) -> bool:
        """获取锁定（启动实例）"""
        try:
            if self.system == "windows":
                return self._acquire_windows_lock()
            else:
                return self._acquire_unix_lock()
        except Exception as e:
            logger.error(f"获取锁定失败: {e}")
            return False
    
    def _acquire_windows_lock(self) -> bool:
        """获取Windows锁定"""
        try:
            self.socket_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            # 不使用SO_REUSEADDR，确保端口独占
            self.socket_server.bind(("127.0.0.1", self.port))
            self.socket_server.listen(1)
            logger.info(f"成功获取Windows锁定，端口: {self.port}")
            return True
        except OSError as e:
            logger.warning(f"获取Windows锁定失败: {e}")
            return False
    
    def _acquire_unix_lock(self) -> bool:
        """获取Unix锁定"""
        try:
            # 写入当前进程PID到锁文件
            with open(self.lock_file, 'w') as f:
                f.write(str(os.getpid()))
            logger.info(f"成功获取Unix锁定，文件: {self.lock_file}")
            return True
        except Exception as e:
            logger.warning(f"获取Unix锁定失败: {e}")
            return False
    
    def release_lock(self):
        """释放锁定"""
        try:
            logger.debug(f"开始释放{self.system}系统的锁定...")
            if self.system == "windows":
                self._release_windows_lock()
            else:
                self._release_unix_lock()
            logger.debug("锁定释放完成")
        except Exception as e:
            logger.error(f"释放锁定失败: {e}")

    def _release_windows_lock(self):
        """释放Windows锁定"""
        if self.socket_server:
            try:
                self.socket_server.close()
                self.socket_server = None
                logger.debug(f"Windows端口锁定已释放: {self.port}")
            except Exception as e:
                logger.warning(f"释放Windows锁定失败: {e}")
        else:
            logger.debug("Windows锁定已经释放或未初始化")

    def _release_unix_lock(self):
        """释放Unix锁定"""
        if self.lock_file and os.path.exists(self.lock_file):
            try:
                os.remove(self.lock_file)
                logger.debug(f"Unix文件锁定已释放: {self.lock_file}")
            except Exception as e:
                logger.warning(f"释放Unix锁定失败: {e}")
        else:
            logger.debug("Unix锁定已经释放或未初始化")
    
    def get_running_instance_info(self) -> dict:
        """获取正在运行的实例信息"""
        info = {
            "system": self.system,
            "app_name": self.app_name,
            "lock_method": "port" if self.system == "windows" else "file",
            "is_running": self.is_running()
        }
        
        if self.system == "windows":
            info["port"] = self.port
        else:
            info["lock_file"] = self.lock_file
            if self.is_running() and os.path.exists(self.lock_file):
                try:
                    with open(self.lock_file, 'r') as f:
                        info["pid"] = int(f.read().strip())
                except:
                    info["pid"] = None
        
        return info
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.acquire_lock():
            raise RuntimeError("无法获取单实例锁定")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release_lock()


def check_single_instance(app_name: str = "wx_group_sender") -> bool:
    """
    检查单实例运行
    
    Args:
        app_name: 应用名称
        
    Returns:
        True: 可以运行（没有其他实例）
        False: 不能运行（已有实例在运行）
    """
    manager = SingleInstanceManager(app_name)
    return not manager.is_running()


def ensure_single_instance(app_name: str = "wx_group_sender") -> SingleInstanceManager:
    """
    确保单实例运行
    
    Args:
        app_name: 应用名称
        
    Returns:
        SingleInstanceManager实例
        
    Raises:
        RuntimeError: 如果已有实例在运行
    """
    manager = SingleInstanceManager(app_name)
    
    if manager.is_running():
        info = manager.get_running_instance_info()
        raise RuntimeError(f"程序已在运行中！\n实例信息: {info}")
    
    if not manager.acquire_lock():
        raise RuntimeError("无法获取单实例锁定")
    
    return manager


if __name__ == "__main__":
    # 测试单实例管理器
    print("🧪 测试单实例管理器")
    print("=" * 50)
    
    try:
        manager = SingleInstanceManager("test_app")
        
        print(f"系统: {manager.system}")
        print(f"应用名: {manager.app_name}")
        
        # 检查是否已运行
        is_running = manager.is_running()
        print(f"是否已运行: {is_running}")
        
        if not is_running:
            # 获取锁定
            if manager.acquire_lock():
                print("✅ 成功获取锁定")
                
                # 获取实例信息
                info = manager.get_running_instance_info()
                print(f"实例信息: {info}")
                
                # 模拟运行
                print("程序运行中...")
                time.sleep(2)
                
                # 释放锁定
                manager.release_lock()
                print("✅ 已释放锁定")
            else:
                print("❌ 获取锁定失败")
        else:
            print("❌ 已有实例在运行")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
