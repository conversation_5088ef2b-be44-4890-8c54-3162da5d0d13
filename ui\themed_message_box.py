#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题化消息对话框

替换系统原生的QMessageBox，提供支持主题的消息对话框
"""

from PyQt6.QtWidgets import (
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QWidget,
    QFrame,
    QApplication,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap, QPainter

from ui.themed_dialog_base import ThemedDialogBase
from ui.modern_theme_manager import theme_manager as modern_theme_manager
from utils.logger import setup_logger

logger = setup_logger("themed_message_box")


class ThemedMessageBox(ThemedDialogBase):
    """主题化消息对话框"""

    # 消息类型
    Information = "information"
    Warning = "warning"
    Critical = "critical"
    Question = "question"

    # 按钮类型
    Ok = "ok"
    Cancel = "cancel"
    Yes = "yes"
    No = "no"

    # 返回值
    Accepted = 1
    Rejected = 0

    def __init__(self, parent=None, message_type=Information):
        super().__init__(parent)
        self.message_type = message_type
        self.result_value = self.Rejected

        self.setModal(True)
        self.setFixedSize(400, 200)

        self.setup_ui()
        self.apply_message_type_style()
        self.apply_current_theme()

    def setup_ui(self):
        """设置UI"""
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # 内容区域
        content_layout = QHBoxLayout()

        # 图标
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(48, 48)
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(self.icon_label)

        # 消息文本
        self.message_label = QLabel()
        self.message_label.setWordWrap(True)
        self.message_label.setAlignment(
            Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter
        )
        content_layout.addWidget(self.message_label, 1)

        layout.addLayout(content_layout)

        # 按钮区域
        self.buttons_layout = QHBoxLayout()
        self.buttons_layout.addStretch()
        layout.addLayout(self.buttons_layout)

        # 按钮容器
        self.buttons = {}

    def apply_message_type_style(self):
        """应用消息类型样式"""
        icon_text = ""

        if self.message_type == self.Information:
            icon_text = "ℹ️"
            self.setWindowTitle("信息")
        elif self.message_type == self.Warning:
            icon_text = "⚠️"
            self.setWindowTitle("警告")
        elif self.message_type == self.Critical:
            icon_text = "❌"
            self.setWindowTitle("错误")
        elif self.message_type == self.Question:
            icon_text = "❓"
            self.setWindowTitle("确认")

        # 设置图标
        self.icon_label.setText(icon_text)
        self.icon_label.setStyleSheet(
            """
            QLabel {
                font-size: 32px;
                background-color: transparent;
            }
        """
        )

    def apply_current_theme(self):
        """应用当前主题和字体大小"""
        try:
            # 获取当前应用字体并应用到对话框
            app = QApplication.instance()
            if app:
                current_font = app.font()
                self.setFont(current_font)

                # 递归设置所有子组件字体
                for child in self.findChildren(QWidget):
                    try:
                        child.setFont(current_font)
                    except Exception:
                        pass

            # 应用Windows标题栏主题（如果支持）
            self._apply_title_bar_theme()

            # 强制刷新样式
            self.style().unpolish(self)
            self.style().polish(self)
            self.update()

            # 调整对话框大小以适应字体变化
            self.adjustSize()

        except Exception as e:
            logger.error(f"应用主题到消息框失败: {e}")

    def _apply_title_bar_theme(self):
        """应用标题栏主题"""
        try:
            import platform
            if platform.system() == "Windows":
                # 获取当前主题
                from ui.modern_theme_manager import theme_manager
                current_theme = theme_manager.get_current_theme()

                # 根据主题设置标题栏
                if current_theme in ["深色主题", "科技主题"]:
                    self._set_windows_dark_title_bar(True)
                else:
                    self._set_windows_dark_title_bar(False)
        except Exception as e:
            logger.debug(f"应用标题栏主题失败: {e}")

    def _set_windows_dark_title_bar(self, dark_mode: bool):
        """设置Windows标题栏为深色模式"""
        try:
            import ctypes
            from ctypes import wintypes

            hwnd = int(self.winId())
            if hwnd:
                # DWMWA_USE_IMMERSIVE_DARK_MODE
                DWMWA_USE_IMMERSIVE_DARK_MODE = 20
                value = ctypes.c_int(1 if dark_mode else 0)

                # 尝试设置深色标题栏
                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_USE_IMMERSIVE_DARK_MODE,
                    ctypes.byref(value),
                    ctypes.sizeof(value)
                )
        except Exception as e:
            logger.debug(f"设置Windows标题栏主题失败: {e}")

    def set_text(self, text: str):
        """设置消息文本"""
        self.message_label.setText(text)

    def set_window_title(self, title: str):
        """设置窗口标题"""
        self.setWindowTitle(title)

    def add_button(self, button_type: str, text: str = None) -> QPushButton:
        """添加按钮"""
        if text is None:
            text = self._get_default_button_text(button_type)

        button = QPushButton(text)
        button.setMinimumWidth(80)

        # 设置按钮样式类
        if button_type in [self.Ok, self.Yes]:
            button.setProperty("class", "primary")
        elif button_type in [self.Cancel, self.No]:
            button.setProperty("class", "secondary")

        # 连接信号
        if button_type == self.Ok:
            button.clicked.connect(lambda: self._button_clicked(self.Accepted))
        elif button_type == self.Cancel:
            button.clicked.connect(lambda: self._button_clicked(self.Rejected))
        elif button_type == self.Yes:
            button.clicked.connect(lambda: self._button_clicked(self.Accepted))
        elif button_type == self.No:
            button.clicked.connect(lambda: self._button_clicked(self.Rejected))

        self.buttons[button_type] = button
        self.buttons_layout.addWidget(button)

        return button

    def _get_default_button_text(self, button_type: str) -> str:
        """获取默认按钮文本"""
        button_texts = {
            self.Ok: "确定",
            self.Cancel: "取消",
            self.Yes: "是",
            self.No: "否",
        }
        return button_texts.get(button_type, "确定")

    def _button_clicked(self, result: int):
        """按钮点击处理"""
        self.result_value = result
        if result == self.Accepted:
            self.accept()
        else:
            self.reject()

    def exec(self) -> int:
        """执行对话框"""
        super().exec()
        return self.result_value

    @staticmethod
    def information(parent, title: str, message: str) -> int:
        """显示信息对话框"""
        dialog = ThemedMessageBox(parent, ThemedMessageBox.Information)
        dialog.set_window_title(title)
        dialog.set_text(message)
        dialog.add_button(ThemedMessageBox.Ok)
        # 确保对话框立即应用当前主题
        dialog.apply_current_theme()
        return dialog.exec()

    @staticmethod
    def warning(parent, title: str, message: str) -> int:
        """显示警告对话框"""
        dialog = ThemedMessageBox(parent, ThemedMessageBox.Warning)
        dialog.set_window_title(title)
        dialog.set_text(message)
        dialog.add_button(ThemedMessageBox.Ok)
        # 确保对话框立即应用当前主题
        dialog.apply_current_theme()
        return dialog.exec()

    @staticmethod
    def critical(parent, title: str, message: str) -> int:
        """显示错误对话框"""
        dialog = ThemedMessageBox(parent, ThemedMessageBox.Critical)
        dialog.set_window_title(title)
        dialog.set_text(message)
        dialog.add_button(ThemedMessageBox.Ok)
        # 确保对话框立即应用当前主题
        dialog.apply_current_theme()
        return dialog.exec()

    @staticmethod
    def question(parent, title: str, message: str) -> int:
        """显示确认对话框"""
        dialog = ThemedMessageBox(parent, ThemedMessageBox.Question)
        dialog.set_window_title(title)
        dialog.set_text(message)
        dialog.add_button(ThemedMessageBox.Yes)
        dialog.add_button(ThemedMessageBox.No)
        # 确保对话框立即应用当前主题
        dialog.apply_current_theme()
        return dialog.exec()


class ThemedMessageBoxHelper:
    """主题化消息对话框助手类

    提供便捷的静态方法来显示各种类型的消息对话框
    """

    @staticmethod
    def show_information(parent, title: str, message: str):
        """显示信息对话框"""
        return ThemedMessageBox.information(parent, title, message)

    @staticmethod
    def show_warning(parent, title: str, message: str):
        """显示警告对话框"""
        return ThemedMessageBox.warning(parent, title, message)

    @staticmethod
    def show_error(parent, title: str, message: str):
        """显示错误对话框"""
        return ThemedMessageBox.critical(parent, title, message)

    @staticmethod
    def show_success(parent, title: str, message: str):
        """显示成功对话框（使用信息对话框样式）"""
        return ThemedMessageBox.information(parent, title, message)

    @staticmethod
    def show_question(parent, title: str, message: str) -> bool:
        """显示确认对话框，返回True表示用户点击了"是"或"确定" """
        result = ThemedMessageBox.question(parent, title, message)
        return result == ThemedMessageBox.Accepted

    @staticmethod
    def show_confirmation(parent, title: str, message: str) -> bool:
        """显示确认对话框（别名方法）"""
        return ThemedMessageBoxHelper.show_question(parent, title, message)


# 为了兼容性，提供一个简化的接口
def show_themed_message(
    parent, message_type: str, title: str, message: str, buttons: list = None
):
    """显示主题化消息对话框

    Args:
        parent: 父窗口
        message_type: 消息类型 ("info", "warning", "error", "question")
        title: 标题
        message: 消息内容
        buttons: 按钮列表，如 ["ok"], ["yes", "no"] 等

    Returns:
        int: 对话框结果
    """
    type_mapping = {
        "info": ThemedMessageBox.Information,
        "information": ThemedMessageBox.Information,
        "warning": ThemedMessageBox.Warning,
        "error": ThemedMessageBox.Critical,
        "critical": ThemedMessageBox.Critical,
        "question": ThemedMessageBox.Question,
    }

    dialog_type = type_mapping.get(message_type.lower(), ThemedMessageBox.Information)
    dialog = ThemedMessageBox(parent, dialog_type)
    dialog.set_window_title(title)
    dialog.set_text(message)

    # 添加按钮
    if buttons is None:
        if message_type.lower() == "question":
            buttons = ["yes", "no"]
        else:
            buttons = ["ok"]

    for button_type in buttons:
        dialog.add_button(button_type)

    return dialog.exec()
