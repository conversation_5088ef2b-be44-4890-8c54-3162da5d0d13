#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序退出时的保存机制
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_path_manager_packaging_support():
    """测试路径管理器的打包支持"""
    print("🔧 测试路径管理器的打包支持...")
    
    try:
        from utils.path_manager import path_manager
        
        # 检查路径管理器的基本功能
        print(f"✅ 可执行文件目录: {path_manager.executable_dir}")
        print(f"✅ 应用数据目录: {path_manager.app_data_dir}")
        print(f"✅ 用户数据目录: {path_manager.user_data_dir}")
        print(f"✅ 临时目录: {path_manager.temp_dir}")
        print(f"✅ 是否打包环境: {path_manager.is_frozen}")

        # 检查配置文件路径
        config_dir = path_manager.app_data_dir / "config"
        print(f"✅ 配置目录: {config_dir}")

        # 检查日志文件路径
        log_dir = path_manager.app_data_dir / "logs"
        print(f"✅ 日志目录: {log_dir}")

        # 检查数据文件路径
        data_dir = path_manager.app_data_dir
        print(f"✅ 数据目录: {data_dir}")
        
        # 验证目录是否存在
        if config_dir.exists():
            print("✅ 配置目录存在")
        else:
            print("❌ 配置目录不存在")
            return False
        
        if log_dir.exists():
            print("✅ 日志目录存在")
        else:
            print("❌ 日志目录不存在")
            return False
        
        if data_dir.exists():
            print("✅ 数据目录存在")
        else:
            print("❌ 数据目录不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exit_cleanup_logic():
    """测试退出清理逻辑"""
    print("\n🔧 测试退出清理逻辑...")
    
    try:
        import inspect
        from ui.main_window import MainWindow
        
        # 检查closeEvent方法
        close_event_source = inspect.getsource(MainWindow.closeEvent)
        
        checks = [
            ("清理资源调用", "cleanup"),
            ("主应用程序清理", "main_app_instance"),
            ("应用程序退出", "app.quit"),
            ("事件接受", "event.accept")
        ]
        
        for check_name, keyword in checks:
            if keyword in close_event_source:
                print(f"✅ {check_name}: 包含 {keyword}")
            else:
                print(f"❌ {check_name}: 缺少 {keyword}")
                return False
        
        # 检查cleanup方法
        cleanup_source = inspect.getsource(MainWindow.cleanup)
        
        cleanup_checks = [
            ("停止定时器", "status_timer"),
            ("断开连接", "disconnect"),
            ("清理异步任务", "async_task_manager"),
            ("停止发送任务", "stop_all_tasks"),
            ("保存任务数据", "save_tasks"),
            ("保存分组设置", "save_groups"),
            ("保存系统设置", "save_config")
        ]
        
        for check_name, keyword in cleanup_checks:
            if keyword in cleanup_source:
                print(f"✅ 清理逻辑-{check_name}: 包含 {keyword}")
            else:
                print(f"❌ 清理逻辑-{check_name}: 缺少 {keyword}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_app_cleanup():
    """测试主应用程序清理逻辑"""
    print("\n🔧 测试主应用程序清理逻辑...")
    
    try:
        import inspect
        
        # 读取main.py文件内容
        main_py_path = Path(__file__).parent / "main.py"
        with open(main_py_path, 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        checks = [
            ("信号处理器", "signal_handler"),
            ("清理方法", "def cleanup"),
            ("保存模板", "save_templates"),
            ("退出备份", "_create_exit_backup"),
            ("清理连接器", "_cleanup_connector")
        ]
        
        for check_name, keyword in checks:
            if keyword in main_content:
                print(f"✅ 主应用程序-{check_name}: 包含 {keyword}")
            else:
                print(f"❌ 主应用程序-{check_name}: 缺少 {keyword}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_save_mechanisms():
    """测试配置保存机制"""
    print("\n🔧 测试配置保存机制...")
    
    try:
        from core.config_manager import config_manager
        from core.group_manager import group_manager
        
        # 测试系统配置保存
        try:
            config_manager.save_config()
            print("✅ 系统配置保存成功")
        except Exception as e:
            print(f"❌ 系统配置保存失败: {e}")
            return False
        
        # 测试分组配置保存
        try:
            group_manager.save_groups("timing")
            print("✅ 定时发送分组保存成功")
        except Exception as e:
            print(f"❌ 定时发送分组保存失败: {e}")
            return False
        
        try:
            group_manager.save_groups("loop")
            print("✅ 循环发送分组保存成功")
        except Exception as e:
            print(f"❌ 循环发送分组保存失败: {e}")
            return False
        
        # 测试任务数据保存
        try:
            from core.timing_sender import timing_sender
            from core.loop_sender import loop_sender
            
            timing_sender.save_tasks()
            print("✅ 定时任务数据保存成功")
            
            loop_sender.save_tasks()
            print("✅ 循环任务数据保存成功")
        except Exception as e:
            print(f"❌ 任务数据保存失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_persistence():
    """测试配置持久化"""
    print("\n🔧 测试配置持久化...")
    
    try:
        from core.config_manager import config_manager
        from core.group_manager import group_manager
        from utils.path_manager import path_manager
        
        # 检查配置文件是否存在
        config_file = path_manager.get_config_path("system_config.json")
        if config_file.exists():
            print(f"✅ 系统配置文件存在: {config_file}")
        else:
            print(f"❌ 系统配置文件不存在: {config_file}")
            return False

        # 检查分组文件是否存在（使用正确的路径）
        timing_groups_file = path_manager.app_data_dir / "timing_groups.json"
        if timing_groups_file.exists():
            print(f"✅ 定时发送分组文件存在: {timing_groups_file}")
        else:
            print(f"⚠️  定时发送分组文件不存在（可能没有分组）: {timing_groups_file}")

        loop_groups_file = path_manager.app_data_dir / "loop_groups.json"
        if loop_groups_file.exists():
            print(f"✅ 循环发送分组文件存在: {loop_groups_file}")
        else:
            print(f"⚠️  循环发送分组文件不存在（可能没有分组）: {loop_groups_file}")

        # 检查任务数据文件是否存在
        timing_tasks_file = path_manager.app_data_dir / "timing_tasks.json"
        if timing_tasks_file.exists():
            print(f"✅ 定时任务数据文件存在: {timing_tasks_file}")
        else:
            print(f"⚠️  定时任务数据文件不存在（可能没有任务）: {timing_tasks_file}")

        loop_tasks_file = path_manager.app_data_dir / "loop_tasks.json"
        if loop_tasks_file.exists():
            print(f"✅ 循环任务数据文件存在: {loop_tasks_file}")
        else:
            print(f"⚠️  循环任务数据文件不存在（可能没有任务）: {loop_tasks_file}")

        # 检查进度数据文件
        progress_file = path_manager.app_data_dir / "progress.json"
        if progress_file.exists():
            print(f"✅ 进度数据文件存在: {progress_file}")
        else:
            print(f"⚠️  进度数据文件不存在（可能没有进度）: {progress_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 程序退出保存机制测试")
    print("=" * 60)
    print("测试程序退出时所有设置的保存机制和打包路径支持")
    print("=" * 60)
    
    tests = [
        ("路径管理器打包支持测试", test_path_manager_packaging_support),
        ("退出清理逻辑测试", test_exit_cleanup_logic),
        ("主应用程序清理测试", test_main_app_cleanup),
        ("配置保存机制测试", test_config_save_mechanisms),
        ("配置持久化测试", test_config_persistence)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 程序退出保存机制测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 程序退出保存机制完善成功！")
        print("\n✨ 退出时保存的内容:")
        print("  💾 所有分组设置（定时发送和循环发送）")
        print("  ⚙️  系统设置和配置")
        print("  📋 定时任务和循环任务数据")
        print("  📝 消息模板数据")
        print("  🔄 程序状态和进度")
        print("  📊 统计数据和日志")
        
        print("\n✨ 打包路径支持:")
        print("  📁 支持PyInstaller打包")
        print("  📁 支持开发环境和打包环境")
        print("  📁 自动创建必要目录")
        print("  📁 正确处理临时文件")
        print("  📁 支持Windows安装程序")
        
        print("\n✨ 退出清理流程:")
        print("  1️⃣ 用户点击关闭按钮")
        print("  2️⃣ 触发closeEvent事件")
        print("  3️⃣ 停止所有定时器和任务")
        print("  4️⃣ 断开微信连接")
        print("  5️⃣ 清理异步任务")
        print("  6️⃣ 保存所有任务数据")
        print("  7️⃣ 保存所有分组设置")
        print("  8️⃣ 保存系统设置")
        print("  9️⃣ 创建退出备份")
        print("  🔟 清理资源并退出")
        
        print("\n🎯 下次开启时所有设置都会正确恢复！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
