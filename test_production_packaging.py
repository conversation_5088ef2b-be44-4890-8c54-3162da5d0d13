#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试生产环境打包支持
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_production_spec_file():
    """测试生产环境spec文件"""
    print("🔧 测试生产环境spec文件...")
    
    try:
        spec_file = project_root / "MeetSpaceWeChatSender_Production.spec"
        
        if not spec_file.exists():
            print("❌ 生产环境spec文件不存在")
            return False
        
        # 读取spec文件内容
        with open(spec_file, 'r', encoding='utf-8') as f:
            spec_content = f.read()
        
        # 检查关键配置
        checks = [
            ("应用名称配置", "app_name"),
            ("版本信息", "version"),
            ("数据文件配置", "datas"),
            ("隐藏导入", "hiddenimports"),
            ("排除模块", "excludes"),
            ("管理员权限", "uac_admin=True"),
            ("无控制台", "console=False"),
            ("UPX压缩", "upx=True"),
            ("优化级别", "optimize=2"),
            ("图标配置", "icon="),
            ("版本信息文件", "version="),
            ("生产环境配置", "PRODUCTION_CONFIG"),
            ("后处理函数", "post_build_production"),
        ]
        
        for check_name, keyword in checks:
            if keyword in spec_content:
                print(f"✅ {check_name}: 包含 {keyword}")
            else:
                print(f"❌ {check_name}: 缺少 {keyword}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_production_build_script():
    """测试生产环境构建脚本"""
    print("\n🔧 测试生产环境构建脚本...")
    
    try:
        build_script = project_root / "build_production.py"
        
        if not build_script.exists():
            print("❌ 生产环境构建脚本不存在")
            return False
        
        # 读取构建脚本内容
        with open(build_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查关键功能
        checks = [
            ("依赖检查", "check_dependencies"),
            ("资源检查", "check_resources"),
            ("构建目录清理", "clean_build_dirs"),
            ("版本信息更新", "update_version_info"),
            ("PyInstaller执行", "run_pyinstaller"),
            ("构建验证", "verify_build"),
            ("部署包创建", "create_deployment_package"),
            ("生产环境配置", "PRODUCTION_CONFIG"),
            ("错误处理", "except Exception"),
            ("进度显示", "✅"),
        ]
        
        for check_name, keyword in checks:
            if keyword in script_content:
                print(f"✅ {check_name}: 包含 {keyword}")
            else:
                print(f"❌ {check_name}: 缺少 {keyword}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_path_manager_production_support():
    """测试路径管理器的生产环境支持"""
    print("\n🔧 测试路径管理器的生产环境支持...")
    
    try:
        from utils.path_manager import path_manager
        
        # 检查打包环境检测
        print(f"✅ 是否打包环境: {path_manager.is_frozen}")
        print(f"✅ 可执行文件目录: {path_manager.executable_dir}")
        print(f"✅ 应用数据目录: {path_manager.app_data_dir}")
        print(f"✅ 用户数据目录: {path_manager.user_data_dir}")
        print(f"✅ 临时目录: {path_manager.temp_dir}")
        
        # 检查关键方法
        methods = [
            'get_resource_path',
            'get_config_path',
            'get_log_path',
            'get_data_path',
            'get_injection_tool_path',
            'get_temp_path',
        ]
        
        for method_name in methods:
            if hasattr(path_manager, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法不存在")
                return False
        
        # 测试路径获取
        try:
            config_path = path_manager.get_config_path("test.json")
            log_path = path_manager.get_log_path("test.log")
            data_path = path_manager.get_data_path("test.dat")
            
            print(f"✅ 配置路径: {config_path}")
            print(f"✅ 日志路径: {log_path}")
            print(f"✅ 数据路径: {data_path}")
            
        except Exception as e:
            print(f"❌ 路径获取失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_required_resources():
    """测试必需资源文件"""
    print("\n🔧 测试必需资源文件...")
    
    try:
        required_resources = [
            # 核心工具
            "tools/Injector.exe",
            "tools/x64/Injector.exe", 
            "tools/Win32/Injector.exe",
            
            # 微信相关
            "wxhelper_files/wxhelper.dll",
            
            # 资源文件
            "resources/icons/app_icon.ico",
            "resources/themes",
            
            # 配置文件
            "config/system_config.json",
            "config/send_settings.json",
            
            # 文档文件
            "version_info.txt",
            "LICENSE.txt",
            "README.md",
        ]
        
        missing_resources = []
        
        for resource in required_resources:
            resource_path = project_root / resource
            if resource_path.exists():
                print(f"✅ {resource}")
            else:
                print(f"❌ {resource} - 缺失")
                missing_resources.append(resource)
        
        if missing_resources:
            print(f"\n⚠️  缺少 {len(missing_resources)} 个资源文件")
            return False
        
        print(f"\n✅ 所有 {len(required_resources)} 个资源文件都存在")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_production_dependencies():
    """测试生产环境依赖"""
    print("\n🔧 测试生产环境依赖...")
    
    try:
        required_packages = [
            'PyInstaller',
            'PyQt6',
            'requests',
            'psutil',
            'wcferry',
            'Pillow',
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                if package == 'PyInstaller':
                    import PyInstaller
                elif package == 'PyQt6':
                    import PyQt6
                elif package == 'requests':
                    import requests
                elif package == 'psutil':
                    import psutil
                elif package == 'wcferry':
                    import wcferry
                elif package == 'Pillow':
                    import PIL
                
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} - 缺失")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n⚠️  缺少 {len(missing_packages)} 个依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        print(f"\n✅ 所有 {len(required_packages)} 个依赖包都已安装")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exit_save_mechanism_production():
    """测试生产环境下的退出保存机制"""
    print("\n🔧 测试生产环境下的退出保存机制...")
    
    try:
        from core.config_manager import config_manager
        from core.group_manager import group_manager
        
        # 测试配置保存
        try:
            config_manager.save_config()
            print("✅ 系统配置保存功能正常")
        except Exception as e:
            print(f"❌ 系统配置保存失败: {e}")
            return False
        
        # 测试分组保存
        try:
            group_manager.save_groups("timing")
            group_manager.save_groups("loop")
            print("✅ 分组配置保存功能正常")
        except Exception as e:
            print(f"❌ 分组配置保存失败: {e}")
            return False
        
        # 测试任务保存
        try:
            from core.timing_sender import timing_sender
            from core.loop_sender import loop_sender
            
            timing_sender.save_tasks()
            loop_sender.save_tasks()
            print("✅ 任务数据保存功能正常")
        except Exception as e:
            print(f"❌ 任务数据保存失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 生产环境打包支持测试")
    print("=" * 60)
    print("测试生产环境打包的完整支持")
    print("=" * 60)
    
    tests = [
        ("生产环境spec文件测试", test_production_spec_file),
        ("生产环境构建脚本测试", test_production_build_script),
        ("路径管理器生产环境支持测试", test_path_manager_production_support),
        ("必需资源文件测试", test_required_resources),
        ("生产环境依赖测试", test_production_dependencies),
        ("生产环境退出保存机制测试", test_exit_save_mechanism_production),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 生产环境打包支持测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 生产环境打包支持完善成功！")
        print("\n✨ 生产环境特性:")
        print("  📦 完整的PyInstaller配置")
        print("  🔧 自动化构建脚本")
        print("  📁 路径自适应支持")
        print("  🛡️  管理员权限支持")
        print("  📊 版本信息完整")
        print("  📋 部署文档齐全")
        print("  🔍 构建验证机制")
        print("  💾 数据持久化保证")
        
        print("\n🚀 生产环境打包流程:")
        print("  1️⃣ 运行 python build_production.py")
        print("  2️⃣ 自动检查依赖和资源")
        print("  3️⃣ 清理构建目录")
        print("  4️⃣ 更新版本信息")
        print("  5️⃣ 执行PyInstaller打包")
        print("  6️⃣ 验证构建结果")
        print("  7️⃣ 创建部署包")
        print("  8️⃣ 生成安装脚本")
        
        print("\n📋 部署说明:")
        print("  📁 将 deploy 目录复制到目标机器")
        print("  🔧 右键点击 install.bat，选择'以管理员身份运行'")
        print("  ✅ 按照提示完成安装")
        print("  🚀 程序将自动配置运行环境")
        
        print("\n🎯 生产环境完全支持，可以安全部署！")
    else:
        print("\n❌ 部分测试失败，需要进一步完善")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
