#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的PyInstaller构建脚本 - 确保不遗漏任何注入脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def verify_all_injection_files():
    """验证所有注入相关文件"""
    print("🔍 验证所有注入相关文件...")
    
    required_files = {
        # 注入工具 - 所有架构
        "主注入器": "tools/Injector.exe",
        "64位注入器": "tools/x64/Injector.exe", 
        "32位注入器": "tools/Win32/Injector.exe",
        "ARM64注入器": "tools/ARM64/Injector.exe",
        
        # DLL文件 - 所有版本
        "主DLL": "wxhelper_files/wxhelper.dll",
        "最新DLL": "wxhelper_files/wxhelper_latest.dll",
        "原始备份DLL": "wxhelper_files/wxhelper_original_backup.dll",
        "x64备份DLL": "wxhelper_files/wxhelper_x64_backup.dll",
        
        # 核心注入模块
        "注入工具模块": "core/injector_tool.py",
        "自动注入器": "core/auto_injector.py",
        "HTTP连接器": "core/http_api_connector.py",
        
        # 主程序
        "主程序": "main.py",
        
        # 资源文件
        "应用图标": "resources/icons/app_icon.ico",
        "版本信息": "version_info.txt"
    }
    
    missing_files = []
    existing_files = []
    
    for name, file_path in required_files.items():
        if Path(file_path).exists():
            size = Path(file_path).stat().st_size
            existing_files.append((name, file_path, size))
            print(f"  ✅ {name}: {file_path} ({size} bytes)")
        else:
            missing_files.append((name, file_path))
            print(f"  ❌ {name}: {file_path}")
    
    if missing_files:
        print(f"\n⚠️  发现缺失文件:")
        for name, path in missing_files:
            print(f"    - {name}: {path}")
        return False, missing_files
    
    print(f"\n✅ 所有 {len(existing_files)} 个关键文件都存在")
    return True, existing_files

def build_complete_package():
    """构建完整包，确保包含所有注入文件"""
    print("🔨 构建完整包...")
    
    # 清理构建目录
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONUTF8': '1',
        'PYTHONLEGACYWINDOWSSTDIO': '1'
    })
    
    # 完整的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 无控制台
        "--uac-admin",                  # 请求管理员权限 (重要!)
        "--name=MeetSpaceWeChatSender_Complete",
        "--icon=resources/icons/app_icon.ico",
        
        # === 注入工具 - 所有架构 ===
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64/Injector.exe;tools/x64",
        "--add-data=tools/Win32/Injector.exe;tools/Win32", 
        "--add-data=tools/ARM64/Injector.exe;tools/ARM64",
        "--add-data=tools/README.md;tools",
        
        # === DLL文件 - 所有版本 ===
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_original_backup.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_x64_backup.dll;wxhelper_files",
        "--add-data=wxhelper_files/README.md;wxhelper_files",
        
        # === 完整资源文件 ===
        "--add-data=resources;resources",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        "--add-data=README.md;.",
        
        # === 配置文件模板 ===
        "--add-data=config/send_settings.json;config",
        "--add-data=config/system_config.json;config",
        "--add-data=config/wechat_config.json;config",
        
        # === 核心隐藏导入 ===
        # PyQt6
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=PyQt6.QtSvg",
        
        # 系统和进程
        "--hidden-import=ctypes",
        "--hidden-import=ctypes.wintypes",
        "--hidden-import=subprocess",
        "--hidden-import=psutil",
        "--hidden-import=tempfile",
        "--hidden-import=shutil",
        "--hidden-import=pathlib",
        
        # 网络和API
        "--hidden-import=requests",
        "--hidden-import=urllib3",
        "--hidden-import=json",
        
        # 数据处理
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        
        # 图像处理
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        "--hidden-import=PIL.ImageDraw",
        "--hidden-import=PIL.ImageFont",
        
        # 配置和序列化
        "--hidden-import=yaml",
        "--hidden-import=pickle",
        "--hidden-import=datetime",
        
        # === 项目核心模块 ===
        # 配置模块
        "--hidden-import=config",
        "--hidden-import=config.settings",
        "--hidden-import=config.wechat_config",
        
        # 核心业务模块
        "--hidden-import=core",
        "--hidden-import=core.injector_tool",        # 核心注入工具
        "--hidden-import=core.auto_injector",        # 自动注入器
        "--hidden-import=core.http_api_connector",   # HTTP API连接器
        "--hidden-import=core.timing_sender",
        "--hidden-import=core.loop_sender",
        "--hidden-import=core.send_monitor",
        "--hidden-import=core.risk_control",
        "--hidden-import=core.group_manager",
        "--hidden-import=core.message_template",
        "--hidden-import=core.config_manager",
        "--hidden-import=core.message_sender_core",
        
        # UI模块
        "--hidden-import=ui",
        "--hidden-import=ui.main_window",
        "--hidden-import=ui.timing_send_page",
        "--hidden-import=ui.loop_send_page",
        "--hidden-import=ui.task_status_page",
        "--hidden-import=ui.modern_theme_manager",
        "--hidden-import=ui.rich_text_editor",
        "--hidden-import=ui.themed_dialog_base",
        "--hidden-import=ui.themed_message_box",
        
        # UI组件
        "--hidden-import=ui.widgets",
        "--hidden-import=ui.widgets.contact_selector",
        "--hidden-import=ui.widgets.group_list_widget",
        "--hidden-import=ui.widgets.loop_cycle_widget",
        "--hidden-import=ui.widgets.message_preview",
        "--hidden-import=ui.widgets.send_progress_widget",
        "--hidden-import=ui.widgets.task_item_widget",
        
        # 工具模块
        "--hidden-import=utils",
        "--hidden-import=utils.logger",
        "--hidden-import=utils.path_manager",
        "--hidden-import=utils.performance_optimizer",
        "--hidden-import=utils.icon_manager",
        "--hidden-import=utils.admin_privileges",
        "--hidden-import=utils.subprocess_helper",
        
        # === 排除不需要的模块 ===
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy.testing",
        "--exclude-module=pytest",
        "--exclude-module=setuptools",
        "--exclude-module=distutils",
        
        # === 构建选项 ===
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不确认覆盖
        "--optimize=1",                 # 优化级别
        "--noupx",                      # 禁用UPX压缩
        
        # 主程序
        "main.py"
    ]
    
    print("📋 执行完整构建...")
    print("   包含所有注入工具和DLL文件")
    print("   包含所有架构版本")
    print("   包含完整的项目功能")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env,
            timeout=600  # 10分钟超时
        )
        
        print("✅ 构建完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e.returncode}")
        if e.stderr:
            print("错误输出:")
            print(e.stderr[-2000:])  # 显示最后2000字符
        return False
    except subprocess.TimeoutExpired:
        print("❌ 构建超时（10分钟）")
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def verify_build_result():
    """验证构建结果"""
    print("🔍 验证构建结果...")
    
    exe_file = Path("dist") / "MeetSpaceWeChatSender_Complete.exe"
    
    if not exe_file.exists():
        print("❌ 可执行文件不存在")
        return False
    
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"✅ 可执行文件: {exe_file}")
    print(f"📊 文件大小: {size_mb:.1f} MB")
    
    # 检查文件大小是否合理
    if size_mb < 50:
        print("⚠️  文件大小可能偏小，检查是否包含所有组件")
        return False
    
    return True

def create_complete_docs():
    """创建完整版本文档"""
    print("📄 创建完整版本文档...")
    
    docs_content = """# Meet space 微信群发助手 - 完整版本

## 🎯 版本特点

### ✅ 完整的注入支持
- **所有架构注入器**: Injector.exe (主版本、x64、Win32、ARM64)
- **所有DLL版本**: wxhelper.dll + 3个备份版本
- **完整功能**: 定时发送、循环发送、分组管理、5套主题

### 📦 包含的注入文件

#### 注入工具 (4个版本)
- `tools/Injector.exe` - 主版本注入器
- `tools/x64/Injector.exe` - 64位专用注入器  
- `tools/Win32/Injector.exe` - 32位专用注入器
- `tools/ARM64/Injector.exe` - ARM64架构注入器

#### DLL文件 (4个版本)
- `wxhelper_files/wxhelper.dll` - 主DLL文件
- `wxhelper_files/wxhelper_latest.dll` - 最新版本
- `wxhelper_files/wxhelper_original_backup.dll` - 原始备份
- `wxhelper_files/wxhelper_x64_backup.dll` - x64备份

## 🚀 使用方法

1. **启动微信PC版并登录**
2. **运行程序**: 双击 `MeetSpaceWeChatSender_Complete.exe`
3. **连接微信**: 点击"连接微信"按钮
4. **自动注入**: 程序会自动选择合适的注入器和DLL
5. **开始使用**: 连接成功后使用所有功能

## 🔧 技术特点

### 智能注入
- 自动检测微信进程架构
- 自动选择对应的注入器版本
- 自动选择合适的DLL文件
- 完整的错误处理和重试机制

### 兼容性
- 支持所有Windows架构 (x86, x64, ARM64)
- 支持多个微信版本
- 包含多个DLL备份版本

## 🛠️ 故障排除

### 如果注入失败
1. **重启微信**: 完全关闭微信后重新启动
2. **管理员权限**: 右键"以管理员身份运行"
3. **关闭杀毒**: 临时关闭杀毒软件
4. **尝试不同版本**: 程序会自动尝试不同的DLL版本

### 架构兼容性
- 程序会自动检测微信进程架构
- 自动选择对应的注入器版本
- 如果一个版本失败，会尝试其他版本

## 📊 文件清单

总共包含 8 个关键注入文件:
- 4 个注入器 (.exe)
- 4 个DLL文件 (.dll)

确保不会遗漏任何注入脚本！

---

**版本**: Complete v1.0.0
**特点**: 包含所有注入文件，确保最大兼容性
**更新日期**: 2025-08-05
"""
    
    try:
        with open("dist/完整版本说明.txt", "w", encoding="utf-8") as f:
            f.write(docs_content)
        print("  ✅ 完整版本文档已创建")
    except Exception as e:
        print(f"  ⚠️  创建文档失败: {e}")

def main():
    print("🔧 Meet space 微信群发助手 - 完整版本构建")
    print("=" * 70)
    print("确保包含所有注入脚本，不遗漏任何文件")
    print("=" * 70)
    
    # 1. 验证所有注入文件
    files_ok, file_info = verify_all_injection_files()
    if not files_ok:
        print("\n❌ 关键文件缺失，无法构建")
        input("\n按回车键退出...")
        return False
    
    # 2. 构建完整包
    if not build_complete_package():
        print("\n❌ 构建失败")
        input("\n按回车键退出...")
        return False
    
    # 3. 验证构建结果
    if not verify_build_result():
        print("\n❌ 构建验证失败")
        input("\n按回车键退出...")
        return False
    
    # 4. 创建文档
    create_complete_docs()
    
    print("\n🎉 完整版本构建成功!")
    print("📁 输出文件:")
    print("  - MeetSpaceWeChatSender_Complete.exe (主程序)")
    print("  - 完整版本说明.txt (详细说明)")
    
    print("\n✨ 包含的注入文件:")
    print("  🔧 4个注入器版本 (主版本、x64、Win32、ARM64)")
    print("  📚 4个DLL文件版本 (主版本 + 3个备份)")
    print("  🚀 完整的项目功能")
    print("  ⚙️  智能架构检测和选择")
    
    print("\n📋 现在可以:")
    print("  1. 在任何Windows架构上运行")
    print("  2. 支持所有微信版本")
    print("  3. 自动选择最佳注入方案")
    print("  4. 完整的错误处理和重试")
    
    input("\n按回车键退出...")
    return True

if __name__ == "__main__":
    main()
