#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速重新构建 - 修复注入问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def quick_rebuild():
    """快速重新构建"""
    print("🔧 快速重新构建修复版本...")
    
    # 清理
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MeetSpaceWeChatSender_Fixed",
        "--icon=resources/icons/app_icon.ico",
        
        # 工具文件
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64/Injector.exe;tools/x64",
        "--add-data=tools/Win32/Injector.exe;tools/Win32",
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        
        # 资源
        "--add-data=resources/icons;resources/icons",
        "--add-data=version_info.txt;.",
        
        # 核心模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=ctypes",
        "--hidden-import=subprocess",
        "--hidden-import=psutil",
        "--hidden-import=tempfile",
        "--hidden-import=shutil",
        
        # 项目模块
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=core.injector_tool",
        "--hidden-import=ui",
        "--hidden-import=utils",
        "--hidden-import=runtime_config_generator",
        
        # 排除
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        
        # 选项
        "--clean",
        "--noconfirm",
        "--noupx",
        
        "main.py"
    ]
    
    print("📋 开始构建...")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=300
        )
        
        print("✅ 构建完成")
        
        # 检查结果
        exe_file = Path("dist") / "MeetSpaceWeChatSender_Fixed.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"✅ 输出文件: {exe_file}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            
            print("\n🎉 修复版本构建成功!")
            print("✨ 修复内容:")
            print("  🔧 注入工具自动提取到临时目录")
            print("  📚 DLL文件正确提取")
            print("  🚀 外部程序可以访问工具文件")
            print("\n📋 测试步骤:")
            print("  1. 启动微信PC版并登录")
            print("  2. 运行 MeetSpaceWeChatSender_Fixed.exe")
            print("  3. 检查是否能成功连接微信")
            
            return True
        else:
            print("❌ 未找到输出文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e.returncode}")
        if e.stderr:
            print("错误:", e.stderr[-1000:])
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def main():
    print("🔧 Meet space 微信群发助手 - 注入问题修复")
    print("=" * 60)
    
    success = quick_rebuild()
    
    if success:
        print("\n✅ 修复完成！请测试新版本是否能正常连接微信。")
    else:
        print("\n❌ 修复失败！")
    
    input("\n按回车键退出...")
    return success

if __name__ == "__main__":
    main()
