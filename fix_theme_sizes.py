#!/usr/bin/env python3
"""
主题尺寸修复工具
修复科技主题中按钮和选项框尺寸问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QCheckBox, QRadioButton, QPushButton, QLabel, 
                            QGroupBox, QSpinBox, QComboBox, QLineEdit)
from PyQt6.QtCore import Qt
from ui.modern_theme_manager import theme_manager
from ui.themed_dialog_base import ThemedDialogBase


class SizeTestDialog(ThemedDialogBase):
    """尺寸测试对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("主题尺寸测试")
        self.setFixedSize(500, 400)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("主题尺寸测试 - 检查控件是否保持正常尺寸")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 按钮测试组
        button_group = QGroupBox("按钮尺寸测试")
        button_layout = QVBoxLayout(button_group)
        
        # 普通按钮行
        normal_button_layout = QHBoxLayout()
        normal_button_layout.addWidget(QLabel("普通按钮:"))
        self.normal_btn = QPushButton("普通按钮")
        normal_button_layout.addWidget(self.normal_btn)
        normal_button_layout.addStretch()
        button_layout.addLayout(normal_button_layout)
        
        # 特殊样式按钮行
        special_button_layout = QHBoxLayout()
        special_button_layout.addWidget(QLabel("特殊按钮:"))
        
        self.success_btn = QPushButton("成功按钮")
        self.success_btn.setProperty("class", "success")
        special_button_layout.addWidget(self.success_btn)
        
        self.secondary_btn = QPushButton("次要按钮")
        self.secondary_btn.setProperty("class", "secondary")
        special_button_layout.addWidget(self.secondary_btn)
        
        self.danger_btn = QPushButton("危险按钮")
        self.danger_btn.setProperty("class", "danger")
        special_button_layout.addWidget(self.danger_btn)
        
        special_button_layout.addStretch()
        button_layout.addLayout(special_button_layout)
        
        layout.addWidget(button_group)
        
        # 选项框测试组
        checkbox_group = QGroupBox("选项框尺寸测试")
        checkbox_layout = QVBoxLayout(checkbox_group)
        
        # 复选框行
        checkbox_row = QHBoxLayout()
        checkbox_row.addWidget(QLabel("复选框:"))
        self.checkbox1 = QCheckBox("选项1")
        self.checkbox2 = QCheckBox("选项2")
        self.checkbox3 = QCheckBox("禁用选项")
        self.checkbox3.setEnabled(False)
        checkbox_row.addWidget(self.checkbox1)
        checkbox_row.addWidget(self.checkbox2)
        checkbox_row.addWidget(self.checkbox3)
        checkbox_row.addStretch()
        checkbox_layout.addLayout(checkbox_row)
        
        # 单选按钮行
        radio_row = QHBoxLayout()
        radio_row.addWidget(QLabel("单选按钮:"))
        self.radio1 = QRadioButton("选项A")
        self.radio2 = QRadioButton("选项B")
        self.radio3 = QRadioButton("禁用选项")
        self.radio3.setEnabled(False)
        self.radio1.setChecked(True)
        radio_row.addWidget(self.radio1)
        radio_row.addWidget(self.radio2)
        radio_row.addWidget(self.radio3)
        radio_row.addStretch()
        checkbox_layout.addLayout(radio_row)
        
        layout.addWidget(checkbox_group)
        
        # 输入控件测试组
        input_group = QGroupBox("输入控件尺寸测试")
        input_layout = QVBoxLayout(input_group)
        
        # SpinBox行
        spinbox_row = QHBoxLayout()
        spinbox_row.addWidget(QLabel("数字输入框:"))
        self.spinbox = QSpinBox()
        self.spinbox.setRange(0, 100)
        self.spinbox.setValue(50)
        spinbox_row.addWidget(self.spinbox)
        spinbox_row.addStretch()
        input_layout.addLayout(spinbox_row)
        
        # ComboBox行
        combobox_row = QHBoxLayout()
        combobox_row.addWidget(QLabel("下拉框:"))
        self.combobox = QComboBox()
        self.combobox.addItems(["选项1", "选项2", "选项3"])
        combobox_row.addWidget(self.combobox)
        combobox_row.addStretch()
        input_layout.addLayout(combobox_row)
        
        # LineEdit行
        lineedit_row = QHBoxLayout()
        lineedit_row.addWidget(QLabel("文本输入框:"))
        self.lineedit = QLineEdit()
        self.lineedit.setPlaceholderText("请输入文本...")
        lineedit_row.addWidget(self.lineedit)
        input_layout.addLayout(lineedit_row)
        
        layout.addWidget(input_group)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        test_button = QPushButton("测试尺寸")
        test_button.clicked.connect(self.test_sizes)
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.accept)
        
        button_layout.addWidget(test_button)
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
    def test_sizes(self):
        """测试控件尺寸"""
        print("=== 控件尺寸测试结果 ===")
        
        # 测试按钮尺寸
        print(f"普通按钮尺寸: {self.normal_btn.size().width()} x {self.normal_btn.size().height()}")
        print(f"成功按钮尺寸: {self.success_btn.size().width()} x {self.success_btn.size().height()}")
        print(f"次要按钮尺寸: {self.secondary_btn.size().width()} x {self.secondary_btn.size().height()}")
        
        # 测试选项框尺寸
        print(f"复选框尺寸: {self.checkbox1.size().width()} x {self.checkbox1.size().height()}")
        print(f"单选按钮尺寸: {self.radio1.size().width()} x {self.radio1.size().height()}")
        
        # 测试输入控件尺寸
        print(f"数字输入框尺寸: {self.spinbox.size().width()} x {self.spinbox.size().height()}")
        print(f"下拉框尺寸: {self.combobox.size().width()} x {self.combobox.size().height()}")
        print(f"文本输入框尺寸: {self.lineedit.size().width()} x {self.lineedit.size().height()}")
        
        print("========================")


def test_theme_sizes():
    """测试主题尺寸问题"""
    app = QApplication(sys.argv)
    
    print("🔧 主题尺寸修复工具")
    print("=" * 40)
    
    # 测试默认主题
    theme_manager.set_theme(app, "默认主题")
    print("✅ 已应用默认主题")
    
    dialog1 = SizeTestDialog()
    dialog1.setWindowTitle("默认主题 - 尺寸测试")
    dialog1.show()
    
    print("\n📋 默认主题测试:")
    print("1. 观察所有控件的尺寸是否正常")
    print("2. 点击'测试尺寸'按钮查看具体数值")
    print("3. 关闭对话框继续测试科技主题")
    
    if dialog1.exec() == QDialog.DialogCode.Accepted:
        print("✅ 默认主题测试完成")
        
        # 测试科技主题
        theme_manager.set_theme(app, "科技主题")
        print("✅ 已切换到科技主题")
        
        dialog2 = SizeTestDialog()
        dialog2.setWindowTitle("科技主题 - 尺寸测试")
        dialog2.show()
        
        print("\n📋 科技主题测试:")
        print("1. 检查控件尺寸是否与默认主题一致")
        print("2. 确认没有异常的尺寸变化")
        print("3. 测试所有控件的交互功能")
        print("4. 观察样式是否正确应用")
        
        if dialog2.exec() == QDialog.DialogCode.Accepted:
            print("✅ 科技主题测试完成")
    
    print("\n🎉 主题尺寸测试完成！")
    print("\n📊 修复内容:")
    print("1. ✅ 移除了可能影响尺寸的CSS属性")
    print("2. ✅ 保持了系统默认的padding和尺寸")
    print("3. ✅ 修复了重复的样式定义")
    print("4. ✅ 确保了控件尺寸的一致性")
    
    sys.exit(0)


def main():
    """主函数"""
    print("🚀 主题尺寸修复工具")
    print("=" * 40)
    print("\n📋 修复内容:")
    print("1. 科技主题按钮样式 - 移除尺寸修改")
    print("2. 选项框样式 - 保持16x16px标准尺寸")
    print("3. 输入控件样式 - 保持系统默认尺寸")
    print("4. 修复重复的样式定义")
    
    print("\n✅ 修复完成！现在可以运行测试:")
    print("python fix_theme_sizes.py --test")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_theme_sizes()


if __name__ == "__main__":
    main()
