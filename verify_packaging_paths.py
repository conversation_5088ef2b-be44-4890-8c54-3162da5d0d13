#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证打包路径配置
确保所有关键路径都适合打包环境
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_path_manager():
    """测试路径管理器"""
    print("🔧 测试路径管理器...")
    
    try:
        from utils.path_manager import path_manager
        
        print("✅ 路径管理器导入成功")
        
        # 测试关键路径
        paths_to_test = [
            ("可执行目录", path_manager.executable_dir),
            ("应用数据目录", path_manager.app_data_dir),
            ("用户数据目录", path_manager.user_data_dir),
            ("临时目录", path_manager.temp_dir),
        ]
        
        for name, path in paths_to_test:
            print(f"   {name}: {path}")
            if not path.exists():
                print(f"     ⚠️  目录不存在，将自动创建")
        
        # 测试资源路径
        resource_tests = [
            "resources/icons/app_icon.ico",
            "wxhelper_files/wxhelper.dll",
            "tools/Injector.exe"
        ]
        
        print("\n📁 测试资源路径:")
        for resource in resource_tests:
            try:
                resource_path = path_manager.get_resource_path(resource)
                exists = resource_path.exists()
                status = "✅" if exists else "❌"
                print(f"   {status} {resource} -> {resource_path}")
            except Exception as e:
                print(f"   ❌ {resource} -> 错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 路径管理器测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n⚙️  测试配置管理器...")
    
    try:
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        print("✅ 配置管理器创建成功")
        
        print(f"   配置目录: {config_manager.config_dir}")
        print(f"   系统配置文件: {config_manager.system_config_file}")
        print(f"   发送设置文件: {config_manager.send_settings_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_injector_tool():
    """测试注入工具"""
    print("\n💉 测试注入工具...")
    
    try:
        from core.injector_tool import InjectorTool
        
        # 不自动提升权限，避免UAC弹窗
        injector = InjectorTool(auto_elevate=False)
        print("✅ 注入工具创建成功")
        
        print(f"   DLL路径: {injector.dll_path}")
        print(f"   注入器路径: {injector.injector_path}")
        
        # 检查文件是否存在
        dll_exists = injector.dll_path.exists()
        injector_exists = injector.injector_path.exists()
        
        print(f"   DLL文件存在: {'✅' if dll_exists else '❌'}")
        print(f"   注入器存在: {'✅' if injector_exists else '❌'}")
        
        return dll_exists and injector_exists
        
    except Exception as e:
        print(f"❌ 注入工具测试失败: {e}")
        return False

def test_logger():
    """测试日志系统"""
    print("\n📝 测试日志系统...")
    
    try:
        from utils.logger import setup_logger
        
        logger = setup_logger("test_packaging")
        print("✅ 日志系统创建成功")
        
        # 测试日志输出
        logger.info("这是一条测试日志")
        logger.warning("这是一条测试警告")
        
        print(f"   处理器数量: {len(logger.handlers)}")
        for i, handler in enumerate(logger.handlers):
            handler_type = type(handler).__name__
            print(f"   处理器 {i+1}: {handler_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False

def test_settings():
    """测试设置模块"""
    print("\n⚙️  测试设置模块...")
    
    try:
        from config import settings
        
        print("✅ 设置模块导入成功")
        
        # 测试关键路径
        paths_to_check = [
            ("项目根目录", settings.PROJECT_ROOT),
            ("配置目录", settings.CONFIG_DIR),
            ("日志目录", settings.LOGS_DIR),
            ("资源目录", settings.RESOURCES_DIR),
        ]
        
        for name, path in paths_to_check:
            print(f"   {name}: {path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置模块测试失败: {e}")
        return False

def simulate_packaging_environment():
    """模拟打包环境"""
    print("\n📦 模拟打包环境...")
    
    # 保存原始状态
    original_frozen = getattr(sys, 'frozen', False)
    original_meipass = getattr(sys, '_MEIPASS', None)
    
    try:
        # 模拟打包环境
        sys.frozen = True
        sys._MEIPASS = str(Path(__file__).parent)
        
        print("✅ 已设置打包环境模拟")
        
        # 重新测试路径管理器
        from utils.path_manager import PathManager
        
        # 创建新的路径管理器实例
        test_pm = PathManager()
        
        print(f"   模拟环境下可执行目录: {test_pm.executable_dir}")
        print(f"   模拟环境下应用数据目录: {test_pm.app_data_dir}")
        
        # 测试资源路径
        test_resource = test_pm.get_resource_path("resources/icons/app_icon.ico")
        print(f"   模拟环境下资源路径: {test_resource}")
        
        return True
        
    except Exception as e:
        print(f"❌ 打包环境模拟失败: {e}")
        return False
    finally:
        # 恢复原始状态
        if original_frozen:
            sys.frozen = original_frozen
        elif hasattr(sys, 'frozen'):
            delattr(sys, 'frozen')
        
        if original_meipass:
            sys._MEIPASS = original_meipass
        elif hasattr(sys, '_MEIPASS'):
            delattr(sys, '_MEIPASS')

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 打包路径验证")
    print("=" * 70)
    
    tests = [
        ("路径管理器", test_path_manager),
        ("配置管理器", test_config_manager),
        ("注入工具", test_injector_tool),
        ("日志系统", test_logger),
        ("设置模块", test_settings),
        ("打包环境模拟", simulate_packaging_environment)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 打包路径验证结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！路径配置适合打包部署！")
        print("\n✨ 验证内容:")
        print("  🔧 路径管理器正常工作")
        print("  ⚙️  配置管理器路径正确")
        print("  💉 注入工具路径可访问")
        print("  📝 日志系统路径安全")
        print("  📦 打包环境兼容性良好")
        
        print("\n📋 现在可以安全打包部署了！")
    else:
        print("⚠️  部分测试失败，需要进一步修复")
        print("\n🔧 建议:")
        print("1. 检查失败的组件")
        print("2. 确保所有路径使用路径管理器")
        print("3. 验证资源文件存在")
        print("4. 测试打包环境兼容性")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
