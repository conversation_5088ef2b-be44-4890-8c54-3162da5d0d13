#!/usr/bin/env python3
"""
Meet Space -by SWD主程序

一个安全、高效的微信群发工具，支持文本、图片、文件等多种消息类型的批量发送。

启动模式：
- 默认模式（无控制台）: python main.py
- 显示控制台模式: python main.py --show-console
"""

import asyncio
import signal
import sys
import time
import warnings
from pathlib import Path
from typing import Optional

from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QIcon
from PyQt6.QtWidgets import QApplication, QMessageBox

# 单实例管理
from utils.single_instance import SingleInstanceManager, ensure_single_instance

# 路径管理
from utils.path_manager import path_manager

# 抑制Protobuf版本警告
warnings.filterwarnings(
    "ignore",
    message=".*Protobuf gencode version.*",
    category=UserWarning
)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root
def setup_no_console_mode():
    """设置无控制台模式 - 默认隐藏CMD窗口并重定向日志"""
    # 默认启用无控制台模式，除非明确指定显示控制台
    show_console = '--show-console' in sys.argv

    if not show_console:
        # 隐藏控制台窗口（仅Windows）
        if sys.platform == 'win32':
            try:
                import ctypes
                console_window = ctypes.windll.kernel32.GetConsoleWindow()
                if console_window:
                    ctypes.windll.user32.ShowWindow(console_window, 0)
            except Exception:
                pass

        # 重定向输出到日志文件
        try:
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)

            # 重定向stdout和stderr到日志文件
            stdout_log = logs_dir / "console_output.log"
            stderr_log = logs_dir / "console_error.log"

            # 以追加模式打开日志文件
            sys.stdout = open(stdout_log, 'a', encoding='utf-8')
            sys.stderr = open(stderr_log, 'a', encoding='utf-8')

            # 记录启动时间
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n=== 程序启动（无控制台模式） {timestamp} ===")

        except Exception:
            # 如果重定向失败，静默忽略
            pass

    return not show_console

from config.settings import APP_NAME, APP_VERSION, ensure_directories
from utils.simple_admin import ensure_admin_privileges
from config.wechat_config import WeChatConfig
from core.message_template import MessageTemplate
from core.risk_control import RiskController
from core.send_monitor import SendMonitor
from core.config_backup_manager import backup_manager
from ui.main_window import MainWindow
from utils.exceptions import (
    ConfigurationError,
    ConnectionError,
    WeChatMassSenderError,
    handle_exception,
)
from utils.logger import setup_logger
from utils.icon_manager import set_app_icon

# 设置主日志记录器
logger = setup_logger("main")

# 应用程序信息
APP_NAME = "Meet space 微信群发助手"
APP_VERSION = "1.0.0"


class WeChatMassSender:
    """Meet space 微信群发助手主应用类"""

    def __init__(self, instance_manager: Optional[SingleInstanceManager] = None):
        self.app: Optional[QApplication] = None
        self.main_window: Optional[MainWindow] = None
        self.config: Optional[WeChatConfig] = None
        self.connector = None  # HTTPAPIConnector
        self.template: Optional[MessageTemplate] = None
        self.monitor: Optional[SendMonitor] = None
        self.risk_controller: Optional[RiskController] = None
        self.instance_manager = instance_manager

        # 性能优化
        self._startup_time = time.time()
        self._cleanup_timer: Optional[QTimer] = None
        self._initialized = False

    def initialize(self) -> bool:
        """
        初始化应用程序

        Returns:
            是否初始化成功
        """
        try:
            logger.info(f"正在初始化 {APP_NAME} v{APP_VERSION}")

            # 确保必要的目录存在
            self._ensure_directories()

            # 加载配置
            self._load_configuration()

            # 创建核心组件
            self._initialize_components()

            # 创建启动时的自动备份
            self._create_startup_backup()

            # 启动性能监控和定期清理
            self._setup_performance_monitoring()
            
            self._initialized = True
            startup_time = time.time() - self._startup_time
            logger.info(f"应用程序初始化完成，耗时: {startup_time:.2f}秒")
            return True

        except WeChatMassSenderError as e:
            error_msg = handle_exception(e, "应用程序初始化")
            logger.error(error_msg)
            return False
        except Exception as e:
            error_msg = handle_exception(e, "应用程序初始化", user_friendly=False)
            logger.error(error_msg)
            return False

    def _ensure_directories(self) -> None:
        """确保必要的目录存在"""
        try:
            ensure_directories()
        except Exception as e:
            raise ConfigurationError(f"创建目录失败: {e}")

    def _create_startup_backup(self) -> None:
        """创建启动时的自动备份"""
        try:
            # 清理旧备份（保留最近10个）
            deleted_count = backup_manager.cleanup_old_backups(keep_count=10)
            if deleted_count > 0:
                logger.info(f"已删除旧备份: {deleted_count} 个")

            # 创建启动备份
            backup_path = backup_manager.create_backup("程序启动自动备份")
            if backup_path:
                logger.info(f"配置文件已备份到: {backup_path}")
            else:
                logger.warning("启动备份创建失败")

        except Exception as e:
            logger.error(f"创建启动备份失败: {e}")

    def _load_configuration(self) -> None:
        """加载配置"""
        try:
            self.config = WeChatConfig.load_from_file()
            if not self.config.validate():
                raise ConfigurationError("配置文件验证失败")
        except Exception as e:
            if isinstance(e, ConfigurationError):
                raise
            raise ConfigurationError(f"加载配置失败: {e}")

    def _initialize_components(self) -> None:
        """初始化核心组件"""
        try:
            # 先初始化不依赖Qt的组件
            self.template = MessageTemplate()
            self.monitor = SendMonitor()
            assert self.config is not None, "Config must be initialized before creating RiskController"
            self.risk_controller = RiskController(self.config)
            # 连接器将在GUI创建后初始化
            self.connector = None
        except Exception as e:
            raise ConfigurationError(f"初始化组件失败: {e}")

    def _initialize_connector(self) -> None:
        """初始化连接器"""
        try:
            # 使用HTTP API连接器（基于Injector.exe的唯一注入方式）
            from core.http_api_connector import HTTPAPIConnector
            self.connector = HTTPAPIConnector(
                api_type="wxhelper",
                base_url="http://localhost:19088",
                auto_inject=False  # 不自动注入，只在用户点击连接时注入
            )
            logger.info("使用HTTP API连接器 - 基于Injector.exe的唯一注入方式")
        except Exception as e:
            raise ConnectionError(f"HTTP API连接器初始化失败: {e}")

    def create_gui(self) -> bool:
        """
        创建图形用户界面

        Returns:
            是否创建成功
        """
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)

            # 设置应用程序图标
            set_app_icon(self.app)

            # 按照PyQt6 UI优化技术规范 - 启用高分屏支持
            # 注意：在PyQt6中，高分屏支持默认启用，这些属性已被弃用
            # self.app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
            # self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)

            self.app.setApplicationName(APP_NAME)
            self.app.setApplicationVersion(APP_VERSION)

            # 设置全局引用，供主窗口访问
            self.app.main_app_instance = self

            # 按照PyQt6 UI优化技术规范 - 设置动态字体
            # 从配置文件加载用户设置的字体大小
            from ui.modern_theme_manager import theme_manager
            saved_font_size = getattr(self.config, "font_size", "中")
            theme_manager.setup_dynamic_font(self.app, saved_font_size)

            # 设置应用图标（如果存在）
            icon_path = project_root / "resources" / "icons" / "app.ico"
            if icon_path.exists():
                self.app.setWindowIcon(QIcon(str(icon_path)))

            # 现在在主线程中初始化连接器
            self._initialize_connector()

            # 创建主窗口
            assert self.connector is not None
            assert self.template is not None
            assert self.monitor is not None
            assert self.risk_controller is not None
            assert self.config is not None
            self.main_window = MainWindow(
                connector=self.connector,
                template=self.template,
                monitor=self.monitor,
                risk_controller=self.risk_controller,
                config=self.config,
            )

            # 显示主窗口
            self.main_window.show()

            # 如果配置了自动启动，则自动连接微信
            if self.config.auto_start:
                QTimer.singleShot(1000, self.auto_connect)

            logger.info("图形用户界面创建完成")
            return True

        except Exception as e:
            logger.error(f"创建图形用户界面失败: {e}")
            return False

    def auto_connect(self) -> None:
        """自动连接微信"""
        if not self.main_window:
            return

        def on_auto_connect_finished(result) -> None:
            """自动连接完成回调"""
            if len(result) == 3:
                success, message, user_info = result
                self._handle_auto_connect_result(success, message, user_info)
            else:
                logger.warning(f"未知的自动连接结果格式: {result}")

        def on_auto_connect_error(error) -> None:
            """自动连接错误回调"""
            if self.main_window.status_label is not None:
                self.main_window.status_label.setText("连接失败")
            if self.main_window.statusBar() is not None:
                self.main_window.statusBar().showMessage(f"自动连接异常: {error}")
            logger.error(f"自动连接异常: {error}")

        self.main_window.run_async_task(
            self.main_window.auto_connect(),
            on_finished=on_auto_connect_finished,
            on_error=on_auto_connect_error,
        )

    def _handle_auto_connect_result(self, success: bool, message: str, user_info: dict) -> None:
        if success:
            if self.main_window is not None:
                if self.main_window.status_label is not None:
                    self.main_window.status_label.setText("已连接，等待登录")
                if self.main_window.connect_btn is not None:
                    self.main_window.connect_btn.setText("断开连接")
                if self.main_window.statusBar() is not None:
                    self.main_window.statusBar().showMessage(message)
        else:
            if self.main_window is not None:
                if self.main_window.status_label is not None:
                    self.main_window.status_label.setText("连接失败")
                if self.main_window.statusBar() is not None:
                    self.main_window.statusBar().showMessage(f"自动连接失败: {message}")

    def setup_signal_handlers(self) -> None:
        """设置信号处理器"""

        def signal_handler(signum, frame) -> None:
            logger.info(f"收到信号 {signum}，正在退出...")
            self.cleanup()
            sys.exit(0)

        # 设置信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def cleanup(self) -> None:
        """清理资源"""
        try:
            logger.info("正在清理应用程序资源...")

            # 停止性能监控
            try:
                from utils.enhanced_performance_monitor import enhanced_performance_monitor
                enhanced_performance_monitor.stop_monitoring()
                logger.debug("性能监控已停止")
            except Exception as e:
                logger.warning(f"停止性能监控失败: {e}")

            # 停止定期清理定时器
            if self._cleanup_timer and self._cleanup_timer.isActive():
                self._cleanup_timer.stop()
                logger.debug("清理定时器已停止")

            # 清理发送任务
            self._cleanup_senders()

            # 断开微信连接
            self._cleanup_connector()

            # 保存配置
            if self.config:
                self.config.save_to_file()
                logger.debug("配置已保存")

            # 保存模板
            if self.template:
                self.template.save_templates()
                logger.debug("模板已保存")

            # 创建退出时的自动备份
            self._create_exit_backup()

            # 释放单实例锁定
            if self.instance_manager:
                try:
                    self.instance_manager.release_lock()
                    logger.info("单实例锁定已释放")
                except Exception as e:
                    logger.warning(f"释放单实例锁定失败: {e}")

            # 最终清理
            self._final_cleanup()

            logger.info("应用程序资源清理完成")

        except Exception as e:
            logger.error(f"清理资源时出错: {e}")

    def _final_cleanup(self) -> None:
        """最终清理任务"""
        try:
            # 强制垃圾回收
            import gc
            collected = gc.collect()
            if collected > 0:
                logger.info(f"最终清理回收了 {collected} 个对象")
                
        except Exception as e:
            logger.error(f"最终清理失败: {e}")

    def _setup_performance_monitoring(self) -> None:
        """设置性能监控和定期清理"""
        try:
            # 确保在主线程中执行
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import QThread
            app = QApplication.instance()
            if app and QThread.currentThread() != app.thread():
                logger.warning("性能监控设置应该在主线程中执行")
                QTimer.singleShot(0, self._setup_performance_monitoring)
                return
            # 启动增强性能监控
            # 注意：enhanced_performance_monitor已暂时禁用以避免Qt定时器警告
            # 如需启用，请确保解决线程兼容性问题
            logger.info("增强性能监控已禁用（避免Qt定时器警告）")
            
            # 添加性能警报回调
            def on_performance_alert(alert):
                if alert.level in ["error", "critical"]:
                    logger.warning(f"性能警报: {alert.message}")
                    # 在关键情况下强制清理
                    if alert.level == "critical":
                        self._emergency_cleanup()
            
            enhanced_performance_monitor.add_alert_callback(on_performance_alert)
            
            # 创建定期清理定时器 - 延迟到主线程中创建
            # 使用QTimer.singleShot确保在主线程中创建，延迟足够长的时间
            QTimer.singleShot(5000, self._create_cleanup_timer)  # 延迟5秒创建，确保主线程完全就绪

            logger.info("性能监控和定期清理设置完成")
        except Exception as e:
            # 性能监控已禁用，这是预期的
            pass

    def _create_cleanup_timer(self) -> None:
        """创建定期清理定时器"""
        try:
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import QThread
            app = QApplication.instance()
            if app and QThread.currentThread() != app.thread():
                logger.warning("定期清理定时器仍在非主线程中创建，重新调度")
                QTimer.singleShot(100, self._create_cleanup_timer)
                return

            self._cleanup_timer = QTimer()
            self._cleanup_timer.timeout.connect(self._periodic_cleanup)
            self._cleanup_timer.start(180000)  # 每3分钟清理一次（更频繁）
            logger.debug("定期清理定时器创建完成")
        except Exception as e:
            logger.error(f"创建定期清理定时器失败: {e}")

    def _periodic_cleanup(self) -> None:
        """定期清理任务"""
        try:
            # 导入性能优化器
            from utils.performance import memory_manager
            
            # 自动检查并清理（如果需要）
            if memory_manager.auto_cleanup_if_needed():
                logger.info("定期清理: 已执行自动内存清理")
            else:
                # 记录当前状态
                stats = memory_manager.get_memory_stats()
                logger.debug(f"定期检查: 内存使用 {stats.get('rss_mb', 0):.1f}MB, "
                           f"活跃对象 {stats.get('active_objects', 0)} 个")
                
        except Exception as e:
            logger.error(f"定期清理失败: {e}")

    def _emergency_cleanup(self) -> None:
        """紧急清理 - 在系统资源紧张时执行"""
        try:
            logger.warning("执行紧急清理...")
            
            from utils.performance import memory_manager
            from utils.enhanced_performance_monitor import enhanced_performance_monitor
            
            # 强制垃圾回收
            result = memory_manager.force_gc()
            
            # 清理主窗口中的异步任务
            if hasattr(self, 'main_window') and self.main_window:
                self.main_window._cleanup_finished_tasks()
                
                # 如果任务过多，取消一些旧任务
                if len(self.main_window.async_tasks) > 5:
                    self.main_window._cancel_oldest_tasks(3)
            
            # 清理Qt应用缓存
            if self.app:
                self.app.processEvents()
            
            logger.warning(f"紧急清理完成: 释放内存 {result.get('memory_freed_mb', 0):.1f}MB")
            
        except Exception as e:
            logger.error(f"紧急清理失败: {e}")

    def _cleanup_senders(self) -> None:
        """清理发送任务"""
        try:
            logger.debug("清理发送任务...")

            # 清理定时发送
            try:
                from core.timing_sender import timing_sender
                timing_sender.stop_all_tasks()
                timing_sender.save_tasks()
                logger.debug("定时发送任务已清理")
            except Exception as e:
                logger.warning(f"清理定时发送任务失败: {e}")

            # 清理循环发送
            try:
                from core.loop_sender import loop_sender
                loop_sender.stop_all_tasks()
                loop_sender.save_tasks()
                logger.debug("循环发送任务已清理")
            except Exception as e:
                logger.warning(f"清理循环发送任务失败: {e}")

        except Exception as e:
            logger.error(f"清理发送任务失败: {e}")

    def _create_exit_backup(self) -> None:
        """创建退出时的自动备份"""
        try:
            backup_path = backup_manager.create_backup("程序退出自动备份")
            if backup_path:
                logger.info(f"配置文件已备份到: {backup_path}")
            else:
                logger.warning("退出备份创建失败")

        except Exception as e:
            logger.error(f"创建退出备份失败: {e}")

    def _cleanup_connector(self) -> None:
        """清理连接器资源"""
        if not (self.connector and self.connector.is_connected):
            return

        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.connector.disconnect())
            loop.close()
        except Exception as e:
            logger.error(f"断开连接时出错: {e}")

    def run(self) -> int:
        """
        运行应用程序

        Returns:
            退出代码
        """
        try:
            # 初始化应用程序
            if not self.initialize():
                return 1

            # 创建图形用户界面
            if not self.create_gui():
                return 1

            # 设置信号处理器
            self.setup_signal_handlers()

            # 运行事件循环
            logger.info("应用程序启动完成")
            exit_code = self.app.exec()

            # 清理资源
            self.cleanup()

            logger.info(f"应用程序退出，退出代码: {exit_code}")
            return exit_code

        except KeyboardInterrupt:
            logger.info("用户中断程序")
            self.cleanup()
            return 0

        except Exception as e:
            logger.error(f"应用程序运行时出错: {e}")

            # 显示错误对话框
            if self.app:
                QMessageBox.critical(
                    None,
                    "错误",
                    f"应用程序运行时出现错误:\n{e}\n\n请查看日志文件获取详细信息。",
                )

            self.cleanup()
            return 1


def main():
    """主函数"""
    instance_manager = None
    try:
        # 默认隐藏控制台窗口，除非明确指定显示
        no_console = setup_no_console_mode()

        # 设置日志记录器用于启动过程
        startup_logger = setup_logger("startup")

        if no_console:
            startup_logger.info("程序以无控制台模式启动（默认模式）")
        else:
            startup_logger.info("程序以控制台模式启动（调试模式）")

        # 初始化路径管理器
        startup_logger.info("初始化路径管理器...")
        try:
            # 迁移旧版本数据
            path_manager.migrate_old_data()
            startup_logger.info("✅ 路径管理器初始化完成")
            startup_logger.info(f"应用数据目录: {path_manager.app_data_dir}")
            startup_logger.info(f"用户数据目录: {path_manager.user_data_dir}")
        except Exception as e:
            startup_logger.error(f"路径管理器初始化失败: {e}")
            sys.exit(1)

        # 单实例检查
        startup_logger.info("检查程序单实例运行...")

        # 先尝试清理可能存在的旧锁定
        try:
            from utils.single_instance import SingleInstanceManager
            cleanup_manager = SingleInstanceManager("wx_group_sender")
            if cleanup_manager.is_running():
                startup_logger.info("发现可能的旧锁定，尝试清理...")
                cleanup_manager.release_lock()
                # 等待一下确保清理完成
                import time
                time.sleep(0.5)
        except Exception as e:
            startup_logger.debug(f"清理旧锁定时出错（可忽略）: {e}")

        try:
            instance_manager = ensure_single_instance("wx_group_sender")
            startup_logger.info("✅ 单实例检查通过，程序可以启动")
        except RuntimeError as e:
            startup_logger.warning(f"❌ 单实例检查失败: {e}")

            # 创建临时QApplication用于显示消息框
            temp_app = QApplication(sys.argv)
            QMessageBox.warning(
                None,
                "程序已运行",
                f"Meet space 微信群发助手已在运行中！\n\n{str(e)}\n\n请检查系统托盘或任务管理器。",
                QMessageBox.StandardButton.Ok
            )
            temp_app.quit()
            sys.exit(1)

        # 检查管理员权限（不强制提升）
        startup_logger.info("检查管理员权限...")
        has_admin = ensure_admin_privileges(auto_elevate=False)

        if not has_admin:
            startup_logger.warning("当前以普通用户权限运行")
            startup_logger.warning("部分功能（如DLL注入）可能受限，建议以管理员身份运行")
        else:
            startup_logger.info("管理员权限检查通过")

        # 创建并运行应用程序
        app = WeChatMassSender(instance_manager)
        exit_code = app.run()

        # 释放单实例锁定
        if instance_manager:
            startup_logger.info("释放单实例锁定...")
            instance_manager.release_lock()
            startup_logger.info("✅ 单实例锁定已释放")

        sys.exit(exit_code)

    except Exception as e:
        # 释放单实例锁定
        if instance_manager:
            try:
                instance_manager.release_lock()
            except:
                pass

        # 使用日志记录错误而不是print
        try:
            error_logger = setup_logger("startup_error")
            error_logger.error(f"启动应用程序失败: {e}")
        except:
            # 如果日志系统也失败了，才使用print
            print(f"启动应用程序失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
