#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级循环发送启动逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_advanced_methods_exist():
    """测试高级方法是否存在"""
    print("🔧 测试高级方法是否存在...")
    
    try:
        from ui.loop_send_page import LoopSendPage
        
        # 检查高级任务创建方法
        if hasattr(LoopSendPage, '_create_advanced_loop_task'):
            print("✅ _create_advanced_loop_task 方法存在")
        else:
            print("❌ _create_advanced_loop_task 方法不存在")
            return False
        
        # 检查风控检查方法
        if hasattr(LoopSendPage, '_check_current_send_limits'):
            print("✅ _check_current_send_limits 方法存在")
        else:
            print("❌ _check_current_send_limits 方法不存在")
            return False
        
        # 检查工作时间判断方法
        if hasattr(LoopSendPage, '_is_in_work_hours'):
            print("✅ _is_in_work_hours 方法存在")
        else:
            print("❌ _is_in_work_hours 方法不存在")
            return False
        
        # 检查智能调度方法
        if hasattr(LoopSendPage, '_should_start_immediately'):
            print("✅ _should_start_immediately 方法存在")
        else:
            print("❌ _should_start_immediately 方法不存在")
            return False
        
        if hasattr(LoopSendPage, '_schedule_delayed_start'):
            print("✅ _schedule_delayed_start 方法存在")
        else:
            print("❌ _schedule_delayed_start 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_settings_logic():
    """测试系统设置逻辑"""
    print("\n🔧 测试系统设置逻辑...")
    
    try:
        import inspect
        from ui.loop_send_page import LoopSendPage
        
        # 检查create_and_start_task_for_group方法的逻辑
        method_source = inspect.getsource(LoopSendPage.create_and_start_task_for_group)
        
        # 检查是否包含系统风控判断
        if 'use_system_risk_control' in method_source:
            print("✅ 包含系统风控设置判断")
        else:
            print("❌ 缺少系统风控设置判断")
            return False
        
        # 检查是否包含发送间隔和批量大小设置
        if 'interval_seconds' in method_source and 'batch_size' in method_source:
            print("✅ 包含发送间隔和批量大小设置")
        else:
            print("❌ 缺少发送间隔和批量大小设置")
            return False
        
        # 检查是否包含循环间隔设置判断
        if 'loop_cycle_enabled' in method_source:
            print("✅ 包含循环间隔设置判断")
        else:
            print("❌ 缺少循环间隔设置判断")
            return False
        
        # 检查是否包含工作时间设置
        if 'work_days' in method_source and 'work_hours' in method_source:
            print("✅ 包含工作时间设置")
        else:
            print("❌ 缺少工作时间设置")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_risk_control_logic():
    """测试风控逻辑"""
    print("\n🔧 测试风控逻辑...")
    
    try:
        import inspect
        from ui.loop_send_page import LoopSendPage
        
        # 检查_create_advanced_loop_task方法的风控逻辑
        method_source = inspect.getsource(LoopSendPage._create_advanced_loop_task)
        
        # 检查是否包含风控保护判断
        if 'enable_risk_control' in method_source:
            print("✅ 包含风控保护启用判断")
        else:
            print("❌ 缺少风控保护启用判断")
            return False
        
        # 检查是否包含每日和每小时限制
        if 'daily_limit' in method_source and 'hourly_limit' in method_source:
            print("✅ 包含每日和每小时发送限制")
        else:
            print("❌ 缺少每日和每小时发送限制")
            return False
        
        # 检查是否包含智能调度模式
        if '_should_start_immediately' in method_source and '_schedule_delayed_start' in method_source:
            print("✅ 包含智能调度模式")
        else:
            print("❌ 缺少智能调度模式")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_work_time_logic():
    """测试工作时间逻辑"""
    print("\n🔧 测试工作时间逻辑...")
    
    try:
        from datetime import datetime
        
        # 创建QApplication以避免GUI错误
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from ui.loop_send_page import LoopSendPage
        
        # 创建实例（不显示）
        page = LoopSendPage()
        
        # 测试工作时间判断
        work_hours = {"start": "09:00", "end": "18:00"}
        
        # 测试工作时间内
        if page._is_in_work_hours("10:00", work_hours):
            print("✅ 工作时间内判断正确")
        else:
            print("❌ 工作时间内判断错误")
            return False
        
        # 测试工作时间外
        if not page._is_in_work_hours("20:00", work_hours):
            print("✅ 工作时间外判断正确")
        else:
            print("❌ 工作时间外判断错误")
            return False
        
        # 测试下次工作时间计算
        work_days = [1, 2, 3, 4, 5]  # 周一到周五
        next_time = page._calculate_next_work_time(work_days, work_hours)
        if next_time:
            print(f"✅ 下次工作时间计算成功: {next_time}")
        else:
            print("❌ 下次工作时间计算失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 高级循环发送启动逻辑测试")
    print("=" * 60)
    print("测试完整的系统设置判断和智能调度功能")
    print("=" * 60)
    
    tests = [
        ("高级方法存在性测试", test_advanced_methods_exist),
        ("系统设置逻辑测试", test_system_settings_logic),
        ("风控逻辑测试", test_risk_control_logic),
        ("工作时间逻辑测试", test_work_time_logic)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 高级循环发送启动逻辑测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 高级循环发送启动逻辑实现成功！")
        print("\n✨ 完整功能:")
        print("  🎯 分组循环发送设置判断")
        print("  ⚙️  系统风控设置 vs 自定义设置")
        print("  📊 发送间隔和批量大小配置")
        print("  🔄 循环间隔设置判断")
        print("  📅 执行时间设置（星期、工作时间）")
        print("  📝 富文本/文本消息类型判断")
        print("  🛡️  风控保护（每日/每小时限制）")
        print("  🧠 智能调度模式（节省内存）")
        
        print("\n📋 启动逻辑流程:")
        print("  1️⃣ 检查分组成员和消息内容")
        print("  2️⃣ 判断是否启用系统风控设置")
        print("  3️⃣ 获取发送间隔和批量大小")
        print("  4️⃣ 判断是否启用循环间隔设置")
        print("  5️⃣ 检查工作日和工作时间设置")
        print("  6️⃣ 创建高级任务配置")
        print("  7️⃣ 检查系统风控保护状态")
        print("  8️⃣ 判断当前时间是否适合启动")
        print("  9️⃣ 立即启动或智能调度延迟启动")
        print("  🔟 采用定时发送的智能调度模式")
        
        print("\n🎯 现在循环发送具备完整的企业级功能！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
