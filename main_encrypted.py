#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet space 微信群发助手 - 加密版主程序入口
支持运行时配置生成和加密存储的单文件客户端
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def initialize_runtime_environment():
    """初始化运行时环境"""
    try:
        print("🔧 初始化应用环境...")
        
        # 导入运行时配置生成器
        from runtime_config_generator import initialize_app_environment
        
        # 初始化应用环境
        success = initialize_app_environment()
        
        if success:
            print("✅ 应用环境初始化成功")
            return True
        else:
            print("❌ 应用环境初始化失败")
            return False
            
    except Exception as e:
        print(f"⚠️  运行时环境初始化异常: {e}")
        # 即使初始化失败，也尝试继续运行
        return True

def setup_application():
    """设置应用程序"""
    try:
        # 导入PyQt6
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QIcon
        
        # 设置高DPI支持
        QApplication.setHighDpiScaleFactorRoundingPolicy(
            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
        )
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("Meet space 微信群发助手")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("Meet space 会客创意空间")
        
        # 设置应用图标
        try:
            # 尝试从资源中加载图标
            icon_path = None
            possible_icon_paths = [
                "resources/icons/app_icon.ico",
                "app_icon.ico",
                str(Path(__file__).parent / "resources" / "icons" / "app_icon.ico")
            ]
            
            for path in possible_icon_paths:
                if os.path.exists(path):
                    icon_path = path
                    break
            
            if icon_path:
                app.setWindowIcon(QIcon(icon_path))
                print(f"✅ 应用图标已设置: {icon_path}")
            else:
                print("⚠️  未找到应用图标文件")
                
        except Exception as e:
            print(f"⚠️  设置应用图标失败: {e}")
        
        return app
        
    except ImportError as e:
        print(f"❌ 导入PyQt6失败: {e}")
        print("请确保PyQt6已正确安装: pip install PyQt6")
        return None
    except Exception as e:
        print(f"❌ 应用程序设置失败: {e}")
        return None

def setup_logging():
    """设置日志系统"""
    try:
        from utils.logger import setup_logger
        from runtime_config_generator import get_app_paths
        
        logger = setup_logger()
        paths = get_app_paths()
        
        logger.info("=== Meet space 微信群发助手启动 ===")
        logger.info(f"版本: 1.0.0 (加密版)")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"可执行文件: {sys.executable}")
        logger.info(f"应用数据目录: {paths['app_data']}")
        logger.info(f"配置目录: {paths['config']}")
        
        return logger
        
    except Exception as e:
        print(f"⚠️  日志系统初始化失败: {e}")
        return None

def create_main_window():
    """创建主窗口"""
    try:
        from ui.main_window import MainWindow
        
        print("🚀 创建主窗口...")
        main_window = MainWindow()
        
        return main_window
        
    except ImportError as e:
        print(f"❌ 导入主窗口失败: {e}")
        print("请检查ui模块是否完整")
        return None
    except Exception as e:
        print(f"❌ 创建主窗口失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def show_error_dialog(title, message):
    """显示错误对话框"""
    try:
        from PyQt6.QtWidgets import QMessageBox, QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.exec()
        
    except Exception:
        # 如果GUI也失败了，就用控制台输出
        print(f"错误: {title}")
        print(f"详情: {message}")

def main():
    """主函数"""
    print("🎉 Meet space 微信群发助手 v1.0.0 (加密版)")
    print("=" * 60)
    
    try:
        # 1. 初始化运行时环境
        if not initialize_runtime_environment():
            print("⚠️  环境初始化失败，但继续尝试启动...")
        
        # 2. 设置应用程序
        app = setup_application()
        if app is None:
            show_error_dialog("启动失败", "无法初始化应用程序，请检查PyQt6安装")
            return 1
        
        # 3. 设置日志系统
        logger = setup_logging()
        
        # 4. 创建主窗口
        main_window = create_main_window()
        if main_window is None:
            show_error_dialog("启动失败", "无法创建主窗口，请检查程序文件完整性")
            return 1
        
        # 5. 显示主窗口
        print("✅ 显示主窗口...")
        main_window.show()
        
        if logger:
            logger.info("主窗口已显示")
        
        # 6. 运行应用程序
        print("🚀 应用程序运行中...")
        exit_code = app.exec()
        
        if logger:
            logger.info(f"应用程序退出，退出代码: {exit_code}")
            logger.info("=== Meet space 微信群发助手结束 ===")
        
        print(f"程序正常退出，代码: {exit_code}")
        return exit_code
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
        return 0
        
    except ImportError as e:
        error_msg = f"导入模块失败: {e}\n请确保所有依赖包已正确安装"
        print(f"❌ {error_msg}")
        show_error_dialog("导入错误", error_msg)
        return 1
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        print(f"❌ {error_msg}")
        
        import traceback
        traceback.print_exc()
        
        show_error_dialog("启动错误", error_msg)
        return 1

if __name__ == "__main__":
    # 设置控制台编码
    if sys.platform == 'win32':
        try:
            import locale
            locale.setlocale(locale.LC_ALL, 'Chinese (Simplified)_China.936')
        except:
            pass
    
    # 运行主程序
    exit_code = main()
    
    # 等待用户确认（仅在开发模式下）
    if '--dev' in sys.argv:
        input("\n按回车键退出...")
    
    sys.exit(exit_code)
