#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复distutils冲突的构建脚本
针对Python 3.13 + PyInstaller 6.14.2的兼容性问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本并给出建议"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 12:
        print("⚠️  检测到Python 3.12+，需要特殊处理distutils问题")
        return True
    return False

def update_pyinstaller():
    """更新PyInstaller到最新版本"""
    print("🔄 更新PyInstaller到最新版本...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "--upgrade", "pyinstaller>=6.15.0"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ PyInstaller更新成功")
            return True
        else:
            print(f"❌ PyInstaller更新失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 更新异常: {e}")
        return False

def create_pyinstaller_hook():
    """创建自定义PyInstaller钩子来解决distutils问题"""
    hooks_dir = Path("hooks")
    hooks_dir.mkdir(exist_ok=True)
    
    hook_content = '''#!/usr/bin/env python3
"""
自定义钩子：解决Python 3.13中的distutils冲突问题
"""

# 隐藏distutils导入，避免冲突
hiddenimports = []

# 排除有问题的distutils模块
excludedimports = ["distutils"]

# 替代方案
def hook(hook_api):
    """钩子函数"""
    # 使用setuptools作为distutils的替代
    hook_api.add_imports("setuptools._distutils")
'''
    
    hook_file = hooks_dir / "hook-distutils.py"
    with open(hook_file, 'w', encoding='utf-8') as f:
        f.write(hook_content)
    
    print(f"✅ 创建自定义钩子: {hook_file}")
    return hook_file

def verify_all_injection_files():
    """验证所有注入相关文件是否存在"""
    print("🔍 验证注入文件...")
    
    required_files = [
        # 注入工具
        "tools/Injector.exe",
        "tools/x64/Injector.exe", 
        "tools/Win32/Injector.exe",
        "tools/ARM64/Injector.exe",
        
        # DLL文件
        "wxhelper_files/wxhelper.dll",
        "wxhelper_files/wxhelper_latest.dll",
        "wxhelper_files/wxhelper_original_backup.dll",
        "wxhelper_files/wxhelper_x64_backup.dll",
        
        # 核心文件
        "main.py",
        "version_info.txt",
        "LICENSE.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            print(f"❌ 缺失: {file_path}")
        else:
            print(f"✅ 存在: {file_path}")
    
    if missing_files:
        print(f"\n❌ 发现 {len(missing_files)} 个缺失文件，构建可能失败")
        return False, missing_files
    else:
        print(f"\n✅ 所有 {len(required_files)} 个关键文件都存在")
        return True, []

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    dirs_to_clean = ["build", "dist", "__pycache__"]
    
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")

def build_with_distutils_fix():
    """使用distutils修复的构建命令"""
    print("🔨 开始修复版构建...")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONUTF8': '1',
        'PYTHONLEGACYWINDOWSSTDIO': '1',
        # 禁用distutils警告
        'SETUPTOOLS_USE_DISTUTILS': 'stdlib'
    })
    
    # 构建命令 - 简化版，避免复杂冲突
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 无控制台
        "--uac-admin",                  # 管理员权限
        "--name=MeetSpaceWeChatSender_Fixed",
        "--icon=resources/icons/app.ico",
        
        # 版本信息
        "--version-file=version_info.txt",
        
        # 注入工具 - 所有架构
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64/Injector.exe;tools/x64",
        "--add-data=tools/Win32/Injector.exe;tools/Win32", 
        "--add-data=tools/ARM64/Injector.exe;tools/ARM64",
        
        # DLL文件
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_original_backup.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_x64_backup.dll;wxhelper_files",
        
        # 基础资源
        "--add-data=resources;resources",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        
        # 关键隐藏导入 - 简化列表
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=psutil",
        "--hidden-import=requests",
        "--hidden-import=json",
        "--hidden-import=pathlib",
        
        # 核心模块
        "--hidden-import=core.auto_injector",
        "--hidden-import=core.injector_tool",
        "--hidden-import=core.http_api_connector",
        "--hidden-import=utils.logger",
        "--hidden-import=utils.simple_admin",
        
        # 排除有问题的模块
        "--exclude-module=distutils",
        "--exclude-module=setuptools._distutils",
        
        # 自定义钩子目录
        "--additional-hooks-dir=hooks",
        
        # 优化选项
        "--optimize=1",
        "--strip",
        "--noupx",
        
        # 主文件
        "main.py"
    ]
    
    print("📋 执行构建命令...")
    print(f"命令: {' '.join(cmd[:10])}...")  # 只显示前10个参数
    
    try:
        result = subprocess.run(
            cmd, 
            env=env,
            capture_output=True, 
            text=True, 
            timeout=600,  # 10分钟超时
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            
            # 检查生成的文件
            exe_path = Path("dist") / "MeetSpaceWeChatSender_Fixed.exe"
            if exe_path.exists():
                size = exe_path.stat().st_size / (1024 * 1024)  # MB
                print(f"📦 生成文件: {exe_path} ({size:.1f} MB)")
                return True
            else:
                print("❌ 未找到生成的exe文件")
                return False
        else:
            print(f"❌ 构建失败，返回码: {result.returncode}")
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            if result.stdout:
                print("标准输出:")
                print(result.stdout[-1000:])  # 最后1000字符
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复版构建流程")
    print("=" * 60)
    
    # 1. 检查Python版本
    is_python_312_plus = check_python_version()
    
    # 2. 更新PyInstaller（可选）
    if is_python_312_plus:
        print("\n🔄 更新PyInstaller以获得更好的兼容性...")
        update_pyinstaller()
    
    # 3. 创建自定义钩子
    print("\n🪝 创建自定义钩子...")
    create_pyinstaller_hook()
    
    # 4. 验证文件
    print("\n🔍 验证构建文件...")
    files_ok, missing_files = verify_all_injection_files()
    if not files_ok:
        print(f"\n❌ 缺失关键文件，无法继续构建")
        print("缺失文件列表:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    # 5. 清理旧文件
    print("\n🧹 清理构建目录...")
    clean_build_dirs()
    
    # 6. 执行构建
    print("\n🔨 执行修复版构建...")
    success = build_with_distutils_fix()
    
    if success:
        print("\n🎉 构建完成！")
        print("\n📋 接下来的步骤:")
        print("  1. 测试生成的exe文件")
        print("  2. 验证管理员权限请求")
        print("  3. 测试注入功能")
        print("  4. 检查UI界面显示")
        return True
    else:
        print("\n❌ 构建失败")
        print("\n💡 可能的解决方案:")
        print("  1. 降级到Python 3.11")
        print("  2. 使用虚拟环境")
        print("  3. 手动安装依赖包")
        print("  4. 检查Windows开发者模式")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)