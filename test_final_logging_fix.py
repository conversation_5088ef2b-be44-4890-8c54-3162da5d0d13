#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终日志修复
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_safe_stream_handler():
    """测试SafeStreamHandler"""
    print("🔧 测试SafeStreamHandler...")
    
    try:
        from utils.logger import SafeStreamHandler
        
        # 测试正常情况
        handler = SafeStreamHandler()
        print("✅ SafeStreamHandler创建成功")
        
        # 测试流有效性检查
        print(f"   流类型: {type(handler.stream)}")
        print(f"   流有效: {handler._is_stream_valid(handler.stream)}")
        
        return True
        
    except Exception as e:
        print(f"❌ SafeStreamHandler测试失败: {e}")
        return False

def test_logger_creation():
    """测试日志器创建"""
    print("\n📝 测试日志器创建...")
    
    try:
        from utils.logger import setup_logger
        
        # 创建测试日志器
        logger = setup_logger("test_final")
        
        print("✅ 日志器创建成功")
        print(f"   处理器数量: {len(logger.handlers)}")
        
        for i, handler in enumerate(logger.handlers):
            handler_type = type(handler).__name__
            print(f"   处理器 {i+1}: {handler_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志器创建失败: {e}")
        return False

def test_logging_output():
    """测试日志输出"""
    print("\n📤 测试日志输出...")
    
    try:
        from utils.logger import setup_logger
        
        logger = setup_logger("test_output")
        
        # 测试各种级别的日志
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.error("这是一条错误日志")
        
        print("✅ 日志输出测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 日志输出测试失败: {e}")
        return False

def test_packed_simulation():
    """测试打包环境模拟"""
    print("\n📦 测试打包环境模拟...")
    
    import sys
    
    # 保存原始流
    original_stdout = sys.stdout
    original_stderr = sys.stderr
    
    try:
        # 模拟打包环境
        sys.stdout = None
        sys.stderr = None
        
        from utils.logger import setup_logger
        
        logger = setup_logger("test_packed")
        logger.info("打包环境测试消息")
        
        print("✅ 打包环境模拟测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 打包环境模拟测试失败: {e}")
        return False
    finally:
        # 恢复原始流
        sys.stdout = original_stdout
        sys.stderr = original_stderr

def test_ui_logging():
    """测试UI相关日志"""
    print("\n🖥️  测试UI日志...")
    
    try:
        from utils.logger import setup_logger
        
        # 模拟UI日志器
        ui_logger = setup_logger("main_window")
        
        # 模拟导致错误的日志消息
        ui_logger.info("表格组件优化完成")
        ui_logger.info("wxhelper未注入: wxhelper未注入")
        ui_logger.info("开始注入wxhelper...")
        
        print("✅ UI日志测试完成")
        return True
        
    except Exception as e:
        print(f"❌ UI日志测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 最终日志修复测试")
    print("=" * 70)
    
    tests = [
        ("SafeStreamHandler", test_safe_stream_handler),
        ("日志器创建", test_logger_creation),
        ("日志输出", test_logging_output),
        ("打包环境模拟", test_packed_simulation),
        ("UI日志", test_ui_logging)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 最终测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！日志系统完全修复！")
        print("\n✨ 修复内容:")
        print("  🔧 创建了SafeStreamHandler类")
        print("  🛡️  添加了流有效性检查")
        print("  🔄 实现了自动流恢复")
        print("  📦 完全支持打包环境")
        print("  🚫 消除了'NoneType' object has no attribute 'write'错误")
        
        print("\n📋 现在程序应该完全不会出现日志错误了！")
        print("包括在客户端运行时的所有日志操作。")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
