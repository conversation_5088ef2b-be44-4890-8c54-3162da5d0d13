#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标管理器

统一管理应用程序图标，支持开发环境和打包环境。
"""

from pathlib import Path
from typing import Optional
from PyQt6.QtGui import QIcon, QPixmap
from PyQt6.QtWidgets import QApplication

from utils.logger import setup_logger

logger = setup_logger("icon_manager")


class IconManager:
    """图标管理器"""
    
    def __init__(self):
        self._icons_cache = {}
        self._icons_dir = None
        self._initialize_icons_dir()
    
    def _initialize_icons_dir(self):
        """初始化图标目录"""
        try:
            # 尝试使用路径管理器
            from utils.path_manager import path_manager
            self._icons_dir = path_manager.get_resource_path("resources/icons")
        except ImportError:
            # 备用方案
            self._icons_dir = Path(__file__).parent.parent / "resources" / "icons"
        
        logger.info(f"图标目录: {self._icons_dir}")
    
    def get_icon(self, icon_name: str) -> QIcon:
        """
        获取图标
        
        Args:
            icon_name: 图标名称（不含扩展名）
            
        Returns:
            QIcon对象
        """
        if icon_name in self._icons_cache:
            return self._icons_cache[icon_name]
        
        # 尝试不同的图标格式
        icon_formats = ['.ico', '.png', '.svg']
        icon_path = None
        
        for fmt in icon_formats:
            potential_path = self._icons_dir / f"{icon_name}{fmt}"
            if potential_path.exists():
                icon_path = potential_path
                break
        
        if icon_path:
            try:
                # 检查是否有QApplication实例
                if QApplication.instance() is None:
                    logger.warning(f"无QApplication实例，延迟加载图标: {icon_name}")
                    # 存储路径，稍后加载
                    self._icons_cache[icon_name] = str(icon_path)
                    return QIcon()

                icon = QIcon(str(icon_path))
                self._icons_cache[icon_name] = icon
                logger.debug(f"加载图标: {icon_name} -> {icon_path}")
                return icon
            except Exception as e:
                logger.error(f"加载图标失败 {icon_name}: {e}")
                return QIcon()
        else:
            # 返回空图标
            logger.warning(f"未找到图标: {icon_name}")
            empty_icon = QIcon()
            self._icons_cache[icon_name] = empty_icon
            return empty_icon
    
    def get_app_icon(self) -> QIcon:
        """获取应用程序主图标"""
        return self.get_icon("app")
    
    def get_installer_icon(self) -> QIcon:
        """获取安装程序图标"""
        return self.get_icon("installer")
    
    def get_uninstall_icon(self) -> QIcon:
        """获取卸载程序图标"""
        return self.get_icon("uninstall")
    
    def get_document_icon(self) -> QIcon:
        """获取文档图标"""
        return self.get_icon("document")
    
    def set_app_icon(self, app: Optional[QApplication] = None):
        """
        设置应用程序图标
        
        Args:
            app: QApplication实例，如果为None则获取当前实例
        """
        if app is None:
            app = QApplication.instance()
        
        if app:
            app_icon = self.get_app_icon()
            if not app_icon.isNull():
                app.setWindowIcon(app_icon)
                logger.info("应用程序图标设置成功")
            else:
                logger.warning("应用程序图标设置失败：图标为空")
        else:
            logger.warning("无法设置应用程序图标：未找到QApplication实例")
    
    def apply_window_icon(self, window):
        """
        为窗口应用图标
        
        Args:
            window: 窗口对象
        """
        try:
            app_icon = self.get_app_icon()
            if not app_icon.isNull():
                window.setWindowIcon(app_icon)
                logger.debug(f"窗口图标设置成功: {window.__class__.__name__}")
            else:
                logger.warning(f"窗口图标设置失败：图标为空 - {window.__class__.__name__}")
        except Exception as e:
            logger.error(f"设置窗口图标失败: {e}")
    
    def get_pixmap(self, icon_name: str, size: tuple = (32, 32)) -> QPixmap:
        """
        获取图标的像素图
        
        Args:
            icon_name: 图标名称
            size: 图标尺寸 (width, height)
            
        Returns:
            QPixmap对象
        """
        icon = self.get_icon(icon_name)
        if not icon.isNull():
            return icon.pixmap(size[0], size[1])
        else:
            # 返回空像素图
            return QPixmap(size[0], size[1])
    
    def preload_icons(self):
        """预加载常用图标"""
        common_icons = [
            "app", "installer", "uninstall", "document"
        ]
        
        logger.info("预加载常用图标...")
        for icon_name in common_icons:
            self.get_icon(icon_name)
        
        logger.info(f"预加载完成，共加载 {len(self._icons_cache)} 个图标")
    
    def clear_cache(self):
        """清理图标缓存"""
        self._icons_cache.clear()
        logger.info("图标缓存已清理")
    
    def get_icon_info(self) -> dict:
        """获取图标信息"""
        info = {
            "icons_dir": str(self._icons_dir),
            "cached_icons": list(self._icons_cache.keys()),
            "available_icons": []
        }
        
        # 扫描可用图标
        if self._icons_dir.exists():
            for icon_file in self._icons_dir.glob("*"):
                if icon_file.suffix.lower() in ['.ico', '.png', '.svg']:
                    info["available_icons"].append(icon_file.stem)
        
        return info


# 全局图标管理器实例
icon_manager = IconManager()


def get_app_icon() -> QIcon:
    """获取应用程序图标的便捷函数"""
    return icon_manager.get_app_icon()


def set_app_icon(app: Optional[QApplication] = None):
    """设置应用程序图标的便捷函数"""
    icon_manager.set_app_icon(app)


def apply_window_icon(window):
    """为窗口应用图标的便捷函数"""
    icon_manager.apply_window_icon(window)


if __name__ == "__main__":
    # 测试图标管理器
    print("🎨 测试图标管理器")
    print("=" * 50)
    
    im = IconManager()
    
    # 预加载图标
    im.preload_icons()
    
    # 显示图标信息
    info = im.get_icon_info()
    print(f"\n📁 图标目录: {info['icons_dir']}")
    print(f"📦 缓存图标: {info['cached_icons']}")
    print(f"📋 可用图标: {info['available_icons']}")
    
    # 测试获取图标
    app_icon = im.get_app_icon()
    print(f"\n🎯 应用程序图标: {'✅ 有效' if not app_icon.isNull() else '❌ 无效'}")
    
    installer_icon = im.get_installer_icon()
    print(f"📦 安装程序图标: {'✅ 有效' if not installer_icon.isNull() else '❌ 无效'}")
    
    print("\n🎉 图标管理器测试完成！")
