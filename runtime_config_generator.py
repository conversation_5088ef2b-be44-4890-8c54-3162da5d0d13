#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行时配置生成器
程序启动时自动生成加密的配置文件
"""

import os
import json
import base64
import hashlib
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class RuntimeConfigGenerator:
    """运行时配置生成器"""
    
    def __init__(self):
        self.app_data_dir = self._get_app_data_dir()
        self.config_dir = self.app_data_dir / "config"
        self.encryption_key = self._generate_encryption_key()
        
    def _get_app_data_dir(self):
        """获取应用数据目录"""
        if os.name == 'nt':  # Windows
            app_data = Path(os.environ.get('APPDATA', ''))
            return app_data / "MeetSpaceWeChatSender"
        else:
            home = Path.home()
            return home / ".meetspace_wechat_sender"
    
    def _generate_encryption_key(self):
        """生成加密密钥"""
        # 使用机器特征生成唯一密钥
        import platform
        import uuid
        
        machine_info = f"{platform.node()}{platform.machine()}{uuid.getnode()}"
        password = machine_info.encode()
        salt = b"meetspace_salt_2024"
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return Fernet(key)
    
    def _encrypt_data(self, data):
        """加密数据"""
        if isinstance(data, dict):
            data = json.dumps(data, ensure_ascii=False, indent=2)
        if isinstance(data, str):
            data = data.encode('utf-8')
        return self.encryption_key.encrypt(data)
    
    def _decrypt_data(self, encrypted_data):
        """解密数据"""
        decrypted = self.encryption_key.decrypt(encrypted_data)
        return decrypted.decode('utf-8')
    
    def ensure_directories(self):
        """确保目录存在"""
        directories = [
            self.app_data_dir,
            self.config_dir,
            self.app_data_dir / "logs",
            self.app_data_dir / "templates",
            self.app_data_dir / "backups",
            self.app_data_dir / "cache",
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def generate_default_configs(self):
        """生成默认配置文件"""
        print("🔧 生成运行时配置文件...")
        
        # 1. 系统配置
        system_config = {
            "app_name": "Meet space 微信群发助手",
            "version": "1.0.0",
            "author": "Meet space 会客创意空间",
            "language": "zh-CN",
            "theme": "default",
            "auto_save": True,
            "auto_backup": True,
            "log_level": "INFO",
            "max_log_files": 10,
            "check_updates": True,
        }
        
        # 2. 微信配置
        wechat_config = {
            "api_type": "wcferry",
            "auto_connect": True,
            "connection_timeout": 30,
            "retry_times": 3,
            "retry_interval": 5,
            "enable_risk_control": True,
            "max_send_per_minute": 10,
            "min_send_interval": 3,
        }
        
        # 3. 发送设置
        send_settings = {
            "default_send_mode": "timing",
            "enable_preview": True,
            "confirm_before_send": True,
            "auto_save_draft": True,
            "max_batch_size": 50,
            "send_timeout": 30,
            "enable_send_log": True,
        }
        
        # 4. 界面设置
        ui_settings = {
            "window_width": 1200,
            "window_height": 800,
            "window_maximized": False,
            "remember_window_state": True,
            "show_toolbar": True,
            "show_statusbar": True,
            "animation_enabled": True,
            "sound_enabled": True,
        }
        
        # 5. 消息模板
        message_templates = {
            "templates": [
                {
                    "id": "welcome",
                    "name": "欢迎消息",
                    "content": "欢迎使用Meet space微信群发助手！",
                    "category": "通用",
                    "created_time": "2024-08-05 14:00:00"
                },
                {
                    "id": "notification",
                    "name": "通知消息",
                    "content": "这是一条重要通知，请注意查收。",
                    "category": "通知",
                    "created_time": "2024-08-05 14:00:00"
                }
            ],
            "categories": ["通用", "通知", "营销", "客服", "其他"]
        }
        
        # 保存加密配置
        configs = {
            "system_config.json": system_config,
            "wechat_config.json": wechat_config,
            "send_settings.json": send_settings,
            "ui_settings.json": ui_settings,
            "templates.json": message_templates,
        }
        
        for filename, config_data in configs.items():
            config_file = self.config_dir / filename
            encrypted_data = self._encrypt_data(config_data)
            
            with open(config_file, 'wb') as f:
                f.write(encrypted_data)
            
            print(f"  ✅ 已生成: {filename}")
    
    def load_config(self, config_name):
        """加载配置文件"""
        config_file = self.config_dir / f"{config_name}.json"
        
        if not config_file.exists():
            return None
        
        try:
            with open(config_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self._decrypt_data(encrypted_data)
            return json.loads(decrypted_data)
        except Exception as e:
            print(f"⚠️  加载配置失败 {config_name}: {e}")
            return None
    
    def save_config(self, config_name, config_data):
        """保存配置文件"""
        config_file = self.config_dir / f"{config_name}.json"
        
        try:
            encrypted_data = self._encrypt_data(config_data)
            with open(config_file, 'wb') as f:
                f.write(encrypted_data)
            return True
        except Exception as e:
            print(f"⚠️  保存配置失败 {config_name}: {e}")
            return False
    
    def initialize_runtime_environment(self):
        """初始化运行时环境"""
        print("🚀 初始化运行时环境...")
        
        # 1. 确保目录存在
        self.ensure_directories()
        print("  ✅ 目录结构已创建")
        
        # 2. 检查配置文件
        required_configs = [
            "system_config",
            "wechat_config", 
            "send_settings",
            "ui_settings",
            "templates"
        ]
        
        missing_configs = []
        for config_name in required_configs:
            if not (self.config_dir / f"{config_name}.json").exists():
                missing_configs.append(config_name)
        
        # 3. 生成缺失的配置
        if missing_configs:
            print(f"  📝 生成缺失配置: {', '.join(missing_configs)}")
            self.generate_default_configs()
        else:
            print("  ✅ 配置文件完整")
        
        # 4. 创建日志文件
        log_file = self.app_data_dir / "logs" / "app.log"
        if not log_file.exists():
            log_file.touch()
            print("  ✅ 日志文件已创建")
        
        # 5. 创建缓存目录
        cache_dir = self.app_data_dir / "cache"
        if not cache_dir.exists():
            cache_dir.mkdir(parents=True, exist_ok=True)
            print("  ✅ 缓存目录已创建")
        
        print("  🎉 运行时环境初始化完成")
        return True
    
    def get_app_data_path(self):
        """获取应用数据路径"""
        return str(self.app_data_dir)
    
    def get_config_path(self):
        """获取配置路径"""
        return str(self.config_dir)

# 全局实例
runtime_config = RuntimeConfigGenerator()

def initialize_app_environment():
    """初始化应用环境 - 在main.py中调用"""
    return runtime_config.initialize_runtime_environment()

def get_config(config_name):
    """获取配置 - 在应用中调用"""
    return runtime_config.load_config(config_name)

def save_config(config_name, config_data):
    """保存配置 - 在应用中调用"""
    return runtime_config.save_config(config_name, config_data)

def get_app_paths():
    """获取应用路径"""
    return {
        "app_data": runtime_config.get_app_data_path(),
        "config": runtime_config.get_config_path(),
        "logs": str(runtime_config.app_data_dir / "logs"),
        "templates": str(runtime_config.app_data_dir / "templates"),
        "backups": str(runtime_config.app_data_dir / "backups"),
        "cache": str(runtime_config.app_data_dir / "cache"),
    }

if __name__ == "__main__":
    # 测试运行
    print("🧪 测试运行时配置生成器...")
    success = initialize_app_environment()
    if success:
        print("✅ 测试成功！")
        
        # 显示路径信息
        paths = get_app_paths()
        print("\\n📁 应用路径:")
        for name, path in paths.items():
            print(f"  {name}: {path}")
    else:
        print("❌ 测试失败！")
