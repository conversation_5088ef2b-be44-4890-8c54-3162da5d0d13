#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题化对话框基类

为所有对话框提供统一的主题应用机制，确保对话框能够正确跟随主题变化。

规范要求：
1. 所有对话框必须继承自 ThemedDialogBase 或手动实现主题应用
2. 对话框必须在初始化时注册到主题管理器
3. 对话框必须在关闭时清理主题管理器注册
4. 对话框中的标签栏、表格等组件会自动跟随主题
"""

from PyQt6.QtWidgets import QDialog, QApplication
from PyQt6.QtCore import pyqtSignal
import platform
from ui.modern_theme_manager import theme_manager as modern_theme_manager
from utils.logger import setup_logger

logger = setup_logger("themed_dialog_base")


class ThemedDialogBase(QDialog):
    """主题化对话框基类

    所有需要跟随主题变化的对话框都应该继承此类，
    或者手动实现相同的主题应用机制。
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self._theme_registered = False

        # 设置主题支持
        self._setup_theme_support()

        # 设置Windows深色标题栏（如果支持）
        self._setup_windows_dark_titlebar()

    def _setup_windows_dark_titlebar(self):
        """设置Windows深色标题栏"""
        try:
            if platform.system() == "Windows":
                # 获取当前主题
                current_theme = modern_theme_manager.get_current_theme()

                # 如果是深色主题，设置深色标题栏
                if current_theme in ["深色主题", "科技主题"]:
                    self._set_windows_dark_mode(True)
                else:
                    self._set_windows_dark_mode(False)

        except Exception as e:
            logger.debug(f"设置Windows深色标题栏失败: {e}")

    def _set_windows_dark_mode(self, dark_mode: bool):
        """使用Windows API设置深色模式"""
        try:
            import ctypes
            from ctypes import wintypes

            # 获取窗口句柄
            hwnd = int(self.winId())

            # Windows 10/11 深色模式API
            # DWMWA_USE_IMMERSIVE_DARK_MODE = 20
            DWMWA_USE_IMMERSIVE_DARK_MODE = 20

            # 设置深色模式
            value = ctypes.c_int(1 if dark_mode else 0)
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_USE_IMMERSIVE_DARK_MODE,
                ctypes.byref(value),
                ctypes.sizeof(value),
            )

            logger.debug(f"Windows深色标题栏设置: {'开启' if dark_mode else '关闭'}")

        except Exception as e:
            logger.debug(f"设置Windows深色模式失败: {e}")

    def _setup_theme_support(self):
        """设置主题支持"""
        try:
            # 注册到现代主题管理器
            modern_theme_manager.register_widget(self, "dialog")
            self._theme_registered = True

            # 连接主题变化信号
            modern_theme_manager.theme_changed.connect(self.on_theme_changed)

            logger.debug(f"对话框已注册到主题管理器: {self.__class__.__name__}")

        except Exception as e:
            logger.error(f"设置对话框主题支持失败: {e}")

    def on_theme_changed(self, theme_name: str):
        """主题变化处理 - 改进版本，同时处理主题和字体变化"""
        try:
            # 获取当前应用字体
            app = QApplication.instance()
            if app:
                current_font = app.font()

                # 设置对话框字体
                self.setFont(current_font)

                # 递归设置所有子组件字体
                self._refresh_children_font(current_font)

            # 更新Windows标题栏主题
            if platform.system() == "Windows":
                if theme_name in ["深色主题", "科技主题"]:
                    self._set_windows_dark_mode(True)
                else:
                    self._set_windows_dark_mode(False)

            # 强制刷新对话框样式
            self.style().unpolish(self)
            self.style().polish(self)
            self.update()

            logger.debug(
                f"对话框主题和字体已更新: {self.__class__.__name__} -> {theme_name}"
            )

        except Exception as e:
            logger.error(f"对话框主题更新失败: {e}")

    def _refresh_children_font(self, font):
        """递归刷新所有子组件的字体"""
        try:
            from PyQt6.QtWidgets import QWidget

            for child in self.findChildren(QWidget):
                try:
                    child.setFont(font)
                except Exception:
                    # 某些组件可能不支持字体设置，忽略错误
                    pass

        except Exception as e:
            logger.debug(f"刷新子组件字体失败: {e}")

    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 清理主题管理器注册
            if self._theme_registered:
                try:
                    modern_theme_manager.theme_changed.disconnect(self.on_theme_changed)
                    # 注意：现代主题管理器可能没有 unregister_widget 方法
                    # 这是正常的，因为它使用弱引用自动清理
                    logger.debug(f"对话框主题注册已清理: {self.__class__.__name__}")
                except Exception as cleanup_error:
                    logger.debug(f"清理对话框主题注册时出现预期错误: {cleanup_error}")

            super().closeEvent(event)

        except Exception as e:
            logger.error(f"对话框关闭处理失败: {e}")
            super().closeEvent(event)


def ensure_dialog_theme_support(dialog_instance):
    """确保对话框支持主题

    为已存在的对话框实例添加主题支持。
    适用于无法修改继承关系的对话框。

    Args:
        dialog_instance: 对话框实例
    """
    try:
        if not hasattr(dialog_instance, "_theme_registered"):
            # 注册到现代主题管理器
            modern_theme_manager.register_widget(dialog_instance, "dialog")
            dialog_instance._theme_registered = True

            # 添加主题变化处理方法
            def on_theme_changed(theme_name: str):
                try:
                    # 获取当前应用字体
                    app = QApplication.instance()
                    if app:
                        current_font = app.font()

                        # 设置对话框字体
                        dialog_instance.setFont(current_font)

                        # 递归设置所有子组件字体
                        from PyQt6.QtWidgets import QWidget

                        for child in dialog_instance.findChildren(QWidget):
                            try:
                                child.setFont(current_font)
                            except Exception:
                                pass

                    dialog_instance.style().unpolish(dialog_instance)
                    dialog_instance.style().polish(dialog_instance)
                    dialog_instance.update()
                    logger.debug(
                        f"对话框主题和字体已更新: {dialog_instance.__class__.__name__} -> {theme_name}"
                    )
                except Exception as e:
                    logger.error(f"对话框主题更新失败: {e}")

            # 连接主题变化信号
            modern_theme_manager.theme_changed.connect(on_theme_changed)
            dialog_instance._on_theme_changed = on_theme_changed

            # 重写关闭事件
            original_close_event = dialog_instance.closeEvent

            def themed_close_event(event):
                try:
                    if (
                        hasattr(dialog_instance, "_theme_registered")
                        and dialog_instance._theme_registered
                    ):
                        try:
                            modern_theme_manager.theme_changed.disconnect(
                                dialog_instance._on_theme_changed
                            )
                            logger.debug(
                                f"对话框主题注册已清理: {dialog_instance.__class__.__name__}"
                            )
                        except Exception as cleanup_error:
                            logger.debug(
                                f"清理对话框主题注册时出现预期错误: {cleanup_error}"
                            )

                    original_close_event(event)

                except Exception as e:
                    logger.error(f"对话框关闭处理失败: {e}")
                    original_close_event(event)

            dialog_instance.closeEvent = themed_close_event

            logger.debug(
                f"已为对话框添加主题支持: {dialog_instance.__class__.__name__}"
            )

    except Exception as e:
        logger.error(f"为对话框添加主题支持失败: {e}")


class DialogThemeValidator:
    """对话框主题验证器

    用于验证对话框是否正确实现了主题支持
    """

    @staticmethod
    def validate_dialog_theme_support(dialog_instance) -> bool:
        """验证对话框是否支持主题

        Args:
            dialog_instance: 对话框实例

        Returns:
            bool: 是否支持主题
        """
        try:
            # 检查是否注册到主题管理器
            if not hasattr(dialog_instance, "_theme_registered"):
                logger.warning(
                    f"对话框未注册主题支持: {dialog_instance.__class__.__name__}"
                )
                return False

            # 检查是否连接了主题变化信号
            if not hasattr(dialog_instance, "on_theme_changed") and not hasattr(
                dialog_instance, "_on_theme_changed"
            ):
                logger.warning(
                    f"对话框未连接主题变化信号: {dialog_instance.__class__.__name__}"
                )
                return False

            logger.debug(
                f"对话框主题支持验证通过: {dialog_instance.__class__.__name__}"
            )
            return True

        except Exception as e:
            logger.error(f"验证对话框主题支持失败: {e}")
            return False

    @staticmethod
    def get_dialog_theme_report() -> dict:
        """获取对话框主题支持报告

        Returns:
            dict: 主题支持报告
        """
        try:
            app = QApplication.instance()
            if not app:
                return {"error": "无法获取应用实例"}

            # 查找所有对话框
            dialogs = []
            for widget in app.allWidgets():
                if isinstance(widget, QDialog) and widget.isVisible():
                    dialogs.append(widget)

            report = {
                "total_dialogs": len(dialogs),
                "themed_dialogs": 0,
                "unthemed_dialogs": 0,
                "dialog_details": [],
            }

            for dialog in dialogs:
                is_themed = DialogThemeValidator.validate_dialog_theme_support(dialog)

                dialog_info = {
                    "class_name": dialog.__class__.__name__,
                    "title": dialog.windowTitle(),
                    "is_themed": is_themed,
                }

                report["dialog_details"].append(dialog_info)

                if is_themed:
                    report["themed_dialogs"] += 1
                else:
                    report["unthemed_dialogs"] += 1

            return report

        except Exception as e:
            logger.error(f"生成对话框主题报告失败: {e}")
            return {"error": str(e)}


# 对话框主题应用规范文档
DIALOG_THEME_GUIDELINES = """
# 对话框主题应用规范

## 1. 新建对话框规范

### 方法一：继承 ThemedDialogBase（推荐）
```python
from ui.themed_dialog_base import ThemedDialogBase

class MyDialog(ThemedDialogBase):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 主题支持已自动设置
        self.setup_ui()
```

### 方法二：手动实现主题支持
```python
from PyQt6.QtWidgets import QDialog
from ui.modern_theme_manager import theme_manager as modern_theme_manager

class MyDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 注册到主题管理器
        modern_theme_manager.register_widget(self, "dialog")
        modern_theme_manager.theme_changed.connect(self.on_theme_changed)
        
        self.setup_ui()
    
    def on_theme_changed(self, theme_name: str):
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()
    
    def closeEvent(self, event):
        try:
            modern_theme_manager.theme_changed.disconnect(self.on_theme_changed)
        except:
            pass
        super().closeEvent(event)
```

## 2. 现有对话框改造规范

### 使用工具函数改造
```python
from ui.themed_dialog_base import ensure_dialog_theme_support

# 在对话框初始化后调用
dialog = ExistingDialog()
ensure_dialog_theme_support(dialog)
```

## 3. 验证规范

### 验证对话框主题支持
```python
from ui.themed_dialog_base import DialogThemeValidator

# 验证单个对话框
is_themed = DialogThemeValidator.validate_dialog_theme_support(dialog)

# 获取全局报告
report = DialogThemeValidator.get_dialog_theme_report()
```

## 4. 强制要求

1. **所有新建对话框必须支持主题**
2. **所有现有对话框必须改造为支持主题**
3. **对话框中的标签栏、表格等组件会自动跟随主题**
4. **对话框关闭时必须清理主题管理器注册**

## 5. 主题样式覆盖

对话框中的组件会自动应用以下样式：
- QTabWidget::pane, QTabBar::tab - 标签栏样式
- QLabel, QPushButton - 基础组件样式  
- QLineEdit, QTextEdit - 输入组件样式
- QTableWidget, QHeaderView - 表格组件样式
- QGroupBox - 分组框样式
"""
