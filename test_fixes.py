#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复的功能
1. 清空日志文件功能
2. 权限提升对话框
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_log_clearing():
    """测试日志清空功能"""
    print("🧹 测试日志清空功能...")
    
    try:
        # 获取日志目录
        from utils.path_manager import path_manager
        logs_dir = path_manager.app_data_dir / "logs"
        
        print(f"日志目录: {logs_dir}")
        
        # 确保日志目录存在
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建一些测试日志文件
        test_files = [
            "test1.log",
            "test2.log", 
            "app.log",
            "main.log"
        ]
        
        print("创建测试日志文件...")
        for filename in test_files:
            test_file = logs_dir / filename
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f"这是测试日志文件 {filename}\n")
                f.write("包含一些测试内容\n")
                f.write("用于测试清空功能\n")
            print(f"   创建: {filename}")
        
        # 检查文件大小
        print("\n清空前文件大小:")
        total_size_before = 0
        for filename in test_files:
            test_file = logs_dir / filename
            if test_file.exists():
                size = test_file.stat().st_size
                total_size_before += size
                print(f"   {filename}: {size} 字节")
        
        print(f"总大小: {total_size_before} 字节")
        
        # 模拟清空操作
        print("\n执行清空操作...")
        
        # 关闭日志处理器
        import logging
        loggers = [logging.getLogger(name) for name in logging.root.manager.loggerDict]
        loggers.append(logging.getLogger())
        
        for logger in loggers:
            for handler in logger.handlers[:]:
                if isinstance(handler, (logging.FileHandler, logging.handlers.RotatingFileHandler)):
                    try:
                        handler.close()
                        logger.removeHandler(handler)
                    except Exception:
                        pass
        
        # 等待文件句柄释放
        time.sleep(0.5)
        
        # 清空文件
        cleared_count = 0
        for filename in test_files:
            test_file = logs_dir / filename
            if test_file.exists():
                try:
                    with open(test_file, "r+", encoding="utf-8") as f:
                        f.truncate(0)
                    cleared_count += 1
                    print(f"   清空: {filename}")
                except Exception as e:
                    print(f"   失败: {filename} - {e}")
        
        # 检查清空后的大小
        print("\n清空后文件大小:")
        total_size_after = 0
        for filename in test_files:
            test_file = logs_dir / filename
            if test_file.exists():
                size = test_file.stat().st_size
                total_size_after += size
                print(f"   {filename}: {size} 字节")
        
        print(f"总大小: {total_size_after} 字节")
        print(f"清空文件数: {cleared_count}")
        print(f"节省空间: {total_size_before - total_size_after} 字节")
        
        # 清理测试文件
        print("\n清理测试文件...")
        for filename in test_files:
            test_file = logs_dir / filename
            if test_file.exists():
                test_file.unlink()
                print(f"   删除: {filename}")
        
        return total_size_after == 0
        
    except Exception as e:
        print(f"❌ 日志清空测试失败: {e}")
        return False

def test_admin_privileges():
    """测试管理员权限检查"""
    print("\n🔐 测试管理员权限检查...")
    
    try:
        from utils.simple_admin import is_admin, ensure_admin_privileges
        
        current_admin = is_admin()
        print(f"当前权限状态: {'管理员' if current_admin else '普通用户'}")
        
        if not current_admin:
            print("检测到普通用户权限，这是正常的测试环境")
            print("在实际注入时会弹出权限提升对话框")
        else:
            print("当前已具有管理员权限")
        
        return True
        
    except Exception as e:
        print(f"❌ 权限检查测试失败: {e}")
        return False

def test_injector_tool():
    """测试注入工具（不执行实际注入）"""
    print("\n💉 测试注入工具初始化...")
    
    try:
        from core.injector_tool import InjectorTool
        
        # 创建注入工具（不自动提升权限，避免弹窗）
        injector = InjectorTool(auto_elevate=False)
        
        print("✅ 注入工具创建成功")
        print(f"   DLL路径: {injector.dll_path}")
        print(f"   注入器路径: {injector.injector_path}")
        print(f"   自动提升权限: {injector.auto_elevate}")
        
        # 检查文件存在性
        dll_exists = injector.dll_path.exists()
        injector_exists = injector.injector_path.exists()
        
        print(f"   DLL文件存在: {'✅' if dll_exists else '❌'}")
        print(f"   注入器存在: {'✅' if injector_exists else '❌'}")
        
        # 检查权限
        has_admin = injector.check_admin_privileges()
        print(f"   管理员权限: {'✅' if has_admin else '❌'}")
        
        return dll_exists and injector_exists
        
    except Exception as e:
        print(f"❌ 注入工具测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 功能修复测试")
    print("=" * 60)
    
    tests = [
        ("日志清空功能", test_log_clearing),
        ("管理员权限检查", test_admin_privileges),
        ("注入工具初始化", test_injector_tool)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 修复测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有修复测试通过！")
        print("\n✨ 修复内容:")
        print("  🧹 日志清空功能 - 正确关闭文件句柄")
        print("  🔐 权限提升对话框 - 用户友好的权限请求")
        print("  💉 注入工具 - 完整的权限检查流程")
        
        print("\n📋 使用说明:")
        print("1. 清空日志文件时会先关闭所有日志处理器")
        print("2. 注入时会弹出权限确认对话框")
        print("3. 用户确认后会显示UAC权限提升对话框")
        print("4. 权限提升成功后程序会重新启动")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
