#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试定时任务修复
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_timing_task_creation():
    """测试定时任务创建"""
    print("🔧 测试定时任务创建...")
    
    try:
        from core.timing_sender import TimingTask
        
        # 创建测试任务
        task = TimingTask(
            task_id="test_task_001",
            group_id="test_group",
            group_name="测试分组",
            execute_date="2025-08-07",
            execute_time="14:00",
            message_content="测试消息",
            message_type="text"
        )
        
        print(f"✅ 任务创建成功: {task.task_id}")
        print(f"   分组: {task.group_name}")
        print(f"   执行时间: {task.execute_date} {task.execute_time}")
        
        # 测试 get_execute_datetime 方法
        execute_datetime = task.get_execute_datetime()
        print(f"   执行时间对象: {execute_datetime}")
        
        # 测试是否有 scheduled_time 属性（应该没有）
        if hasattr(task, 'scheduled_time'):
            print("❌ 任务仍有 scheduled_time 属性")
            return False
        else:
            print("✅ 任务没有 scheduled_time 属性（正确）")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_timing_sender_schedule():
    """测试定时发送器调度"""
    print("\n🔧 测试定时发送器调度...")
    
    try:
        from core.timing_sender import timing_sender, TimingTask
        
        # 创建未来的测试任务
        future_time = datetime.now() + timedelta(minutes=5)
        task = TimingTask(
            task_id="test_schedule_001",
            group_id="test_group",
            group_name="测试分组",
            execute_date=future_time.strftime("%Y-%m-%d"),
            execute_time=future_time.strftime("%H:%M"),
            message_content="测试调度消息",
            message_type="text"
        )
        
        # 添加任务到发送器
        timing_sender.tasks[task.task_id] = task
        
        print(f"✅ 任务添加成功: {task.task_id}")
        
        # 测试调度下次检查时间（这应该不会出错）
        try:
            timing_sender._schedule_next_check()
            print("✅ 调度下次检查时间成功")
        except Exception as e:
            print(f"❌ 调度下次检查时间失败: {e}")
            return False
        
        # 清理测试任务
        if task.task_id in timing_sender.tasks:
            del timing_sender.tasks[task.task_id]
        
        return True
        
    except Exception as e:
        print(f"❌ 调度测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_execution_check():
    """测试任务执行检查"""
    print("\n🔧 测试任务执行检查...")
    
    try:
        from core.timing_sender import TimingTask
        
        # 创建过去的任务（应该准备执行）
        past_time = datetime.now() - timedelta(minutes=1)
        past_task = TimingTask(
            task_id="test_past_001",
            group_id="test_group",
            group_name="测试分组",
            execute_date=past_time.strftime("%Y-%m-%d"),
            execute_time=past_time.strftime("%H:%M"),
            message_content="过去的测试消息",
            message_type="text"
        )
        
        # 创建未来的任务（不应该执行）
        future_time = datetime.now() + timedelta(minutes=5)
        future_task = TimingTask(
            task_id="test_future_001",
            group_id="test_group",
            group_name="测试分组",
            execute_date=future_time.strftime("%Y-%m-%d"),
            execute_time=future_time.strftime("%H:%M"),
            message_content="未来的测试消息",
            message_type="text"
        )
        
        # 测试执行时间检查
        if past_task.is_ready_to_execute():
            print("✅ 过去的任务准备执行（正确）")
        else:
            print("❌ 过去的任务不准备执行（错误）")
            return False
        
        if not future_task.is_ready_to_execute():
            print("✅ 未来的任务不准备执行（正确）")
        else:
            print("❌ 未来的任务准备执行（错误）")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 执行检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 定时任务修复测试")
    print("=" * 60)
    print("测试修复后的定时任务功能")
    print("=" * 60)
    
    tests = [
        ("定时任务创建测试", test_timing_task_creation),
        ("定时发送器调度测试", test_timing_sender_schedule),
        ("任务执行检查测试", test_task_execution_check)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 定时任务修复测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 定时任务修复成功！")
        print("\n✨ 修复内容:")
        print("  🔧 修复了 scheduled_time 属性错误")
        print("  📅 使用 get_execute_datetime() 方法")
        print("  ⏰ 调度下次检查时间正常")
        print("  ✅ 任务执行时间检查正常")
        
        print("\n📋 现在定时发送功能应该正常工作了！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
