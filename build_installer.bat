@echo off
chcp 65001 >nul
title Meet space 微信群发助手 - 自动化打包工具

echo.
echo ========================================
echo   Meet space 微信群发助手
echo   自动化打包工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

:: 检查是否在项目根目录
if not exist "main.py" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    echo    当前目录应包含 main.py 文件
    pause
    exit /b 1
)

echo ✅ 项目目录检查通过

:: 运行自动化构建脚本
echo.
echo 🚀 开始自动化构建...
echo.

python auto_build.py

:: 检查构建结果
if errorlevel 1 (
    echo.
    echo ❌ 构建失败！请检查上面的错误信息
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 构建成功！
    echo.
    
    :: 显示构建结果
    if exist "dist\MeetSpaceWeChatSender\MeetSpaceWeChatSender.exe" (
        echo 📁 可执行文件位置: dist\MeetSpaceWeChatSender\
        echo 📄 主程序: MeetSpaceWeChatSender.exe
    )
    
    if exist "installer\Output\*.exe" (
        echo 📦 安装包位置: installer\Output\
        for %%f in (installer\Output\*.exe) do (
            echo 📄 安装包: %%~nxf
        )
    )
    
    echo.
    echo 🎉 打包完成！
    echo.
    
    :: 询问是否打开文件夹
    set /p choice="是否打开输出文件夹？(y/n): "
    if /i "%choice%"=="y" (
        if exist "installer\Output" (
            explorer "installer\Output"
        ) else (
            explorer "dist"
        )
    )
)

pause
