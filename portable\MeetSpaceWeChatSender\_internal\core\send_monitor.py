"""
发送监控模块

监控和管理消息发送任务的状态和进度。
"""

import asyncio
import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from PyQt6.QtCore import QObject, pyqtSignal

from utils.logger import setup_logger

logger = setup_logger("send_monitor")


class SendStatus(Enum):
    """发送状态枚举"""

    PENDING = "pending"  # 等待发送
    SENDING = "sending"  # 正在发送
    SUCCESS = "success"  # 发送成功
    FAILED = "failed"  # 发送失败
    CANCELLED = "cancelled"  # 已取消


@dataclass
class SendTask:
    """发送任务"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    wxid: str = ""
    name: str = ""
    message_type: str = "text"  # text, image, file
    content: str = ""
    file_path: str = ""
    status: SendStatus = SendStatus.PENDING
    error_message: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    sent_at: Optional[datetime] = None
    retry_count: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "wxid": self.wxid,
            "name": self.name,
            "message_type": self.message_type,
            "content": self.content,
            "file_path": self.file_path,
            "status": self.status.value,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat(),
            "sent_at": self.sent_at.isoformat() if self.sent_at else None,
            "retry_count": self.retry_count,
        }


class SendMonitor(QObject):
    """发送监控器"""

    # 信号定义
    task_status_changed = pyqtSignal(str, str)  # 任务ID, 新状态
    progress_updated = pyqtSignal(int, int)  # 已完成数量, 总数量
    send_completed = pyqtSignal(dict)  # 发送完成统计

    def __init__(self):
        super().__init__()
        self.tasks: Dict[str, SendTask] = {}
        self.is_running = False
        self.is_paused = False
        self.current_task_id: Optional[str] = None

        # 统计信息
        self.total_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.cancelled_count = 0

        # 定时循环模式支持
        self.is_timed_mode = False
        self.daily_total_count = 0  # 当天需要发送的总数
        self.daily_completed_count = 0  # 当天已完成的数量

    def set_timed_mode(self, enabled: bool, daily_total: int = 0):
        """
        设置定时循环模式

        Args:
            enabled: 是否启用定时模式
            daily_total: 当天需要发送的总数（用于进度计算）
        """
        self.is_timed_mode = enabled
        if enabled:
            self.daily_total_count = daily_total
            self.daily_completed_count = 0
            logger.info(f"启用定时模式，当天总数: {daily_total}")
        else:
            logger.info("禁用定时模式")

    def reset_daily_progress(self):
        """重置当天进度（新的一天开始时调用）"""
        if self.is_timed_mode:
            self.daily_completed_count = 0
            logger.info("重置当天进度")

    def add_task(self, task: SendTask) -> str:
        """
        添加发送任务

        Args:
            task: 发送任务

        Returns:
            任务ID
        """
        self.tasks[task.id] = task
        self.total_count = len(self.tasks)
        logger.info(f"添加发送任务: {task.name} ({task.wxid})")
        return task.id

    def add_tasks(self, tasks: List[SendTask]) -> List[str]:
        """
        批量添加发送任务

        Args:
            tasks: 发送任务列表

        Returns:
            任务ID列表
        """
        task_ids = []
        for task in tasks:
            task_id = self.add_task(task)
            task_ids.append(task_id)
        return task_ids

    def get_task(self, task_id: str) -> Optional[SendTask]:
        """获取任务"""
        return self.tasks.get(task_id)

    def get_all_tasks(self) -> List[SendTask]:
        """获取所有任务"""
        return list(self.tasks.values())

    def get_tasks_by_status(self, status: SendStatus) -> List[SendTask]:
        """根据状态获取任务"""
        return [task for task in self.tasks.values() if task.status == status]

    def update_task_status(
        self, task_id: str, status: SendStatus, error_message: str = ""
    ) -> bool:
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            error_message: 错误消息

        Returns:
            是否更新成功
        """
        task = self.get_task(task_id)
        if not task:
            return False

        old_status = task.status
        task.status = status
        task.error_message = error_message

        if status == SendStatus.SUCCESS:
            task.sent_at = datetime.now()
            self.success_count += 1
        elif status == SendStatus.FAILED:
            self.failed_count += 1
        elif status == SendStatus.CANCELLED:
            self.cancelled_count += 1

        # 发送状态变更信号
        self.task_status_changed.emit(task_id, status.value)

        # 更新进度
        completed_count = self.success_count + self.failed_count + self.cancelled_count

        if self.is_timed_mode:
            # 定时模式：更新当天完成数量
            if status in [SendStatus.SUCCESS, SendStatus.FAILED]:
                self.daily_completed_count += 1

            # 发送当天进度
            self.progress_updated.emit(
                self.daily_completed_count, self.daily_total_count
            )
            logger.debug(
                f"定时模式进度: {self.daily_completed_count}/{self.daily_total_count}"
            )
        else:
            # 普通模式：按总任务数计算
            self.progress_updated.emit(completed_count, self.total_count)
            logger.debug(f"普通模式进度: {completed_count}/{self.total_count}")

        logger.info(f"任务状态更新: {task.name} {old_status.value} -> {status.value}")

        # 检查是否全部完成
        if completed_count >= self.total_count:
            self.on_all_tasks_completed()

        return True

    def clear_tasks(self) -> None:
        """清空所有任务"""
        self.tasks.clear()
        self.reset_statistics()
        logger.info("清空所有发送任务")

    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.total_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.cancelled_count = 0
        self.is_running = False
        self.is_paused = False
        self.current_task_id = None

    def start_monitoring(self) -> None:
        """开始监控"""
        self.is_running = True
        self.is_paused = False
        logger.info("开始发送监控")

    def pause_monitoring(self) -> None:
        """暂停监控"""
        self.is_paused = True
        logger.info("暂停发送监控")

    def resume_monitoring(self) -> None:
        """恢复监控"""
        self.is_paused = False
        logger.info("恢复发送监控")

    def stop_monitoring(self) -> None:
        """停止监控"""
        self.is_running = False
        self.is_paused = False

        # 取消所有待发送的任务
        pending_tasks = self.get_tasks_by_status(SendStatus.PENDING)
        for task in pending_tasks:
            self.update_task_status(task.id, SendStatus.CANCELLED)

        logger.info("停止发送监控")

    def on_all_tasks_completed(self) -> None:
        """所有任务完成回调"""
        self.is_running = False

        # 生成统计报告
        statistics = {
            "total": self.total_count,
            "success": self.success_count,
            "failed": self.failed_count,
            "cancelled": self.cancelled_count,
            "success_rate": (
                self.success_count / self.total_count * 100
                if self.total_count > 0
                else 0
            ),
        }

        self.send_completed.emit(statistics)
        logger.info(f"所有发送任务完成: {statistics}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        completed_count = self.success_count + self.failed_count + self.cancelled_count
        return {
            "total": self.total_count,
            "completed": completed_count,
            "success": self.success_count,
            "failed": self.failed_count,
            "cancelled": self.cancelled_count,
            "pending": self.total_count - completed_count,
            "success_rate": (
                self.success_count / self.total_count * 100
                if self.total_count > 0
                else 0
            ),
            "is_running": self.is_running,
            "is_paused": self.is_paused,
        }
