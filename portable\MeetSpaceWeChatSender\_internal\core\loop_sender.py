"""
循环发送管理模块

管理循环发送任务的创建、执行和监控。
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from PyQt6.QtCore import QObject, QTimer, QThread, pyqtSignal

from core.group_manager import ContactGroup, group_manager
from core.message_sender_core import (
    message_sender_core,
    MessageType,
    SendTask,
    SendMember,
    SendProgress,
    SendResult,
)
from utils.logger import setup_logger

logger = setup_logger("loop_sender")


class LoopTask:
    """循环发送任务类"""

    def __init__(
        self,
        task_id: str,
        group_id: str,
        group_name: str,
        interval_minutes: int,
        message_content: str,
        message_type: str = "text",
    ):
        self.task_id = task_id
        self.group_id = group_id
        self.group_name = group_name
        self.interval_minutes = interval_minutes
        self.message_content = message_content
        self.message_type = message_type  # text, rich_text
        self.status = "stopped"  # stopped, running, paused
        self.created_time = datetime.now()
        self.started_time = None
        self.last_send_time = None
        self.next_send_time = None
        self.total_cycles = 0  # 总周期数
        self.total_sent_count = 0
        self.current_cycle_sent = 0
        self.current_cycle_total = 0
        self.error_count = 0
        self.last_error = ""

        # 新增：已发送联系人跟踪和内容更新支持
        self.sent_members = set()  # 当前周期已发送的联系人wxid集合
        self.failed_members = set()  # 当前周期发送失败的联系人wxid集合
        self.original_content = message_content  # 原始内容（用于检测内容是否更改）
        self.content_updated = False  # 内容是否已更新

    def start(self):
        """启动循环任务"""
        self.status = "running"
        self.started_time = datetime.now()
        self.next_send_time = datetime.now()
        logger.info(f"启动循环任务: {self.task_id}")

    def pause(self):
        """暂停循环任务"""
        self.status = "paused"
        logger.info(f"暂停循环任务: {self.task_id}")

    def stop(self):
        """停止循环任务"""
        self.status = "stopped"
        self.next_send_time = None
        # 清空当前周期的发送记录
        self.sent_members.clear()
        self.failed_members.clear()
        logger.info(f"停止循环任务: {self.task_id}")

    def resume(self):
        """恢复循环任务"""
        if self.status == "paused":
            self.status = "running"
            # 重新计算下次发送时间
            if self.last_send_time:
                self.next_send_time = self.last_send_time + timedelta(
                    minutes=self.interval_minutes
                )
            else:
                self.next_send_time = datetime.now()
            logger.info(f"恢复循环任务: {self.task_id}")

    def update_content(self, new_content: str, new_type: str = None):
        """更新任务内容（暂停后修改内容时调用）"""
        if new_content != self.original_content:
            self.message_content = new_content
            self.content_updated = True
            logger.info(f"循环任务 {self.task_id} 内容已更新")

        if new_type and new_type != self.message_type:
            self.message_type = new_type
            self.content_updated = True
            logger.info(f"循环任务 {self.task_id} 消息类型已更新为 {new_type}")

    def add_sent_member(self, wxid: str):
        """添加已发送的联系人（当前周期）"""
        self.sent_members.add(wxid)

    def add_failed_member(self, wxid: str):
        """添加发送失败的联系人（当前周期）"""
        self.failed_members.add(wxid)

    def is_member_sent(self, wxid: str) -> bool:
        """检查联系人是否已发送（当前周期）"""
        return wxid in self.sent_members

    def get_unsent_members(self, all_members: list) -> list:
        """获取未发送的联系人列表（当前周期）"""
        return [member for member in all_members if member.wxid not in self.sent_members]

    def get_failed_count(self) -> int:
        """获取发送失败的数量（当前周期）"""
        return len(self.failed_members)

    def start_new_cycle(self):
        """开始新的发送周期"""
        self.sent_members.clear()
        self.failed_members.clear()
        self.total_cycles += 1
        logger.info(f"循环任务 {self.task_id} 开始第 {self.total_cycles} 个周期")

    def is_ready_to_send(self) -> bool:
        """检查是否到了发送时间"""
        if self.status != "running":
            return False

        if not self.next_send_time:
            return True  # 首次发送

        return datetime.now() >= self.next_send_time

    def update_after_send(
        self, sent_count: int, total_count: int, success: bool = True
    ):
        """发送后更新状态"""
        self.last_send_time = datetime.now()
        self.next_send_time = self.last_send_time + timedelta(
            minutes=self.interval_minutes
        )
        self.current_cycle_sent = sent_count
        self.current_cycle_total = total_count

        if success:
            self.total_sent_count += sent_count
        else:
            self.error_count += 1

        # 更新分组进度
        group_manager.update_group_progress(self.group_id, sent_count, total_count)

    def record_error(self, error_message: str):
        """记录错误"""
        self.error_count += 1
        self.last_error = error_message
        logger.error(f"循环任务错误: {self.task_id}, {error_message}")

    def get_next_send_time_str(self) -> str:
        """获取下次发送时间字符串"""
        if not self.next_send_time:
            return "未设置"

        if self.status != "running":
            return "已停止"

        return self.next_send_time.strftime("%H:%M:%S")

    def get_status_text(self) -> str:
        """获取状态文本"""
        status_map = {"running": "运行中", "paused": "已暂停", "stopped": "已停止"}
        return status_map.get(self.status, "未知")

    def get_progress_percent(self) -> float:
        """获取当前周期进度百分比"""
        if self.current_cycle_total == 0:
            return 0.0
        return (self.current_cycle_sent / self.current_cycle_total) * 100

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "group_id": self.group_id,
            "group_name": self.group_name,
            "interval_minutes": self.interval_minutes,
            "message_content": self.message_content,
            "message_type": self.message_type,
            # 移除时间规则字段
            "status": self.status,
            "created_time": self.created_time.isoformat(),
            "started_time": (
                self.started_time.isoformat() if self.started_time else None
            ),
            "last_send_time": (
                self.last_send_time.isoformat() if self.last_send_time else None
            ),
            "next_send_time": (
                self.next_send_time.isoformat() if self.next_send_time else None
            ),
            "total_cycles": self.total_cycles,
            "total_sent_count": self.total_sent_count,
            "current_cycle_sent": self.current_cycle_sent,
            "current_cycle_total": self.current_cycle_total,
            "error_count": self.error_count,
            "last_error": self.last_error,
            # 新增字段
            "sent_members": list(self.sent_members),
            "failed_members": list(self.failed_members),
            "original_content": self.original_content,
            "content_updated": self.content_updated,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "LoopTask":
        """从字典创建任务"""
        task = cls(
            data["task_id"],
            data["group_id"],
            data["group_name"],
            data["interval_minutes"],
            data["message_content"],
            data.get("message_type", "text"),  # 兼容旧数据，默认为text
        )
        task.status = data.get("status", "stopped")
        task.created_time = datetime.fromisoformat(data["created_time"])
        if data.get("started_time"):
            task.started_time = datetime.fromisoformat(data["started_time"])
        if data.get("last_send_time"):
            task.last_send_time = datetime.fromisoformat(data["last_send_time"])
        if data.get("next_send_time"):
            task.next_send_time = datetime.fromisoformat(data["next_send_time"])
        task.total_cycles = data.get("total_cycles", 0)
        task.total_sent_count = data.get("total_sent_count", 0)
        task.current_cycle_sent = data.get("current_cycle_sent", 0)
        task.current_cycle_total = data.get("current_cycle_total", 0)
        task.error_count = data.get("error_count", 0)
        task.last_error = data.get("last_error", "")

        # 新增字段（向后兼容）
        task.sent_members = set(data.get("sent_members", []))
        task.failed_members = set(data.get("failed_members", []))
        task.original_content = data.get("original_content", data["message_content"])
        task.content_updated = data.get("content_updated", False)

        return task


class LoopSender(QObject):
    """循环发送管理器"""

    # 信号定义
    task_started = pyqtSignal(str)  # 任务开始
    task_progress = pyqtSignal(str, int, int)  # 任务进度 (task_id, sent, total)
    task_cycle_completed = pyqtSignal(str)  # 周期完成
    task_paused = pyqtSignal(str)  # 任务暂停
    task_resumed = pyqtSignal(str)  # 任务恢复
    task_stopped = pyqtSignal(str)  # 任务停止
    task_error = pyqtSignal(str, str)  # 任务错误 (task_id, error)

    def __init__(self):
        super().__init__()
        self.data_dir = Path("data/loop")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        self.tasks_file = self.data_dir / "loop_tasks.json"
        self.tasks: Dict[str, LoopTask] = {}

        # 定时器初始化为None，稍后在主线程中创建
        self.check_timer = None

        self.load_tasks()
        logger.info("循环发送管理器初始化完成")

    def init_timer(self):
        """在主线程中初始化定时器"""
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app and QThread.currentThread() != app.thread():
            logger.warning("定时器应该在主线程中初始化，当前在子线程中")
            # 使用QMetaObject.invokeMethod确保在主线程中执行
            from PyQt6.QtCore import QMetaObject, Qt
            QMetaObject.invokeMethod(self, "_init_timer_impl", Qt.ConnectionType.QueuedConnection)
            return

        self._init_timer_impl()

    def _init_timer_impl(self):
        """实际的定时器初始化实现"""
        if self.check_timer is None:
            # 定时器，每30秒检查一次循环任务
            self.check_timer = QTimer()
            self.check_timer.timeout.connect(self.check_running_tasks)
            self.check_timer.start(30000)  # 30秒
            logger.info("循环任务检查器已启动，每30秒检查一次")
        else:
            logger.debug("定时器已经初始化")

    def generate_task_id(self) -> str:
        """生成任务ID"""
        import random
        timestamp = int(time.time())
        random_suffix = random.randint(1000, 9999)
        task_id = f"loop_{timestamp}_{random_suffix}"

        # 确保ID唯一
        while task_id in self.tasks:
            random_suffix = random.randint(1000, 9999)
            task_id = f"loop_{timestamp}_{random_suffix}"

        return task_id

    def create_task(
        self,
        group_id: str,
        interval_minutes: int,
        message_content: str,
        message_type: str = "text",
    ) -> Optional[LoopTask]:
        """创建循环发送任务"""
        # 获取分组信息
        group = group_manager.get_group(group_id, "loop")
        if not group:
            logger.error(f"分组不存在: {group_id}")
            return None

        # 验证间隔时间
        if interval_minutes <= 0:
            logger.error(f"间隔时间无效: {interval_minutes}")
            return None

        # 创建任务
        task_id = self.generate_task_id()
        task = LoopTask(
            task_id,
            group_id,
            group.name,
            interval_minutes,
            message_content,
            message_type,
        )

        # 设置总数
        task.current_cycle_total = group.get_member_count()

        self.tasks[task_id] = task
        self.save_tasks()

        logger.info(
            f"创建循环任务: {task_id}, 分组: {group.name}, 间隔: {interval_minutes}分钟"
        )
        return task

    def start_task(self, task_id: str) -> bool:
        """启动循环任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.start()
            self.save_tasks()
            self.task_started.emit(task_id)
            return True
        return False

    def pause_task(self, task_id: str) -> bool:
        """暂停循环任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.pause()
            self.save_tasks()
            self.task_paused.emit(task_id)
            return True
        return False

    def stop_task(self, task_id: str) -> bool:
        """停止循环任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.stop()
            self.save_tasks()
            self.task_stopped.emit(task_id)
            return True
        return False

    def resume_task(self, task_id: str, new_content: str = None, new_type: str = None) -> bool:
        """恢复循环任务，支持更新内容"""
        if task_id in self.tasks:
            task = self.tasks[task_id]

            # 如果提供了新内容，更新任务内容
            if new_content is not None:
                task.update_content(new_content, new_type)

            task.resume()
            self.save_tasks()
            self.task_resumed.emit(task_id)
            return True
        return False

    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        if task_id in self.tasks:
            # 先停止任务
            self.stop_task(task_id)
            del self.tasks[task_id]
            self.save_tasks()
            logger.info(f"删除循环任务: {task_id}")
            return True
        return False

    def get_task(self, task_id: str) -> Optional[LoopTask]:
        """获取任务"""
        return self.tasks.get(task_id)

    def get_all_tasks(self) -> List[LoopTask]:
        """获取所有任务"""
        return list(self.tasks.values())

    def get_running_tasks(self) -> List[LoopTask]:
        """获取运行中的任务"""
        return [task for task in self.tasks.values() if task.status == "running"]

    def get_tasks_by_group(self, group_id: str) -> List[LoopTask]:
        """获取指定分组的任务"""
        return [task for task in self.tasks.values() if task.group_id == group_id]

    def check_running_tasks(self):
        """检查运行中的任务"""
        running_tasks = self.get_running_tasks()
        ready_tasks = [task for task in running_tasks if task.is_ready_to_send()]

        for task in ready_tasks:
            self.execute_task_cycle(task)

    def execute_task_cycle(self, task: LoopTask):
        """执行循环任务的一个周期（完整逻辑确认版）"""
        logger.info(f"执行循环任务周期: {task.task_id}")

        try:
            # 1. 确认系统设置逻辑
            if not self._check_system_settings():
                task.record_error("系统设置检查失败")
                self.task_error.emit(task.task_id, "系统设置检查失败")
                self.save_tasks()
                return

            # 2. 获取分组
            group = group_manager.get_group(task.group_id, "loop")
            if not group:
                task.record_error("分组不存在")
                self.task_error.emit(task.task_id, "分组不存在")
                self.save_tasks()
                return

            # 3. 确认循环发送设置
            if not self._check_loop_settings(task):
                task.record_error("循环发送设置检查失败")
                self.task_error.emit(task.task_id, "循环发送设置检查失败")
                self.save_tasks()
                return

            # 4. 更新分组使用统计
            group.update_usage()
            group_manager.save_groups("loop")

            # 5. 执行实际发送逻辑
            self._execute_real_send_cycle(task, group)

        except Exception as e:
            logger.error(f"执行循环任务周期失败: {e}")
            task.record_error(str(e))
            self.task_error.emit(task.task_id, str(e))
            self.save_tasks()

    def _check_system_settings(self) -> bool:
        """确认系统设置逻辑"""
        try:
            # 检查连接器是否可用
            if not hasattr(self, "connector") or not self.connector:
                logger.error("连接器未初始化")
                return False

            # 检查连接状态
            if not self.connector.is_connected:
                logger.error("微信未连接")
                return False

            # 检查登录状态
            if not self.connector.is_logged_in:
                logger.error("微信未登录")
                return False

            logger.debug("循环发送系统设置检查通过")
            return True

        except Exception as e:
            logger.error(f"系统设置检查失败: {e}")
            return False

    def _check_loop_settings(self, task: LoopTask) -> bool:
        """确认循环发送设置"""
        try:
            # 检查任务基本信息
            if not task.message_content.strip():
                logger.error("消息内容为空")
                return False

            # 检查循环间隔是否合理
            if task.interval_minutes < 1:
                logger.error(f"循环间隔过短: {task.interval_minutes}分钟")
                return False

            if task.interval_minutes > 1440:  # 24小时
                logger.warning(f"循环间隔较长: {task.interval_minutes}分钟")

            # 检查任务状态
            if task.status != "running":
                logger.error(f"任务状态异常: {task.status}")
                return False

            # 检查是否到了发送时间
            if not task.is_ready_to_send():
                logger.debug("尚未到发送时间")
                return False

            logger.debug("循环发送设置检查通过")
            return True

        except Exception as e:
            logger.error(f"循环发送设置检查失败: {e}")
            return False

    def _execute_real_send_cycle(self, task: LoopTask, group: ContactGroup):
        """执行真实的发送周期（使用统一的消息发送核心）"""
        import threading
        import asyncio

        def send_worker():
            try:
                logger.info(f"开始循环发送周期: {task.task_id} 到分组: {group.name}")

                # 获取分组成员
                members = group.members
                if not members:
                    task.record_error("分组成员为空")
                    self.task_error.emit(task.task_id, "分组成员为空")
                    self.save_tasks()
                    return

                # 转换为SendMember格式
                send_members = [SendMember(wxid=m.wxid, name=m.name) for m in members]

                # 创建发送任务
                send_task = SendTask(
                    task_id=task.task_id,
                    message_content=task.message_content,
                    message_type=MessageType(task.message_type),
                    members=send_members,
                    send_interval=1.0,  # 1秒间隔
                    enable_risk_control=True,
                )

                # 设置连接器
                message_sender_core.set_connector(self.connector)

                # 清除之前的回调
                message_sender_core._progress_callbacks.clear()
                message_sender_core._member_callbacks.clear()

                # 添加进度回调
                def progress_callback(progress: SendProgress):
                    task.current_cycle_sent = progress.success_count
                    task.current_cycle_total = progress.total
                    self.task_progress.emit(
                        task.task_id, progress.current, progress.total
                    )

                def member_callback(
                    task_id: str, member: SendMember, result: SendResult, error_msg: str
                ):
                    if result == SendResult.SUCCESS:
                        logger.debug(f"循环发送成功: {member.name}")
                    elif result == SendResult.FAILED:
                        logger.warning(f"循环发送失败: {member.name} - {error_msg}")
                    elif result == SendResult.SKIPPED:
                        logger.info(f"循环发送跳过: {member.name} - {error_msg}")
                    else:
                        logger.error(f"循环发送错误: {member.name} - {error_msg}")

                message_sender_core.add_progress_callback(progress_callback)
                message_sender_core.add_member_callback(member_callback)

                # 执行发送
                final_progress = asyncio.run(message_sender_core.send_task(send_task))

                # 周期完成
                if task.status == "running":
                    task.update_after_send(
                        final_progress.success_count,
                        final_progress.total,
                        final_progress.success_count > 0,
                    )
                    self.task_cycle_completed.emit(task.task_id)
                    logger.info(f"循环发送周期完成: {task.task_id}")
                    logger.info(
                        f"结果统计: 成功={final_progress.success_count}, 失败={final_progress.failed_count}, 跳过={final_progress.skipped_count}"
                    )

                    # 设置下次发送时间
                    task.next_send_time = datetime.now() + timedelta(
                        minutes=task.interval_minutes
                    )

                self.save_tasks()

            except Exception as e:
                logger.error(f"循环发送周期执行失败: {e}")
                task.record_error(str(e))
                self.task_error.emit(task.task_id, str(e))
                self.save_tasks()

        # 在后台线程执行发送
        thread = threading.Thread(target=send_worker, name=f"LoopSend-{task.task_id}")
        thread.daemon = True
        thread.start()

    def set_connector(self, connector):
        """设置连接器"""
        self.connector = connector
        logger.info("循环发送器已设置连接器")

    def simulate_send_cycle(self, task: LoopTask, group: ContactGroup):
        """模拟发送周期（实际实现时需要替换为真实发送逻辑）"""
        import threading

        def send_worker():
            try:
                total_members = len(group.members)
                task.current_cycle_total = total_members

                for i, member in enumerate(group.members):
                    # 检查任务是否仍在运行
                    if task.status != "running":
                        break

                    # 模拟发送延迟
                    time.sleep(0.1)

                    # 更新进度
                    sent_count = i + 1
                    task.current_cycle_sent = sent_count
                    self.task_progress.emit(task.task_id, sent_count, total_members)

                    logger.debug(
                        f"模拟循环发送到 {member.name} ({sent_count}/{total_members})"
                    )

                # 周期完成
                if task.status == "running":
                    task.update_after_send(total_members, total_members, True)
                    self.task_cycle_completed.emit(task.task_id)
                    self.save_tasks()

            except Exception as e:
                task.record_error(str(e))
                self.task_error.emit(task.task_id, str(e))
                self.save_tasks()

        # 在后台线程执行发送
        thread = threading.Thread(target=send_worker)
        thread.daemon = True
        thread.start()

    def get_task_statistics(self) -> dict:
        """获取任务统计信息"""
        total = len(self.tasks)
        running = len([t for t in self.tasks.values() if t.status == "running"])
        paused = len([t for t in self.tasks.values() if t.status == "paused"])
        stopped = len([t for t in self.tasks.values() if t.status == "stopped"])

        return {
            "total": total,
            "running": running,
            "paused": paused,
            "stopped": stopped,
        }

    def stop_all_tasks(self):
        """停止所有运行中的任务"""
        running_tasks = self.get_running_tasks()
        for task in running_tasks:
            task.stop()

        if running_tasks:
            self.save_tasks()
            logger.info(f"停止了 {len(running_tasks)} 个运行中的循环任务")

    def save_tasks(self):
        """保存任务数据"""
        try:
            data = {task_id: task.to_dict() for task_id, task in self.tasks.items()}
            with open(self.tasks_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.debug("保存循环任务数据成功")
        except Exception as e:
            logger.error(f"保存循环任务数据失败: {e}")

    def load_tasks(self):
        """加载任务数据"""
        try:
            if self.tasks_file.exists():
                with open(self.tasks_file, "r", encoding="utf-8") as f:
                    data = json.load(f)

                self.tasks.clear()
                for task_id, task_data in data.items():
                    task = LoopTask.from_dict(task_data)
                    self.tasks[task_id] = task

                logger.info(f"加载循环任务数据成功，共{len(self.tasks)}个任务")
        except Exception as e:
            logger.error(f"加载循环任务数据失败: {e}")


# 全局循环发送管理器实例
loop_sender = LoopSender()
