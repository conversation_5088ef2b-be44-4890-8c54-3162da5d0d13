#!/usr/bin/env python3
"""
输入控件尺寸测试工具
测试SpinBox和ComboBox的尺寸修复效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QLabel, QGroupBox, QSpinBox, QComboBox, QPushButton)
from PyQt6.QtCore import Qt
from ui.modern_theme_manager import theme_manager
from ui.themed_dialog_base import ThemedDialogBase


class InputWidgetTestDialog(ThemedDialogBase):
    """输入控件测试对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("输入控件尺寸测试")
        self.setFixedSize(600, 400)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("输入控件尺寸测试 - 验证SpinBox和ComboBox宽度")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # SpinBox测试组
        spinbox_group = QGroupBox("数字输入框 (SpinBox) 测试")
        spinbox_layout = QVBoxLayout(spinbox_group)
        
        # 各种SpinBox测试
        spinbox_tests = [
            ("重试次数:", 0, 10, 3),
            ("延迟时间(秒):", 1, 60, 5),
            ("批量大小:", 1, 100, 10),
            ("最大重试:", 0, 5, 2),
        ]
        
        self.spinboxes = []
        for label_text, min_val, max_val, default_val in spinbox_tests:
            row_layout = QHBoxLayout()
            row_layout.addWidget(QLabel(label_text))
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            self.spinboxes.append(spinbox)
            
            row_layout.addWidget(spinbox)
            row_layout.addStretch()
            spinbox_layout.addLayout(row_layout)
        
        layout.addWidget(spinbox_group)
        
        # ComboBox测试组
        combobox_group = QGroupBox("下拉框 (ComboBox) 测试")
        combobox_layout = QVBoxLayout(combobox_group)
        
        # 各种ComboBox测试
        combobox_tests = [
            ("主题选择:", ["默认主题", "浅色主题", "深色主题", "护眼主题", "科技主题"]),
            ("字体大小:", ["小", "中", "大", "特大"]),
            ("发送模式:", ["立即发送", "定时发送", "循环发送"]),
            ("消息类型:", ["文本消息", "图片消息", "文件消息", "混合消息"]),
        ]
        
        self.comboboxes = []
        for label_text, items in combobox_tests:
            row_layout = QHBoxLayout()
            row_layout.addWidget(QLabel(label_text))
            
            combobox = QComboBox()
            combobox.addItems(items)
            self.comboboxes.append(combobox)
            
            row_layout.addWidget(combobox)
            row_layout.addStretch()
            combobox_layout.addLayout(row_layout)
        
        layout.addWidget(combobox_group)
        
        # 尺寸信息显示
        info_group = QGroupBox("尺寸信息")
        info_layout = QVBoxLayout(info_group)
        
        self.size_info_label = QLabel("点击'检查尺寸'按钮查看控件尺寸信息")
        info_layout.addWidget(self.size_info_label)
        
        layout.addWidget(info_group)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        check_size_btn = QPushButton("检查尺寸")
        check_size_btn.clicked.connect(self.check_sizes)
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(check_size_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def check_sizes(self):
        """检查控件尺寸"""
        size_info = []
        
        # 检查SpinBox尺寸
        size_info.append("📊 SpinBox尺寸信息:")
        for i, spinbox in enumerate(self.spinboxes):
            width = spinbox.size().width()
            height = spinbox.size().height()
            size_info.append(f"  SpinBox {i+1}: {width} x {height} px")
        
        size_info.append("")
        
        # 检查ComboBox尺寸
        size_info.append("📊 ComboBox尺寸信息:")
        for i, combobox in enumerate(self.comboboxes):
            width = combobox.size().width()
            height = combobox.size().height()
            size_info.append(f"  ComboBox {i+1}: {width} x {height} px")
        
        size_info.append("")
        
        # 尺寸评估
        spinbox_widths = [sb.size().width() for sb in self.spinboxes]
        combobox_widths = [cb.size().width() for cb in self.comboboxes]
        
        size_info.append("📈 尺寸评估:")
        
        # SpinBox评估
        min_spinbox_width = min(spinbox_widths)
        if min_spinbox_width >= 80:
            size_info.append(f"  ✅ SpinBox最小宽度: {min_spinbox_width}px (≥80px)")
        else:
            size_info.append(f"  ❌ SpinBox最小宽度: {min_spinbox_width}px (<80px)")
        
        # ComboBox评估
        min_combobox_width = min(combobox_widths)
        if min_combobox_width >= 120:
            size_info.append(f"  ✅ ComboBox最小宽度: {min_combobox_width}px (≥120px)")
        else:
            size_info.append(f"  ❌ ComboBox最小宽度: {min_combobox_width}px (<120px)")
        
        # 更新显示
        self.size_info_label.setText("\n".join(size_info))
        
        # 同时在控制台输出
        print("\n" + "="*50)
        for line in size_info:
            print(line)
        print("="*50)


def test_input_widget_sizes():
    """测试输入控件尺寸"""
    app = QApplication(sys.argv)
    
    print("🔧 输入控件尺寸测试工具")
    print("=" * 50)
    
    themes_to_test = ["默认主题", "科技主题", "浅色主题", "护眼主题"]
    
    for theme_name in themes_to_test:
        print(f"\n🎨 测试主题: {theme_name}")
        
        # 应用主题
        theme_manager.set_theme(app, theme_name)
        print(f"✅ 已应用{theme_name}")
        
        # 创建测试对话框
        dialog = InputWidgetTestDialog()
        dialog.setWindowTitle(f"{theme_name} - 输入控件尺寸测试")
        dialog.show()
        
        print(f"\n📋 {theme_name}测试:")
        print("1. 观察SpinBox和ComboBox的宽度是否合适")
        print("2. 点击'检查尺寸'按钮查看具体数值")
        print("3. 确认所有控件都能正常交互")
        print("4. 关闭对话框继续测试下一个主题")
        
        # 运行对话框
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            print(f"✅ {theme_name}测试完成")
        else:
            print(f"⏭️ {theme_name}测试跳过")
    
    print("\n🎉 所有主题的输入控件尺寸测试完成！")
    print("\n📊 修复总结:")
    print("1. ✅ SpinBox最小宽度设置为80px")
    print("2. ✅ ComboBox最小宽度设置为120px") 
    print("3. ✅ 所有主题保持一致的控件尺寸")
    print("4. ✅ 添加了适当的内边距(padding)")
    
    sys.exit(0)


def main():
    """主函数"""
    print("🚀 输入控件尺寸修复工具")
    print("=" * 50)
    print("\n📋 修复内容:")
    print("1. SpinBox添加最小宽度80px和内边距")
    print("2. ComboBox添加最小宽度120px和内边距")
    print("3. 所有主题保持一致的控件尺寸")
    
    print("\n✅ 修复完成！现在可以运行测试:")
    print("python test_input_widget_sizes.py --test")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_input_widget_sizes()


if __name__ == "__main__":
    main()
