#!/usr/bin/env python3
"""
简单的日志清空修复检查工具
"""

import sys
from pathlib import Path

def check_log_clear_fix():
    """检查日志清空修复情况"""
    print("🔍 检查日志清空重复执行修复...")
    
    main_window_file = Path(__file__).parent / "ui" / "main_window.py"
    
    if not main_window_file.exists():
        print("❌ main_window.py文件不存在")
        return False
    
    try:
        content = main_window_file.read_text(encoding='utf-8')
        
        # 检查重复连接的情况
        connect_lines = []
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'clear_log_files_btn.clicked.connect(self.clear_log_files)' in line:
                connect_lines.append(f"第{i}行: {line.strip()}")
        
        print(f"\n📊 检查结果:")
        print(f"发现 {len(connect_lines)} 处事件连接:")
        for line in connect_lines:
            print(f"  - {line}")
        
        # 检查是否有注释说明
        comment_found = False
        for i, line in enumerate(lines, 1):
            if '注意：事件连接在connect_signals方法中统一处理' in line:
                comment_found = True
                print(f"  - 第{i}行: 找到修复注释")
                break
        
        if len(connect_lines) == 1:
            print("\n✅ 修复成功！只有一处事件连接")
            if comment_found:
                print("✅ 修复注释已添加")
            print("\n📋 修复内容:")
            print("  - 移除了按钮创建时的重复事件连接")
            print("  - 保留了connect_signals方法中的统一事件连接")
            print("  - 现在点击按钮只会执行一次清空操作")
            return True
        elif len(connect_lines) > 1:
            print(f"\n❌ 仍存在重复连接！发现 {len(connect_lines)} 处连接")
            print("需要进一步修复")
            return False
        else:
            print("\n❌ 未找到事件连接，可能存在其他问题")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 日志清空重复执行修复检查")
    print("=" * 50)
    
    success = check_log_clear_fix()
    
    if success:
        print("\n🎉 修复验证通过！")
        print("\n📋 测试建议:")
        print("1. 启动程序: python main.py")
        print("2. 进入日志页面")
        print("3. 点击'清空日志文件'按钮")
        print("4. 观察是否只执行一次清空操作")
        print("5. 检查日志中是否还有重复的清空记录")
        
        print("\n🎯 预期效果:")
        print("  - 点击一次按钮只执行一次清空操作")
        print("  - 不再有重复的日志记录")
        print("  - 清空操作更加可靠")
        
        return 0
    else:
        print("\n❌ 修复验证失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
