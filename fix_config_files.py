#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件修复工具
修复空的或损坏的配置文件
"""

import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def get_config_directory():
    """获取配置目录"""
    app_data = Path(os.environ.get('APPDATA', ''))
    config_dir = app_data / "MeetSpaceWeChatSender" / "config"
    return config_dir

def backup_file(file_path: Path):
    """备份文件"""
    if not file_path.exists():
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{file_path.stem}_backup_{timestamp}{file_path.suffix}"
    backup_path = file_path.parent / backup_name
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✅ 已备份: {file_path.name} -> {backup_name}")
        return backup_path
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None

def check_json_file(file_path: Path):
    """检查JSON文件是否有效"""
    if not file_path.exists():
        return False, "文件不存在"
    
    if file_path.stat().st_size == 0:
        return False, "文件为空"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                return False, "文件内容为空"
            
            json.loads(content)
            return True, "JSON格式正确"
    except json.JSONDecodeError as e:
        return False, f"JSON格式错误: {e}"
    except Exception as e:
        return False, f"读取文件失败: {e}"

def create_default_system_config():
    """创建默认系统配置"""
    return {
        "theme": "modern_blue",
        "auto_save": True,
        "check_updates": True,
        "minimize_to_tray": False,
        "startup_check_connection": True,
        "log_level": "INFO",
        "max_log_files": 10,
        "log_file_size_mb": 10,
        "backup_enabled": True,
        "backup_interval_days": 7,
        "max_backup_files": 5,
        "export_format": "json",
        "language": "zh_CN",
        "font_size": 12,
        "window_width": 1200,
        "window_height": 800,
        "remember_window_position": True,
        "show_tooltips": True,
        "confirm_before_send": True,
        "sound_enabled": True,
        "animation_enabled": True
    }

def create_default_send_settings():
    """创建默认发送设置"""
    return {
        "send_interval_min": 1,
        "send_interval_max": 3,
        "max_retries": 3,
        "retry_delay": 5,
        "batch_size": 10,
        "batch_delay": 2,
        "enable_risk_control": True,
        "daily_limit": 100,
        "hourly_limit": 20,
        "enable_smart_delay": True,
        "smart_delay_factor": 1.5,
        "weekend_mode": False,
        "night_mode_start": "22:00",
        "night_mode_end": "08:00",
        "enable_night_mode": False,
        "priority_contacts": [],
        "blocked_contacts": [],
        "enable_message_preview": True,
        "auto_save_drafts": True,
        "draft_save_interval": 30,
        "enable_send_confirmation": True,
        "enable_delivery_report": False,
        "message_timeout": 30,
        "connection_timeout": 10,
        "max_message_length": 1000,
        "enable_emoji_support": True,
        "enable_image_compression": True,
        "image_quality": 80,
        "max_image_size_mb": 5
    }

def fix_config_file(file_path: Path, default_content: dict, file_type: str):
    """修复配置文件"""
    print(f"\n🔧 修复 {file_type} 配置文件...")
    
    # 检查文件状态
    is_valid, message = check_json_file(file_path)
    print(f"   状态检查: {message}")
    
    if is_valid:
        print(f"   ✅ {file_type} 配置文件正常，无需修复")
        return True
    
    # 备份现有文件（如果存在且不为空）
    if file_path.exists() and file_path.stat().st_size > 0:
        backup_path = backup_file(file_path)
        if backup_path:
            print(f"   📁 已备份到: {backup_path.name}")
    
    # 创建新的配置文件
    try:
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入默认配置
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(default_content, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 已创建新的 {file_type} 配置文件")
        
        # 验证新文件
        is_valid, message = check_json_file(file_path)
        if is_valid:
            print(f"   ✅ 新配置文件验证通过")
            return True
        else:
            print(f"   ❌ 新配置文件验证失败: {message}")
            return False
            
    except Exception as e:
        print(f"   ❌ 创建配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 配置文件修复工具")
    print("=" * 60)
    
    # 获取配置目录
    config_dir = get_config_directory()
    print(f"配置目录: {config_dir}")
    
    if not config_dir.exists():
        print("配置目录不存在，创建配置目录...")
        try:
            config_dir.mkdir(parents=True, exist_ok=True)
            print("✅ 配置目录创建成功")
        except Exception as e:
            print(f"❌ 配置目录创建失败: {e}")
            input("\n按回车键退出...")
            return
    
    # 配置文件路径
    system_config_file = config_dir / "system_config.json"
    send_settings_file = config_dir / "send_settings.json"
    
    print(f"\n📁 配置文件位置:")
    print(f"   系统配置: {system_config_file}")
    print(f"   发送设置: {send_settings_file}")
    
    # 修复系统配置
    success1 = fix_config_file(
        system_config_file, 
        create_default_system_config(), 
        "系统配置"
    )
    
    # 修复发送设置
    success2 = fix_config_file(
        send_settings_file, 
        create_default_send_settings(), 
        "发送设置"
    )
    
    # 总结
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 配置文件修复完成!")
        print("✅ 系统配置文件正常")
        print("✅ 发送设置文件正常")
        print("\n现在可以正常启动程序了。")
    else:
        print("⚠️  配置文件修复部分完成:")
        print(f"   系统配置: {'✅ 正常' if success1 else '❌ 失败'}")
        print(f"   发送设置: {'✅ 正常' if success2 else '❌ 失败'}")
        
        if not success1 or not success2:
            print("\n🔧 手动修复建议:")
            print("1. 检查配置目录权限")
            print("2. 确保磁盘空间充足")
            print("3. 关闭杀毒软件后重试")
    
    print("\n📋 配置文件说明:")
    print("- system_config.json: 系统设置（主题、日志等）")
    print("- send_settings.json: 发送设置（间隔、重试等）")
    print("- 这些文件损坏时程序会自动重新创建")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
