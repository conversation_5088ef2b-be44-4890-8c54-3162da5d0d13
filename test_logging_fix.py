#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志系统修复
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_logging_system():
    """测试日志系统"""
    print("🔧 测试日志系统修复...")
    
    try:
        # 导入日志模块
        from utils.logger import setup_logger
        
        print("✅ 成功导入日志模块")
        
        # 测试创建日志器
        logger = setup_logger("test_logger")
        print("✅ 成功创建日志器")
        
        # 测试日志输出
        logger.info("这是一条测试信息日志")
        logger.warning("这是一条测试警告日志")
        logger.error("这是一条测试错误日志")
        
        print("✅ 日志输出测试完成")
        
        # 检查处理器
        print(f"📋 日志器处理器数量: {len(logger.handlers)}")
        for i, handler in enumerate(logger.handlers):
            handler_type = type(handler).__name__
            print(f"   处理器 {i+1}: {handler_type}")
            
            # 检查处理器是否有效
            if hasattr(handler, 'stream'):
                if handler.stream is None:
                    print(f"     ❌ 处理器流为None")
                else:
                    print(f"     ✅ 处理器流正常")
            else:
                print(f"     ✅ 文件处理器")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stream_handlers():
    """测试流处理器"""
    print("\n🔍 测试流处理器...")
    
    import sys
    print(f"sys.stdout: {sys.stdout}")
    print(f"sys.stderr: {sys.stderr}")
    
    # 测试标准输出
    if sys.stdout is not None:
        try:
            sys.stdout.write("测试标准输出\n")
            sys.stdout.flush()
            print("✅ 标准输出正常")
        except Exception as e:
            print(f"❌ 标准输出异常: {e}")
    else:
        print("❌ 标准输出为None")
    
    # 测试标准错误
    if sys.stderr is not None:
        try:
            sys.stderr.write("测试标准错误\n")
            sys.stderr.flush()
            print("✅ 标准错误正常")
        except Exception as e:
            print(f"❌ 标准错误异常: {e}")
    else:
        print("❌ 标准错误为None")

def test_multiple_loggers():
    """测试多个日志器"""
    print("\n🔄 测试多个日志器...")
    
    try:
        from utils.logger import setup_logger
        
        # 创建多个日志器
        loggers = []
        for i in range(3):
            logger_name = f"test_logger_{i}"
            logger = setup_logger(logger_name)
            loggers.append(logger)
            print(f"✅ 创建日志器: {logger_name}")
        
        # 测试每个日志器
        for i, logger in enumerate(loggers):
            try:
                logger.info(f"日志器 {i} 测试消息")
                print(f"✅ 日志器 {i} 输出正常")
            except Exception as e:
                print(f"❌ 日志器 {i} 输出失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多日志器测试失败: {e}")
        return False

def test_ui_logger():
    """测试UI相关的日志器"""
    print("\n🖥️  测试UI日志器...")
    
    try:
        from utils.logger import setup_logger
        
        # 创建UI相关的日志器
        ui_logger = setup_logger("main_window")
        
        # 模拟UI日志输出
        ui_logger.info("表格组件优化完成")
        ui_logger.info("UI初始化完成")
        ui_logger.warning("UI组件警告")
        
        print("✅ UI日志器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ UI日志器测试失败: {e}")
        return False

def simulate_packed_environment():
    """模拟打包环境"""
    print("\n📦 模拟打包环境...")
    
    import sys
    
    # 保存原始流
    original_stdout = sys.stdout
    original_stderr = sys.stderr
    
    try:
        # 模拟打包环境中的None流
        sys.stdout = None
        sys.stderr = None
        
        print("⚠️  已设置stdout和stderr为None")
        
        # 测试日志系统
        from utils.logger import setup_logger
        logger = setup_logger("packed_test")
        
        # 尝试输出日志
        logger.info("打包环境测试消息")
        logger.error("打包环境错误消息")
        
        print("✅ 打包环境日志测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 打包环境测试失败: {e}")
        return False
    finally:
        # 恢复原始流
        sys.stdout = original_stdout
        sys.stderr = original_stderr

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 日志系统修复测试")
    print("=" * 70)
    
    tests = [
        ("基础日志系统", test_logging_system),
        ("流处理器", test_stream_handlers),
        ("多个日志器", test_multiple_loggers),
        ("UI日志器", test_ui_logger),
        ("打包环境模拟", simulate_packed_environment)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！日志系统修复成功！")
        print("\n✨ 修复内容:")
        print("  🔧 修复了None流处理器问题")
        print("  🛡️  添加了完整的错误处理")
        print("  📁 改进了文件处理器创建")
        print("  🔄 添加了处理器有效性检查")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    print("\n📋 现在程序应该不会再出现日志错误了！")
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
