@echo off
chcp 65001 >nul
title Meet space 微信群发助手 - 安装程序

echo.
echo ========================================
echo   Meet space 微信群发助手
echo   Windows安装程序 v1.0.0
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限检查通过
) else (
    echo ❌ 需要管理员权限，请以管理员身份运行
    pause
    exit /b 1
)

:: 设置安装目录
set "INSTALL_DIR=%ProgramFiles%\MeetSpaceWeChatSender"

echo 📁 安装目录: %INSTALL_DIR%
echo.

:: 创建安装目录
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo ✅ 创建安装目录成功
) else (
    echo ℹ️ 安装目录已存在
)

:: 复制文件
echo 📋 正在复制程序文件...
xcopy /E /I /Y "%~dp0MeetSpaceWeChatSender" "%INSTALL_DIR%" >nul
if %errorLevel% == 0 (
    echo ✅ 程序文件复制完成
) else (
    echo ❌ 文件复制失败
    pause
    exit /b 1
)

:: 创建开始菜单快捷方式
echo 📋 创建开始菜单快捷方式...
set "START_MENU=%ProgramData%\Microsoft\Windows\Start Menu\Programs"
if not exist "%START_MENU%\Meet space 微信群发助手" (
    mkdir "%START_MENU%\Meet space 微信群发助手"
)

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\Meet space 微信群发助手\Meet space 微信群发助手.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\MeetSpaceWeChatSender.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

:: 创建桌面快捷方式（可选）
set /p desktop_shortcut="是否创建桌面快捷方式？(y/n): "
if /i "%desktop_shortcut%"=="y" (
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Meet space 微信群发助手.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\MeetSpaceWeChatSender.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"
    echo ✅ 桌面快捷方式已创建
)

:: 注册到控制面板
echo 📋 注册到控制面板...
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender" /v "DisplayName" /t REG_SZ /d "Meet space 微信群发助手" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender" /v "DisplayVersion" /t REG_SZ /d "1.0.0" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender" /v "Publisher" /t REG_SZ /d "Meet space 会客创意空间" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\uninstall.bat" /f >nul

:: 创建卸载脚本
echo 📋 创建卸载脚本...
echo @echo off > "%INSTALL_DIR%\uninstall.bat"
echo title Meet space 微信群发助手 - 卸载程序 >> "%INSTALL_DIR%\uninstall.bat"
echo echo 正在卸载 Meet space 微信群发助手... >> "%INSTALL_DIR%\uninstall.bat"
echo taskkill /f /im MeetSpaceWeChatSender.exe 2^>nul >> "%INSTALL_DIR%\uninstall.bat"
echo timeout /t 2 /nobreak ^>nul >> "%INSTALL_DIR%\uninstall.bat"
echo rd /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\uninstall.bat"
echo del /q "%START_MENU%\Meet space 微信群发助手\*.lnk" 2^>nul >> "%INSTALL_DIR%\uninstall.bat"
echo rd "%START_MENU%\Meet space 微信群发助手" 2^>nul >> "%INSTALL_DIR%\uninstall.bat"
echo del /q "%USERPROFILE%\Desktop\Meet space 微信群发助手.lnk" 2^>nul >> "%INSTALL_DIR%\uninstall.bat"
echo reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender" /f 2^>nul >> "%INSTALL_DIR%\uninstall.bat"
echo echo 卸载完成！ >> "%INSTALL_DIR%\uninstall.bat"
echo pause >> "%INSTALL_DIR%\uninstall.bat"

echo.
echo ✅ 安装完成！
echo.
echo 📁 安装位置: %INSTALL_DIR%
echo 🚀 可从开始菜单启动程序
echo.

:: 询问是否立即启动
set /p launch_now="是否立即启动程序？(y/n): "
if /i "%launch_now%"=="y" (
    start "" "%INSTALL_DIR%\MeetSpaceWeChatSender.exe"
)

echo.
echo 感谢使用 Meet space 微信群发助手！
pause
