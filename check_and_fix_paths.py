#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查和修复项目中的路径问题
确保所有路径都适合打包环境
"""

import os
import sys
import re
from pathlib import Path
from typing import List, Dict, Tu<PERSON>

def check_file_for_path_issues(file_path: Path) -> List[Dict]:
    """检查文件中的路径问题"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 检查模式
        patterns = [
            # 硬编码相对路径
            (r'"logs/[^"]*"', "硬编码logs路径"),
            (r'"config/[^"]*"', "硬编码config路径"),
            (r'"resources/[^"]*"', "硬编码resources路径"),
            (r"'logs/[^']*'", "硬编码logs路径"),
            (r"'config/[^']*'", "硬编码config路径"),
            (r"'resources/[^']*'", "硬编码resources路径"),
            
            # 危险的Path构造
            (r'Path\(["\'][^"\']*["\']\)', "直接Path构造"),
            (r'os\.path\.join\(["\'][^"\']*["\']', "os.path.join硬编码"),
            
            # 当前工作目录依赖
            (r'Path\.cwd\(\)', "依赖当前工作目录"),
            (r'os\.getcwd\(\)', "依赖当前工作目录"),
            
            # 相对路径文件操作
            (r'open\(["\'][^"\']*\.log["\']', "直接打开日志文件"),
            (r'open\(["\'][^"\']*\.json["\']', "直接打开配置文件"),
        ]
        
        for line_num, line in enumerate(lines, 1):
            for pattern, description in patterns:
                matches = re.findall(pattern, line)
                if matches:
                    issues.append({
                        'file': str(file_path),
                        'line': line_num,
                        'content': line.strip(),
                        'matches': matches,
                        'issue': description
                    })
    
    except Exception as e:
        print(f"检查文件失败 {file_path}: {e}")
    
    return issues

def scan_project_for_path_issues() -> Dict[str, List]:
    """扫描项目中的路径问题"""
    print("🔍 扫描项目中的路径问题...")
    
    # 要检查的文件类型
    file_patterns = ['*.py']
    
    # 要排除的目录
    exclude_dirs = {
        '__pycache__', '.git', 'build', 'dist', 'venv', 'env',
        '.pytest_cache', '.mypy_cache', 'node_modules'
    }
    
    all_issues = {}
    total_files = 0
    
    for pattern in file_patterns:
        for file_path in Path('.').rglob(pattern):
            # 跳过排除的目录
            if any(part in exclude_dirs for part in file_path.parts):
                continue
            
            total_files += 1
            issues = check_file_for_path_issues(file_path)
            if issues:
                all_issues[str(file_path)] = issues
    
    print(f"📊 扫描完成: 检查了 {total_files} 个文件")
    print(f"🚨 发现问题文件: {len(all_issues)} 个")
    
    return all_issues

def generate_path_fix_suggestions(issues: Dict[str, List]) -> List[str]:
    """生成路径修复建议"""
    suggestions = []
    
    suggestions.append("# 🔧 路径问题修复建议\n")
    
    for file_path, file_issues in issues.items():
        suggestions.append(f"## 📁 {file_path}")
        suggestions.append("")
        
        for issue in file_issues:
            suggestions.append(f"**行 {issue['line']}**: {issue['issue']}")
            suggestions.append(f"```python")
            suggestions.append(f"# 问题代码:")
            suggestions.append(f"{issue['content']}")
            suggestions.append(f"```")
            
            # 生成修复建议
            if "logs路径" in issue['issue']:
                suggestions.append("**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`")
            elif "config路径" in issue['issue']:
                suggestions.append("**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`")
            elif "resources路径" in issue['issue']:
                suggestions.append("**修复建议**: 使用 `path_manager.get_resource_path()`")
            elif "直接Path构造" in issue['issue']:
                suggestions.append("**修复建议**: 使用路径管理器的相应方法")
            elif "当前工作目录" in issue['issue']:
                suggestions.append("**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法")
            elif "直接打开" in issue['issue']:
                suggestions.append("**修复建议**: 先通过路径管理器获取正确路径，再打开文件")
            
            suggestions.append("")
    
    return suggestions

def check_path_manager_usage() -> List[str]:
    """检查路径管理器的使用情况"""
    print("🔍 检查路径管理器使用情况...")
    
    issues = []
    
    # 检查是否有文件没有导入路径管理器但使用了路径操作
    for py_file in Path('.').rglob('*.py'):
        if any(part in {'__pycache__', '.git', 'build', 'dist'} for part in py_file.parts):
            continue
        
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有路径操作但没有导入路径管理器
            has_path_ops = any(pattern in content for pattern in [
                'Path(', 'os.path', 'open(', '.log', '.json', '.txt'
            ])
            
            has_path_manager_import = any(pattern in content for pattern in [
                'from utils.path_manager import',
                'import utils.path_manager',
                'path_manager'
            ])
            
            if has_path_ops and not has_path_manager_import and 'path_manager.py' not in str(py_file):
                issues.append(f"⚠️  {py_file}: 有路径操作但未导入路径管理器")
        
        except Exception as e:
            continue
    
    return issues

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 路径问题检查和修复")
    print("=" * 70)
    
    # 1. 扫描路径问题
    issues = scan_project_for_path_issues()
    
    # 2. 检查路径管理器使用
    manager_issues = check_path_manager_usage()
    
    # 3. 生成报告
    if issues or manager_issues:
        print("\n🚨 发现路径问题:")
        
        # 显示主要问题
        for file_path, file_issues in list(issues.items())[:5]:  # 只显示前5个文件
            print(f"\n📁 {file_path}:")
            for issue in file_issues[:3]:  # 每个文件只显示前3个问题
                print(f"   行 {issue['line']}: {issue['issue']}")
                print(f"   代码: {issue['content'][:60]}...")
        
        if len(issues) > 5:
            print(f"\n   ... 还有 {len(issues) - 5} 个文件存在问题")
        
        # 显示路径管理器问题
        if manager_issues:
            print(f"\n📋 路径管理器使用问题:")
            for issue in manager_issues[:5]:
                print(f"   {issue}")
            if len(manager_issues) > 5:
                print(f"   ... 还有 {len(manager_issues) - 5} 个问题")
        
        # 生成修复建议
        suggestions = generate_path_fix_suggestions(issues)
        
        # 保存报告
        report_file = Path("path_issues_report.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(suggestions))
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        print("\n🔧 主要修复建议:")
        print("1. 将所有硬编码路径替换为路径管理器调用")
        print("2. 在需要路径操作的文件中导入路径管理器")
        print("3. 使用 path_manager.get_*_path() 方法获取路径")
        print("4. 避免使用 Path.cwd() 和 os.getcwd()")
        print("5. 确保所有文件操作使用绝对路径")
        
    else:
        print("\n✅ 未发现路径问题！")
        print("项目路径使用规范，适合打包部署。")
    
    print(f"\n📋 检查完成！")
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
