#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理版构建脚本 - 仅支持 Injector.exe + wxhelper.dll
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_clean_version():
    """构建清理版本 - 仅支持单一注入方式"""
    print("🔧 构建清理版本...")
    print("仅支持：Injector.exe + wxhelper.dll")
    
    # 清理
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 构建命令 - 仅包含必要的组件
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MeetSpaceWeChatSender_Clean",
        "--icon=resources/icons/app_icon.ico",
        
        # 仅包含必要的工具文件
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64/Injector.exe;tools/x64",
        "--add-data=tools/Win32/Injector.exe;tools/Win32",
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        
        # 基础资源
        "--add-data=resources/icons;resources/icons",
        "--add-data=version_info.txt;.",
        
        # 核心隐藏导入 - 仅必要模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=ctypes",
        "--hidden-import=subprocess",
        "--hidden-import=psutil",
        "--hidden-import=requests",
        "--hidden-import=pandas",
        "--hidden-import=PIL",
        
        # 项目核心模块 - 仅必要的
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=core.http_api_connector",  # 唯一连接器
        "--hidden-import=core.auto_injector",       # 唯一注入器
        "--hidden-import=core.injector_tool",       # 核心注入工具
        "--hidden-import=ui",
        "--hidden-import=utils",
        
        # 排除不需要的模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=wcferry",  # 不再需要 WeChatFerry
        
        # 构建选项
        "--clean",
        "--noconfirm",
        "--noupx",
        
        "main.py"
    ]
    
    print("📋 开始构建清理版本...")
    print("   - 仅支持 Injector.exe + wxhelper.dll")
    print("   - 移除了其他连接器和注入器")
    print("   - 简化了依赖关系")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=400
        )
        
        print("✅ 构建完成")
        
        # 检查结果
        exe_file = Path("dist") / "MeetSpaceWeChatSender_Clean.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"✅ 输出文件: {exe_file}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            
            # 创建说明文档
            create_clean_version_docs()
            
            return True
        else:
            print("❌ 未找到输出文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e.returncode}")
        if e.stderr:
            print("错误:", e.stderr[-1000:])
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def create_clean_version_docs():
    """创建清理版本说明文档"""
    print("📄 创建说明文档...")
    
    docs_content = """# Meet space 微信群发助手 - 清理版本

## 🎯 版本特点

### ✅ 唯一支持的注入方式
- **Injector.exe + wxhelper.dll**
- 基于 HTTP API (localhost:19088)
- 自动架构检测（32位/64位）

### 🗑️ 已移除的组件
- WeChatFerry 连接器
- UI 自动化连接器  
- WinAPI 连接器
- 其他注入器（smart_injector, wechat_injector 等）

### 📦 保留的核心功能
- ✅ 定时发送
- ✅ 循环发送
- ✅ 分组管理
- ✅ 5套主题界面
- ✅ 风险控制
- ✅ 发送监控

## 🚀 使用方法

1. **启动微信PC版并登录**
2. **运行程序**: 双击 `MeetSpaceWeChatSender_Clean.exe`
3. **连接微信**: 点击"连接微信"按钮
4. **等待注入**: 程序会自动使用 Injector.exe 注入 wxhelper.dll
5. **开始使用**: 连接成功后即可使用所有功能

## 🔧 技术架构

```
主程序 (main.py)
    ↓
HTTP API 连接器 (http_api_connector.py)
    ↓
自动注入器 (auto_injector.py)
    ↓
注入工具 (injector_tool.py)
    ↓
Injector.exe + wxhelper.dll
```

## 🛠️ 故障排除

### 连接失败
1. 确保微信PC版已启动并登录
2. 以管理员身份运行程序
3. 关闭杀毒软件
4. 检查防火墙设置

### 注入失败
1. 重启微信后再试
2. 检查微信版本兼容性
3. 确保 tools 目录包含正确的 Injector.exe
4. 确保 wxhelper_files 目录包含 wxhelper.dll

## 📊 优势

- **简化架构**: 移除了不必要的组件
- **稳定性高**: 专注于单一可靠的注入方式
- **体积更小**: 减少了依赖和文件大小
- **维护简单**: 代码结构更清晰

---

**版本**: Clean v1.0.0
**支持方式**: 仅 Injector.exe + wxhelper.dll
**更新日期**: 2025-08-05
"""
    
    try:
        with open("dist/清理版本说明.txt", "w", encoding="utf-8") as f:
            f.write(docs_content)
        print("  ✅ 说明文档已创建: dist/清理版本说明.txt")
    except Exception as e:
        print(f"  ⚠️  创建说明文档失败: {e}")

def main():
    print("🔧 Meet space 微信群发助手 - 清理版本构建")
    print("=" * 70)
    print("仅保留：Injector.exe + wxhelper.dll")
    print("移除：其他所有连接器和注入器")
    print("=" * 70)
    
    success = build_clean_version()
    
    if success:
        print("\n🎉 清理版本构建成功!")
        print("📁 输出文件:")
        print("  - MeetSpaceWeChatSender_Clean.exe (主程序)")
        print("  - 清理版本说明.txt (详细说明)")
        
        print("\n✨ 清理内容:")
        print("  🗑️  移除了 WeChatFerry 连接器")
        print("  🗑️  移除了 UI 自动化连接器")
        print("  🗑️  移除了 WinAPI 连接器")
        print("  🗑️  移除了其他注入器")
        print("  ✅ 保留了 Injector.exe + wxhelper.dll")
        print("  ✅ 保留了所有核心功能")
        
        print("\n📋 现在程序:")
        print("  1. 架构更简洁")
        print("  2. 依赖更少")
        print("  3. 更稳定可靠")
        print("  4. 专注于您验证可用的注入方式")
        
    else:
        print("\n❌ 构建失败!")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
