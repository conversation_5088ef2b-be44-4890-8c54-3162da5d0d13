"""
定时发送页面

实现定时发送功能的用户界面。
"""

from datetime import datetime

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QDateEdit,
    QDialog,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QMessageBox,
    QPushButton,
    QScrollArea,
    QSpinBox,
    QTextEdit,
    QTimeEdit,
    QVBoxLayout,
    QWidget,
    QStackedWidget,
)

from core.group_manager import GroupMember, group_manager
from core.timing_sender import timing_sender
from core.send_monitor import SendTask, SendStatus, SendMonitor
from core.message_template import MessageTemplate
from ui.widgets.group_card import GroupCard, NewGroupCard
from ui.widgets.button_state_manager import (
    create_timing_button_manager,
    ButtonType,
    TaskStatus,
    map_task_status_to_enum,
)
from ui.group_config_manager import GroupConfigManager
from ui.themed_message_box import ThemedMessageBoxHelper
from ui.themed_dialog_base import ThemedDialogBase
from utils.logger import setup_logger

logger = setup_logger("timing_send_page")


class GroupEditDialog(ThemedDialogBase):
    """分组编辑对话框"""

    group_saved = pyqtSignal()

    def __init__(self, group_id: str = None, parent=None):
        super().__init__(parent)
        self.group_id = group_id
        self.group = None
        self.is_edit_mode = group_id is not None

        if self.is_edit_mode:
            self.group = group_manager.get_group(group_id, "timing")

        self.setup_ui()
        self.setWindowTitle("编辑分组" if self.is_edit_mode else "新建分组")
        self.setModal(True)
        self.resize(400, 300)

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 分组名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("分组名称:"))
        self.name_edit = QLineEdit()
        if self.group:
            self.name_edit.setText(self.group.name)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)

        # 成员列表（简化版，实际应该有更复杂的选择界面）
        layout.addWidget(QLabel("成员管理:"))
        self.members_text = QTextEdit()
        self.members_text.setMaximumHeight(100)
        self.members_text.setPlaceholderText("每行一个成员，格式：姓名(微信ID)")

        if self.group:
            members_text = "\n".join(
                [f"{m.name}({m.wxid})" for m in self.group.members]
            )
            self.members_text.setPlainText(members_text)

        layout.addWidget(self.members_text)

        # 标签
        tags_layout = QHBoxLayout()
        tags_layout.addWidget(QLabel("标签:"))
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("用逗号分隔多个标签")
        if self.group:
            self.tags_edit.setText(",".join(self.group.tags))
        tags_layout.addWidget(self.tags_edit)
        layout.addLayout(tags_layout)

        # 按钮
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("保存")
        save_btn.clicked.connect(self.save_group)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.close)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def save_group(self):
        """保存分组"""
        name = self.name_edit.text().strip()
        if not name:
            ThemedMessageBoxHelper.show_warning(self, "警告", "请输入分组名称")
            return

        # 解析成员
        members = []
        members_text = self.members_text.toPlainText().strip()
        if members_text:
            for line in members_text.split("\n"):
                line = line.strip()
                if line and "(" in line and ")" in line:
                    try:
                        name_part = line[: line.rfind("(")]
                        wxid_part = line[line.rfind("(") + 1 : line.rfind(")")]
                        member = GroupMember(wxid_part, name_part, "contact")
                        members.append(member)
                    except:
                        continue

        # 解析标签
        tags = []
        tags_text = self.tags_edit.text().strip()
        if tags_text:
            tags = [tag.strip() for tag in tags_text.split(",") if tag.strip()]

        try:
            if self.is_edit_mode:
                # 更新现有分组
                self.group.name = name
                self.group.members = members
                self.group.tags = tags
                group_manager.save_groups("timing")
            else:
                # 创建新分组
                group = group_manager.create_group(name, "timing")
                group.members = members
                group.tags = tags
                group_manager.save_groups("timing")

            self.group_saved.emit()
            self.close()

        except Exception as e:
            ThemedMessageBoxHelper.show_error(self, "错误", f"保存分组失败: {e}")


class TimingSendPage(QWidget):
    """定时发送页面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_selected_group = None  # 单选模式，只保留当前选中的分组
        self.group_cards = {}

        # 配置管理器
        self.config_manager = GroupConfigManager(self)

        # 按钮状态管理器
        self.button_manager = create_timing_button_manager()

        # 发送监控器
        self.send_monitor = SendMonitor()
        self.send_monitor.progress_updated.connect(self.on_send_progress)
        self.send_monitor.send_completed.connect(self.on_send_completed)

        # 获取连接器和模板管理器
        self.connector = None
        self.template_manager = MessageTemplate()

        # 获取主窗口的连接器
        try:
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, "connector"):
                self.connector = main_window.connector
                logger.info(f"获取到连接器: {type(self.connector).__name__}")
            else:
                # 尝试从父窗口获取连接器
                parent = self.parent()
                while parent:
                    if hasattr(parent, "connector") and parent.connector:
                        self.connector = parent.connector
                        logger.info(
                            f"从父窗口获取到连接器: {type(self.connector).__name__}"
                        )
                        break
                    parent = parent.parent()

                if not self.connector:
                    logger.warning("无法获取连接器，将在后续设置")
        except Exception as e:
            logger.warning(f"无法获取主窗口连接器: {e}")

        self.setup_ui()
        self.refresh_groups()

        # 连接信号
        timing_sender.task_started.connect(self.on_task_started)
        timing_sender.task_progress.connect(self.on_task_progress)
        timing_sender.task_completed.connect(self.on_task_completed)
        timing_sender.task_failed.connect(self.on_task_failed)

        # 连接配置管理器信号
        self.config_manager.group_switched.connect(self.on_group_switched)
        self.config_manager.config_changed.connect(self.on_config_changed)

    def get_main_window(self):
        """获取主窗口"""
        widget = self
        while widget:
            if hasattr(widget, "connector"):
                return widget
            widget = widget.parent()
        return None

    def set_connector(self, connector):
        """设置连接器"""
        self.connector = connector
        logger.info(f"定时发送页面连接器已设置: {type(self.connector).__name__}")

        # 同时设置给定时发送器
        if hasattr(timing_sender, "set_connector"):
            timing_sender.set_connector(connector)

    def refresh_connection_status(self):
        """刷新连接状态"""
        try:
            if not self.connector:
                logger.warning("连接器为空，无法刷新连接状态")
                return False

            logger.info("正在刷新连接状态...")

            # 首先尝试强制刷新连接状态
            if hasattr(self.connector, "force_refresh_connection_status"):
                status = self.connector.force_refresh_connection_status()
                logger.info(f"强制刷新连接状态结果: {status}")
                if status:
                    return True

            # 如果强制刷新失败，尝试普通检查
            if hasattr(self.connector, "check_connection_status"):
                status = self.connector.check_connection_status()
                logger.info(f"普通连接状态检查结果: {status}")
                return status
            else:
                logger.warning("连接器没有check_connection_status方法")
                return False

        except Exception as e:
            logger.error(f"刷新连接状态失败: {e}")
            return False

    def setup_ui(self):
        """设置UI"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(10)

        # 左侧：分组管理区域
        left_widget = QWidget()
        left_widget.setMaximumWidth(600)
        left_layout = QVBoxLayout(left_widget)

        # 分组管理标题和批量操作
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("定时发送分组管理"))
        header_layout.addStretch()

        left_layout.addLayout(header_layout)

        # 分组卡片滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.cards_widget = QWidget()
        self.cards_layout = QVBoxLayout(self.cards_widget)
        self.cards_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        scroll_area.setWidget(self.cards_widget)
        left_layout.addWidget(scroll_area)

        main_layout.addWidget(left_widget)

        # 右侧：发送设置区域
        right_widget = QGroupBox("定时发送设置")
        right_layout = QVBoxLayout(right_widget)

        # 当前分组显示
        self.current_group_header = QLabel("📁 请选择一个分组进行配置")
        self.current_group_header.setStyleSheet(
            """
            font-weight: bold;
            font-size: 14px;
            color: #007acc;
            padding: 8px;
            border-bottom: 1px solid #007acc;
            margin-bottom: 8px;
        """
        )
        right_layout.addWidget(self.current_group_header)

        # 配置区域容器
        self.config_container = QWidget()
        config_container_layout = QVBoxLayout(self.config_container)

        # 未选择分组时的提示
        self.no_selection_widget = QWidget()
        no_selection_layout = QVBoxLayout(self.no_selection_widget)
        no_selection_layout.addStretch()

        tip_label = QLabel("👈 请在左侧选择一个分组")
        tip_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tip_label.setStyleSheet(
            """
            font-size: 16px;
            color: #666;
            padding: 20px;
        """
        )
        no_selection_layout.addWidget(tip_label)

        tip_detail = QLabel(
            "选择分组后，您可以配置：\n• 消息模板和内容\n• 执行时间设置"
        )
        tip_detail.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tip_detail.setStyleSheet(
            """
            font-size: 11px;
            color: #888;
            line-height: 1.4;
        """
        )
        no_selection_layout.addWidget(tip_detail)
        no_selection_layout.addStretch()

        config_container_layout.addWidget(self.no_selection_widget)

        # 配置表单区域（初始隐藏）
        self.config_form_widget = QWidget()
        self.config_form_widget.setVisible(False)
        config_form_layout = QVBoxLayout(self.config_form_widget)

        # 执行日期
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("执行日期:"))
        self.date_edit = QDateEdit()
        self.date_edit.setDate(datetime.now().date())
        self.date_edit.setMinimumDate(datetime.now().date())
        self.date_edit.setCalendarPopup(True)
        date_layout.addWidget(self.date_edit)
        date_layout.addStretch()
        config_form_layout.addLayout(date_layout)

        # 执行时间
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("执行时间:"))
        self.time_edit = QTimeEdit()
        self.time_edit.setTime(datetime.now().time())
        self.time_edit.timeChanged.connect(self.on_time_changed)
        time_layout.addWidget(self.time_edit)
        time_layout.addStretch()
        config_form_layout.addLayout(time_layout)

        # 发送设置组
        send_settings_group = QGroupBox("发送设置")
        send_settings_layout = QVBoxLayout(send_settings_group)

        # 发送间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("发送间隔:"))
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setMinimum(1)
        self.interval_spinbox.setMaximum(300)
        self.interval_spinbox.setValue(5)
        self.interval_spinbox.setSuffix(" 秒")
        self.interval_spinbox.valueChanged.connect(self.on_interval_changed)
        interval_layout.addWidget(self.interval_spinbox)
        interval_layout.addStretch()
        send_settings_layout.addLayout(interval_layout)

        # 批量大小
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("批量大小:"))
        self.batch_spinbox = QSpinBox()
        self.batch_spinbox.setMinimum(1)
        self.batch_spinbox.setMaximum(100)
        self.batch_spinbox.setValue(10)
        self.batch_spinbox.setSuffix(" 个")
        self.batch_spinbox.valueChanged.connect(self.on_batch_size_changed)
        batch_layout.addWidget(self.batch_spinbox)
        batch_layout.addStretch()
        send_settings_layout.addLayout(batch_layout)

        # 风控设置
        risk_layout = QHBoxLayout()
        self.use_system_risk_cb = QCheckBox("使用系统风控设置")
        self.use_system_risk_cb.setChecked(True)
        self.use_system_risk_cb.stateChanged.connect(self.on_risk_control_changed)
        risk_layout.addWidget(self.use_system_risk_cb)
        risk_layout.addStretch()
        send_settings_layout.addLayout(risk_layout)

        config_form_layout.addWidget(send_settings_group)

        # 消息模板选择
        template_layout = QHBoxLayout()
        template_layout.addWidget(QLabel("消息模板:"))
        self.template_combo = QComboBox()
        self.template_combo.addItem("手动输入", "")
        self.load_templates()
        self.template_combo.currentTextChanged.connect(self.on_template_changed)
        template_layout.addWidget(self.template_combo)
        template_layout.addStretch()
        config_form_layout.addLayout(template_layout)

        # 消息类型选择
        message_type_layout = QHBoxLayout()
        message_type_layout.addWidget(QLabel("消息类型:"))
        self.message_type_combo = QComboBox()
        self.message_type_combo.addItems(["富文本消息", "文本消息"])
        self.message_type_combo.currentTextChanged.connect(self.on_message_type_changed)
        message_type_layout.addWidget(self.message_type_combo)
        message_type_layout.addStretch()
        config_form_layout.addLayout(message_type_layout)

        # 消息编辑器堆栈
        self.message_editor_stack = QStackedWidget()

        # 富文本编辑器
        from ui.rich_text_editor import RichTextMessageEditor

        self.rich_text_editor = RichTextMessageEditor()
        self.rich_text_editor.content_changed.connect(self.on_message_content_changed)
        self.message_editor_stack.addWidget(self.rich_text_editor)

        # 传统文本编辑器
        self.message_edit = QTextEdit()
        self.message_edit.setMaximumHeight(150)
        self.message_edit.setPlaceholderText("请输入要发送的消息内容...")
        self.message_edit.textChanged.connect(self.on_message_content_changed)
        self.message_editor_stack.addWidget(self.message_edit)

        config_form_layout.addWidget(QLabel("消息内容:"))
        config_form_layout.addWidget(self.message_editor_stack)

        # 保存设置按钮（靠右对齐）
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()  # 添加弹性空间，使按钮靠右

        # 保存设置按钮
        self.send_btn = QPushButton("💾 保存设置")
        self.send_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """
        )
        self.send_btn.clicked.connect(self.save_timing_settings)
        self.send_btn.setEnabled(False)
        buttons_layout.addWidget(self.send_btn)

        # 注册按钮到状态管理器
        self.button_manager.register_button(ButtonType.PRIMARY, self.send_btn)

        config_form_layout.addLayout(buttons_layout)

        # 将配置表单添加到容器
        config_container_layout.addWidget(self.config_form_widget)

        # 将容器添加到右侧布局
        right_layout.addWidget(self.config_container)
        right_layout.addStretch()

        main_layout.addWidget(right_widget)

    def refresh_groups(self):
        """刷新分组显示"""
        # 清空现有卡片
        for card in self.group_cards.values():
            card.setParent(None)
        self.group_cards.clear()

        # 清空布局中的所有widget
        while self.cards_layout.count():
            child = self.cards_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

        # 获取排序后的分组
        groups = group_manager.get_sorted_groups("timing")

        # 添加新建分组卡片
        new_card = NewGroupCard()
        new_card.create_group_requested.connect(self.create_new_group)
        self.cards_layout.addWidget(new_card)

        # 添加分组卡片
        for group in groups:
            card = GroupCard(group, "timing")
            card.start_requested.connect(self.start_group_task)
            card.stop_requested.connect(self.stop_group_task)
            card.pause_requested.connect(self.pause_group_task)
            card.delete_requested.connect(self.delete_group)
            card.details_requested.connect(self.show_group_details)
            card.group_chosen.connect(self.choose_group)

            self.group_cards[group.group_id] = card
            self.cards_layout.addWidget(card)

            # 更新按钮状态
            self.update_group_card_button_states(card, group.group_id)

        logger.info(f"刷新定时发送分组，共{len(groups)}个分组")

    def load_templates(self):
        """加载消息模板"""
        try:
            from core.message_template import MessageTemplate

            template_manager = MessageTemplate()
            templates = template_manager.get_all_templates()

            # 清空现有选项（保留"手动输入"）
            while self.template_combo.count() > 1:
                self.template_combo.removeItem(1)

            # 添加模板选项
            for template in templates:
                self.template_combo.addItem(template.name, template.id)

            logger.info(f"加载了 {len(templates)} 个消息模板")

        except Exception as e:
            logger.error(f"加载消息模板失败: {e}")

    def on_template_changed(self, template_name):
        """模板选择改变"""
        try:
            template_id = self.template_combo.currentData()

            # 保存模板选择到配置
            if self.current_selected_group:
                self.config_manager.update_config(
                    "message_settings.template_id", template_id or ""
                )

            if not template_id:  # 手动输入
                logger.info("切换到手动输入模式")
                return

            from core.message_template import MessageTemplate

            template_manager = MessageTemplate()
            template = template_manager.get_template(template_id)

            if template:
                # 获取当前消息类型
                current_message_type = self.message_type_combo.currentText()

                # 根据当前消息类型和模板类型更新相应的编辑器
                if current_message_type == "富文本消息":
                    # 当前是富文本模式，更新富文本编辑器
                    self._update_rich_text_editor_with_template(template)
                else:
                    # 当前是文本模式，更新文本编辑器
                    self._update_text_editor_with_template(template)

                logger.info(
                    f"应用模板: {template.name} (类型: {template.type}, 当前模式: {current_message_type})"
                )
            else:
                logger.warning(f"未找到模板: {template_id}")

        except Exception as e:
            logger.error(f"应用模板失败: {e}")

    def _update_text_editor_with_template(self, template):
        """更新文本编辑器内容"""
        try:
            # 暂时断开信号连接，避免触发保存
            self.message_edit.textChanged.disconnect()

            if template.type == "text":
                self.message_edit.setPlainText(template.content)
            elif template.type == "rich_text":
                # 对于富文本模板，提取纯文本内容
                if isinstance(template.content, dict):
                    text_content = template.content.get("text", "")
                else:
                    text_content = str(template.content)
                self.message_edit.setPlainText(text_content)
            else:
                self.message_edit.setPlainText(str(template.content))

            # 重新连接信号
            self.message_edit.textChanged.connect(self.on_message_content_changed)

            # 保存模板内容到配置
            if self.current_selected_group:
                content = self.message_edit.toPlainText()
                self.config_manager.update_config(
                    "message_settings.default_content", content, True
                )

        except Exception as e:
            logger.error(f"更新文本编辑器失败: {e}")
            # 确保信号重新连接
            try:
                self.message_edit.textChanged.connect(self.on_message_content_changed)
            except:
                pass

    def _update_rich_text_editor_with_template(self, template):
        """更新富文本编辑器内容"""
        try:
            if template.type == "rich_text":
                # 富文本模板，直接应用到富文本编辑器
                if isinstance(template.content, dict):
                    self.rich_text_editor.load_template_content(template.content)
                else:
                    # 如果不是字典格式，尝试解析JSON
                    import json

                    try:
                        content_dict = json.loads(template.content)
                        self.rich_text_editor.load_template_content(content_dict)
                    except (json.JSONDecodeError, TypeError):
                        # 解析失败，作为纯文本处理
                        template_data = {"content": str(template.content)}
                        self.rich_text_editor.load_template_content(template_data)
            else:
                # 文本模板，转换为富文本格式
                text_content = str(template.content)
                template_data = {"content": text_content}
                self.rich_text_editor.load_template_content(template_data)

            # 保存模板内容到配置
            if self.current_selected_group:
                content_data = self.rich_text_editor.get_message_content()
                import json

                content_json = json.dumps(content_data, ensure_ascii=False)
                self.config_manager.update_config(
                    "message_settings.default_content", content_json, True
                )

        except Exception as e:
            logger.error(f"更新富文本编辑器失败: {e}")

    def create_new_group(self):
        """创建新分组"""
        dialog = GroupEditDialog(parent=self)
        dialog.group_saved.connect(self.refresh_groups)
        dialog.exec()

    def edit_group(self, group_id: str):
        """编辑分组"""
        dialog = GroupEditDialog(group_id, parent=self)
        dialog.group_saved.connect(self.refresh_groups)
        dialog.exec()

    def delete_group(self, group_id: str):
        """删除分组"""
        group = group_manager.get_group(group_id, "timing")
        if not group:
            return

        reply = ThemedMessageBoxHelper.show_question(
            self, "确认删除", f"确定要删除分组 '{group.name}' 吗？\n此操作不可撤销！"
        )

        if reply:
            if group_manager.delete_group(group_id, "timing"):
                self.refresh_groups()
                logger.info("分组删除成功")

    def start_group_task(self, group_id: str):
        """启动分组任务"""
        try:
            from core.timing_sender import timing_sender

            # 获取该分组的任务
            tasks = timing_sender.get_tasks_by_group(group_id)

            # 检查是否有暂停的任务需要继续
            paused_tasks = [task for task in tasks if task.status == "paused"]

            if paused_tasks:
                # 有暂停的任务，继续执行
                self.resume_group_task(group_id)
            else:
                # 没有暂停的任务，选择分组并创建新的定时任务
                self.choose_group(group_id)
                if self.current_selected_group == group_id:
                    self.create_timing_task()

        except Exception as e:
            logger.error(f"启动分组任务失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"启动任务失败:\n{e}")

    def stop_group_task(self, group_id: str):
        """停止分组任务"""
        try:
            from core.timing_sender import timing_sender

            # 获取该分组的任务
            tasks = timing_sender.get_tasks_by_group(group_id)

            # 停止所有运行中的任务
            stopped_count = 0
            for task in tasks:
                if task.status in ["pending", "executing", "paused"]:
                    if timing_sender.stop_task(task.task_id):
                        stopped_count += 1
                        logger.info(f"停止定时任务: {task.task_id}")

            if stopped_count > 0:
                logger.info(f"已停止 {stopped_count} 个定时任务")
                # 刷新分组显示
                self.refresh_groups()
            else:
                logger.info("没有运行中的定时任务")

        except Exception as e:
            logger.error(f"停止分组任务失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"停止任务失败:\n{e}")

    def pause_group_task(self, group_id: str):
        """暂停分组任务"""
        try:
            from core.timing_sender import timing_sender

            # 获取该分组的任务
            tasks = timing_sender.get_tasks_by_group(group_id)

            # 暂停所有运行中的任务
            paused_count = 0
            for task in tasks:
                if task.status in ["pending", "executing"]:
                    if timing_sender.pause_task(task.task_id):
                        paused_count += 1
                        logger.info(f"暂停定时任务: {task.task_id}")

            if paused_count > 0:
                logger.info(f"已暂停 {paused_count} 个定时任务")
                # 刷新分组显示
                self.refresh_groups()
            else:
                logger.info("没有可暂停的定时任务")

        except Exception as e:
            logger.error(f"暂停分组任务失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"暂停任务失败:\n{e}")

    def resume_group_task(self, group_id: str):
        """继续分组任务，使用当前的消息内容"""
        try:
            from core.timing_sender import timing_sender

            # 获取该分组的任务
            tasks = timing_sender.get_tasks_by_group(group_id)

            # 获取当前的消息内容和类型
            current_content = self.get_current_message_content()
            current_type = self.get_current_message_type()

            # 继续所有暂停的任务，并更新内容
            resumed_count = 0
            for task in tasks:
                if task.status == "paused":
                    # 恢复任务时传入新的内容
                    if timing_sender.resume_task(task.task_id, current_content, current_type):
                        resumed_count += 1
                        logger.info(f"继续定时任务: {task.task_id}，已更新内容")

            if resumed_count > 0:
                logger.info(f"已继续 {resumed_count} 个定时任务，任务将使用当前的消息内容")
                # 刷新分组显示
                self.refresh_groups()
            else:
                logger.info("没有可继续的定时任务")

        except Exception as e:
            logger.error(f"继续分组任务失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"继续任务失败:\n{e}")

    def get_current_message_content(self) -> str:
        """获取当前的消息内容"""
        message_type = self.message_type_combo.currentText()
        if message_type == "富文本消息":
            if self.rich_text_editor.has_content():
                message_data = self.rich_text_editor.get_message_content()
                send_parts = self.rich_text_editor.get_send_parts()
                message_data["send_parts"] = send_parts
                import json
                return json.dumps(message_data, ensure_ascii=False)
            else:
                return ""
        else:
            return self.message_edit.toPlainText().strip()

    def get_current_message_type(self) -> str:
        """获取当前的消息类型"""
        message_type = self.message_type_combo.currentText()
        if message_type == "富文本消息":
            return "rich_text"
        else:
            return "text"

    def delete_group(self, group_id: str):
        """删除分组"""
        group = group_manager.get_group(group_id, "timing")
        if not group:
            return

        reply = ThemedMessageBoxHelper.show_question(
            self, "确认删除", f"确定要删除分组 '{group.name}' 吗？\n\n此操作不可撤销。"
        )

        if reply:
            if group_manager.delete_group(group_id, "timing"):
                self.refresh_groups()
                logger.info(f"删除分组: {group.name}")
            else:
                logger.error("删除分组失败")

    def show_group_details(self, group_id: str):
        """显示分组详情"""
        try:
            from ui.group_details_dialog import GroupDetailsDialog

            dialog = GroupDetailsDialog(group_id, "timing", None, self)
            dialog.group_updated.connect(self.refresh_groups)
            dialog.exec()
        except Exception as e:
            logger.error(f"显示分组详情失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"显示分组详情失败:\n{e}")

    def choose_group(self, group_id: str):
        """选择分组进行发送（单选模式）"""
        # 如果已经选中了这个分组，不需要重复操作
        if self.current_selected_group == group_id:
            return

        # 更新所有分组卡片的选中状态（单选）
        for card_group_id, card in self.group_cards.items():
            if hasattr(card, "set_selected"):
                card.set_selected(card_group_id == group_id)

        # 使用配置管理器切换分组
        if self.config_manager.switch_group(group_id, "timing"):
            self.current_selected_group = group_id
            group = group_manager.get_group(group_id, "timing")
            if group:
                # 更新标题显示
                self.current_group_header.setText(f"📁 {group.name} 的设置")

                # 显示配置表单，隐藏提示
                self.no_selection_widget.setVisible(False)
                self.config_form_widget.setVisible(True)

                # 设置按钮管理器的分组选择状态
                self.button_manager.set_selected_group(True)

                # 加载分组配置到UI
                self.load_group_config_to_ui()

                # 更新按钮状态
                self.update_button_states("idle")

                logger.info(f"选择分组: {group.name} ({group_id})")

    def save_timing_settings(self):
        """保存定时发送设置"""
        if not self.current_selected_group:
            ThemedMessageBoxHelper.show_warning(self, "警告", "请先选择分组")
            return

        try:
            # 检查并处理正在运行的任务
            self._handle_running_tasks_before_save()

            # 获取当前设置
            message_type = self.message_type_combo.currentText()
            execute_date = self.date_edit.date().toString("yyyy-MM-dd")
            execute_time = self.time_edit.time().toString("HH:mm")

            # 验证消息内容
            if message_type == "富文本消息":
                try:
                    content_details = self.rich_text_editor.has_content_detailed()
                    if not content_details["has_content"]:
                        error_msg = "请输入消息内容"
                        if content_details["text_length"] == 0 and content_details["image_count"] == 0:
                            error_msg += "\n\n提示：您可以输入文字或粘贴图片"
                        ThemedMessageBoxHelper.show_warning(self, "内容验证", error_msg)
                        return

                    logger.info(f"富文本内容验证通过: 文字{content_details['text_length']}字, 图片{content_details['image_count']}张")

                except Exception as e:
                    logger.error(f"富文本内容验证失败: {e}")
                    ThemedMessageBoxHelper.show_error(self, "验证错误", f"内容验证过程中出现错误：\n{e}")
                    return
                message_data = self.rich_text_editor.get_message_content()
                # 获取按顺序的发送部分
                send_parts = self.rich_text_editor.get_send_parts()
                message_data["send_parts"] = send_parts

                # 记录发送顺序信息
                logger.info(f"保存定时任务富文本消息发送顺序:")
                for i, (text, image) in enumerate(send_parts):
                    if text:
                        logger.info(
                            f"  第{i+1}部分: 文字 ({len(text)}字) - {text[:30]}..."
                        )
                    else:
                        import os

                        image_name = os.path.basename(image) if image else "unknown"
                        logger.info(f"  第{i+1}部分: 图片 ({image_name})")

                import json

                message_content = json.dumps(message_data, ensure_ascii=False)
            else:
                message_content = self.message_edit.toPlainText().strip()
                if not message_content:
                    ThemedMessageBoxHelper.show_warning(self, "警告", "请输入消息内容")
                    return

            # 保存设置到分组配置
            group = group_manager.get_group(self.current_selected_group, "timing")
            if group:
                # 保存消息设置
                group.update_config("message_settings.default_content", message_content)
                group.update_config("message_settings.default_type", message_type)

                # 保存时间设置
                group.update_config("schedule_settings.default_date", execute_date)
                group.update_config("schedule_settings.default_time", execute_time)

                # 保存模板设置
                template_id = self.template_combo.currentData()
                if template_id:
                    group.update_config("message_settings.template_id", template_id)

                # 保存系统风控设置
                use_system_risk = self.use_system_risk_cb.isChecked()
                group.update_config("send_settings.use_system_risk_control", use_system_risk)

                # 保存分组配置
                group_manager.save_groups("timing")

                # 保存定时任务数据
                from core.timing_sender import timing_sender
                timing_sender.save_tasks()

                logger.info(
                    f"保存定时发送设置成功: 分组={group.name}, 时间={execute_date} {execute_time}"
                )

                # 刷新分组显示
                self.refresh_groups()
            else:
                logger.error("分组不存在")

        except Exception as e:
            logger.error(f"保存定时发送设置失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"保存设置失败:\n{e}")

    def _handle_running_tasks_before_save(self):
        """在保存设置前处理正在运行的任务"""
        try:
            from core.timing_sender import timing_sender

            # 获取当前分组的所有任务
            tasks = timing_sender.get_tasks_by_group(self.current_selected_group)

            # 查找正在运行的任务
            running_tasks = [
                task for task in tasks if task.status in ["pending", "executing"]
            ]

            if running_tasks:
                # 自动暂停正在运行的任务，不需要用户确认
                paused_count = 0
                for task in running_tasks:
                    if timing_sender.pause_task(task.task_id):
                        paused_count += 1
                        logger.info(f"保存设置前暂停任务: {task.task_id}")

                if paused_count > 0:
                    # 通知状态变化
                    self._notify_task_status_changed()

                    # 更新分组卡片状态
                    self.refresh_groups()

                    logger.info(f"保存设置前已暂停 {paused_count} 个任务")

        except Exception as e:
            logger.error(f"处理运行中任务失败: {e}")
            # 不阻止保存流程，只记录错误

    def _notify_task_status_changed(self):
        """通知任务状态变化"""
        try:
            # 发送信号给主窗口，通知任务状态变化
            if hasattr(self.parent(), "on_group_status_changed"):
                self.parent().on_group_status_changed(
                    self.current_selected_group, "timing"
                )

            # 如果有任务状态页面，通知刷新
            if hasattr(self.parent(), "task_status_page"):
                self.parent().task_status_page.refresh_timing_tasks()

        except Exception as e:
            logger.error(f"通知任务状态变化失败: {e}")

    def _validate_execution_time(self, execute_date: str, execute_time: str) -> bool:
        """验证执行时间是否有效"""
        try:
            # 解析设置的执行时间
            execute_datetime_str = f"{execute_date} {execute_time}:00"
            execute_datetime = datetime.strptime(
                execute_datetime_str, "%Y-%m-%d %H:%M:%S"
            )

            # 获取当前时间
            current_datetime = datetime.now()

            # 比较时间（精确到分钟）
            # 将秒数设为0，只比较到分钟级别
            execute_datetime_minute = execute_datetime.replace(second=0, microsecond=0)
            current_datetime_minute = current_datetime.replace(second=0, microsecond=0)

            logger.info(
                f"执行时间验证 - 设置时间: {execute_datetime_minute}, 当前时间: {current_datetime_minute}"
            )

            if execute_datetime_minute <= current_datetime_minute:
                # 执行时间小于或等于当前时间
                ThemedMessageBoxHelper.show_error(
                    self,
                    "执行时间错误",
                    "执行时间设置错误，不能设置过去的时间，请重新设置\n\n"
                    f"当前时间: {current_datetime.strftime('%Y-%m-%d %H:%M')}\n"
                    f"设置时间: {execute_datetime.strftime('%Y-%m-%d %H:%M')}\n\n"
                    "请选择未来的时间进行定时发送。",
                )
                logger.warning(f"执行时间验证失败 - 设置的时间不能是过去的时间")
                return False

            # 检查时间是否过于久远（超过30天），只记录警告
            time_diff = execute_datetime - current_datetime
            if time_diff.days > 30:
                logger.warning(
                    f"执行时间设置较远: {time_diff.days} 天后 ({execute_datetime.strftime('%Y-%m-%d %H:%M')})"
                )

            logger.info(f"执行时间验证通过 - 将在 {time_diff} 后执行")
            return True

        except ValueError as e:
            ThemedMessageBoxHelper.show_error(
                self,
                "时间格式错误",
                f"时间格式解析失败，请检查日期和时间设置\n\n错误详情: {e}",
            )
            logger.error(f"执行时间格式解析失败: {e}")
            return False
        except Exception as e:
            ThemedMessageBoxHelper.show_error(
                self, "时间验证错误", f"执行时间验证过程中发生错误\n\n错误详情: {e}"
            )
            logger.error(f"执行时间验证异常: {e}")
            return False

    def create_timing_task(self):
        """创建定时任务并执行发送"""
        if not self.current_selected_group:
            ThemedMessageBoxHelper.show_warning(self, "警告", "请先选择分组")
            return

        # 检查连接状态
        if not self.connector:
            logger.warning("连接器为空，显示请先连接微信提示")
            ThemedMessageBoxHelper.show_warning(self, "警告", "请先连接微信")
            return

        # 检查连接状态
        try:
            logger.info(f"检查连接器状态: {type(self.connector).__name__}")
            logger.info(
                f"连接器属性: is_connected={getattr(self.connector, 'is_connected', 'N/A')}, is_logged_in={getattr(self.connector, 'is_logged_in', 'N/A')}, connected={getattr(self.connector, 'connected', 'N/A')}"
            )

            # 首先尝试刷新连接状态
            refresh_success = self.refresh_connection_status()
            if refresh_success:
                logger.info("连接状态刷新成功，继续执行")
            else:
                logger.warning("连接状态刷新失败，尝试其他检查方法")

            # 首先尝试使用同步检查方法
            connection_ok = False
            if hasattr(self.connector, "check_connection_status"):
                if self.connector.check_connection_status():
                    logger.info("同步连接状态检查通过")
                    connection_ok = True
                else:
                    logger.warning("同步连接状态检查失败")
            # 检查连接器是否有连接状态属性
            elif hasattr(self.connector, "is_connected"):
                if self.connector.is_connected:
                    logger.info("is_connected为True，连接正常")
                    connection_ok = True
                else:
                    logger.warning("is_connected为False")
            elif hasattr(self.connector, "connected"):
                if self.connector.connected:
                    logger.info("connected为True，连接正常")
                    connection_ok = True
                else:
                    logger.warning("connected为False")
            elif hasattr(self.connector, "is_logged_in"):
                if self.connector.is_logged_in:
                    logger.info("is_logged_in为True，连接正常")
                    connection_ok = True
                else:
                    logger.warning("is_logged_in为False")
            else:
                # 如果没有连接状态属性，假设已连接
                logger.info("连接器没有连接状态属性，假设已连接")
                connection_ok = True

            # 如果连接检查失败，记录警告但继续执行
            if not connection_ok:
                logger.warning("连接状态检查失败，但继续执行任务")
        except Exception as e:
            logger.warning(f"连接状态检测异常: {e}")
            # 如果检测异常，继续执行（假设连接正常）

        # 根据消息类型获取内容
        message_type = self.message_type_combo.currentText()
        logger.info(f"当前消息类型: {message_type}")

        if message_type == "富文本消息":
            # 检查富文本编辑器是否有内容
            try:
                content_details = self.rich_text_editor.has_content_detailed()
                if not content_details["has_content"]:
                    logger.warning("富文本编辑器没有内容")
                    error_msg = "请输入消息内容"
                    if content_details["text_length"] == 0 and content_details["image_count"] == 0:
                        error_msg += "\n\n提示：您可以输入文字或粘贴图片"
                    ThemedMessageBoxHelper.show_warning(self, "内容验证", error_msg)
                    return

                logger.info(f"创建任务时内容验证通过: 文字{content_details['text_length']}字, 图片{content_details['image_count']}张")

            except Exception as e:
                logger.error(f"创建任务时内容验证失败: {e}")
                ThemedMessageBoxHelper.show_error(self, "验证错误", f"内容验证过程中出现错误：\n{e}")
                return

            message_data = self.rich_text_editor.get_message_content()
            # 获取按顺序的发送部分
            send_parts = self.rich_text_editor.get_send_parts()
            message_data["send_parts"] = send_parts

            # 记录发送顺序信息
            logger.info(f"定时任务富文本消息发送顺序:")
            for i, (text, image) in enumerate(send_parts):
                if text:
                    logger.info(f"  第{i+1}部分: 文字 ({len(text)}字) - {text[:30]}...")
                else:
                    import os

                    image_name = os.path.basename(image) if image else "unknown"
                    logger.info(f"  第{i+1}部分: 图片 ({image_name})")

            # 富文本消息需要将整个数据序列化为JSON
            import json

            message_content = json.dumps(message_data, ensure_ascii=False)
            message_type = "rich_text"
            logger.info(f"富文本消息内容长度: {len(message_content)}")
            logger.info(f"富文本消息数据: {message_data}")
        else:
            # 检查文本编辑器是否有内容
            message_content = self.message_edit.toPlainText().strip()
            if not message_content:
                logger.warning("文本编辑器没有内容")
                ThemedMessageBoxHelper.show_warning(self, "警告", "请输入消息内容")
                return
            message_type = "text"
            logger.info(f"文本消息内容长度: {len(message_content)}")

        logger.info(f"最终消息内容: '{message_content}'")

        # 获取分组成员
        group = group_manager.get_group(self.current_selected_group, "timing")
        if not group or not group.members:
            ThemedMessageBoxHelper.show_warning(self, "警告", "分组中没有成员")
            return

        # 获取执行时间
        execute_date = self.date_edit.date().toString("yyyy-MM-dd")
        execute_time = self.time_edit.time().toString("HH:mm")

        # 验证执行时间
        if not self._validate_execution_time(execute_date, execute_time):
            return

        # 移除确认发送对话框，直接创建任务

        try:
            # 创建定时任务
            self.create_simple_timing_task(
                group, message_content, execute_date, execute_time, message_type
            )

        except Exception as e:
            logger.error(f"创建定时任务失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"创建定时任务失败:\n{e}")

    def create_simple_timing_task(
        self, group, message_content, execute_date, execute_time, message_type="text"
    ):
        """创建简单定时任务"""
        # 创建定时任务
        task = timing_sender.create_task(
            self.current_selected_group,
            execute_date,
            execute_time,
            message_content,
            message_type,
        )

        if task:
            logger.info(f"定时任务创建成功: {task.task_id}")

            # 保存当前设置到分组配置
            try:
                # 保存消息设置（使用统一的内部标识）
                group.update_config("message_settings.default_content", message_content)
                group.update_config("message_settings.default_type", message_type)

                # 保存时间设置
                group.update_config("schedule_settings.default_date", execute_date)
                group.update_config("schedule_settings.default_time", execute_time)

                # 保存分组配置
                group_manager.save_groups("timing")

                # 保存定时任务数据
                timing_sender.save_tasks()

                logger.info(f"定时任务设置已保存: 分组={group.name}")
            except Exception as e:
                logger.error(f"保存定时任务设置失败: {e}")

            # 更新按钮状态
            self.update_button_states("running")
            self.update_group_card_button_states(None, self.current_selected_group)

            # 不清空输入，保持用户设置
            logger.info(
                f"定时任务创建成功！任务ID: {task.task_id}, "
                f"执行时间: {execute_date} {execute_time}, "
                f"目标分组: {group.name}, 成员数量: {len(group.members)}"
            )
        else:
            logger.error("创建定时任务失败")

    def execute_group_sending(self, group, message_content):
        """执行分组发送"""
        try:
            # 检查连接状态
            if not self.connector:
                logger.error("连接器未初始化，无法发送")
                return False

            # 更宽松的连接状态检测
            try:
                if (
                    hasattr(self.connector, "is_connected")
                    and not self.connector.is_connected
                ):
                    logger.error("微信未连接，无法发送")
                    return False

                if (
                    hasattr(self.connector, "is_logged_in")
                    and not self.connector.is_logged_in
                ):
                    logger.error("微信未登录，无法发送")
                    return False
            except Exception as e:
                logger.warning(f"连接状态检测异常: {e}")
                # 如果检测异常，继续执行

            # 获取分组的发送设置
            send_settings = self.config_manager.get_merged_send_settings()

            # 创建发送任务
            tasks = []
            for member in group.members:
                # 渲染消息内容（替换变量）
                rendered_content = self.template_manager.render_content(
                    message_content, {"name": member.name, "wxid": member.wxid}
                )

                task = SendTask(
                    wxid=member.wxid,
                    name=member.name,
                    message_type="text",
                    content=rendered_content,
                )
                tasks.append(task)

            # 清空之前的发送任务
            self.send_monitor.clear_tasks()

            # 添加任务到监控器
            self.send_monitor.add_tasks(tasks)

            # 开始监控
            self.send_monitor.start_monitoring()

            # 异步发送
            import asyncio
            from PyQt6.QtCore import QThread

            # 创建发送线程
            send_thread = QThread()

            def run_send():
                try:
                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 执行发送
                    result = loop.run_until_complete(
                        self.send_messages_async(tasks, send_settings)
                    )

                    # 清理事件循环
                    loop.close()

                    return result
                except Exception as e:
                    logger.error(f"发送线程执行失败: {e}")
                    return False

            # 在线程中执行发送
            send_thread.run = run_send
            send_thread.start()
            send_thread.wait()  # 等待完成

            logger.info(f"分组发送完成: {group.name}")
            return True

        except Exception as e:
            logger.error(f"执行分组发送失败: {e}")
            return False

    async def send_messages_async(self, tasks, send_settings):
        """异步发送消息"""
        import asyncio

        try:
            batch_size = send_settings.get("batch_size", 10)
            interval_seconds = send_settings.get("interval_seconds", 5)

            # 将任务分批处理
            total_batches = (len(tasks) + batch_size - 1) // batch_size
            logger.info(f"开始发送，共 {len(tasks)} 个任务，分为 {total_batches} 批")

            for batch_index in range(total_batches):
                # 检查是否需要停止
                if not self.send_monitor.is_running:
                    logger.info("发送被停止")
                    break

                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, len(tasks))
                batch_tasks = tasks[start_idx:end_idx]

                logger.info(
                    f"发送第 {batch_index + 1}/{total_batches} 批，共 {len(batch_tasks)} 个任务"
                )

                # 发送当前批次的任务
                for task in batch_tasks:
                    # 检查是否需要停止
                    if not self.send_monitor.is_running:
                        self.send_monitor.update_task_status(
                            task.id, SendStatus.CANCELLED
                        )
                        continue

                    # 更新任务状态为发送中
                    self.send_monitor.update_task_status(task.id, SendStatus.SENDING)

                    try:
                        # 发送消息
                        success = await self.connector.send_text_message(
                            task.wxid, task.content
                        )

                        # 更新任务状态
                        if success:
                            self.send_monitor.update_task_status(
                                task.id, SendStatus.SUCCESS
                            )
                            logger.info(f"发送成功: {task.name}")
                        else:
                            self.send_monitor.update_task_status(
                                task.id, SendStatus.FAILED, "发送失败"
                            )
                            logger.warning(f"发送失败: {task.name}")

                    except Exception as e:
                        error_msg = str(e)
                        self.send_monitor.update_task_status(
                            task.id, SendStatus.FAILED, error_msg
                        )
                        logger.error(f"发送消息失败 {task.name}: {error_msg}")

                    # 消息间隔
                    if task != batch_tasks[-1]:  # 不是批次内最后一个任务
                        await asyncio.sleep(interval_seconds)

                # 批次间隔
                if batch_index < total_batches - 1:  # 不是最后一批
                    logger.info(
                        f"第 {batch_index + 1} 批发送完成，等待 {interval_seconds} 秒"
                    )
                    await asyncio.sleep(interval_seconds)

            return True

        except Exception as e:
            logger.error(f"发送消息过程中出错: {e}")
            return False

    def on_send_progress(self, completed, total):
        """发送进度更新"""
        logger.info(f"发送进度: {completed}/{total}")
        # 可以在这里更新UI进度显示

    def on_send_completed(self, statistics):
        """发送完成"""
        logger.info(f"发送完成: {statistics}")
        self.update_button_states("completed")

        success_rate = statistics.get("success_rate", 0)
        logger.info(
            f"分组发送已完成！总数: {statistics.get('total', 0)}, "
            f"成功: {statistics.get('success', 0)}, "
            f"失败: {statistics.get('failed', 0)}, "
            f"成功率: {success_rate:.1f}%"
        )

    def on_task_started(self, task_id: str):
        """任务开始"""
        logger.info(f"定时任务开始: {task_id}")

        # 重置对应分组卡片的实时进度条
        try:
            from core.timing_sender import timing_sender

            task = timing_sender.get_task(task_id)
            if task and task.group_id in self.group_cards:
                card = self.group_cards[task.group_id]
                card.reset_realtime_progress()
                logger.info(f"重置分组 {task.group_id} 实时进度条")
        except Exception as e:
            logger.error(f"重置实时进度条失败: {e}")

        # 刷新相关分组的进度显示
        self.refresh_group_progress()

    def on_task_progress(self, task_id: str, sent: int, total: int):
        """任务进度更新"""
        logger.debug(f"定时任务进度: {task_id} - {sent}/{total}")

        # 获取任务对应的分组ID
        try:
            from core.timing_sender import timing_sender

            task = timing_sender.get_task(task_id)
            if task and task.group_id in self.group_cards:
                # 更新对应分组卡片的实时进度条
                card = self.group_cards[task.group_id]
                card.update_realtime_progress_value(sent, total)
                logger.debug(f"更新分组 {task.group_id} 实时进度: {sent}/{total}")
        except Exception as e:
            logger.error(f"更新实时进度失败: {e}")

        # 刷新相关分组的总体进度显示
        self.refresh_group_progress()

    def on_task_completed(self, task_id: str):
        """任务完成"""
        logger.info(f"定时任务完成: {task_id}")
        # 刷新相关分组的进度显示
        self.refresh_group_progress()

    def on_task_failed(self, task_id: str, error: str):
        """任务失败"""
        logger.error(f"定时任务失败: {task_id} - {error}")

    def refresh_group_progress(self):
        """刷新分组进度显示"""
        for group_id, card in self.group_cards.items():
            card.update_progress()
            # 同时更新按钮状态
            self.update_group_card_button_states(card, group_id)

    def load_group_config_to_ui(self):
        """加载分组配置到UI"""
        if not self.current_selected_group:
            return

        try:
            # 设置加载标志，避免触发保存
            self._loading_config = True
            # 加载风控设置（从分组独立配置中加载）
            use_system_risk = group.get_config("send_settings.use_system_risk_control", True)
            # 暂时断开信号连接，避免触发保存
            try:
                self.use_system_risk_cb.stateChanged.disconnect()
            except:
                pass  # 如果信号未连接，忽略错误
            # 确保布尔值正确转换
            self.use_system_risk_cb.setChecked(bool(use_system_risk))
            # 立即重新连接信号，但使用更安全的方式
            self.use_system_risk_cb.stateChanged.connect(self.on_risk_control_changed)

            # 获取当前分组对象，从分组独立配置中加载设置
            from core.group_manager import group_manager
            group = group_manager.get_group(self.current_selected_group, "timing")

            if not group:
                logger.error(f"分组不存在: {self.current_selected_group}")
                return

            # 加载消息设置（从分组独立配置中加载）
            template_id = group.get_config("message_settings.template_id", "")
            if template_id:
                # 设置模板选择
                for i in range(self.template_combo.count()):
                    if self.template_combo.itemData(i) == template_id:
                        self.template_combo.setCurrentIndex(i)
                        break
            else:
                # 没有模板时设置为默认
                self.template_combo.setCurrentIndex(0)

            # 从分组独立配置中加载消息内容和类型
            default_content = group.get_config("message_settings.default_content", "")
            default_type = group.get_config("message_settings.default_type", "text")

            # 智能检测消息类型（防止类型和内容不匹配）
            actual_type = self._detect_message_type(default_content, default_type)

            # 设置消息类型
            if actual_type == "rich_text":
                self.message_type_combo.setCurrentText("富文本消息")
                self.message_editor_stack.setCurrentWidget(self.rich_text_editor)
            else:
                self.message_type_combo.setCurrentText("文本消息")
                self.message_editor_stack.setCurrentWidget(self.message_edit)

            # 暂时断开信号连接，避免触发保存
            self.rich_text_editor.content_changed.disconnect()
            self.message_edit.textChanged.disconnect()

            if default_content:
                if actual_type == "rich_text":
                    # 加载富文本内容
                    try:
                        import json
                        if isinstance(default_content, str):
                            content_data = json.loads(default_content)
                        else:
                            content_data = default_content

                        self.rich_text_editor.load_template_content(content_data)
                        logger.info(f"成功加载富文本内容: {len(content_data)} 个元素")
                    except Exception as e:
                        # 如果解析失败，记录错误但不清空编辑器
                        logger.warning(
                            f"富文本内容解析失败: {e}, 内容: {default_content[:100]}..."
                        )
                        # 清空编辑器
                        self.rich_text_editor.clear_content()
                else:
                    # 加载普通文本内容
                    if isinstance(default_content, str):
                        self.message_edit.setPlainText(default_content)
                    else:
                        # 如果是富文本格式，提取纯文本
                        try:
                            import json
                            if isinstance(default_content, dict):
                                content_data = default_content
                            else:
                                content_data = json.loads(str(default_content))
                            text_content = content_data.get("plain_text", "")
                            self.message_edit.setPlainText(text_content)
                        except:
                            self.message_edit.setPlainText("")
            else:
                # 没有内容时清空编辑器
                if actual_type == "rich_text":
                    self.rich_text_editor.clear_content()
                else:
                    self.message_edit.setPlainText("")

            # 重新连接信号
            self.rich_text_editor.content_changed.connect(self.on_message_content_changed)
            self.message_edit.textChanged.connect(self.on_message_content_changed)

            # 加载时间设置（从分组独立配置中加载）
            default_time = group.get_config("schedule_settings.default_time", "09:00")
            try:
                time_obj = datetime.strptime(default_time, "%H:%M").time()
                self.time_edit.setTime(time_obj)
            except:
                # 如果时间格式错误，使用默认时间
                self.time_edit.setTime(datetime.strptime("09:00", "%H:%M").time())

            # 加载日期设置（从分组独立配置中加载）
            default_date = group.get_config("schedule_settings.default_date", "")
            if default_date:
                try:
                    date_obj = datetime.strptime(default_date, "%Y-%m-%d").date()
                    self.date_edit.setDate(date_obj)
                except:
                    # 如果日期格式错误，使用今天
                    from datetime import date
                    self.date_edit.setDate(date.today())
            else:
                # 没有保存的日期时，使用今天
                from datetime import date
                self.date_edit.setDate(date.today())

            logger.info(f"加载分组 {group.name} 的独立配置到UI完成")

        except Exception as e:
            logger.error(f"加载分组配置失败: {e}")
        finally:
            # 清除加载标志
            self._loading_config = False

    def _detect_message_type(self, content, declared_type):
        """
        智能检测消息类型

        Args:
            content: 消息内容
            declared_type: 声明的类型

        Returns:
            实际的消息类型 ("text" 或 "rich_text")
        """
        try:
            # 如果内容为空，使用声明的类型
            if not content:
                return declared_type

            # 如果内容是字符串，尝试解析为JSON
            if isinstance(content, str):
                # 检查是否是JSON格式的富文本内容
                if content.strip().startswith('{"type": "rich_text"'):
                    try:
                        import json
                        json.loads(content)  # 验证JSON格式
                        logger.debug("检测到富文本JSON格式内容")
                        return "rich_text"
                    except json.JSONDecodeError:
                        logger.warning("内容看起来像富文本JSON但解析失败，当作普通文本处理")
                        return "text"
                else:
                    # 普通字符串内容
                    return "text"

            # 如果内容是字典格式
            elif isinstance(content, dict):
                if content.get("type") == "rich_text":
                    logger.debug("检测到富文本字典格式内容")
                    return "rich_text"
                else:
                    return "text"

            # 其他情况使用声明的类型
            else:
                logger.debug(f"未知内容格式，使用声明类型: {declared_type}")
                return declared_type

        except Exception as e:
            logger.error(f"检测消息类型失败: {e}")
            return declared_type

    def on_group_switched(self, group_id: str, group_type: str):
        """分组切换事件"""
        logger.info(f"分组切换: {group_id}")
        # UI已经在choose_group中更新了

    def on_config_changed(
        self, group_id: str, group_type: str, config_path: str, value
    ):
        """配置更改事件"""
        logger.debug(f"配置更改: {group_id} - {config_path} = {value}")

    def on_time_changed(self, time):
        """时间更改事件"""
        if self.current_selected_group:
            # 避免在加载配置时触发保存
            if hasattr(self, "_loading_config") and self._loading_config:
                return

            try:
                # 获取当前分组对象
                from core.group_manager import group_manager
                group = group_manager.get_group(self.current_selected_group, "timing")

                if group:
                    time_str = time.toString("HH:mm")
                    # 保存到分组的独立配置中
                    group.update_config("schedule_settings.default_time", time_str)
                    group_manager.save_groups("timing")

                    # 保存定时任务数据
                    from core.timing_sender import timing_sender
                    timing_sender.save_tasks()

                    logger.debug(f"分组 {group.name} 默认时间已更新: {time_str}")
            except Exception as e:
                logger.error(f"保存时间设置失败: {e}")

    def on_interval_changed(self, value):
        """发送间隔更改事件"""
        if self.current_selected_group:
            self.config_manager.update_config("send_settings.interval_seconds", value)

    def on_batch_size_changed(self, value):
        """批量大小更改事件"""
        if self.current_selected_group:
            self.config_manager.update_config("send_settings.batch_size", value)

    def on_risk_control_changed(self, state):
        """风控设置更改事件"""
        # 避免在加载配置时触发保存
        if hasattr(self, "_loading_config") and self._loading_config:
            logger.debug("跳过风控设置保存：正在加载配置")
            return

        if not self.current_selected_group:
            logger.debug("跳过风控设置保存：未选择分组")
            return

        try:
            # 获取当前分组对象
            from core.group_manager import group_manager
            group = group_manager.get_group(self.current_selected_group, "timing")

            if group:
                use_system = state == Qt.CheckState.Checked
                # 保存到分组的独立配置中
                group.update_config("send_settings.use_system_risk_control", use_system)
                group_manager.save_groups("timing")

                # 保存定时任务数据
                from core.timing_sender import timing_sender
                timing_sender.save_tasks()

                logger.debug(f"分组 {group.name} 风控设置已更新: {use_system}")
            else:
                logger.error(f"分组不存在: {self.current_selected_group}")

        except Exception as e:
            logger.error(f"保存风控设置失败: {e}")

    def on_message_type_changed(self, message_type: str):
        """消息类型切换处理"""
        if message_type == "富文本消息":
            self.message_editor_stack.setCurrentWidget(self.rich_text_editor)
        else:
            self.message_editor_stack.setCurrentWidget(self.message_edit)

        # 切换消息类型后，重新应用当前选中的模板
        self._apply_current_template_to_new_editor(message_type)

        # 触发内容变化信号
        self.on_message_content_changed()

    def _apply_current_template_to_new_editor(self, message_type: str):
        """在切换编辑器后应用当前选中的模板"""
        try:
            template_id = self.template_combo.currentData()

            if not template_id:  # 手动输入模式
                return

            from core.message_template import MessageTemplate

            template_manager = MessageTemplate()
            template = template_manager.get_template(template_id)

            if template:
                if message_type == "富文本消息":
                    # 切换到富文本编辑器，应用模板
                    self._update_rich_text_editor_with_template(template)
                else:
                    # 切换到文本编辑器，应用模板
                    self._update_text_editor_with_template(template)

                logger.info(f"消息类型切换后重新应用模板: {template.name}")

        except Exception as e:
            logger.error(f"切换编辑器后应用模板失败: {e}")

    def on_message_content_changed(self):
        """消息内容更改事件"""
        if not self.current_selected_group:
            return

        # 避免在加载配置时触发保存
        if hasattr(self, "_loading_config") and self._loading_config:
            return

        try:
            # 获取当前分组对象
            from core.group_manager import group_manager
            group = group_manager.get_group(self.current_selected_group, "timing")

            if not group:
                logger.error(f"分组不存在: {self.current_selected_group}")
                return

            # 根据当前编辑器类型获取内容和类型
            if self.message_type_combo.currentText() == "富文本消息":
                # 富文本消息处理
                content_data = self.rich_text_editor.get_message_content()
                import json
                content = json.dumps(content_data, ensure_ascii=False)
                message_type = "rich_text"
            else:
                # 普通文本消息处理
                content = self.message_edit.toPlainText()
                message_type = "text"

            # 保存到分组的独立配置中
            group.update_config("message_settings.default_type", message_type)
            group.update_config("message_settings.default_content", content)

            # 立即保存分组配置
            group_manager.save_groups("timing")

            # 保存定时任务数据
            from core.timing_sender import timing_sender
            timing_sender.save_tasks()

            # 使用用户友好的格式记录日志
            if message_type == "rich_text":
                from ui.rich_text_editor import RichTextMessageEditor
                display_content = RichTextMessageEditor.format_rich_text_for_display(content_data)
                logger.debug(f"分组 {group.name} 消息内容已更新: {display_content}")
            else:
                logger.debug(f"分组 {group.name} 消息内容已更新: 类型={message_type}, 长度={len(str(content))}")

        except Exception as e:
            logger.error(f"保存分组消息内容失败: {e}")
            # 不抛出异常，避免影响用户体验

    def update_button_states(self, task_status: str):
        """更新按钮状态（使用统一的按钮状态管理器）"""
        # 将字符串状态映射到枚举
        status_enum = map_task_status_to_enum(task_status)

        # 使用按钮状态管理器更新按钮
        self.button_manager.set_task_status(status_enum)

    def update_group_card_button_states(self, card, group_id: str):
        """更新分组卡片按钮状态"""
        try:
            # 如果没有传入card，尝试从group_cards中获取
            if card is None and group_id in self.group_cards:
                card = self.group_cards[group_id]

            # 如果仍然没有card，跳过更新
            if card is None:
                logger.debug(f"未找到分组卡片: {group_id}")
                return

            from core.timing_sender import timing_sender

            # 获取该分组的任务
            tasks = timing_sender.get_tasks_by_group(group_id)

            # 检查是否有运行中的任务
            has_running_task = any(
                task.status in ["pending", "executing"] for task in tasks
            )
            has_paused_task = any(task.status == "paused" for task in tasks)

            # 更新卡片按钮状态
            card.update_button_states(has_running_task, has_paused_task)

        except Exception as e:
            logger.error(f"更新分组卡片按钮状态失败: {e}")
