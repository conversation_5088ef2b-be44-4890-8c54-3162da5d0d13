#!/usr/bin/env python3
"""
日志清空重复执行修复验证工具
验证清空日志文件功能不再重复执行
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QPushButton, QLabel, QTextEdit)
from PyQt6.QtCore import Qt
from ui.themed_dialog_base import ThemedDialogBase


class LogClearTestDialog(ThemedDialogBase):
    """日志清空测试对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("日志清空重复执行修复验证")
        self.setFixedSize(600, 400)
        self.call_count = 0  # 记录调用次数
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("日志清空重复执行修复验证")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明
        info_label = QLabel(
            "此工具用于验证清空日志文件功能是否还会重复执行。\n"
            "修复前：点击一次按钮会执行两次清空操作\n"
            "修复后：点击一次按钮只执行一次清空操作"
        )
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 测试按钮
        test_button = QPushButton("🧪 测试清空日志文件")
        test_button.clicked.connect(self.test_clear_log_files)
        test_button.setProperty("class", "danger")
        layout.addWidget(test_button)
        
        # 调用计数显示
        self.call_count_label = QLabel("调用次数: 0")
        self.call_count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.call_count_label)
        
        # 日志显示
        self.log_display = QTextEdit()
        self.log_display.setMaximumHeight(200)
        layout.addWidget(self.log_display)
        
        # 重置按钮
        reset_button = QPushButton("🔄 重置计数")
        reset_button.clicked.connect(self.reset_count)
        layout.addWidget(reset_button)
        
        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)
        
    def test_clear_log_files(self):
        """测试清空日志文件功能"""
        self.call_count += 1
        self.call_count_label.setText(f"调用次数: {self.call_count}")
        
        # 记录调用
        log_message = f"第 {self.call_count} 次调用 test_clear_log_files 方法"
        self.log_display.append(log_message)
        print(log_message)  # 同时输出到控制台
        
        # 模拟清空操作（不实际清空文件）
        self.log_display.append("  -> 模拟清空日志文件操作")
        self.log_display.append("  -> 操作完成")
        
        # 检查是否重复调用
        if self.call_count > 1:
            # 检查是否在短时间内多次调用（可能是重复执行）
            import time
            current_time = time.time()
            if not hasattr(self, 'last_call_time'):
                self.last_call_time = current_time
            else:
                time_diff = current_time - self.last_call_time
                if time_diff < 0.1:  # 如果两次调用间隔小于0.1秒，可能是重复执行
                    self.log_display.append(f"  ⚠️ 警告：检测到可能的重复执行！间隔时间: {time_diff:.3f}秒")
                    self.log_display.append("  ❌ 修复可能未生效，仍存在重复执行问题")
                else:
                    self.log_display.append(f"  ✅ 正常调用，间隔时间: {time_diff:.3f}秒")
                self.last_call_time = current_time
        else:
            import time
            self.last_call_time = time.time()
            self.log_display.append("  ✅ 首次调用，正常")
        
        self.log_display.append("")  # 空行分隔
        
    def reset_count(self):
        """重置计数"""
        self.call_count = 0
        self.call_count_label.setText("调用次数: 0")
        self.log_display.clear()
        if hasattr(self, 'last_call_time'):
            delattr(self, 'last_call_time')
        self.log_display.append("计数已重置")


def check_code_fix():
    """检查代码修复情况"""
    print("🔍 检查日志清空重复执行修复...")
    
    main_window_file = Path(__file__).parent / "ui" / "main_window.py"
    
    if not main_window_file.exists():
        print("❌ main_window.py文件不存在")
        return False
    
    try:
        content = main_window_file.read_text(encoding='utf-8')
        
        # 检查重复连接的情况
        connect_lines = []
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'clear_log_files_btn.clicked.connect(self.clear_log_files)' in line:
                connect_lines.append(f"第{i}行: {line.strip()}")
        
        print(f"\n📊 检查结果:")
        print(f"发现 {len(connect_lines)} 处事件连接:")
        for line in connect_lines:
            print(f"  - {line}")
        
        if len(connect_lines) == 1:
            print("\n✅ 修复成功！只有一处事件连接")
            print("📋 修复内容:")
            print("  - 移除了按钮创建时的重复事件连接")
            print("  - 保留了connect_signals方法中的统一事件连接")
            print("  - 现在点击按钮只会执行一次清空操作")
            return True
        elif len(connect_lines) > 1:
            print(f"\n❌ 仍存在重复连接！发现 {len(connect_lines)} 处连接")
            print("需要进一步修复")
            return False
        else:
            print("\n❌ 未找到事件连接，可能存在其他问题")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False


def test_log_clear_fix():
    """测试日志清空修复"""
    app = QApplication(sys.argv)
    
    print("🧪 日志清空重复执行修复测试")
    print("=" * 50)
    
    # 先检查代码修复情况
    code_fixed = check_code_fix()
    
    if code_fixed:
        print("\n🎉 代码修复验证通过！")
        print("\n📋 测试说明:")
        print("1. 点击'测试清空日志文件'按钮")
        print("2. 观察调用次数是否只增加1")
        print("3. 检查是否有重复执行警告")
        print("4. 正常情况下每次点击只应该调用一次")
        
        # 创建测试对话框
        dialog = LogClearTestDialog()
        dialog.show()
        
        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted:
            print("✅ 测试完成")
    else:
        print("\n❌ 代码修复验证失败，请检查修复内容")
    
    sys.exit(0)


def main():
    """主函数"""
    print("🚀 日志清空重复执行修复工具")
    print("=" * 50)
    print("\n📋 问题描述:")
    print("  清空日志文件的逻辑执行了2遍")
    print("  原因：按钮事件被重复连接")
    
    print("\n🔧 修复内容:")
    print("  移除了按钮创建时的重复事件连接")
    print("  保留connect_signals方法中的统一连接")
    
    print("\n✅ 修复完成！现在可以运行测试:")
    print("python test_log_clear_fix.py --test")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_log_clear_fix()


if __name__ == "__main__":
    main()
