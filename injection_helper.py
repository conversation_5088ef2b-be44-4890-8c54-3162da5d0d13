#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注入帮助模块 - 确保在打包环境中正确找到注入工具
"""

import os
import sys
import shutil
import tempfile
from pathlib import Path

def get_resource_path(relative_path):
    """获取资源文件路径，兼容打包和开发环境"""
    try:
        # PyInstaller打包后的路径
        base_path = Path(sys._MEIPASS)
    except AttributeError:
        # 开发环境路径
        base_path = Path(__file__).parent
    
    return base_path / relative_path

def extract_injection_tools():
    """提取注入工具到临时目录"""
    print("🔧 提取注入工具...")
    
    # 创建临时目录
    temp_dir = Path(tempfile.gettempdir()) / "MeetSpaceWeChatSender"
    temp_dir.mkdir(exist_ok=True)
    
    tools_dir = temp_dir / "tools"
    wxhelper_dir = temp_dir / "wxhelper_files"
    
    tools_dir.mkdir(exist_ok=True)
    wxhelper_dir.mkdir(exist_ok=True)
    
    # 提取注入工具
    try:
        injector_src = get_resource_path("tools/Injector.exe")
        injector_dst = tools_dir / "Injector.exe"
        
        if injector_src.exists():
            shutil.copy2(injector_src, injector_dst)
            print(f"  ✅ 提取注入工具: {injector_dst}")
        else:
            print(f"  ❌ 未找到注入工具: {injector_src}")
            return None
    except Exception as e:
        print(f"  ❌ 提取注入工具失败: {e}")
        return None
    
    # 提取DLL文件
    dll_files = ["wxhelper.dll", "wxhelper_latest.dll"]
    for dll_file in dll_files:
        try:
            dll_src = get_resource_path(f"wxhelper_files/{dll_file}")
            dll_dst = wxhelper_dir / dll_file
            
            if dll_src.exists():
                shutil.copy2(dll_src, dll_dst)
                print(f"  ✅ 提取DLL: {dll_dst}")
            else:
                print(f"  ⚠️  未找到DLL: {dll_src}")
        except Exception as e:
            print(f"  ❌ 提取DLL失败: {e}")
    
    return {
        "tools_dir": str(tools_dir),
        "wxhelper_dir": str(wxhelper_dir),
        "injector_path": str(injector_dst)
    }

def get_injection_paths():
    """获取注入工具路径"""
    return extract_injection_tools()

if __name__ == "__main__":
    paths = extract_injection_tools()
    if paths:
        print("✅ 注入工具提取成功")
        for key, value in paths.items():
            print(f"  {key}: {value}")
    else:
        print("❌ 注入工具提取失败")
