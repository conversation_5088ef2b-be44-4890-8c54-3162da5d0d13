"""
配置备份管理器

实现配置的自动备份和手动导入/导出功能。
"""

import json
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from utils.logger import setup_logger

logger = setup_logger("config_backup_manager")


@dataclass
class BackupInfo:
    """备份信息"""

    filename: str
    filepath: str
    timestamp: datetime
    size: int
    description: str


class ConfigBackupManager:
    """配置备份管理器"""

    def __init__(self):
        self.backup_dir = Path("config_backups")
        self.backup_dir.mkdir(exist_ok=True)

        # 配置文件路径
        self.config_paths = {
            "timing_groups": "config/timing_groups.json",
            "loop_groups": "config/loop_groups.json",
            "message_groups": "config/message_groups.json",
            "system_config": "config/config.json",
            "message_templates": "config/message_templates.json",
            "group_progress": "config/group_progress.json",
        }

        logger.info("配置备份管理器初始化完成")

    def create_backup(self, description: str = "") -> Optional[str]:
        """
        创建配置备份

        Args:
            description: 备份描述

        Returns:
            备份文件路径，失败返回None
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"config_backup_{timestamp}.json"
            backup_filepath = self.backup_dir / backup_filename

            # 收集所有配置数据
            backup_data = {
                "backup_info": {
                    "timestamp": timestamp,
                    "description": description
                    or f"自动备份 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "version": "1.0",
                },
                "configs": {},
            }

            # 备份各个配置文件
            for config_name, config_path in self.config_paths.items():
                if os.path.exists(config_path):
                    try:
                        with open(config_path, "r", encoding="utf-8") as f:
                            backup_data["configs"][config_name] = json.load(f)
                        logger.debug(f"已备份配置: {config_name}")
                    except Exception as e:
                        logger.warning(f"备份配置 {config_name} 失败: {e}")
                        backup_data["configs"][config_name] = None
                else:
                    backup_data["configs"][config_name] = None

            # 写入备份文件
            with open(backup_filepath, "w", encoding="utf-8") as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            logger.info(f"配置备份创建成功: {backup_filename}")
            return str(backup_filepath)

        except Exception as e:
            logger.error(f"创建配置备份失败: {e}")
            return None

    def restore_backup(self, backup_filepath: str) -> bool:
        """
        恢复配置备份

        Args:
            backup_filepath: 备份文件路径

        Returns:
            是否恢复成功
        """
        try:
            if not os.path.exists(backup_filepath):
                logger.error(f"备份文件不存在: {backup_filepath}")
                return False

            # 读取备份数据
            with open(backup_filepath, "r", encoding="utf-8") as f:
                backup_data = json.load(f)

            if "configs" not in backup_data:
                logger.error("备份文件格式错误")
                return False

            # 创建当前配置的备份（作为恢复前的保护）
            protection_backup = self.create_backup("恢复前保护备份")
            if protection_backup:
                logger.info(f"已创建保护备份: {protection_backup}")

            # 恢复各个配置文件
            restored_count = 0
            for config_name, config_data in backup_data["configs"].items():
                if config_name in self.config_paths and config_data is not None:
                    config_path = self.config_paths[config_name]

                    try:
                        # 确保目录存在
                        os.makedirs(os.path.dirname(config_path), exist_ok=True)

                        # 写入配置文件
                        with open(config_path, "w", encoding="utf-8") as f:
                            json.dump(config_data, f, ensure_ascii=False, indent=2)

                        restored_count += 1
                        logger.debug(f"已恢复配置: {config_name}")

                    except Exception as e:
                        logger.error(f"恢复配置 {config_name} 失败: {e}")

            backup_info = backup_data.get("backup_info", {})
            logger.info(f"配置恢复完成: 成功恢复 {restored_count} 个配置文件")
            logger.info(f"备份信息: {backup_info.get('description', '无描述')}")

            return restored_count > 0

        except Exception as e:
            logger.error(f"恢复配置备份失败: {e}")
            return False

    def get_backup_list(self) -> List[BackupInfo]:
        """
        获取备份列表

        Returns:
            备份信息列表
        """
        backups = []

        try:
            for backup_file in self.backup_dir.glob("config_backup_*.json"):
                try:
                    # 获取文件信息
                    stat = backup_file.stat()

                    # 尝试读取备份描述
                    description = "配置备份"
                    try:
                        with open(backup_file, "r", encoding="utf-8") as f:
                            backup_data = json.load(f)
                            backup_info = backup_data.get("backup_info", {})
                            description = backup_info.get("description", "配置备份")
                    except:
                        pass

                    # 从文件名解析时间戳
                    timestamp_str = backup_file.stem.replace("config_backup_", "")
                    try:
                        timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                    except:
                        timestamp = datetime.fromtimestamp(stat.st_mtime)

                    backup_info = BackupInfo(
                        filename=backup_file.name,
                        filepath=str(backup_file),
                        timestamp=timestamp,
                        size=stat.st_size,
                        description=description,
                    )

                    backups.append(backup_info)

                except Exception as e:
                    logger.warning(f"读取备份文件信息失败 {backup_file}: {e}")

            # 按时间戳降序排序
            backups.sort(key=lambda x: x.timestamp, reverse=True)

        except Exception as e:
            logger.error(f"获取备份列表失败: {e}")

        return backups

    def delete_backup(self, backup_filepath: str) -> bool:
        """
        删除备份文件

        Args:
            backup_filepath: 备份文件路径

        Returns:
            是否删除成功
        """
        try:
            if os.path.exists(backup_filepath):
                os.remove(backup_filepath)
                logger.info(f"已删除备份文件: {os.path.basename(backup_filepath)}")
                return True
            else:
                logger.warning(f"备份文件不存在: {backup_filepath}")
                return False

        except Exception as e:
            logger.error(f"删除备份文件失败: {e}")
            return False

    def export_backup(self, backup_filepath: str, export_path: str) -> bool:
        """
        导出备份到指定位置

        Args:
            backup_filepath: 源备份文件路径
            export_path: 导出目标路径

        Returns:
            是否导出成功
        """
        try:
            if not os.path.exists(backup_filepath):
                logger.error(f"源备份文件不存在: {backup_filepath}")
                return False

            shutil.copy2(backup_filepath, export_path)
            logger.info(f"备份导出成功: {export_path}")
            return True

        except Exception as e:
            logger.error(f"导出备份失败: {e}")
            return False

    def import_backup(self, import_path: str) -> Optional[str]:
        """
        导入外部备份文件

        Args:
            import_path: 导入文件路径

        Returns:
            导入后的备份文件路径，失败返回None
        """
        try:
            if not os.path.exists(import_path):
                logger.error(f"导入文件不存在: {import_path}")
                return None

            # 验证备份文件格式
            try:
                with open(import_path, "r", encoding="utf-8") as f:
                    backup_data = json.load(f)

                if "configs" not in backup_data:
                    logger.error("导入文件格式错误：缺少configs字段")
                    return None

            except json.JSONDecodeError:
                logger.error("导入文件不是有效的JSON格式")
                return None

            # 生成新的备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"config_backup_imported_{timestamp}.json"
            backup_filepath = self.backup_dir / backup_filename

            # 复制文件
            shutil.copy2(import_path, backup_filepath)

            logger.info(f"备份导入成功: {backup_filename}")
            return str(backup_filepath)

        except Exception as e:
            logger.error(f"导入备份失败: {e}")
            return None

    def cleanup_old_backups(self, keep_count: int = 10) -> int:
        """
        清理旧备份文件

        Args:
            keep_count: 保留的备份数量

        Returns:
            删除的备份数量
        """
        try:
            backups = self.get_backup_list()

            if len(backups) <= keep_count:
                return 0

            # 删除多余的备份
            deleted_count = 0
            for backup in backups[keep_count:]:
                if self.delete_backup(backup.filepath):
                    deleted_count += 1

            logger.info(f"清理完成: 删除了 {deleted_count} 个旧备份")
            return deleted_count

        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")
            return 0


# 全局备份管理器实例
backup_manager = ConfigBackupManager()
