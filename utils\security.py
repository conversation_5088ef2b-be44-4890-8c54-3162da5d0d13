"""
安全工具模块

提供数据加密、输入验证和安全相关功能。
"""

import hashlib
import hmac
import os
import secrets
import time
from typing import Any, Dict, Optional, Tuple

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from utils.logger import setup_logger

logger = setup_logger("security")


class SecurityManager:
    """安全管理器"""

    def __init__(self, master_key: Optional[str] = None):
        self.master_key = master_key or self._generate_master_key()
        self._cipher_suite = None
        self._init_cipher()

    def _generate_master_key(self) -> str:
        """生成主密钥"""
        return base64.urlsafe_b64encode(os.urandom(32)).decode()

    def _init_cipher(self) -> None:
        """初始化加密套件"""
        try:
            # 使用主密钥派生加密密钥
            key_bytes = self.master_key.encode()
            salt = b"wx_mass_sender_salt"  # 在生产环境中应该使用随机盐

            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )

            key = base64.urlsafe_b64encode(kdf.derive(key_bytes))
            self._cipher_suite = Fernet(key)

        except Exception as e:
            logger.error(f"初始化加密套件失败: {e}")
            raise

    def encrypt_data(self, data: str) -> str:
        """
        加密数据

        Args:
            data: 要加密的数据

        Returns:
            加密后的数据（Base64编码）
        """
        try:
            if not self._cipher_suite:
                raise ValueError("加密套件未初始化")

            encrypted_data = self._cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()

        except Exception as e:
            logger.error(f"数据加密失败: {e}")
            raise

    def decrypt_data(self, encrypted_data: str) -> str:
        """
        解密数据

        Args:
            encrypted_data: 加密的数据（Base64编码）

        Returns:
            解密后的数据
        """
        try:
            if not self._cipher_suite:
                raise ValueError("加密套件未初始化")

            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode()

        except Exception as e:
            logger.error(f"数据解密失败: {e}")
            raise

    def hash_password(
        self, password: str, salt: Optional[str] = None
    ) -> Tuple[str, str]:
        """
        哈希密码

        Args:
            password: 原始密码
            salt: 盐值，如果为None则自动生成

        Returns:
            (哈希值, 盐值)
        """
        if salt is None:
            salt = secrets.token_hex(16)

        # 使用PBKDF2进行密码哈希
        password_hash = hashlib.pbkdf2_hmac(
            "sha256", password.encode(), salt.encode(), 100000  # 迭代次数
        )

        return password_hash.hex(), salt

    def verify_password(self, password: str, hash_value: str, salt: str) -> bool:
        """
        验证密码

        Args:
            password: 输入的密码
            hash_value: 存储的哈希值
            salt: 盐值

        Returns:
            密码是否正确
        """
        try:
            computed_hash, _ = self.hash_password(password, salt)
            return hmac.compare_digest(computed_hash, hash_value)
        except Exception as e:
            logger.error(f"密码验证失败: {e}")
            return False


class InputSanitizer:
    """输入清理器"""

    @staticmethod
    def sanitize_string(input_str: str, max_length: int = 1000) -> str:
        """
        清理字符串输入

        Args:
            input_str: 输入字符串
            max_length: 最大长度

        Returns:
            清理后的字符串
        """
        if not isinstance(input_str, str):
            return ""

        # 移除危险的HTML标签和脚本
        dangerous_patterns = [
            "<script>",
            "</script>",
            "<iframe>",
            "</iframe>",
            "<object>",
            "</object>",
            "<embed>",
            "</embed>",
            "javascript:",
            "vbscript:",
            "onload=",
            "onclick=",
            "onerror=",
        ]

        sanitized = input_str
        for pattern in dangerous_patterns:
            sanitized = sanitized.replace(pattern, "")
            sanitized = sanitized.replace(pattern.upper(), "")

        # 移除控制字符
        sanitized = "".join(
            char for char in sanitized if ord(char) >= 32 or char in "\t\n\r"
        )

        # 限制长度
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]

        # 移除首尾空白
        return sanitized.strip()

    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        清理文件名

        Args:
            filename: 原始文件名

        Returns:
            安全的文件名
        """
        if not filename:
            return "untitled"

        # 移除危险字符
        dangerous_chars = ["/", "\\", ":", "*", "?", '"', "<", ">", "|", "\0"]
        sanitized = filename

        for char in dangerous_chars:
            sanitized = sanitized.replace(char, "_")

        # 移除首尾的点和空格
        sanitized = sanitized.strip(". ")

        # 限制长度
        if len(sanitized) > 255:
            name, ext = os.path.splitext(sanitized)
            max_name_length = 255 - len(ext)
            sanitized = name[:max_name_length] + ext

        return sanitized or "untitled"

    @staticmethod
    def validate_wxid(wxid: str) -> bool:
        """
        验证微信ID格式

        Args:
            wxid: 微信ID

        Returns:
            是否有效
        """
        if not wxid or not isinstance(wxid, str):
            return False

        # 微信ID长度限制
        if len(wxid) < 3 or len(wxid) > 50:
            return False

        # 只允许字母、数字、下划线和连字符
        allowed_chars = set(
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-"
        )
        return all(char in allowed_chars for char in wxid)


class RateLimiter:
    """速率限制器"""

    def __init__(self, max_requests: int = 100, time_window: int = 3600):
        """
        初始化速率限制器

        Args:
            max_requests: 时间窗口内的最大请求数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests: Dict[str, list] = {}

    def is_allowed(self, identifier: str) -> bool:
        """
        检查是否允许请求

        Args:
            identifier: 请求标识符（如IP地址、用户ID等）

        Returns:
            是否允许请求
        """
        current_time = time.time()

        # 获取该标识符的请求历史
        if identifier not in self.requests:
            self.requests[identifier] = []

        request_times = self.requests[identifier]

        # 清理过期的请求记录
        cutoff_time = current_time - self.time_window
        request_times[:] = [t for t in request_times if t > cutoff_time]

        # 检查是否超过限制
        if len(request_times) >= self.max_requests:
            logger.warning(f"速率限制触发: {identifier}")
            return False

        # 记录当前请求
        request_times.append(current_time)
        return True

    def get_remaining_requests(self, identifier: str) -> int:
        """
        获取剩余请求数

        Args:
            identifier: 请求标识符

        Returns:
            剩余请求数
        """
        if identifier not in self.requests:
            return self.max_requests

        current_time = time.time()
        cutoff_time = current_time - self.time_window

        # 计算有效请求数
        valid_requests = sum(1 for t in self.requests[identifier] if t > cutoff_time)
        return max(0, self.max_requests - valid_requests)


class SecurityAuditor:
    """安全审计器"""

    def __init__(self):
        self.audit_log: list = []

    def log_security_event(
        self,
        event_type: str,
        description: str,
        severity: str = "INFO",
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        记录安全事件

        Args:
            event_type: 事件类型
            description: 事件描述
            severity: 严重程度 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            metadata: 附加元数据
        """
        event = {
            "timestamp": time.time(),
            "event_type": event_type,
            "description": description,
            "severity": severity,
            "metadata": metadata or {},
        }

        self.audit_log.append(event)

        # 记录到日志文件
        log_message = f"[{event_type}] {description}"
        if metadata:
            log_message += f" | 元数据: {metadata}"

        if severity == "CRITICAL":
            logger.critical(log_message)
        elif severity == "ERROR":
            logger.error(log_message)
        elif severity == "WARNING":
            logger.warning(log_message)
        else:
            logger.info(log_message)

    def get_security_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取安全摘要

        Args:
            hours: 统计的小时数

        Returns:
            安全摘要
        """
        cutoff_time = time.time() - (hours * 3600)
        recent_events = [e for e in self.audit_log if e["timestamp"] > cutoff_time]

        summary = {
            "total_events": len(recent_events),
            "by_severity": {},
            "by_type": {},
            "critical_events": [],
        }

        for event in recent_events:
            # 按严重程度统计
            severity = event["severity"]
            summary["by_severity"][severity] = (
                summary["by_severity"].get(severity, 0) + 1
            )

            # 按类型统计
            event_type = event["event_type"]
            summary["by_type"][event_type] = summary["by_type"].get(event_type, 0) + 1

            # 收集严重事件
            if severity in ["ERROR", "CRITICAL"]:
                summary["critical_events"].append(event)

        return summary


# 全局实例
security_manager = SecurityManager()
input_sanitizer = InputSanitizer()
rate_limiter = RateLimiter()
security_auditor = SecurityAuditor()


def secure_compare(a: str, b: str) -> bool:
    """
    安全的字符串比较，防止时序攻击

    Args:
        a: 字符串A
        b: 字符串B

    Returns:
        是否相等
    """
    return hmac.compare_digest(a, b)


def generate_token(length: int = 32) -> str:
    """
    生成安全的随机令牌

    Args:
        length: 令牌长度

    Returns:
        随机令牌
    """
    return secrets.token_urlsafe(length)


def validate_file_type(file_path: str, allowed_extensions: list) -> bool:
    """
    验证文件类型

    Args:
        file_path: 文件路径
        allowed_extensions: 允许的扩展名列表

    Returns:
        是否为允许的文件类型
    """
    if not file_path:
        return False

    file_ext = os.path.splitext(file_path)[1].lower()
    return file_ext in [ext.lower() for ext in allowed_extensions]
