#!/usr/bin/env python3
"""
快速尺寸测试工具
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_sizes():
    """测试控件尺寸"""
    try:
        from PyQt6.QtWidgets import QApplication, QWidget, QSpinBox, QComboBox, QCheckBox, QRadioButton
        from ui.modern_theme_manager import theme_manager
        
        app = QApplication(sys.argv)
        
        print("🔧 快速尺寸测试")
        print("=" * 40)
        
        # 目标尺寸
        target_sizes = {
            "SpinBox": (77, 23),
            "ComboBox": (86, 23),
            "CheckBox": (57, 19),
            "RadioButton": (58, 19),
        }
        
        print("📋 目标尺寸:")
        for name, (w, h) in target_sizes.items():
            print(f"  {name}: {w} x {h} px")
        
        # 测试默认主题
        print("\n🎨 测试默认主题...")
        theme_manager.set_theme(app, "默认主题")
        
        widget = QWidget()
        spinbox = QSpinBox(widget)
        combobox = QComboBox(widget)
        checkbox = QCheckBox("测试", widget)
        radiobutton = QRadioButton("测试", widget)
        
        widget.show()
        app.processEvents()  # 确保控件渲染
        
        print("📊 默认主题实际尺寸:")
        controls = [
            ("SpinBox", spinbox),
            ("ComboBox", combobox),
            ("CheckBox", checkbox),
            ("RadioButton", radiobutton),
        ]
        
        default_sizes = {}
        for name, control in controls:
            w, h = control.size().width(), control.size().height()
            default_sizes[name] = (w, h)
            target_w, target_h = target_sizes[name]
            match = "✅" if abs(w - target_w) <= 2 and abs(h - target_h) <= 2 else "❌"
            print(f"  {match} {name}: {w} x {h} px")
        
        # 测试科技主题
        print("\n🎨 测试科技主题...")
        theme_manager.set_theme(app, "科技主题")
        app.processEvents()  # 确保主题切换完成
        
        print("📊 科技主题实际尺寸:")
        tech_sizes = {}
        all_match = True
        
        for name, control in controls:
            w, h = control.size().width(), control.size().height()
            tech_sizes[name] = (w, h)
            default_w, default_h = default_sizes[name]
            
            # 与默认主题对比
            size_match = (w == default_w and h == default_h)
            match_symbol = "✅" if size_match else "❌"
            
            print(f"  {match_symbol} {name}: {w} x {h} px", end="")
            if not size_match:
                diff_w = w - default_w
                diff_h = h - default_h
                print(f" (差异: {diff_w:+d}x{diff_h:+d})")
                all_match = False
            else:
                print(" (与默认主题相同)")
        
        print(f"\n🎯 总体结果:")
        if all_match:
            print("✅ 科技主题尺寸与默认主题完全一致！")
            print("🎉 修复成功！")
        else:
            print("❌ 科技主题尺寸与默认主题不一致")
            print("⚠️  需要进一步调整")
        
        widget.close()
        app.quit()
        
        return all_match
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速尺寸测试工具")
    print("=" * 40)
    
    success = test_sizes()
    
    if success:
        print("\n✅ 测试完成：科技主题尺寸正确")
    else:
        print("\n❌ 测试完成：科技主题尺寸需要调整")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
