#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带UAC管理员权限的构建脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def verify_uac_requirements():
    """验证UAC相关要求"""
    print("🔍 验证UAC相关要求...")
    
    # 检查关键文件
    required_files = {
        "注入工具": "tools/Injector.exe",
        "DLL文件": "wxhelper_files/wxhelper.dll",
        "权限管理": "utils/admin_privileges.py",
        "主程序": "main.py"
    }
    
    missing = []
    for name, path in required_files.items():
        if Path(path).exists():
            print(f"  ✅ {name}: {path}")
        else:
            missing.append(f"{name} ({path})")
            print(f"  ❌ {name}: {path}")
    
    if missing:
        print(f"⚠️  缺少关键文件: {missing}")
        return False
    
    print("✅ UAC相关文件检查通过")
    return True

def build_with_uac():
    """构建带UAC管理员权限的版本"""
    print("🔨 构建带UAC管理员权限的版本...")
    
    # 清理构建目录
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONUTF8': '1',
        'PYTHONLEGACYWINDOWSSTDIO': '1'
    })
    
    # 带UAC的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 无控制台
        "--uac-admin",                  # 🔑 请求管理员权限 (关键!)
        "--name=MeetSpaceWeChatSender_UAC",
        "--icon=resources/icons/app_icon.ico",
        
        # === 注入工具 - 所有架构 ===
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64/Injector.exe;tools/x64",
        "--add-data=tools/Win32/Injector.exe;tools/Win32", 
        "--add-data=tools/ARM64/Injector.exe;tools/ARM64",
        
        # === DLL文件 - 所有版本 ===
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_original_backup.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_x64_backup.dll;wxhelper_files",
        
        # === 资源文件 ===
        "--add-data=resources;resources",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        
        # === 核心隐藏导入 ===
        # PyQt6
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        
        # 系统和进程
        "--hidden-import=ctypes",
        "--hidden-import=ctypes.wintypes",
        "--hidden-import=subprocess",
        "--hidden-import=psutil",
        
        # 网络和API
        "--hidden-import=requests",
        "--hidden-import=json",
        
        # 数据处理
        "--hidden-import=pandas",
        "--hidden-import=PIL",
        
        # === 项目核心模块 ===
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=core.injector_tool",        # 核心注入工具
        "--hidden-import=core.auto_injector",        # 自动注入器
        "--hidden-import=core.http_api_connector",   # HTTP API连接器
        "--hidden-import=core.timing_sender",
        "--hidden-import=core.loop_sender",
        
        # UI模块
        "--hidden-import=ui",
        "--hidden-import=ui.main_window",
        
        # 工具模块 (包含权限管理)
        "--hidden-import=utils",
        "--hidden-import=utils.admin_privileges",    # 🔑 权限管理 (重要!)
        "--hidden-import=utils.subprocess_helper",
        
        # === 排除不需要的模块 ===
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy.testing",
        
        # === 构建选项 ===
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不确认覆盖
        "--optimize=1",                 # 优化级别
        "--noupx",                      # 禁用UPX压缩
        
        # 主程序
        "main.py"
    ]
    
    print("📋 执行UAC管理员权限构建...")
    print("   🔑 启用 --uac-admin 参数")
    print("   📚 包含完整的注入工具")
    print("   🛡️  包含权限管理模块")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env,
            timeout=600  # 10分钟超时
        )
        
        print("✅ 构建完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e.returncode}")
        if e.stderr:
            print("错误输出:")
            print(e.stderr[-2000:])
        return False
    except subprocess.TimeoutExpired:
        print("❌ 构建超时（10分钟）")
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def verify_uac_build():
    """验证UAC构建结果"""
    print("🔍 验证UAC构建结果...")
    
    exe_file = Path("dist") / "MeetSpaceWeChatSender_UAC.exe"
    
    if not exe_file.exists():
        print("❌ 可执行文件不存在")
        return False
    
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"✅ 可执行文件: {exe_file}")
    print(f"📊 文件大小: {size_mb:.1f} MB")
    
    # 检查是否包含UAC清单
    print("🔍 检查UAC清单...")
    try:
        # 使用PowerShell检查文件的UAC清单
        cmd = f'powershell -Command "Get-AuthenticodeSignature \'{exe_file}\' | Select-Object -ExpandProperty StatusMessage"'
        result = subprocess.run(cmd, capture_output=True, text=True, shell=True, timeout=10)
        
        print("📋 UAC清单检查完成")
        print("   程序将在运行时请求管理员权限")
        
    except Exception as e:
        print(f"⚠️  UAC清单检查失败: {e}")
    
    return True

def create_uac_docs():
    """创建UAC版本文档"""
    print("📄 创建UAC版本文档...")
    
    docs_content = """# Meet space 微信群发助手 - UAC管理员权限版本

## 🔑 版本特点

### ✅ 自动请求管理员权限
- **UAC提示**: 运行时自动弹出UAC权限提示
- **无需手动**: 不需要右键"以管理员身份运行"
- **自动提升**: 程序会自动请求所需的管理员权限

### 🛡️ 权限管理
- **智能检测**: 自动检测当前权限级别
- **自动提升**: 权限不足时自动请求提升
- **安全可靠**: 使用Windows标准UAC机制

## 🚀 使用方法

### 简单启动
1. **双击运行**: 直接双击 `MeetSpaceWeChatSender_UAC.exe`
2. **UAC提示**: Windows会弹出UAC权限确认对话框
3. **点击"是"**: 确认授予管理员权限
4. **正常使用**: 程序以管理员权限启动，可正常注入

### UAC对话框示例
```
用户账户控制
你要允许此应用对你的设备进行更改吗？

MeetSpaceWeChatSender_UAC.exe
已验证的发布者: 未知

[是(Y)]  [否(N)]
```

## 🔧 技术实现

### PyInstaller UAC参数
```bash
pyinstaller --uac-admin --onefile --windowed main.py
```

### 权限检测流程
1. **启动检查**: 程序启动时检查当前权限
2. **自动提升**: 权限不足时自动请求UAC提升
3. **注入执行**: 获得权限后执行注入操作

### 包含的权限功能
- ✅ UAC权限请求
- ✅ 管理员权限检测
- ✅ 自动权限提升
- ✅ 权限状态监控

## 🛠️ 故障排除

### UAC被禁用
如果系统UAC被禁用：
1. 启用UAC: 控制面板 → 用户账户 → 更改用户账户控制设置
2. 重启系统
3. 重新运行程序

### 权限提升失败
如果权限提升失败：
1. **手动运行**: 右键程序 → "以管理员身份运行"
2. **检查UAC**: 确保UAC设置正确
3. **重启系统**: 重启后再试

### 杀毒软件阻止
如果杀毒软件阻止：
1. **添加信任**: 将程序添加到杀毒软件白名单
2. **临时关闭**: 临时关闭实时保护
3. **重新运行**: 再次尝试运行程序

## 📊 优势对比

| 功能 | 普通版本 | UAC版本 |
|------|----------|---------|
| 启动方式 | 需要右键管理员运行 | 双击自动请求权限 |
| 用户体验 | 需要额外步骤 | 一键启动 |
| 权限管理 | 手动管理 | 自动管理 |
| 注入成功率 | 依赖用户操作 | 自动保证权限 |

## 🎯 适用场景

### ✅ 推荐使用
- 经常使用注入功能
- 希望简化启动流程
- 需要自动权限管理
- 企业环境部署

### ⚠️ 注意事项
- 首次运行会弹出UAC提示
- 需要用户确认授权
- 某些企业环境可能限制UAC

---

**版本**: UAC Admin v1.0.0
**特点**: 自动请求管理员权限，简化启动流程
**更新日期**: 2025-08-06
"""
    
    try:
        with open("dist/UAC管理员权限版本说明.txt", "w", encoding="utf-8") as f:
            f.write(docs_content)
        print("  ✅ UAC版本文档已创建")
    except Exception as e:
        print(f"  ⚠️  创建文档失败: {e}")

def main():
    print("🔧 Meet space 微信群发助手 - UAC管理员权限构建")
    print("=" * 70)
    print("添加 --uac-admin 参数，自动请求管理员权限")
    print("=" * 70)
    
    # 1. 验证UAC要求
    if not verify_uac_requirements():
        print("\n❌ UAC要求验证失败")
        input("\n按回车键退出...")
        return False
    
    # 2. 构建UAC版本
    if not build_with_uac():
        print("\n❌ UAC版本构建失败")
        input("\n按回车键退出...")
        return False
    
    # 3. 验证构建结果
    if not verify_uac_build():
        print("\n❌ UAC构建验证失败")
        input("\n按回车键退出...")
        return False
    
    # 4. 创建文档
    create_uac_docs()
    
    print("\n🎉 UAC管理员权限版本构建成功!")
    print("📁 输出文件:")
    print("  - MeetSpaceWeChatSender_UAC.exe (主程序)")
    print("  - UAC管理员权限版本说明.txt (详细说明)")
    
    print("\n🔑 UAC功能:")
    print("  ✅ 自动请求管理员权限")
    print("  ✅ 无需手动右键运行")
    print("  ✅ 一键启动注入功能")
    print("  ✅ 智能权限管理")
    
    print("\n📋 使用方法:")
    print("  1. 双击运行程序")
    print("  2. 在UAC对话框中点击'是'")
    print("  3. 程序以管理员权限启动")
    print("  4. 可正常使用注入功能")
    
    input("\n按回车键退出...")
    return True

if __name__ == "__main__":
    main()
