#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分组详情对话框
"""

import sys
from typing import List, Optional
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QMessageBox,
    QGroupBox,
    QSplitter,
    QTextEdit,
    QComboBox,
    QSpinBox,
    QCheckBox,
    QDialogButtonBox,
    QTabWidget,
    QWidget,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

from core.group_manager import ContactGroup, GroupMember
from core.wechatferry_connector import Contact
from ui.contact_selector_dialog import ContactSelectorDialog
from ui.themed_message_box import ThemedMessageBoxHelper
from ui.themed_dialog_base import ThemedDialogBase
from utils.logger import setup_logger

logger = setup_logger("group_detail_dialog")


class GroupDetailDialog(ThemedDialogBase):
    """分组详情对话框"""

    # 信号定义
    group_updated = pyqtSignal(str, str)  # group_id, group_type
    members_changed = pyqtSignal(str, str, list)  # group_id, group_type, members

    def __init__(self, group: ContactGroup, parent=None, main_window=None):
        super().__init__(parent)
        self.group = group
        self.main_window = main_window
        self.all_contacts = []

        self.init_ui()
        self.setup_connections()
        self.load_group_data()

        # 从主窗口获取联系人数据
        self.load_contacts_from_main_window()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle(f"分组详情 - {self.group.name}")
        self.setMinimumSize(900, 700)
        self.setModal(True)

        # 主布局
        main_layout = QVBoxLayout(self)

        # 分组基本信息
        info_group = QGroupBox("分组信息")
        info_layout = QVBoxLayout(info_group)

        # 分组名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("分组名称:"))
        self.name_edit = QLineEdit()
        self.name_edit.setText(self.group.name)
        name_layout.addWidget(self.name_edit)
        info_layout.addLayout(name_layout)

        # 分组类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("分组类型:"))
        self.type_label = QLabel(
            "定时发送" if self.group.group_type == "timing" else "循环发送"
        )
        type_layout.addWidget(self.type_label)
        type_layout.addStretch()
        info_layout.addLayout(type_layout)

        # 成员统计
        stats_layout = QHBoxLayout()
        stats_layout.addWidget(QLabel("成员数量:"))
        self.member_count_label = QLabel(f"{len(self.group.members)} 个")
        stats_layout.addWidget(self.member_count_label)
        stats_layout.addStretch()
        info_layout.addLayout(stats_layout)

        main_layout.addWidget(info_group)

        # 成员管理区域
        members_group = QGroupBox("成员列表")
        members_layout = QVBoxLayout(members_group)

        # 成员操作按钮
        members_btn_layout = QHBoxLayout()
        self.add_members_btn = QPushButton("添加成员")
        self.remove_members_btn = QPushButton("移除成员")
        self.clear_members_btn = QPushButton("清空成员")

        members_btn_layout.addWidget(self.add_members_btn)
        members_btn_layout.addWidget(self.remove_members_btn)
        members_btn_layout.addWidget(self.clear_members_btn)
        members_btn_layout.addStretch()
        members_layout.addLayout(members_btn_layout)

        # 成员表格
        self.members_table = QTableWidget(0, 5)
        self.members_table.setHorizontalHeaderLabels(
            ["选择", "类型", "名称", "微信ID", "备注"]
        )
        self.members_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        self.members_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        members_layout.addWidget(self.members_table)

        main_layout.addWidget(members_group)

        # 底部按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save
            | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.save_changes)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def setup_connections(self):
        """设置信号连接"""
        # 成员操作
        self.add_members_btn.clicked.connect(self.add_members)
        self.remove_members_btn.clicked.connect(self.remove_members)
        self.clear_members_btn.clicked.connect(self.clear_members)

    def load_group_data(self):
        """加载分组数据"""
        self.update_members_table()

    def update_members_table(self):
        """更新成员表格"""
        self.members_table.setRowCount(len(self.group.members))

        for row, member in enumerate(self.group.members):
            # 选择框
            checkbox = QCheckBox()
            self.members_table.setCellWidget(row, 0, checkbox)

            # 类型
            type_item = QTableWidgetItem(
                "好友" if member.member_type == "contact" else "群聊"
            )
            self.members_table.setItem(row, 1, type_item)

            # 名称
            name_item = QTableWidgetItem(member.name)
            self.members_table.setItem(row, 2, name_item)

            # 微信ID
            wxid_item = QTableWidgetItem(member.wxid)
            self.members_table.setItem(row, 3, wxid_item)

            # 备注
            remark_item = QTableWidgetItem(member.remark)
            self.members_table.setItem(row, 4, remark_item)

        # 更新统计
        self.member_count_label.setText(f"{len(self.group.members)} 个")

    def load_contacts_from_main_window(self):
        """从主窗口获取联系人数据"""
        try:
            # 如果没有主窗口引用，尝试查找
            if not self.main_window:
                self.main_window = self.find_main_window()

            if self.main_window and hasattr(self.main_window, "contacts") and self.main_window.contacts:
                self.all_contacts = self.main_window.contacts.copy()
                logger.info(f"从主窗口获取到 {len(self.all_contacts)} 个联系人")
            else:
                logger.warning("主窗口未提供或没有联系人数据")
                self.all_contacts = []
        except Exception as e:
            logger.error(f"从主窗口获取联系人失败: {e}")
            self.all_contacts = []

    def find_main_window(self):
        """查找主窗口实例"""
        try:
            # 通过父窗口链查找
            parent = self.parent()
            while parent:
                if hasattr(parent, "contacts") and parent.contacts:
                    return parent
                parent = parent.parent()

            # 通过应用程序查找
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.topLevelWidgets():
                    if (hasattr(widget, "contacts") and
                        widget.contacts and
                        widget.__class__.__name__ == "MainWindow"):
                        return widget

            return None
        except Exception as e:
            logger.error(f"查找主窗口失败: {e}")
            return None

    def add_members(self):
        """添加成员"""
        # 从主窗口获取最新的联系人数据
        self.load_contacts_from_main_window()

        if not self.all_contacts:
            contacts_count = len(self.all_contacts) if self.all_contacts else 0
            ThemedMessageBoxHelper.show_warning(
                self,
                "没有联系人数据",
                f"当前联系人数据为空（{contacts_count} 个联系人）。\n\n"
                "请按以下步骤操作：\n"
                "1. 确保微信已登录并正常运行\n"
                "2. 在主界面点击'联系人管理'标签页\n"
                "3. 点击'获取联系人'按钮\n"
                "4. 等待联系人数据加载完成后重试"
            )
            return

        logger.info(f"准备添加成员，可用联系人: {len(self.all_contacts)} 个")

        # 将分组成员转换为Contact对象
        from core.wechatferry_connector import Contact
        existing_contacts = []
        for member in self.group.members:
            contact = Contact(
                wxid=member.wxid,
                name=member.name,
                type="friend" if member.member_type == "contact" else "group",
                remark=member.remark
            )
            existing_contacts.append(contact)

        # 创建联系人选择对话框
        dialog = ContactSelectorDialog(self, existing_contacts)
        dialog.set_contacts(self.all_contacts)

        # 连接信号
        dialog.contacts_selected.connect(self.on_contacts_selected)

        # 显示对话框
        if dialog.exec() == QDialog.DialogCode.Accepted:
            logger.info(
                f"为分组 {self.group.name} 添加了 {len(dialog.get_selected_contacts())} 个成员"
            )

    def on_contacts_selected(self, contacts: List[Contact]):
        """联系人选择回调"""
        added_count = 0
        for contact in contacts:
            # 检查是否已存在
            if not any(member.wxid == contact.wxid for member in self.group.members):
                # 创建分组成员
                member = GroupMember(
                    wxid=contact.wxid,
                    name=contact.name,
                    member_type="contact" if contact.type == "friend" else "group",
                    remark=contact.remark,
                )
                self.group.add_member(member)
                added_count += 1

        if added_count > 0:
            self.update_members_table()
            ThemedMessageBoxHelper.show_information(
                self, "成功", f"已添加 {added_count} 个成员到分组"
            )
        else:
            ThemedMessageBoxHelper.show_information(
                self, "提示", "所有选中的联系人已存在于分组中"
            )

    def remove_members(self):
        """移除成员"""
        selected_rows = set()
        for item in self.members_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            ThemedMessageBoxHelper.show_warning(self, "提示", "请选择要移除的成员")
            return

        # 确认删除
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认删除",
            f"确定要移除选中的 {len(selected_rows)} 个成员吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply:
            # 从后往前删除，避免索引变化
            removed_count = 0
            for row in sorted(selected_rows, reverse=True):
                if row < len(self.group.members):
                    member = self.group.members.pop(row)
                    removed_count += 1

            self.update_members_table()
            ThemedMessageBoxHelper.show_information(
                self, "成功", f"已移除 {removed_count} 个成员"
            )

    def clear_members(self):
        """清空成员"""
        if not self.group.members:
            ThemedMessageBoxHelper.show_information(self, "提示", "分组中已经没有成员")
            return

        # 确认清空
        reply = ThemedMessageBoxHelper.show_question(
            self,
            "确认清空",
            f"确定要清空分组中的所有 {len(self.group.members)} 个成员吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply:
            self.group.members.clear()
            self.update_members_table()
            ThemedMessageBoxHelper.show_information(self, "成功", "已清空所有成员")

    def save_changes(self):
        """保存更改"""
        try:
            # 保存分组名称
            if self.name_edit.text().strip():
                self.group.name = self.name_edit.text().strip()

            # 发送更新信号
            self.group_updated.emit(self.group.group_id, self.group.group_type)
            self.members_changed.emit(
                self.group.group_id, self.group.group_type, self.group.members
            )

            ThemedMessageBoxHelper.show_information(self, "成功", "分组信息已保存")
            self.accept()

        except Exception as e:
            logger.error(f"保存分组信息失败: {e}")
            ThemedMessageBoxHelper.show_error(self, "错误", f"保存失败: {str(e)}")


if __name__ == "__main__":
    # 测试代码
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # 演示代码已移除

    sys.exit(app.exec())
