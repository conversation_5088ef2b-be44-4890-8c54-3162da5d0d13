#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试系统风控设置问题
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_risk_control_issue():
    """调试系统风控设置问题"""
    
    print("🔍 调试系统风控设置问题")
    print("=" * 50)
    
    try:
        from core.group_manager import group_manager
        
        # 获取测试分组
        timing_groups = group_manager.get_all_groups("timing")
        if not timing_groups:
            print("❌ 没有找到定时发送分组")
            return
        
        test_group = timing_groups[0]
        print(f"📋 使用测试分组: {test_group.name} (ID: {test_group.group_id})")
        
        # 步骤1: 检查当前配置
        print("\n🔍 步骤1: 检查当前配置")
        current_value = test_group.get_config("send_settings.use_system_risk_control", None)
        print(f"当前风控设置: {current_value} (类型: {type(current_value)})")
        
        # 步骤2: 设置为False并检查内存中的值
        print("\n🔍 步骤2: 设置为False并检查内存中的值")
        success = test_group.update_config("send_settings.use_system_risk_control", False)
        print(f"更新配置结果: {success}")
        
        # 立即检查内存中的值
        memory_value = test_group.get_config("send_settings.use_system_risk_control", None)
        print(f"内存中的风控设置: {memory_value} (类型: {type(memory_value)})")
        
        # 检查完整的配置结构
        print(f"完整的send_settings配置: {test_group.config.get('send_settings', {})}")
        
        # 步骤3: 保存到文件
        print("\n🔍 步骤3: 保存到文件")
        group_manager.save_groups("timing")
        print("✅ 配置已保存到文件")
        
        # 步骤4: 检查文件内容
        print("\n🔍 步骤4: 检查文件内容")
        with open("data/groups/timing_groups.json", "r", encoding="utf-8") as f:
            file_data = json.load(f)
        
        group_data = file_data.get(test_group.group_id, {})
        file_risk_value = group_data.get("config", {}).get("send_settings", {}).get("use_system_risk_control", "NOT_FOUND")
        print(f"文件中的风控设置: {file_risk_value} (类型: {type(file_risk_value)})")
        
        # 步骤5: 重新加载并检查
        print("\n🔍 步骤5: 重新加载并检查")
        group_manager.load_groups("timing")
        
        reloaded_group = group_manager.get_group(test_group.group_id, "timing")
        if reloaded_group:
            reloaded_value = reloaded_group.get_config("send_settings.use_system_risk_control", None)
            print(f"重新加载后的风控设置: {reloaded_value} (类型: {type(reloaded_value)})")
            
            # 检查完整的配置结构
            print(f"重新加载后的完整send_settings: {reloaded_group.config.get('send_settings', {})}")
        else:
            print("❌ 重新加载分组失败")
        
        # 步骤6: 测试True值
        print("\n🔍 步骤6: 测试True值")
        test_group.update_config("send_settings.use_system_risk_control", True)
        memory_true = test_group.get_config("send_settings.use_system_risk_control", None)
        print(f"设置True后内存中的值: {memory_true} (类型: {type(memory_true)})")
        
        group_manager.save_groups("timing")
        group_manager.load_groups("timing")
        
        reloaded_group_true = group_manager.get_group(test_group.group_id, "timing")
        if reloaded_group_true:
            reloaded_true = reloaded_group_true.get_config("send_settings.use_system_risk_control", None)
            print(f"设置True后重新加载的值: {reloaded_true} (类型: {type(reloaded_true)})")
        
        # 步骤7: 再次测试False值
        print("\n🔍 步骤7: 再次测试False值")
        test_group.update_config("send_settings.use_system_risk_control", False)
        memory_false = test_group.get_config("send_settings.use_system_risk_control", None)
        print(f"再次设置False后内存中的值: {memory_false} (类型: {type(memory_false)})")
        
        group_manager.save_groups("timing")
        
        # 直接检查文件
        with open("data/groups/timing_groups.json", "r", encoding="utf-8") as f:
            file_data_false = json.load(f)
        
        group_data_false = file_data_false.get(test_group.group_id, {})
        file_risk_false = group_data_false.get("config", {}).get("send_settings", {}).get("use_system_risk_control", "NOT_FOUND")
        print(f"再次保存False后文件中的值: {file_risk_false} (类型: {type(file_risk_false)})")
        
        group_manager.load_groups("timing")
        reloaded_group_false = group_manager.get_group(test_group.group_id, "timing")
        if reloaded_group_false:
            reloaded_false = reloaded_group_false.get_config("send_settings.use_system_risk_control", None)
            print(f"再次设置False后重新加载的值: {reloaded_false} (类型: {type(reloaded_false)})")
        
        # 分析结果
        print("\n📊 分析结果:")
        if memory_false == False:
            print("✅ 内存中False值设置正确")
        else:
            print(f"❌ 内存中False值设置错误: {memory_false}")
        
        if file_risk_false == False:
            print("✅ 文件中False值保存正确")
        else:
            print(f"❌ 文件中False值保存错误: {file_risk_false}")
        
        if reloaded_false == False:
            print("✅ 重新加载False值正确")
        else:
            print(f"❌ 重新加载False值错误: {reloaded_false}")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_risk_control_issue()
