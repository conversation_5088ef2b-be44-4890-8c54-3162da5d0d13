[Setup]
; 应用程序基本信息
AppName=Meet space 微信群发助手
AppVersion=1.0.0
AppPublisher=Meet space 会客创意空间
AppPublisherURL=https://meetspace.cn
AppSupportURL=https://meetspace.cn/support
AppUpdatesURL=https://meetspace.cn/updates
DefaultDirName={autopf}\MeetSpaceWeChatSender
DefaultGroupName=Meet space 微信群发助手
AllowNoIcons=yes
LicenseFile=..\LICENSE.txt
InfoBeforeFile=..\README.md
OutputDir=.\Output
OutputBaseFilename=MeetSpace_WeChatSender_Setup_v1.0.0
SetupIconFile=..\resources\icons\installer.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; 语言设置
ShowLanguageDialog=no
DefaultLanguage=ChineseSimplified

; 安装选项
DisableProgramGroupPage=yes
DisableReadyPage=no
DisableFinishedPage=no
DisableWelcomePage=no

; 卸载设置
UninstallDisplayIcon={app}\resources\icons\uninstall.ico
UninstallDisplayName=Meet space 微信群发助手
CreateUninstallRegKey=yes

[Languages]
Name: "ChineseSimplified"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "startmenu"; Description: "创建开始菜单项"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkedonce
Name: "autostart"; Description: "开机自动启动"; GroupDescription: "启动选项"; Flags: unchecked

[Files]
; 主程序文件
Source: "..\dist\MeetSpaceWeChatSender\MeetSpaceWeChatSender.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\dist\MeetSpaceWeChatSender\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; 文档文件
Source: "..\README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Meet space 微信群发助手"; Filename: "{app}\MeetSpaceWeChatSender.exe"; IconFilename: "{app}\resources\icons\app.ico"
Name: "{group}\卸载 Meet space 微信群发助手"; Filename: "{uninstallexe}"; IconFilename: "{app}\resources\icons\uninstall.ico"
Name: "{autodesktop}\Meet space 微信群发助手"; Filename: "{app}\MeetSpaceWeChatSender.exe"; IconFilename: "{app}\resources\icons\app.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\Meet space 微信群发助手"; Filename: "{app}\MeetSpaceWeChatSender.exe"; IconFilename: "{app}\resources\icons\app.ico"; Tasks: quicklaunchicon

[Registry]
; 自动启动注册表项
Root: HKCU; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "MeetSpaceWeChatSender"; ValueData: "{app}\MeetSpaceWeChatSender.exe"; Flags: uninsdeletevalue; Tasks: autostart

; 应用程序注册信息
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender"; ValueType: string; ValueName: "DisplayName"; ValueData: "Meet space 微信群发助手"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender"; ValueType: string; ValueName: "DisplayVersion"; ValueData: "1.0.0"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender"; ValueType: string; ValueName: "Publisher"; ValueData: "Meet space 会客创意空间"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender"; ValueType: string; ValueName: "UninstallString"; ValueData: "{uninstallexe}"

[Run]
Filename: "{app}\MeetSpaceWeChatSender.exe"; Description: "{cm:LaunchProgram,Meet space 微信群发助手}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; 删除用户数据目录（可选）
Type: filesandordirs; Name: "{userappdata}\MeetSpaceWeChatSender"
Type: filesandordirs; Name: "{userdocs}\MeetSpaceWeChatSender"

[Code]
var
  DataDirPage: TInputDirWizardPage;
  KeepUserDataCheck: TNewCheckBox;

procedure InitializeWizard;
begin
  // 创建用户数据目录选择页面
  DataDirPage := CreateInputDirPage(wpSelectDir,
    '选择用户数据目录', '请选择存储配置文件和日志的目录',
    '程序将在此目录存储配置文件、日志文件和用户数据。' + #13#10 +
    '建议使用默认位置以确保程序正常运行。',
    False, '');
  DataDirPage.Add('用户数据目录：');
  DataDirPage.Values[0] := ExpandConstant('{userappdata}\MeetSpaceWeChatSender');

  // 创建保留用户数据选项
  KeepUserDataCheck := TNewCheckBox.Create(DataDirPage);
  KeepUserDataCheck.Parent := DataDirPage.Surface;
  KeepUserDataCheck.Caption := '卸载时保留用户数据（配置文件、日志等）';
  KeepUserDataCheck.Checked := True;
  KeepUserDataCheck.Top := DataDirPage.Edits[0].Top + DataDirPage.Edits[0].Height + 20;
  KeepUserDataCheck.Width := DataDirPage.SurfaceWidth;
end;

function ShouldSkipPage(PageID: Integer): Boolean;
begin
  // 在静默安装时跳过用户数据目录页面
  Result := (PageID = DataDirPage.ID) and WizardSilent;
end;

procedure CurStepChanged(CurStep: TSetupStep);
var
  UserDataDir: String;
begin
  if CurStep = ssPostInstall then
  begin
    // 创建用户数据目录
    UserDataDir := DataDirPage.Values[0];
    if not DirExists(UserDataDir) then
      ForceDirectories(UserDataDir);
    
    // 创建子目录
    ForceDirectories(UserDataDir + '\config');
    ForceDirectories(UserDataDir + '\logs');
    ForceDirectories(UserDataDir + '\cache');
    ForceDirectories(UserDataDir + '\temp');
    
    // 在用户文档目录创建数据目录
    ForceDirectories(ExpandConstant('{userdocs}\MeetSpaceWeChatSender\backups'));
    ForceDirectories(ExpandConstant('{userdocs}\MeetSpaceWeChatSender\exports'));
    ForceDirectories(ExpandConstant('{userdocs}\MeetSpaceWeChatSender\templates'));
  end;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
begin
  if CurUninstallStep = usPostUninstall then
  begin
    // 如果用户选择不保留数据，则删除用户数据目录
    if not KeepUserDataCheck.Checked then
    begin
      DelTree(ExpandConstant('{userappdata}\MeetSpaceWeChatSender'), True, True, True);
      DelTree(ExpandConstant('{userdocs}\MeetSpaceWeChatSender'), True, True, True);
    end;
  end;
end;

function GetUserDataDir(Param: String): String;
begin
  Result := DataDirPage.Values[0];
end;

// 检查是否已安装
function IsAppInstalled: Boolean;
var
  UninstallKey: String;
begin
  UninstallKey := 'Software\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender';
  Result := RegKeyExists(HKLM, UninstallKey);
end;

// 获取已安装版本
function GetInstalledVersion: String;
var
  UninstallKey: String;
begin
  UninstallKey := 'Software\Microsoft\Windows\CurrentVersion\Uninstall\MeetSpaceWeChatSender';
  if not RegQueryStringValue(HKLM, UninstallKey, 'DisplayVersion', Result) then
    Result := '';
end;

function InitializeSetup: Boolean;
var
  InstalledVersion: String;
  ResultCode: Integer;
begin
  Result := True;
  
  // 检查是否已安装
  if IsAppInstalled then
  begin
    InstalledVersion := GetInstalledVersion;
    if MsgBox('检测到已安装版本 ' + InstalledVersion + '。' + #13#10 +
              '是否要继续安装新版本？', mbConfirmation, MB_YESNO) = IDNO then
    begin
      Result := False;
      Exit;
    end;
  end;
  
  // 检查是否有程序正在运行
  if CheckForMutexes('MeetSpaceWeChatSender_SingleInstance') then
  begin
    if MsgBox('检测到程序正在运行。请先关闭程序再继续安装。' + #13#10 +
              '是否要强制继续安装？', mbConfirmation, MB_YESNO) = IDNO then
    begin
      Result := False;
      Exit;
    end;
  end;
end;
