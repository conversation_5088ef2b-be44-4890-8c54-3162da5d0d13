#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版工具包构建 - 包含注入工具和DLL
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_and_setup():
    """清理并设置环境"""
    print("🔧 准备构建环境...")
    
    # 清理
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    print("  ✅ 环境已设置")

def build_with_tools():
    """构建包含工具的版本"""
    print("🔨 构建包含注入工具的版本...")
    
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--name=MeetSpaceWeChatSender",
        "--icon=resources/icons/app_icon.ico",
        
        # 关键工具文件
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64;tools/x64",
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        
        # 基础资源
        "--add-data=resources/icons;resources/icons",
        "--add-data=version_info.txt;.",
        
        # 核心模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=pandas",
        "--hidden-import=requests",
        "--hidden-import=PIL",
        "--hidden-import=ctypes",
        "--hidden-import=subprocess",
        "--hidden-import=psutil",
        
        # 项目模块
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=ui",
        "--hidden-import=utils",
        "--hidden-import=runtime_config_generator",
        
        # 排除
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        
        # 选项
        "--clean",
        "--noconfirm",
        
        "main.py"
    ]
    
    print("📋 开始构建...")
    
    try:
        result = subprocess.run(
            cmd, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=600
        )
        
        print("✅ 构建完成")
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e.returncode}")
        if e.stderr:
            print("错误:", e.stderr[-1000:])
        return False

def check_result():
    """检查构建结果"""
    print("🔍 检查构建结果...")
    
    exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"✅ 构建成功: {exe_file}")
        print(f"📊 大小: {size_mb:.1f} MB")
        return True
    else:
        print("❌ 未找到输出文件")
        return False

def main():
    """主函数"""
    print("🛠️  简化工具包构建")
    print("=" * 40)
    
    # 检查关键文件
    required = [
        "tools/Injector.exe",
        "wxhelper_files/wxhelper.dll",
        "main.py"
    ]
    
    for file_path in required:
        if not Path(file_path).exists():
            print(f"❌ 缺少: {file_path}")
            return False
        print(f"✅ 找到: {file_path}")
    
    # 构建
    clean_and_setup()
    
    if not build_with_tools():
        return False
    
    if not check_result():
        return False
    
    print("\n🎉 工具包构建成功!")
    print("📁 输出: dist/MeetSpaceWeChatSender.exe")
    print("🔧 包含: 注入工具 + DLL文件 + 完整功能")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
