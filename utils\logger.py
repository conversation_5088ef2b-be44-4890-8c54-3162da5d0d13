"""
日志工具模块

提供统一的日志管理功能。
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional

# 日志配置常量
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5


class ChineseFormatter(logging.Formatter):
    """中文化的日志格式化器"""

    # 日志级别中文映射
    LEVEL_MAP = {
        "DEBUG": "调试",
        "INFO": "信息",
        "WARNING": "警告",
        "ERROR": "错误",
        "CRITICAL": "严重",
    }

    # 模块名称中文映射
    MODULE_MAP = {
        "timing_sender": "定时发送",
        "loop_sender": "循环发送",
        "timing_send_page": "定时发送页面",
        "loop_send_page": "循环发送页面",
        "task_status_page": "任务状态页面",
        "group_manager": "分组管理",
        "message_sender_core": "消息发送核心",
        "message_template": "消息模板",
        "rich_text_editor": "富文本编辑器",
        "config_manager": "配置管理",
        "risk_control": "风控管理",
        "wechatferry_connector": "微信连接器",
        "http_api_connector": "HTTP接口连接器",
        "ui_automation_connector": "UI自动化连接器",
        "winapi_connector": "Windows接口连接器",
        "enhanced_theme_manager": "主题管理",
        "performance_optimizer": "性能优化",
        "ui_optimizer": "UI优化",
        "startup": "启动管理",
        "main": "主程序",
        "ui": "用户界面",
        "send_monitor": "发送监控",
        "optimized_sender": "优化发送器",
        "button_state_manager": "按钮状态管理",
        "group_card": "分组卡片",
        "loop_cycle_widget": "循环设置组件",
        "group_config_manager": "分组配置管理",
        "simple_admin": "权限管理",
        "smart_injector": "智能注入器",
        "auto_injector": "自动注入器",
        "injector_adapter": "注入器适配器",
        "custom_main_window": "自定义主窗口",
        "custom_title_bar": "自定义标题栏",
    }

    def format(self, record):
        """格式化日志记录"""
        # 转换日志级别为中文
        chinese_level = self.LEVEL_MAP.get(record.levelname, record.levelname)

        # 转换模块名称为中文
        chinese_module = self.MODULE_MAP.get(record.name, record.name)

        # 创建新的记录副本
        new_record = logging.makeLogRecord(record.__dict__)
        new_record.levelname = chinese_level
        new_record.name = chinese_module

        # 使用父类格式化
        return super().format(new_record)


def setup_logger(
    name: str, log_file: Optional[str] = None, level: str = LOG_LEVEL
) -> logging.Logger:
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，如果为None则使用默认路径
        level: 日志级别

    Returns:
        配置好的日志记录器
    """
    # 获取日志目录（延迟导入避免循环依赖）
    try:
        from utils.path_manager import path_manager
        logs_dir = path_manager.app_data_dir / "logs"
    except ImportError:
        # 备用方案：使用相对路径
        logs_dir = Path(__file__).parent.parent / "logs"

    # 确保日志目录存在
    logs_dir.mkdir(parents=True, exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))

    # 如果已经有处理器，直接返回
    if logger.handlers:
        return logger

    # 创建中文化格式化器
    formatter = ChineseFormatter(LOG_FORMAT, LOG_DATE_FORMAT)

    # 添加控制台处理器
    _add_console_handler(logger, formatter)

    # 添加文件处理器
    _add_file_handler(logger, formatter, log_file, level, name)

    return logger


def _add_console_handler(logger: logging.Logger, formatter: logging.Formatter) -> None:
    """添加控制台处理器"""
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)


def _add_file_handler(
    logger: logging.Logger,
    formatter: logging.Formatter,
    log_file: Optional[str],
    level: str,
    name: str,
) -> None:
    """添加文件处理器"""
    if log_file is None:
        # 获取日志目录
        try:
            from utils.path_manager import path_manager
            logs_dir = path_manager.app_data_dir / "logs"
        except ImportError:
            logs_dir = Path(__file__).parent.parent / "logs"

        log_file_path = logs_dir / f"{name}.log"
    else:
        log_file_path = Path(log_file)
        log_file_path.parent.mkdir(parents=True, exist_ok=True)

    file_handler = logging.handlers.RotatingFileHandler(
        log_file_path,
        maxBytes=LOG_MAX_SIZE,
        backupCount=LOG_BACKUP_COUNT,
        encoding="utf-8",
    )
    file_handler.setLevel(getattr(logging, level.upper()))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)


def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        日志记录器
    """
    return logging.getLogger(name)


class LoggerMixin:
    """日志记录器混入类"""

    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志记录器"""
        if not hasattr(self, "_logger"):
            self._logger = get_logger(self.__class__.__name__)
        return self._logger
