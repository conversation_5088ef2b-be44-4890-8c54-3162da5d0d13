#!/usr/bin/env python3
"""
主题修复验证脚本
验证科技主题中的尺寸和交互问题是否已修复
"""

import sys
import os
from pathlib import Path

def check_theme_file():
    """检查主题文件的修复状态"""
    print("🔍 检查主题文件修复状态...")
    
    theme_file = Path(__file__).parent / "ui" / "modern_theme_manager.py"
    
    if not theme_file.exists():
        print("❌ 主题文件不存在")
        return False
    
    try:
        content = theme_file.read_text(encoding='utf-8')
        
        # 检查修复项目
        checks = {
            "选项框尺寸定义": "width: 16px;\n                    height: 16px;",
            "按钮尺寸保护": "/* 不修改padding和尺寸，保持系统默认 */",
            "SpinBox尺寸保护": "/* 保持系统默认尺寸和padding */",
            "成功按钮样式": 'QPushButton[class="success"]',
            "次要按钮样式": 'QPushButton[class="secondary"]',
            "危险按钮样式": 'QPushButton[class="danger"]',
            "禁用状态样式": "QCheckBox::indicator:disabled",
            "单选按钮禁用样式": "QRadioButton::indicator:disabled",
        }
        
        results = {}
        for check_name, check_pattern in checks.items():
            if check_pattern in content:
                results[check_name] = "✅ 通过"
            else:
                results[check_name] = "❌ 缺失"
        
        # 检查问题项目
        issues = {
            "CSS content属性": "content:",
            "重复样式定义": "QRadioButton::indicator:checked {",  # 应该只有一个
        }
        
        for issue_name, issue_pattern in issues.items():
            count = content.count(issue_pattern)
            if issue_name == "重复样式定义":
                if count <= 3:  # 每个主题一个，总共3个主题
                    results[f"无{issue_name}"] = "✅ 通过"
                else:
                    results[f"无{issue_name}"] = f"❌ 发现{count}个"
            else:
                if count == 0:
                    results[f"无{issue_name}"] = "✅ 通过"
                else:
                    results[f"无{issue_name}"] = f"❌ 发现{count}个"
        
        # 显示结果
        print("\n📊 检查结果:")
        for check_name, result in results.items():
            print(f"  {check_name}: {result}")
        
        # 统计
        passed = sum(1 for result in results.values() if result.startswith("✅"))
        total = len(results)
        
        print(f"\n📈 总体状态: {passed}/{total} 项通过")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 检查主题文件失败: {e}")
        return False

def generate_test_instructions():
    """生成测试说明"""
    print("\n📋 手动测试说明:")
    print("=" * 50)
    
    print("\n1. 🎨 主题切换测试")
    print("   - 启动程序: python main.py")
    print("   - 进入'系统设置'页面")
    print("   - 切换主题: 默认主题 → 科技主题")
    print("   - 观察控件尺寸是否保持一致")
    
    print("\n2. 🔘 选项框交互测试")
    print("   - 点击'最小化到系统托盘'复选框")
    print("   - 点击'启动时检查更新'复选框")
    print("   - 点击'自动备份'复选框")
    print("   - 确认所有选项框都能正常点击和选择")
    
    print("\n3. 🔲 按钮尺寸测试")
    print("   - 观察'保存设置'按钮（绿色成功按钮）")
    print("   - 观察'重置设置'按钮（灰色次要按钮）")
    print("   - 确认按钮尺寸与默认主题一致")
    
    print("\n4. 📝 输入控件测试")
    print("   - 测试'重试延迟'数字输入框")
    print("   - 测试'主题选择'下拉框")
    print("   - 测试'字体大小'下拉框")
    print("   - 确认所有控件尺寸正常")
    
    print("\n5. 🎯 标题栏测试")
    print("   - 打开任意对话框")
    print("   - 观察标题栏是否为深色主题")
    print("   - 切换回默认主题，标题栏应恢复系统样式")
    
    print("\n✅ 预期结果:")
    print("   - 所有控件尺寸在不同主题下保持一致")
    print("   - 选项框能正常点击和选择")
    print("   - 按钮样式正确，无尺寸异常")
    print("   - 对话框标题栏正确跟随主题")
    print("   - 控制台无CSS属性错误警告")

def main():
    """主函数"""
    print("🚀 主题修复验证工具")
    print("=" * 50)
    
    # 检查文件修复状态
    file_check_passed = check_theme_file()
    
    if file_check_passed:
        print("\n🎉 所有修复项目检查通过！")
        print("\n📋 修复内容总结:")
        print("1. ✅ 选项框尺寸已标准化为16x16px")
        print("2. ✅ 按钮样式已保护，不修改原始尺寸")
        print("3. ✅ 输入控件已保护，保持系统默认尺寸")
        print("4. ✅ 特殊按钮样式已完整定义")
        print("5. ✅ 禁用状态样式已添加")
        print("6. ✅ CSS属性错误已修复")
        print("7. ✅ 重复样式定义已清理")
        
        # 生成测试说明
        generate_test_instructions()
        
        print("\n🔧 如需进一步测试，请运行:")
        print("   python fix_theme_sizes.py --test")
        
    else:
        print("\n❌ 发现未修复的问题，请检查上述结果")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
