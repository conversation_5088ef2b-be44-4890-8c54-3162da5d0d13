#!/usr/bin/env python3
"""
创建便携版打包
"""

import os
import shutil
import zipfile
from pathlib import Path
import time

def create_portable_package():
    """创建便携版打包"""
    print("🚀 创建便携版打包...")
    
    project_root = Path(__file__).parent
    dist_dir = project_root / "dist" / "MeetSpaceWeChatSender"
    
    if not dist_dir.exists():
        print("❌ 构建目录不存在，请先运行构建")
        return False
    
    # 创建便携版目录
    portable_dir = project_root / "portable"
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    
    portable_dir.mkdir()
    
    print("📁 复制程序文件...")
    
    # 复制整个dist目录内容
    app_dir = portable_dir / "MeetSpaceWeChatSender"
    shutil.copytree(dist_dir, app_dir)
    
    # 创建启动脚本
    print("📝 创建启动脚本...")
    
    # Windows批处理文件
    bat_content = '''@echo off
chcp 65001 >nul
title Meet space 微信群发助手

echo.
echo ========================================
echo   Meet space 微信群发助手
echo   便携版启动器
echo ========================================
echo.

cd /d "%~dp0MeetSpaceWeChatSender"

if not exist "MeetSpaceWeChatSender.exe" (
    echo ❌ 错误: 找不到主程序文件
    echo    请确保文件完整性
    pause
    exit /b 1
)

echo ✅ 启动程序...
start "" "MeetSpaceWeChatSender.exe"

echo.
echo 程序已启动，可以关闭此窗口
timeout /t 3 >nul
'''
    
    bat_file = portable_dir / "启动程序.bat"
    bat_file.write_text(bat_content, encoding='utf-8')
    
    # 创建说明文件
    print("📄 创建说明文件...")
    
    readme_content = '''# Meet space 微信群发助手 - 便携版

## 使用说明

1. 双击 "启动程序.bat" 启动程序
2. 或者直接运行 MeetSpaceWeChatSender/MeetSpaceWeChatSender.exe

## 系统要求

- Windows 10/11 (64位)
- 微信PC版 (最新版本)

## 功能特性

- 微信群发消息
- 支持文本、图片、文件发送
- 定时发送功能
- 循环发送功能
- 风险控制机制

## 注意事项

1. 首次使用需要以管理员身份运行
2. 确保微信PC版已登录
3. 建议关闭杀毒软件的实时保护（可能误报）

## 技术支持

- 官网: https://meetspace.cn
- 邮箱: <EMAIL>

---
Meet space 会客创意空间
版本: 1.0.0
'''
    
    readme_file = portable_dir / "使用说明.txt"
    readme_file.write_text(readme_content, encoding='utf-8')
    
    # 创建ZIP压缩包
    print("📦 创建ZIP压缩包...")
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    zip_name = f"MeetSpace_WeChatSender_Portable_v1.0.0_{timestamp}.zip"
    zip_path = project_root / zip_name
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
        for root, dirs, files in os.walk(portable_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(portable_dir)
                zipf.write(file_path, arc_name)
                
    # 计算文件大小
    zip_size = zip_path.stat().st_size / (1024 * 1024)  # MB
    portable_size = sum(f.stat().st_size for f in portable_dir.rglob('*') if f.is_file()) / (1024 * 1024)
    
    print(f"✅ 便携版创建完成!")
    print(f"📁 便携版目录: {portable_dir}")
    print(f"📦 压缩包: {zip_path}")
    print(f"📊 便携版大小: {portable_size:.1f} MB")
    print(f"📊 压缩包大小: {zip_size:.1f} MB")
    
    return True

def main():
    """主函数"""
    print("📦 Meet space 微信群发助手 - 便携版打包工具")
    print("=" * 50)
    
    success = create_portable_package()
    
    if success:
        print("\n🎉 便携版打包成功！")
        
        # 询问是否打开文件夹
        try:
            choice = input("\n是否打开输出文件夹？(y/n): ").strip().lower()
            if choice == 'y':
                import subprocess
                subprocess.run(['explorer', str(Path(__file__).parent)], check=False)
        except KeyboardInterrupt:
            pass
            
    else:
        print("\n💥 便携版打包失败！")

if __name__ == "__main__":
    main()
