# UTF-8
#
# 版本信息文件
#
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'Meet Space'),
            StringStruct(u'FileDescription', u'安全、高效的微信群发工具'),
            StringStruct(u'FileVersion', u'*******'),
            StringStruct(u'InternalName', u'MeetSpaceWeChatSender'),
            StringStruct(u'LegalCopyright', u'© 2025 Meet Space. All rights reserved.'),
            StringStruct(u'OriginalFilename', u'MeetSpaceWeChatSender.exe'),
            StringStruct(u'ProductName', u'Meet Space 微信群发助手'),
            StringStruct(u'ProductVersion', u'*******'),
            StringStruct(u'Comments', u'生产环境版本 - 2025-08-07'),
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)