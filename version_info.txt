
# Meet space 微信群发助手 - 版本信息文件
# Copyright © 2024 Meet space 会客创意空间
# 用于PyInstaller打包时的版本信息配置

# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    # 文件版本号 (主版本.次版本.修订版.构建版)
    filevers=(1, 0, 0, 0),
    # 产品版本号
    prodvers=(1, 0, 0, 0),
    # 版本信息掩码
    mask=0x3f,
    # 版本标志
    flags=0x0,
    # 操作系统标识 (Windows NT)
    OS=0x40004,
    # 文件类型 (应用程序)
    fileType=0x1,
    # 文件子类型
    subtype=0x0,
    # 文件日期
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',  # 语言代码 (简体中文)
        [
        # 公司名称
        StringStruct(u'CompanyName', u'Meet space 会客创意空间'),
        # 文件描述
        StringStruct(u'FileDescription', u'Meet space 微信群发助手 - 功能强大的微信群发工具'),
        # 文件版本
        StringStruct(u'FileVersion', u'*******'),
        # 内部名称
        StringStruct(u'InternalName', u'MeetSpaceWeChatSender'),
        # 版权信息
        StringStruct(u'LegalCopyright', u'Copyright © 2024 Meet space 会客创意空间. All rights reserved.'),
        # 法律商标
        StringStruct(u'LegalTrademarks', u'Meet space™'),
        # 原始文件名
        StringStruct(u'OriginalFilename', u'MeetSpaceWeChatSender.exe'),
        # 产品名称
        StringStruct(u'ProductName', u'Meet space 微信群发助手'),
        # 产品版本
        StringStruct(u'ProductVersion', u'1.0.0'),
        # 注释信息
        StringStruct(u'Comments', u'支持定时发送、循环发送、多主题界面的专业微信群发工具'),
        # 私有构建信息
        StringStruct(u'PrivateBuild', u'Release Build'),
        # 特殊构建信息
        StringStruct(u'SpecialBuild', u'Production Release')
        ]
      )
      ]),
    # 变量文件信息 (语言和字符集)
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)

# 版本历史：
# v1.0.0 (2024-08-05) - 首个正式版本
#   - 完整的定时发送和循环发送功能
#   - 五大主题系统（默认、浅色、深色、护眼、科技）
#   - 智能风控和安全机制
#   - 响应式界面设计
#   - 性能优化和稳定性提升
