"""
定时发送管理模块

管理定时发送任务的创建、执行和监控。
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from PyQt6.QtCore import QObject, QTimer, QThread, pyqtSignal

from core.group_manager import ContactGroup, group_manager
from core.message_sender_core import (
    message_sender_core,
    MessageType,
    SendTask,
    SendMember,
    SendProgress,
    SendResult,
)
from utils.logger import setup_logger

logger = setup_logger("timing_sender")


class TimingTask:
    """定时发送任务类"""

    def __init__(
        self,
        task_id: str,
        group_id: str,
        group_name: str,
        execute_date: str,
        execute_time: str,
        message_content: str,
        message_type: str = "text",
    ):
        self.task_id = task_id
        self.group_id = group_id
        self.group_name = group_name
        self.execute_date = execute_date  # YYYY-MM-DD
        self.execute_time = execute_time  # HH:MM
        self.message_content = message_content
        self.message_type = message_type  # text, rich_text
        self.status = "pending"  # pending, executing, completed, failed, cancelled, paused
        self.created_time = datetime.now()
        self.executed_time = None
        self.end_time = None  # 任务结束时间
        self.error_message = ""
        self.sent_count = 0
        self.total_count = 0

        # 新增：已发送联系人跟踪和内容更新支持
        self.sent_members = set()  # 已发送的联系人wxid集合
        self.failed_members = set()  # 发送失败的联系人wxid集合
        self.original_content = message_content  # 原始内容（用于检测内容是否更改）
        self.content_updated = False  # 内容是否已更新

    def get_execute_datetime(self) -> datetime:
        """获取执行时间的datetime对象"""
        datetime_str = f"{self.execute_date} {self.execute_time}:00"
        return datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")

    def is_ready_to_execute(self) -> bool:
        """检查是否到了执行时间"""
        if self.status != "pending":
            return False

        execute_datetime = self.get_execute_datetime()
        return datetime.now() >= execute_datetime

    def update_progress(self, sent_count: int, total_count: int):
        """更新发送进度"""
        self.sent_count = sent_count
        self.total_count = total_count

        # 更新分组进度
        group_manager.update_group_progress(self.group_id, sent_count, total_count)

    def mark_completed(self):
        """标记任务完成"""
        self.status = "completed"
        self.executed_time = datetime.now()
        self.end_time = datetime.now()
        logger.info(f"定时任务完成: {self.task_id}")

    def mark_failed(self, error_message: str):
        """标记任务失败"""
        self.status = "failed"
        self.executed_time = datetime.now()
        self.end_time = datetime.now()
        self.error_message = error_message
        logger.error(f"定时任务失败: {self.task_id}, 错误: {error_message}")

    def cancel(self):
        """取消任务"""
        self.status = "cancelled"
        logger.info(f"定时任务已取消: {self.task_id}")

    def pause(self):
        """暂停任务"""
        if self.status == "executing":
            self.status = "paused"
            logger.info(f"任务 {self.task_id} 已暂停")

    def resume(self):
        """恢复任务"""
        if self.status == "paused":
            self.status = "executing"
            logger.info(f"任务 {self.task_id} 已恢复")

    def update_content(self, new_content: str, new_type: str = None):
        """更新任务内容（暂停后修改内容时调用）"""
        if new_content != self.original_content:
            self.message_content = new_content
            self.content_updated = True
            logger.info(f"任务 {self.task_id} 内容已更新")

        if new_type and new_type != self.message_type:
            self.message_type = new_type
            self.content_updated = True
            logger.info(f"任务 {self.task_id} 消息类型已更新为 {new_type}")

    def add_sent_member(self, wxid: str):
        """添加已发送的联系人"""
        self.sent_members.add(wxid)

    def add_failed_member(self, wxid: str):
        """添加发送失败的联系人"""
        self.failed_members.add(wxid)

    def is_member_sent(self, wxid: str) -> bool:
        """检查联系人是否已发送"""
        return wxid in self.sent_members

    def get_unsent_members(self, all_members: list) -> list:
        """获取未发送的联系人列表"""
        return [member for member in all_members if member.wxid not in self.sent_members]

    def get_failed_count(self) -> int:
        """获取发送失败的数量"""
        return len(self.failed_members)

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "group_id": self.group_id,
            "group_name": self.group_name,
            "execute_date": self.execute_date,
            "execute_time": self.execute_time,
            "message_content": self.message_content,
            "message_type": self.message_type,
            "status": self.status,
            "created_time": self.created_time.isoformat(),
            "executed_time": (
                self.executed_time.isoformat() if self.executed_time else None
            ),
            "error_message": self.error_message,
            "sent_count": self.sent_count,
            "total_count": self.total_count,
            # 新增字段
            "sent_members": list(self.sent_members),
            "failed_members": list(self.failed_members),
            "original_content": self.original_content,
            "content_updated": self.content_updated,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "TimingTask":
        """从字典创建任务"""
        task = cls(
            data["task_id"],
            data["group_id"],
            data["group_name"],
            data["execute_date"],
            data["execute_time"],
            data["message_content"],
            data.get("message_type", "text"),  # 兼容旧数据
        )
        task.status = data.get("status", "pending")
        task.created_time = datetime.fromisoformat(data["created_time"])
        if data.get("executed_time"):
            task.executed_time = datetime.fromisoformat(data["executed_time"])
        task.error_message = data.get("error_message", "")
        task.sent_count = data.get("sent_count", 0)
        task.total_count = data.get("total_count", 0)

        # 新增字段（向后兼容）
        task.sent_members = set(data.get("sent_members", []))
        task.failed_members = set(data.get("failed_members", []))
        task.original_content = data.get("original_content", data["message_content"])
        task.content_updated = data.get("content_updated", False)

        return task


class TimingSender(QObject):
    """定时发送管理器"""

    # 信号定义
    task_started = pyqtSignal(str)  # 任务开始
    task_progress = pyqtSignal(str, int, int)  # 任务进度 (task_id, sent, total)
    task_completed = pyqtSignal(str)  # 任务完成
    task_failed = pyqtSignal(str, str)  # 任务失败 (task_id, error)
    task_paused = pyqtSignal(str)  # 任务暂停
    task_resumed = pyqtSignal(str)  # 任务恢复

    def __init__(self):
        super().__init__()
        self.data_dir = Path("data/timing")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        self.tasks_file = self.data_dir / "timing_tasks.json"
        self.tasks: Dict[str, TimingTask] = {}

        # 定时器初始化为None，稍后在主线程中创建
        self.check_timer = None

        self.load_tasks()
        logger.info("定时发送管理器初始化完成")

    def init_timer(self):
        """在主线程中初始化定时器"""
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app and QThread.currentThread() != app.thread():
            logger.warning("定时器应该在主线程中初始化，当前在子线程中")
            # 使用QMetaObject.invokeMethod确保在主线程中执行
            from PyQt6.QtCore import QMetaObject, Qt
            QMetaObject.invokeMethod(self, "_init_timer_impl", Qt.ConnectionType.QueuedConnection)
            return

        self._init_timer_impl()

    def _init_timer_impl(self):
        """实际的定时器初始化实现"""
        if self.check_timer is None:
            # 智能定时器，根据最近任务时间动态调整检查间隔
            self.check_timer = QTimer()
            self.check_timer.timeout.connect(self.check_pending_tasks)
            self._schedule_next_check()
            logger.info("定时任务检查器已启动，智能调度模式")
        else:
            logger.debug("定时器已经初始化")

    def _schedule_next_check(self):
        """智能调度下次检查时间"""
        try:
            # 获取最近的待执行任务
            pending_tasks = self.get_pending_tasks()
            if not pending_tasks:
                # 没有待执行任务，使用较长间隔检查
                interval = 60000  # 60秒
                logger.debug("无待执行任务，60秒后再次检查")
            else:
                # 找到最近的任务执行时间
                now = datetime.now()
                next_times = []

                for task in pending_tasks:
                    task_execute_time = task.get_execute_datetime()
                    if task_execute_time > now:
                        next_times.append(task_execute_time)

                if next_times:
                    # 找到最近的执行时间
                    nearest_time = min(next_times)
                    time_diff = (nearest_time - now).total_seconds()

                    if time_diff <= 60:  # 1分钟内
                        interval = max(1000, int(time_diff * 1000 * 0.8))  # 提前20%检查，最少1秒
                        logger.info(f"最近任务将在{time_diff:.1f}秒后执行，{interval/1000:.1f}秒后检查")
                    elif time_diff <= 300:  # 5分钟内
                        interval = 30000  # 30秒检查一次
                        logger.debug(f"最近任务将在{time_diff/60:.1f}分钟后执行，30秒后检查")
                    elif time_diff <= 3600:  # 1小时内
                        interval = 60000  # 1分钟检查一次
                        logger.debug(f"最近任务将在{time_diff/60:.1f}分钟后执行，1分钟后检查")
                    else:  # 1小时以上
                        interval = 300000  # 5分钟检查一次
                        logger.debug(f"最近任务将在{time_diff/3600:.1f}小时后执行，5分钟后检查")
                else:
                    # 所有任务都已过期，立即检查
                    interval = 1000  # 1秒
                    logger.info("发现过期任务，1秒后立即检查")

            # 启动定时器
            if self.check_timer:
                self.check_timer.start(interval)
            else:
                logger.warning("定时器未初始化，无法启动")

        except Exception as e:
            logger.error(f"调度下次检查时间失败: {e}")
            # 回退到固定间隔
            if self.check_timer:
                self.check_timer.start(10000)  # 10秒
            else:
                logger.warning("定时器未初始化，无法启动回退定时器")

    def generate_task_id(self) -> str:
        """生成任务ID"""
        import random
        timestamp = int(time.time())
        random_suffix = random.randint(1000, 9999)
        task_id = f"timing_{timestamp}_{random_suffix}"

        # 确保ID唯一
        while task_id in self.tasks:
            random_suffix = random.randint(1000, 9999)
            task_id = f"timing_{timestamp}_{random_suffix}"

        return task_id

    def create_task(
        self,
        group_id: str,
        execute_date: str,
        execute_time: str,
        message_content: str,
        message_type: str = "text",
    ) -> Optional[TimingTask]:
        """创建定时发送任务"""
        # 获取分组信息
        group = group_manager.get_group(group_id, "timing")
        if not group:
            logger.error(f"分组不存在: {group_id}")
            return None

        # 验证时间格式
        try:
            datetime.strptime(f"{execute_date} {execute_time}", "%Y-%m-%d %H:%M")
        except ValueError:
            logger.error(f"时间格式错误: {execute_date} {execute_time}")
            return None

        # 创建任务
        task_id = self.generate_task_id()
        task = TimingTask(
            task_id,
            group_id,
            group.name,
            execute_date,
            execute_time,
            message_content,
            message_type,
        )

        # 设置总数
        task.total_count = group.get_member_count()

        self.tasks[task_id] = task
        self.save_tasks()

        logger.info(
            f"创建定时任务: {task_id}, 分组: {group.name}, 执行时间: {execute_date} {execute_time}, 消息类型: {message_type}"
        )

        # 检查是否应该立即执行
        if task.is_ready_to_execute():
            logger.info(f"任务已到执行时间，立即执行: {task_id}")
            self.execute_task(task)
        else:
            # 立即触发一次检查，确保定时器能及时发现新任务
            QTimer.singleShot(1000, self.check_pending_tasks)

        return task

    def cancel_task(self, task_id: str) -> bool:
        """取消定时任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == "pending":
                task.cancel()
                self.save_tasks()
                return True
        return False

    def pause_task(self, task_id: str) -> bool:
        """暂停定时任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status in ["pending", "executing"]:
                task.pause()
                self.save_tasks()
                self.task_paused.emit(task_id)
                return True
        return False

    def resume_task(self, task_id: str, new_content: str = None, new_type: str = None) -> bool:
        """继续定时任务，支持更新内容"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == "paused":
                # 如果提供了新内容，更新任务内容
                if new_content is not None:
                    task.update_content(new_content, new_type)

                task.resume()
                # 如果任务已到执行时间，将状态设为pending让调度器处理
                if task.is_ready_to_execute():
                    task.status = "pending"
                else:
                    task.status = "pending"

                self.save_tasks()
                self.task_resumed.emit(task_id)
                return True
        return False

    def stop_task(self, task_id: str) -> bool:
        """停止定时任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status in ["pending", "executing", "paused"]:
                task.status = "cancelled"
                self.save_tasks()
                logger.info(f"停止定时任务: {task_id}")
                return True
        return False

    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            self.save_tasks()
            logger.info(f"删除定时任务: {task_id}")
            return True
        return False

    def get_task(self, task_id: str) -> Optional[TimingTask]:
        """获取任务"""
        return self.tasks.get(task_id)

    def get_all_tasks(self) -> List[TimingTask]:
        """获取所有任务"""
        return list(self.tasks.values())

    def get_pending_tasks(self) -> List[TimingTask]:
        """获取待执行任务"""
        return [task for task in self.tasks.values() if task.status == "pending"]

    def get_tasks_by_group(self, group_id: str) -> List[TimingTask]:
        """获取指定分组的任务"""
        return [task for task in self.tasks.values() if task.group_id == group_id]

    def check_pending_tasks(self):
        """检查待执行任务"""
        try:
            pending_tasks = self.get_pending_tasks()
            ready_tasks = [task for task in pending_tasks if task.is_ready_to_execute()]

            if pending_tasks:
                logger.debug(
                    f"检查待执行任务: 总数={len(pending_tasks)}, 就绪={len(ready_tasks)}"
                )

                # 显示每个待执行任务的状态
                for task in pending_tasks:
                    execute_time = task.get_execute_datetime()
                    current_time = datetime.now()
                    time_diff = (execute_time - current_time).total_seconds()
                    logger.debug(
                        f"任务 {task.task_id}: 执行时间={execute_time}, 当前时间={current_time}, 差值={time_diff}秒, 状态={task.status}"
                    )

            for task in ready_tasks:
                logger.info(f"执行就绪任务: {task.task_id}")
                self.execute_task(task)

        except Exception as e:
            logger.error(f"检查待执行任务异常: {e}")
        finally:
            # 检查完成后，重新调度下次检查时间
            self._schedule_next_check()

    def execute_task(self, task: TimingTask):
        """执行定时任务（完整逻辑确认版）"""
        logger.info(f"开始执行定时任务: {task.task_id}")

        try:
            # 1. 确认系统设置逻辑
            if not self._check_system_settings():
                task.mark_failed("系统设置检查失败")
                self.task_failed.emit(task.task_id, "系统设置检查失败")
                self.save_tasks()
                return

            # 2. 获取分组
            group = group_manager.get_group(task.group_id, "timing")
            if not group:
                task.mark_failed("分组不存在")
                self.task_failed.emit(task.task_id, "分组不存在")
                self.save_tasks()
                return

            # 3. 确认定时发送设置
            if not self._check_timing_settings(task):
                task.mark_failed("定时发送设置检查失败")
                self.task_failed.emit(task.task_id, "定时发送设置检查失败")
                self.save_tasks()
                return

            # 4. 更新分组使用统计
            group.update_usage()
            group_manager.save_groups("timing")

            # 5. 标记任务开始执行
            task.status = "executing"
            task.executed_time = datetime.now()
            self.task_started.emit(task.task_id)
            self.save_tasks()

            # 6. 执行实际发送逻辑
            self._execute_real_send_task(task, group)

        except Exception as e:
            logger.error(f"执行定时任务失败: {e}")
            task.mark_failed(str(e))
            self.task_failed.emit(task.task_id, str(e))
            self.save_tasks()

    def _check_system_settings(self) -> bool:
        """确认系统设置逻辑"""
        try:
            # 检查连接器是否可用
            if not hasattr(self, "connector") or not self.connector:
                logger.warning("连接器未初始化，将跳过发送")
                return False

            # 检查连接状态（更宽松的检查）
            try:
                if not self.connector.is_connected:
                    logger.warning("微信未连接，将跳过发送")
                    return False
            except Exception as e:
                logger.warning(f"无法检查连接状态: {e}")
                return False

            # 检查登录状态（更宽松的检查）
            try:
                if not self.connector.is_logged_in:
                    logger.warning("微信未登录，将跳过发送")
                    return False
            except Exception as e:
                logger.warning(f"无法检查登录状态: {e}")
                return False

            logger.info("系统设置检查通过")
            return True

        except Exception as e:
            logger.error(f"系统设置检查失败: {e}")
            return False

    def _check_timing_settings(self, task: TimingTask) -> bool:
        """确认定时发送设置"""
        try:
            # 检查任务基本信息
            if not task.message_content.strip():
                logger.error("消息内容为空")
                return False

            # 检查执行时间是否合理
            execute_datetime = task.get_execute_datetime()
            now = datetime.now()

            # 允许一定的时间误差（5分钟）
            time_diff = abs((execute_datetime - now).total_seconds())
            if time_diff > 300:  # 5分钟
                logger.warning(f"执行时间与当前时间差异较大: {time_diff}秒")

            # 注意：这里不检查任务状态，因为在execute_task中会先设置状态为executing
            # 这个方法是在状态设置之前调用的

            logger.info("定时发送设置检查通过")
            return True

        except Exception as e:
            logger.error(f"定时发送设置检查失败: {e}")
            return False

    def _check_risk_limits(self, group_id: str) -> dict:
        """检查风控限制"""
        try:
            from core.config_manager import config_manager

            # 获取风控设置
            config = config_manager.get_config()
            risk_settings = config.get("risk_control", {})

            # 检查是否启用风控
            if not risk_settings.get("enable_risk_control", False):
                return {"allowed": True, "reason": ""}

            # 获取限制设置
            daily_limit = risk_settings.get("daily_send_limit", 500)
            hourly_limit = risk_settings.get("hourly_send_limit", 50)

            # 统计当天发送数量
            daily_count = self._get_daily_send_count(group_id)

            # 统计当前小时发送数量
            hourly_count = self._get_hourly_send_count(group_id)

            logger.info(
                f"风控检查 - 分组: {group_id}, 今日发送: {daily_count}/{daily_limit}, 本小时发送: {hourly_count}/{hourly_limit}"
            )

            # 检查每日限制
            if daily_count >= daily_limit:
                reason = (
                    f"❌ 超出风控限制 - 已达每日发送上限({daily_count}/{daily_limit})"
                )
                return {"allowed": False, "reason": reason}

            # 检查每小时限制
            if hourly_count >= hourly_limit:
                reason = f"❌ 超出风控限制 - 已达每小时发送上限({hourly_count}/{hourly_limit})"
                return {"allowed": False, "reason": reason}

            return {"allowed": True, "reason": ""}

        except Exception as e:
            logger.error(f"风控限制检查失败: {e}")
            # 出错时允许发送，但记录错误
            return {"allowed": True, "reason": ""}

    def _get_daily_send_count(self, group_id: str) -> int:
        """获取当天该分组的发送数量"""
        try:
            today = datetime.now().date()
            count = 0

            # 统计所有已完成任务的发送数量
            for task in self.tasks.values():
                if (
                    task.group_id == group_id
                    and task.status == "completed"
                    and task.executed_time
                    and task.executed_time.date() == today
                ):
                    count += task.sent_count

            return count

        except Exception as e:
            logger.error(f"获取每日发送数量失败: {e}")
            return 0

    def _get_hourly_send_count(self, group_id: str) -> int:
        """获取当前小时该分组的发送数量"""
        try:
            now = datetime.now()
            current_hour_start = now.replace(minute=0, second=0, microsecond=0)
            current_hour_end = current_hour_start + timedelta(hours=1)

            count = 0

            # 统计当前小时内已完成任务的发送数量
            for task in self.tasks.values():
                if (
                    task.group_id == group_id
                    and task.status == "completed"
                    and task.executed_time
                    and current_hour_start <= task.executed_time < current_hour_end
                ):
                    count += task.sent_count

            return count

        except Exception as e:
            logger.error(f"获取每小时发送数量失败: {e}")
            return 0

    def _execute_real_send_task(self, task: TimingTask, group: ContactGroup):
        """执行真实的发送任务（使用统一的消息发送核心）"""
        import threading
        import asyncio

        def send_worker():
            try:
                logger.info(f"开始发送定时任务: {task.task_id} 到分组: {group.name}")

                # 获取分组成员
                members = group.members
                if not members:
                    task.mark_failed("分组成员为空")
                    self.task_failed.emit(task.task_id, "分组成员为空")
                    self.save_tasks()
                    return

                # 过滤出未发送的成员（支持暂停恢复）
                unsent_members = task.get_unsent_members(members)
                if not unsent_members:
                    # 所有成员都已发送，标记任务完成
                    task.mark_completed()
                    self.task_completed.emit(task.task_id)
                    self.save_tasks()
                    logger.info(f"定时任务 {task.task_id} 所有成员已发送完成")
                    return

                # 转换为SendMember格式
                send_members = [SendMember(wxid=m.wxid, name=m.name) for m in unsent_members]
                logger.info(f"定时任务 {task.task_id}: 总成员{len(members)}个，未发送{len(unsent_members)}个")

                # 获取发送设置
                from core.config_manager import config_manager

                send_settings = config_manager.get_merged_send_settings()
                interval_seconds = send_settings.get("interval_seconds", 8)

                # 创建发送任务
                send_task = SendTask(
                    task_id=task.task_id,
                    message_content=task.message_content,
                    message_type=MessageType(task.message_type),
                    members=send_members,
                    send_interval=interval_seconds,
                    enable_risk_control=True,
                )

                # 设置连接器
                message_sender_core.set_connector(self.connector)

                # 清除之前的回调
                message_sender_core._progress_callbacks.clear()
                message_sender_core._member_callbacks.clear()

                # 添加进度回调
                def progress_callback(progress: SendProgress):
                    task.update_progress(progress.current, progress.total)
                    self.task_progress.emit(
                        task.task_id, progress.current, progress.total
                    )

                def member_callback(
                    task_id: str, member: SendMember, result: SendResult, error_msg: str
                ):
                    if result == SendResult.SUCCESS:
                        task.add_sent_member(member.wxid)
                        logger.debug(f"定时发送成功: {member.name}")
                    elif result == SendResult.FAILED:
                        task.add_failed_member(member.wxid)
                        logger.warning(f"定时发送失败: {member.name} - {error_msg}")
                    elif result == SendResult.SKIPPED:
                        logger.info(f"定时发送跳过: {member.name} - {error_msg}")
                    else:
                        task.add_failed_member(member.wxid)
                        logger.error(f"定时发送错误: {member.name} - {error_msg}")

                    # 实时保存任务状态
                    self.save_tasks()

                message_sender_core.add_progress_callback(progress_callback)
                message_sender_core.add_member_callback(member_callback)

                # 执行发送
                final_progress = asyncio.run(message_sender_core.send_task(send_task))

                # 任务完成
                task.sent_count = final_progress.success_count
                task.total_count = final_progress.total

                if final_progress.success_count > 0:
                    task.mark_completed()
                    self.task_completed.emit(task.task_id)
                    logger.info(f"定时任务完成: {task.task_id}")
                    logger.info(
                        f"结果统计: 成功={final_progress.success_count}, 失败={final_progress.failed_count}, 跳过={final_progress.skipped_count}"
                    )
                else:
                    task.mark_failed("所有消息发送失败")
                    self.task_failed.emit(task.task_id, "所有消息发送失败")

                self.save_tasks()

            except Exception as e:
                logger.error(f"发送任务执行失败: {e}")
                task.mark_failed(str(e))
                self.task_failed.emit(task.task_id, str(e))
                self.save_tasks()

        # 在后台线程执行发送
        thread = threading.Thread(target=send_worker, name=f"TimingSend-{task.task_id}")
        thread.daemon = True
        thread.start()

    def set_connector(self, connector):
        """设置连接器"""
        self.connector = connector
        logger.info("定时发送器已设置连接器")

    def simulate_send_task(self, task: TimingTask, group: ContactGroup):
        """模拟发送任务（实际实现时需要替换为真实发送逻辑）"""
        import threading

        def send_worker():
            try:
                total_members = len(group.members)
                for i, member in enumerate(group.members):
                    # 模拟发送延迟
                    time.sleep(0.1)

                    # 更新进度
                    sent_count = i + 1
                    task.update_progress(sent_count, total_members)
                    self.task_progress.emit(task.task_id, sent_count, total_members)

                    logger.debug(
                        f"模拟发送到 {member.name} ({sent_count}/{total_members})"
                    )

                # 任务完成
                task.mark_completed()
                self.task_completed.emit(task.task_id)
                self.save_tasks()

            except Exception as e:
                task.mark_failed(str(e))
                self.task_failed.emit(task.task_id, str(e))
                self.save_tasks()

        # 在后台线程执行发送
        thread = threading.Thread(target=send_worker)
        thread.daemon = True
        thread.start()

    def get_task_statistics(self) -> dict:
        """获取任务统计信息"""
        total = len(self.tasks)
        pending = len([t for t in self.tasks.values() if t.status == "pending"])
        executing = len([t for t in self.tasks.values() if t.status == "executing"])
        completed = len([t for t in self.tasks.values() if t.status == "completed"])
        failed = len([t for t in self.tasks.values() if t.status == "failed"])
        cancelled = len([t for t in self.tasks.values() if t.status == "cancelled"])

        return {
            "total": total,
            "pending": pending,
            "executing": executing,
            "completed": completed,
            "failed": failed,
            "cancelled": cancelled,
        }

    def cleanup_old_tasks(self, days: int = 30):
        """清理旧任务"""
        cutoff_date = datetime.now() - timedelta(days=days)
        old_tasks = []

        for task_id, task in self.tasks.items():
            if task.status in ["completed", "failed", "cancelled"]:
                if task.executed_time and task.executed_time < cutoff_date:
                    old_tasks.append(task_id)

        for task_id in old_tasks:
            del self.tasks[task_id]

        if old_tasks:
            self.save_tasks()
            logger.info(f"清理了 {len(old_tasks)} 个旧任务")

    def save_tasks(self):
        """保存任务数据"""
        try:
            data = {task_id: task.to_dict() for task_id, task in self.tasks.items()}
            with open(self.tasks_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.debug("保存定时任务数据成功")
        except Exception as e:
            logger.error(f"保存定时任务数据失败: {e}")

    def load_tasks(self):
        """加载任务数据"""
        try:
            if self.tasks_file.exists():
                with open(self.tasks_file, "r", encoding="utf-8") as f:
                    data = json.load(f)

                self.tasks.clear()
                for task_id, task_data in data.items():
                    task = TimingTask.from_dict(task_data)
                    self.tasks[task_id] = task

                logger.info(f"加载定时任务数据成功，共{len(self.tasks)}个任务")
        except Exception as e:
            logger.error(f"加载定时任务数据失败: {e}")


# 全局定时发送管理器实例
timing_sender = TimingSender()
