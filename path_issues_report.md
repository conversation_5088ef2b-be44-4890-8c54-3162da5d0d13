# 🔧 路径问题修复建议

## 📁 build_clean_single_injection.py

**行 98**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender_Clean.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_complete_no_missing.py

**行 39**: 硬编码resources路径
```python
# 问题代码:
"应用图标": "resources/icons/app_icon.ico",
```
**修复建议**: 使用 `path_manager.get_resource_path()`

**行 256**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender_Complete.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_final_fixed.py

**行 126**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender_Final.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_fixed.py

**行 49**: 依赖当前工作目录
```python
# 问题代码:
'TEMP': str(Path.cwd() / "temp_build"),  # 使用项目内的临时目录
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 50**: 依赖当前工作目录
```python
# 问题代码:
'TMP': str(Path.cwd() / "temp_build"),
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 57**: 直接Path构造
```python
# 问题代码:
temp_build = Path("temp_build")
```
**修复建议**: 使用路径管理器的相应方法

**行 71**: 硬编码resources路径
```python
# 问题代码:
"应用图标": "resources/icons/app_icon.ico"
```
**修复建议**: 使用 `path_manager.get_resource_path()`

**行 151**: 依赖当前工作目录
```python
# 问题代码:
cwd=str(Path.cwd())
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 182**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_fixed_distutils.py

**行 45**: 直接Path构造
```python
# 问题代码:
hooks_dir = Path("hooks")
```
**修复建议**: 使用路径管理器的相应方法

**行 206**: 依赖当前工作目录
```python
# 问题代码:
cwd=os.getcwd()
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 213**: 直接Path构造
```python
# 问题代码:
exe_path = Path("dist") / "MeetSpaceWeChatSender_Fixed.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_improved_deployment.py

**行 20**: 依赖当前工作目录
```python
# 问题代码:
self.project_root = Path.cwd()
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 82**: 硬编码resources路径
```python
# 问题代码:
"resources/icons/app_icon.ico",
```
**修复建议**: 使用 `path_manager.get_resource_path()`

**行 344**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / f"{self.build_config['app_name']}.exe"
```
**修复建议**: 使用路径管理器的相应方法

**行 392**: 直接Path构造
```python
# 问题代码:
info_file = Path("dist") / "deployment_info.json"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_original.py

**行 92**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender_Original.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_simple_working.py

**行 79**: 依赖当前工作目录
```python
# 问题代码:
cwd=str(Path.cwd())
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 85**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender_Working.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_with_diagnosis.py

**行 143**: 直接Path构造
```python
# 问题代码:
main_exe = Path("dist") / "MeetSpaceWeChatSender_Diagnostic.exe"
```
**修复建议**: 使用路径管理器的相应方法

**行 144**: 直接Path构造
```python
# 问题代码:
diag_exe = Path("dist") / "DiagnoseInjection.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 build_with_uac_admin.py

**行 174**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender_UAC.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 check_and_fix_paths.py

**行 26**: 硬编码logs路径
```python
# 问题代码:
(r'"logs/[^"]*"', "硬编码logs路径"),
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 27**: 硬编码config路径
```python
# 问题代码:
(r'"config/[^"]*"', "硬编码config路径"),
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 28**: 硬编码resources路径
```python
# 问题代码:
(r'"resources/[^"]*"', "硬编码resources路径"),
```
**修复建议**: 使用 `path_manager.get_resource_path()`

**行 29**: 硬编码logs路径
```python
# 问题代码:
(r"'logs/[^']*'", "硬编码logs路径"),
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 30**: 硬编码config路径
```python
# 问题代码:
(r"'config/[^']*'", "硬编码config路径"),
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 31**: 硬编码resources路径
```python
# 问题代码:
(r"'resources/[^']*'", "硬编码resources路径"),
```
**修复建议**: 使用 `path_manager.get_resource_path()`

**行 80**: 直接Path构造
```python
# 问题代码:
for file_path in Path('.').rglob(pattern):
```
**修复建议**: 使用路径管理器的相应方法

**行 137**: 直接Path构造
```python
# 问题代码:
for py_file in Path('.').rglob('*.py'):
```
**修复建议**: 使用路径管理器的相应方法

**行 201**: 直接Path构造
```python
# 问题代码:
report_file = Path("path_issues_report.md")
```
**修复建议**: 使用路径管理器的相应方法

**行 211**: 依赖当前工作目录
```python
# 问题代码:
print("4. 避免使用 Path.cwd() 和 os.getcwd()")
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 211**: 依赖当前工作目录
```python
# 问题代码:
print("4. 避免使用 Path.cwd() 和 os.getcwd()")
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

## 📁 diagnose_injection.py

**行 212**: 依赖当前工作目录
```python
# 问题代码:
print(f"当前工作目录: {os.getcwd()}")
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

## 📁 fix_injection_issue.py

**行 237**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 main.py

**行 58**: 直接Path构造
```python
# 问题代码:
logs_dir = Path("logs")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 rebuild_fixed.py

**行 93**: 直接Path构造
```python
# 问题代码:
exe_file = Path("dist") / "MeetSpaceWeChatSender_Fixed.exe"
```
**修复建议**: 使用路径管理器的相应方法

## 📁 test_injection_directly.py

**行 123**: 依赖当前工作目录
```python
# 问题代码:
print(f"工作目录: {os.getcwd()}")
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

## 📁 config\settings.py

**行 20**: 硬编码resources路径
```python
# 问题代码:
'TEMPLATES_DIR': path_manager.get_resource_path("resources/templates"),
```
**修复建议**: 使用 `path_manager.get_resource_path()`

**行 21**: 硬编码resources路径
```python
# 问题代码:
'ICONS_DIR': path_manager.get_resource_path("resources/icons"),
```
**修复建议**: 使用 `path_manager.get_resource_path()`

## 📁 core\config_backup_manager.py

**行 35**: 直接Path构造
```python
# 问题代码:
self.backup_dir = Path("config_backups")
```
**修复建议**: 使用路径管理器的相应方法

**行 40**: 硬编码config路径
```python
# 问题代码:
"timing_groups": "config/timing_groups.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 41**: 硬编码config路径
```python
# 问题代码:
"loop_groups": "config/loop_groups.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 42**: 硬编码config路径
```python
# 问题代码:
"message_groups": "config/message_groups.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 43**: 硬编码config路径
```python
# 问题代码:
"system_config": "config/config.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 44**: 硬编码config路径
```python
# 问题代码:
"message_templates": "config/message_templates.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 45**: 硬编码config路径
```python
# 问题代码:
"group_progress": "config/group_progress.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

## 📁 core\config_manager.py

**行 51**: 直接Path构造
```python
# 问题代码:
self.config_dir = Path("config")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 core\group_manager.py

**行 307**: 直接Path构造
```python
# 问题代码:
self.data_dir = Path("data/groups")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 core\loop_sender.py

**行 277**: 直接Path构造
```python
# 问题代码:
self.data_dir = Path("data/loop")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 core\timing_sender.py

**行 215**: 直接Path构造
```python
# 问题代码:
self.data_dir = Path("data/timing")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 ui\modern_theme_manager.py

**行 2407**: 硬编码config路径
```python
# 问题代码:
self, theme_name: str, config_path: str = "config/settings.json"
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 2435**: 硬编码config路径
```python
# 问题代码:
def load_theme_preference(self, config_path: str = "config/settings.json") -> str:
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

## 📁 utils\admin_privileges.py

**行 198**: 依赖当前工作目录
```python
# 问题代码:
cmd, creationflags=subprocess.CREATE_NEW_CONSOLE, cwd=os.getcwd()
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

## 📁 utils\icon_manager.py

**行 32**: 硬编码resources路径
```python
# 问题代码:
self._icons_dir = path_manager.get_resource_path("resources/icons")
```
**修复建议**: 使用 `path_manager.get_resource_path()`

## 📁 utils\path_manager.py

**行 280**: 硬编码resources路径
```python
# 问题代码:
return self.get_resource_path(f"resources/templates/{filename}")
```
**修复建议**: 使用 `path_manager.get_resource_path()`

**行 306**: 依赖当前工作目录
```python
# 问题代码:
Path.cwd() / "config",
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 307**: 依赖当前工作目录
```python
# 问题代码:
Path.cwd() / "logs",
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 308**: 依赖当前工作目录
```python
# 问题代码:
Path.cwd() / "data"
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

## 📁 utils\simple_admin.py

**行 60**: 依赖当前工作目录
```python
# 问题代码:
cd /d "{os.getcwd()}"
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

## 📁 portable\MeetSpaceWeChatSender\_internal\main.py

**行 60**: 直接Path构造
```python
# 问题代码:
logs_dir = Path("logs")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 portable\MeetSpaceWeChatSender\_internal\core\config_backup_manager.py

**行 35**: 直接Path构造
```python
# 问题代码:
self.backup_dir = Path("config_backups")
```
**修复建议**: 使用路径管理器的相应方法

**行 40**: 硬编码config路径
```python
# 问题代码:
"timing_groups": "config/timing_groups.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 41**: 硬编码config路径
```python
# 问题代码:
"loop_groups": "config/loop_groups.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 42**: 硬编码config路径
```python
# 问题代码:
"message_groups": "config/message_groups.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 43**: 硬编码config路径
```python
# 问题代码:
"system_config": "config/config.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 44**: 硬编码config路径
```python
# 问题代码:
"message_templates": "config/message_templates.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 45**: 硬编码config路径
```python
# 问题代码:
"group_progress": "config/group_progress.json",
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

## 📁 portable\MeetSpaceWeChatSender\_internal\core\config_manager.py

**行 51**: 直接Path构造
```python
# 问题代码:
self.config_dir = Path("config")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 portable\MeetSpaceWeChatSender\_internal\core\group_manager.py

**行 307**: 直接Path构造
```python
# 问题代码:
self.data_dir = Path("data/groups")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 portable\MeetSpaceWeChatSender\_internal\core\loop_sender.py

**行 277**: 直接Path构造
```python
# 问题代码:
self.data_dir = Path("data/loop")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 portable\MeetSpaceWeChatSender\_internal\core\timing_sender.py

**行 215**: 直接Path构造
```python
# 问题代码:
self.data_dir = Path("data/timing")
```
**修复建议**: 使用路径管理器的相应方法

## 📁 portable\MeetSpaceWeChatSender\_internal\ui\main_window.py

**行 5557**: 硬编码logs路径
```python
# 问题代码:
"logs/main.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5558**: 硬编码logs路径
```python
# 问题代码:
"logs/startup.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5560**: 硬编码logs路径
```python
# 问题代码:
"logs/http_api_connector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5561**: 硬编码logs路径
```python
# 问题代码:
"logs/wechatferry_connector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5562**: 硬编码logs路径
```python
# 问题代码:
"logs/ui_automation_connector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5563**: 硬编码logs路径
```python
# 问题代码:
"logs/winapi_connector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5565**: 硬编码logs路径
```python
# 问题代码:
"logs/timing_sender.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5566**: 硬编码logs路径
```python
# 问题代码:
"logs/loop_sender.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5567**: 硬编码logs路径
```python
# 问题代码:
"logs/timed_sender.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5568**: 硬编码logs路径
```python
# 问题代码:
"logs/send_monitor.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5569**: 硬编码logs路径
```python
# 问题代码:
"logs/optimized_sender.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5571**: 硬编码logs路径
```python
# 问题代码:
"logs/auto_injector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5572**: 硬编码logs路径
```python
# 问题代码:
"logs/injector_tool.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5573**: 硬编码logs路径
```python
# 问题代码:
"logs/smart_injector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5574**: 硬编码logs路径
```python
# 问题代码:
"logs/wechat_injector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5575**: 硬编码logs路径
```python
# 问题代码:
"logs/injector_adapter.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5576**: 硬编码logs路径
```python
# 问题代码:
"logs/ctypes_injector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5577**: 硬编码logs路径
```python
# 问题代码:
"logs/cmdline_injector.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5579**: 硬编码logs路径
```python
# 问题代码:
"logs/ui.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5580**: 硬编码logs路径
```python
# 问题代码:
"logs/timing_send_page.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5581**: 硬编码logs路径
```python
# 问题代码:
"logs/loop_send_page.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5582**: 硬编码logs路径
```python
# 问题代码:
"logs/task_status_page.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5583**: 硬编码logs路径
```python
# 问题代码:
"logs/rich_text_editor.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5584**: 硬编码logs路径
```python
# 问题代码:
"logs/custom_main_window.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5585**: 硬编码logs路径
```python
# 问题代码:
"logs/custom_title_bar.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5587**: 硬编码logs路径
```python
# 问题代码:
"logs/ui_optimizer.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5589**: 硬编码logs路径
```python
# 问题代码:
"logs/group_manager.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5590**: 硬编码logs路径
```python
# 问题代码:
"logs/message_template.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5591**: 硬编码logs路径
```python
# 问题代码:
"logs/config_manager.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5592**: 硬编码logs路径
```python
# 问题代码:
"logs/group_config_manager.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5593**: 硬编码logs路径
```python
# 问题代码:
"logs/version_manager.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5595**: 硬编码logs路径
```python
# 问题代码:
"logs/risk_control.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5596**: 硬编码logs路径
```python
# 问题代码:
"logs/security.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5597**: 硬编码logs路径
```python
# 问题代码:
"logs/admin_privileges.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5598**: 硬编码logs路径
```python
# 问题代码:
"logs/simple_admin.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5600**: 硬编码logs路径
```python
# 问题代码:
"logs/performance.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5601**: 硬编码logs路径
```python
# 问题代码:
"logs/performance_optimizer.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5603**: 硬编码logs路径
```python
# 问题代码:
"logs/group_card.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5604**: 硬编码logs路径
```python
# 问题代码:
"logs/group_detail_dialog.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5605**: 硬编码logs路径
```python
# 问题代码:
"logs/group_details_dialog.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5606**: 硬编码logs路径
```python
# 问题代码:
"logs/contact_selector_dialog.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5607**: 硬编码logs路径
```python
# 问题代码:
"logs/member_selection_dialog.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5608**: 硬编码logs路径
```python
# 问题代码:
"logs/loop_cycle_widget.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5609**: 硬编码logs路径
```python
# 问题代码:
"logs/timing_cycle_widget.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5610**: 硬编码logs路径
```python
# 问题代码:
"logs/performance_panel.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5612**: 硬编码logs路径
```python
# 问题代码:
"logs/validators.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5613**: 硬编码logs路径
```python
# 问题代码:
"logs/file_handler.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5614**: 硬编码logs路径
```python
# 问题代码:
"logs/dll_downloader.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5615**: 硬编码logs路径
```python
# 问题代码:
"logs/wxhelper_downloader.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5617**: 硬编码logs路径
```python
# 问题代码:
"logs/error.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5618**: 硬编码logs路径
```python
# 问题代码:
"logs/debug.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5619**: 硬编码logs路径
```python
# 问题代码:
"logs/console_error.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5620**: 硬编码logs路径
```python
# 问题代码:
"logs/console_output.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

**行 5627**: 硬编码logs路径
```python
# 问题代码:
"logs/app.log",
```
**修复建议**: 使用 `path_manager.get_log_path()` 或 `path_manager.app_data_dir / 'logs'`

## 📁 portable\MeetSpaceWeChatSender\_internal\ui\modern_theme_manager.py

**行 2301**: 硬编码config路径
```python
# 问题代码:
self, theme_name: str, config_path: str = "config/settings.json"
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

**行 2329**: 硬编码config路径
```python
# 问题代码:
def load_theme_preference(self, config_path: str = "config/settings.json") -> str:
```
**修复建议**: 使用 `path_manager.get_config_path()` 或 `path_manager.app_data_dir / 'config'`

## 📁 portable\MeetSpaceWeChatSender\_internal\utils\admin_privileges.py

**行 198**: 依赖当前工作目录
```python
# 问题代码:
cmd, creationflags=subprocess.CREATE_NEW_CONSOLE, cwd=os.getcwd()
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

## 📁 portable\MeetSpaceWeChatSender\_internal\utils\icon_manager.py

**行 32**: 硬编码resources路径
```python
# 问题代码:
self._icons_dir = path_manager.get_resource_path("resources/icons")
```
**修复建议**: 使用 `path_manager.get_resource_path()`

## 📁 portable\MeetSpaceWeChatSender\_internal\utils\path_manager.py

**行 186**: 硬编码resources路径
```python
# 问题代码:
return self.get_resource_path(f"resources/templates/{filename}")
```
**修复建议**: 使用 `path_manager.get_resource_path()`

**行 212**: 依赖当前工作目录
```python
# 问题代码:
Path.cwd() / "config",
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 213**: 依赖当前工作目录
```python
# 问题代码:
Path.cwd() / "logs",
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

**行 214**: 依赖当前工作目录
```python
# 问题代码:
Path.cwd() / "data"
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法

## 📁 portable\MeetSpaceWeChatSender\_internal\utils\simple_admin.py

**行 60**: 依赖当前工作目录
```python
# 问题代码:
cd /d "{os.getcwd()}"
```
**修复建议**: 使用 `path_manager.executable_dir` 或相应的路径管理器方法
