#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复注入环境 - 解决昨天还能用今天不能用的问题
"""

import sys
import os
import subprocess
import winreg
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_wechat_version():
    """检查微信版本"""
    print("🔍 检查微信版本...")
    
    try:
        import psutil
        
        # 找到微信进程
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and 'WeChat.exe' in proc.info['name']:
                    wechat_exe = proc.info['exe']
                    if wechat_exe:
                        print(f"微信路径: {wechat_exe}")
                        
                        # 获取文件版本信息
                        try:
                            import win32api
                            info = win32api.GetFileVersionInfo(wechat_exe, "\\")
                            ms = info['FileVersionMS']
                            ls = info['FileVersionLS']
                            version = f"{win32api.HIWORD(ms)}.{win32api.LOWORD(ms)}.{win32api.HIWORD(ls)}.{win32api.LOWORD(ls)}"
                            print(f"微信版本: {version}")
                            
                            # 获取文件修改时间
                            file_path = Path(wechat_exe)
                            mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                            print(f"文件修改时间: {mod_time}")
                            
                            # 检查是否是最近更新的
                            now = datetime.now()
                            hours_diff = (now - mod_time).total_seconds() / 3600
                            
                            if hours_diff < 24:
                                print(f"⚠️  微信在 {hours_diff:.1f} 小时前更新过！")
                                return True, version
                            else:
                                print(f"✅ 微信文件较旧 ({hours_diff:.1f} 小时前)")
                                return False, version
                                
                        except ImportError:
                            print("⚠️  无法获取详细版本信息（需要pywin32）")
                            return False, "未知"
                        except Exception as e:
                            print(f"⚠️  获取版本信息失败: {e}")
                            return False, "未知"
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print("❌ 未找到微信进程")
        return False, None
        
    except Exception as e:
        print(f"❌ 检查微信版本失败: {e}")
        return False, None

def check_windows_updates():
    """检查Windows更新"""
    print("\n🔍 检查Windows更新...")
    
    try:
        # 检查最近的Windows更新
        cmd = 'Get-HotFix | Sort-Object InstalledOn -Descending | Select-Object -First 5 | Format-Table HotFixID, InstalledOn -AutoSize'
        result = subprocess.run(
            ['powershell', '-Command', cmd],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("最近的Windows更新:")
            print(result.stdout)
            
            # 检查是否有昨天的更新
            lines = result.stdout.split('\n')
            for line in lines:
                if 'KB' in line and datetime.now().strftime('%Y') in line:
                    print("⚠️  发现最近的Windows更新")
                    return True
        else:
            print("⚠️  无法获取Windows更新信息")
        
        return False
        
    except Exception as e:
        print(f"❌ 检查Windows更新失败: {e}")
        return False

def check_defender_status():
    """检查Windows Defender状态"""
    print("\n🔍 检查Windows Defender状态...")
    
    try:
        # 检查实时保护状态
        cmd = 'Get-MpPreference | Select-Object DisableRealtimeMonitoring'
        result = subprocess.run(
            ['powershell', '-Command', cmd],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            if 'False' in result.stdout:
                print("⚠️  Windows Defender实时保护已启用")
                return True
            else:
                print("✅ Windows Defender实时保护已禁用")
                return False
        else:
            print("⚠️  无法获取Defender状态")
            return False
            
    except Exception as e:
        print(f"❌ 检查Defender状态失败: {e}")
        return False

def try_different_dll_versions():
    """尝试不同的DLL版本"""
    print("\n🔧 尝试不同的DLL版本...")
    
    try:
        from core.injector_tool import InjectorTool
        from utils.path_manager import path_manager
        
        # 不同的DLL版本
        dll_versions = [
            ("最新版本", "wxhelper_files/wxhelper_latest.dll"),
            ("原始备份", "wxhelper_files/wxhelper_original_backup.dll"),
            ("x64备份", "wxhelper_files/wxhelper_x64_backup.dll"),
            ("默认版本", "wxhelper_files/wxhelper.dll")
        ]
        
        for version_name, dll_path in dll_versions:
            dll_file = path_manager.get_resource_path(dll_path)
            if not dll_file.exists():
                print(f"❌ {version_name}: 文件不存在")
                continue
            
            print(f"\n🧪 测试 {version_name}...")
            print(f"   DLL路径: {dll_file}")
            print(f"   文件大小: {dll_file.stat().st_size} bytes")
            
            # 创建临时注入器
            injector = InjectorTool(auto_elevate=False)
            
            # 替换DLL路径
            original_dll = injector.dll_path
            injector.dll_path = dll_file
            
            try:
                # 尝试注入
                success, message = injector.inject_dll()
                
                if success:
                    print(f"✅ {version_name} 注入成功！")
                    print(f"   成功消息: {message}")
                    
                    # 恢复原始DLL路径
                    injector.dll_path = original_dll
                    
                    return True, version_name, dll_file
                else:
                    print(f"❌ {version_name} 注入失败: {message}")
                    
            except Exception as e:
                print(f"❌ {version_name} 测试异常: {e}")
            
            # 恢复原始DLL路径
            injector.dll_path = original_dll
        
        print("\n❌ 所有DLL版本都无法注入")
        return False, None, None
        
    except Exception as e:
        print(f"❌ 测试DLL版本失败: {e}")
        return False, None, None

def suggest_solutions(wechat_updated, windows_updated, defender_active):
    """提供解决方案建议"""
    print("\n🔧 解决方案建议:")
    
    solutions = []
    
    if wechat_updated:
        solutions.extend([
            "1. 微信版本问题:",
            "   - 尝试降级到昨天的微信版本",
            "   - 等待wxhelper更新以支持新版微信",
            "   - 查找兼容当前微信版本的wxhelper"
        ])
    
    if windows_updated:
        solutions.extend([
            "2. Windows更新问题:",
            "   - 检查是否有安全补丁影响了DLL注入",
            "   - 考虑回滚最近的Windows更新",
            "   - 调整Windows安全设置"
        ])
    
    if defender_active:
        solutions.extend([
            "3. Windows Defender问题:",
            "   - 临时禁用实时保护",
            "   - 将程序添加到Defender排除列表",
            "   - 使用管理员权限运行PowerShell:",
            "     Set-MpPreference -DisableRealtimeMonitoring $true"
        ])
    
    solutions.extend([
        "4. 通用解决方案:",
        "   - 完全重启微信（关闭所有微信进程）",
        "   - 重启计算机",
        "   - 以管理员身份运行程序",
        "   - 检查其他杀毒软件设置"
    ])
    
    for solution in solutions:
        print(solution)

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 注入环境修复")
    print("=" * 60)
    print("解决昨天能用今天不能用的问题")
    print("=" * 60)
    
    # 检查各种可能的变化
    wechat_updated, wechat_version = check_wechat_version()
    windows_updated = check_windows_updates()
    defender_active = check_defender_status()
    
    # 尝试不同的DLL版本
    dll_success, working_dll_name, working_dll_path = try_different_dll_versions()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 环境检查结果:")
    print(f"   微信最近更新: {'是' if wechat_updated else '否'}")
    print(f"   微信版本: {wechat_version}")
    print(f"   Windows最近更新: {'是' if windows_updated else '否'}")
    print(f"   Defender实时保护: {'启用' if defender_active else '禁用'}")
    print(f"   找到可用DLL: {'是' if dll_success else '否'}")
    
    if dll_success:
        print(f"   可用DLL: {working_dll_name}")
        print(f"   DLL路径: {working_dll_path}")
        print("\n🎉 找到了可用的DLL版本！")
        print("建议：将此DLL版本设为默认版本")
    else:
        print("\n⚠️  所有DLL版本都无法注入")
        suggest_solutions(wechat_updated, windows_updated, defender_active)
    
    # 提供立即解决方案
    if wechat_updated or defender_active:
        print("\n🚀 立即尝试的解决方案:")
        print("1. 重启微信（关闭所有微信进程后重新启动）")
        print("2. 临时禁用Windows Defender实时保护")
        print("3. 以管理员身份重新运行程序")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
