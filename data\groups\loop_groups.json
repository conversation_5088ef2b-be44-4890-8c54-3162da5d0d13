{"loop_1754544106": {"group_id": "loop_1754544106", "name": "123", "group_type": "loop", "members": [{"wxid": "filehelper", "name": "文件传输助手", "type": "contact", "remark": ""}], "created_time": "2025-08-07T13:21:46.121220", "updated_time": "2025-08-07T13:23:05.060845", "use_count": 0, "last_used": null, "tags": [], "sort_score": 0.24166666666666667, "config": {"send_settings": {"interval_seconds": 5, "batch_size": 10, "retry_count": 3, "timeout": 30, "use_system_risk_control": true, "custom_risk_control": {"max_per_hour": 100, "detection_mode": "smart"}}, "message_settings": {"template_id": "", "default_content": "{\"type\": \"rich_text\", \"html\": \"<!DOCTYPE HTML PUBLIC \\\"-//W3C//DTD HTML 4.0//EN\\\" \\\"http://www.w3.org/TR/REC-html40/strict.dtd\\\">\\n<html><head><meta name=\\\"qrichtext\\\" content=\\\"1\\\" /><meta charset=\\\"utf-8\\\" /><style type=\\\"text/css\\\">\\np, li { white-space: pre-wrap; }\\nhr { height: 1px; border-width: 0; }\\nli.unchecked::marker { content: \\\"\\\\2610\\\"; }\\nli.checked::marker { content: \\\"\\\\2612\\\"; }\\n</style></head><body style=\\\" font-family:'Microsoft YaHei','Segoe UI','Arial'; font-size:11pt; font-weight:400; font-style:normal;\\\">\\n<p style=\\\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\\\">123</p>\\n<p style=\\\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\\\"><br /></p></body></html>\", \"plain_text\": \"123\", \"images\": [{\"path\": \"temp_images\\\\clipboard_image_1754544182_f7b3b573.png\", \"name\": \"clipboard_image_1754544182_f7b3b573.png\", \"timestamp\": 1754544182}], \"send_parts\": [[\"123\", null], [null, \"temp_images\\\\clipboard_image_1754544182_f7b3b573.png\"]]}", "variables": {}, "personalization": true, "default_type": "rich_text"}, "schedule_settings": {"default_interval": 60, "auto_start": false, "timezone": "Asia/Shanghai"}, "loop_cycle": {"enabled": true, "config": {"cycle_interval": 1, "selected_weekdays": [0, 1, 2, 3, 4], "start_time": 9, "end_time": 18}}}}}