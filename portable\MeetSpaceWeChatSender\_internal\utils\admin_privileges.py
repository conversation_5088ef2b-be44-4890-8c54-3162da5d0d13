#!/usr/bin/env python3
"""
管理员权限提升工具
支持UAC弹窗提升和静默提升
"""

import os
import sys
import ctypes
import subprocess
from pathlib import Path
from typing import Tuple, Optional

from utils.logger import setup_logger

logger = setup_logger("admin_privileges")


class AdminPrivileges:
    """管理员权限管理器"""

    def __init__(self):
        self.is_admin = self.check_admin_privileges()
        logger.info(f"当前权限状态: {'管理员' if self.is_admin else '普通用户'}")

    def check_admin_privileges(self) -> bool:
        """检查是否具有管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except Exception as e:
            logger.error(f"检查管理员权限失败: {e}")
            return False

    def request_admin_privileges_uac(self, show_console: bool = True) -> bool:
        """
        通过UAC弹窗请求管理员权限

        Args:
            show_console: 是否显示控制台窗口

        Returns:
            是否成功获取管理员权限
        """
        if self.is_admin:
            logger.info("已具有管理员权限")
            return True

        try:
            logger.info("请求UAC管理员权限提升...")

            # 获取当前脚本路径
            if getattr(sys, "frozen", False):
                # 打包后的exe文件
                script_path = sys.executable
                python_exe = None
            else:
                # Python脚本
                script_path = os.path.abspath(sys.argv[0])
                python_exe = sys.executable

            # 构建完整的命令
            if python_exe:
                # Python脚本模式：python script.py args
                full_command = f'"{python_exe}" "{script_path}"'
                if len(sys.argv) > 1:
                    params = " ".join(f'"{arg}"' for arg in sys.argv[1:])
                    full_command += f" {params}"

                # 使用cmd /c来执行完整命令
                target_file = "cmd"
                parameters = f"/c {full_command}"
            else:
                # 可执行文件模式
                target_file = script_path
                parameters = " ".join(sys.argv[1:]) if len(sys.argv) > 1 else None

            logger.info(f"UAC提升目标: {target_file}")
            logger.info(f"UAC提升参数: {parameters}")

            # 使用ShellExecute请求管理员权限
            result = ctypes.windll.shell32.ShellExecuteW(
                None,  # hwnd
                "runas",  # lpOperation (以管理员身份运行)
                target_file,  # lpFile
                parameters,  # lpParameters
                None,  # lpDirectory
                1 if show_console else 0,  # nShowCmd (1=SW_SHOWNORMAL, 0=SW_HIDE)
            )

            # ShellExecute返回值说明：
            # > 32: 成功
            # 0: 内存不足
            # 2: 文件未找到
            # 3: 路径未找到
            # 5: 访问被拒绝
            # 31: 没有关联的应用程序

            if result > 32:
                logger.info("UAC权限提升请求已发送，程序将退出")
                # 当前进程应该退出，让新的管理员进程接管
                sys.exit(0)
            else:
                error_messages = {
                    0: "内存不足",
                    2: "文件未找到",
                    3: "路径未找到",
                    5: "访问被拒绝",
                    31: "没有关联的应用程序或用户取消了UAC提升",
                }
                error_msg = error_messages.get(result, f"未知错误码: {result}")
                logger.error(f"UAC权限提升失败: {error_msg}")

                # 如果是错误码31，尝试备用方法
                if result == 31:
                    logger.info("尝试使用备用方法进行权限提升...")
                    return self._try_alternative_elevation()

                return False

        except Exception as e:
            logger.error(f"UAC权限提升异常: {e}")
            return False

    def _try_alternative_elevation(self) -> bool:
        """
        尝试备用的权限提升方法

        Returns:
            是否成功
        """
        try:
            logger.info("尝试使用PowerShell进行权限提升...")

            # 获取当前脚本信息
            if getattr(sys, "frozen", False):
                script_path = sys.executable
                command = f'"{script_path}"'
            else:
                script_path = os.path.abspath(sys.argv[0])
                python_exe = sys.executable
                command = f'"{python_exe}" "{script_path}"'

            # 添加命令行参数
            if len(sys.argv) > 1:
                params = " ".join(f'"{arg}"' for arg in sys.argv[1:])
                command += f" {params}"

            # 使用PowerShell的Start-Process -Verb RunAs
            ps_command = f"""
            try {{
                Start-Process -FilePath 'cmd' -ArgumentList '/c {command}' -Verb RunAs -WindowStyle Normal
                exit 0
            }} catch {{
                exit 1
            }}
            """

            # 执行PowerShell命令
            result = subprocess.run(
                ["powershell", "-WindowStyle", "Hidden", "-Command", ps_command],
                capture_output=True,
                text=True,
                timeout=30,
            )

            if result.returncode == 0:
                logger.info("PowerShell权限提升成功，程序将退出")
                sys.exit(0)
            else:
                logger.error(f"PowerShell权限提升失败: {result.stderr}")

                # 最后尝试直接重启
                logger.info("尝试直接重启程序...")
                return self._restart_as_admin()

        except Exception as e:
            logger.error(f"备用权限提升方法异常: {e}")
            return self._restart_as_admin()

    def _restart_as_admin(self) -> bool:
        """
        直接重启程序获取管理员权限

        Returns:
            是否成功启动
        """
        try:
            logger.info("直接重启程序以获取管理员权限...")

            # 构建启动命令
            if getattr(sys, "frozen", False):
                cmd = [sys.executable] + sys.argv[1:]
            else:
                cmd = [sys.executable, os.path.abspath(sys.argv[0])] + sys.argv[1:]

            # 使用subprocess启动新进程
            subprocess.Popen(
                cmd, creationflags=subprocess.CREATE_NEW_CONSOLE, cwd=os.getcwd()
            )

            logger.info("新进程已启动，当前进程将退出")
            sys.exit(0)

        except Exception as e:
            logger.error(f"重启程序失败: {e}")
            return False

    def request_admin_privileges_restart(self) -> bool:
        """
        通过重启程序请求管理员权限

        Returns:
            是否成功启动管理员进程
        """
        if self.is_admin:
            logger.info("已具有管理员权限")
            return True

        try:
            logger.info("重启程序以获取管理员权限...")

            # 获取当前脚本路径和参数
            if getattr(sys, "frozen", False):
                script_path = sys.executable
                python_exe = None
            else:
                script_path = os.path.abspath(sys.argv[0])
                python_exe = sys.executable

            # 构建命令
            if python_exe:
                cmd = [python_exe, script_path] + sys.argv[1:]
            else:
                cmd = [script_path] + sys.argv[1:]

            # 使用subprocess以管理员身份启动
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_NORMAL

            process = subprocess.Popen(
                cmd,
                startupinfo=startupinfo,
                shell=True,
                creationflags=subprocess.CREATE_NEW_CONSOLE,
            )

            logger.info(f"管理员进程已启动，PID: {process.pid}")

            # 当前进程退出
            sys.exit(0)

        except Exception as e:
            logger.error(f"重启权限提升失败: {e}")
            return False

    def elevate_privileges_powershell(self) -> bool:
        """
        使用PowerShell请求管理员权限

        Returns:
            是否成功获取管理员权限
        """
        if self.is_admin:
            logger.info("已具有管理员权限")
            return True

        try:
            logger.info("使用PowerShell请求管理员权限...")

            # 获取当前脚本信息
            if getattr(sys, "frozen", False):
                script_path = sys.executable
                command = f'"{script_path}"'
            else:
                script_path = os.path.abspath(sys.argv[0])
                python_exe = sys.executable
                command = f'"{python_exe}" "{script_path}"'

            # 添加命令行参数
            if len(sys.argv) > 1:
                params = " ".join(f'"{arg}"' for arg in sys.argv[1:])
                command += f" {params}"

            # PowerShell命令
            ps_command = f"""
            Start-Process -FilePath 'cmd' -ArgumentList '/c {command}' -Verb RunAs -WindowStyle Normal
            """

            # 执行PowerShell命令
            result = subprocess.run(
                ["powershell", "-Command", ps_command],
                capture_output=True,
                text=True,
                timeout=30,
            )

            if result.returncode == 0:
                logger.info("PowerShell权限提升成功")
                sys.exit(0)
            else:
                logger.error(f"PowerShell权限提升失败: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"PowerShell权限提升异常: {e}")
            return False

    def create_elevated_shortcut(self, shortcut_path: str = None) -> bool:
        """
        创建以管理员身份运行的快捷方式

        Args:
            shortcut_path: 快捷方式保存路径

        Returns:
            是否创建成功
        """
        try:
            import winshell
            from win32com.client import Dispatch

            if not shortcut_path:
                desktop = winshell.desktop()
                shortcut_path = os.path.join(desktop, "微信群发助手(管理员).lnk")

            # 获取当前程序路径
            if getattr(sys, "frozen", False):
                target_path = sys.executable
            else:
                target_path = os.path.abspath(sys.argv[0])

            # 创建快捷方式
            shell = Dispatch("WScript.Shell")
            shortcut = shell.CreateShortCut(shortcut_path)
            shortcut.Targetpath = target_path
            shortcut.WorkingDirectory = os.path.dirname(target_path)
            shortcut.IconLocation = target_path

            # 设置以管理员身份运行
            # 这需要修改快捷方式的字节码
            shortcut.save()

            # 使用PowerShell设置管理员权限
            ps_command = f"""
            $bytes = [System.IO.File]::ReadAllBytes('{shortcut_path}')
            $bytes[0x15] = $bytes[0x15] -bor 0x20
            [System.IO.File]::WriteAllBytes('{shortcut_path}', $bytes)
            """

            subprocess.run(
                ["powershell", "-Command", ps_command], capture_output=True, text=True
            )

            logger.info(f"管理员快捷方式创建成功: {shortcut_path}")
            return True

        except ImportError:
            logger.warning("缺少winshell或pywin32库，无法创建快捷方式")
            return False
        except Exception as e:
            logger.error(f"创建管理员快捷方式失败: {e}")
            return False

    def setup_auto_admin_registry(self) -> bool:
        """
        设置注册表以自动获取管理员权限（需要管理员权限）

        Returns:
            是否设置成功
        """
        if not self.is_admin:
            logger.error("设置自动管理员权限需要当前具有管理员权限")
            return False

        try:
            import winreg

            # 获取程序路径
            if getattr(sys, "frozen", False):
                program_path = sys.executable
            else:
                program_path = os.path.abspath(sys.argv[0])

            program_name = os.path.basename(program_path)

            # 注册表路径
            reg_path = (
                r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers"
            )

            # 打开注册表键
            with winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE, reg_path, 0, winreg.KEY_SET_VALUE
            ) as key:
                # 设置程序以管理员身份运行
                winreg.SetValueEx(key, program_path, 0, winreg.REG_SZ, "RUNASADMIN")

            logger.info(f"已设置程序自动以管理员身份运行: {program_name}")
            return True

        except Exception as e:
            logger.error(f"设置自动管理员权限失败: {e}")
            return False

    def check_uac_settings(self) -> dict:
        """
        检查UAC设置

        Returns:
            UAC设置信息
        """
        try:
            import winreg

            uac_info = {}

            # 检查UAC是否启用
            reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"

            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                    try:
                        uac_enabled = winreg.QueryValueEx(key, "EnableLUA")[0]
                        uac_info["uac_enabled"] = bool(uac_enabled)
                    except FileNotFoundError:
                        uac_info["uac_enabled"] = True  # 默认启用

                    try:
                        consent_prompt = winreg.QueryValueEx(
                            key, "ConsentPromptBehaviorAdmin"
                        )[0]
                        uac_info["consent_prompt"] = consent_prompt
                    except FileNotFoundError:
                        uac_info["consent_prompt"] = 5  # 默认值

            except Exception as e:
                logger.warning(f"无法读取UAC设置: {e}")
                uac_info = {"uac_enabled": True, "consent_prompt": 5}

            return uac_info

        except Exception as e:
            logger.error(f"检查UAC设置失败: {e}")
            return {"uac_enabled": True, "consent_prompt": 5}

    def auto_elevate_privileges(self, method: str = "uac") -> bool:
        """
        自动提升权限

        Args:
            method: 提升方法 ("uac", "restart", "powershell")

        Returns:
            是否成功获取管理员权限
        """
        if self.is_admin:
            return True

        logger.info(f"自动提升管理员权限，方法: {method}")

        if method == "uac":
            return self.request_admin_privileges_uac()
        elif method == "restart":
            return self.request_admin_privileges_restart()
        elif method == "powershell":
            return self.elevate_privileges_powershell()
        else:
            logger.error(f"未知的权限提升方法: {method}")
            return False

    def ensure_admin_privileges(
        self, auto_elevate: bool = True, method: str = "uac"
    ) -> bool:
        """
        确保具有管理员权限

        Args:
            auto_elevate: 是否自动提升权限
            method: 提升方法

        Returns:
            是否具有管理员权限
        """
        if self.is_admin:
            return True

        if auto_elevate:
            logger.info("检测到权限不足，尝试自动提升...")
            return self.auto_elevate_privileges(method)
        else:
            logger.warning("权限不足且未启用自动提升")
            return False


def check_and_elevate_if_needed(auto_elevate: bool = True, method: str = "uac") -> bool:
    """
    检查并在需要时提升管理员权限

    Args:
        auto_elevate: 是否自动提升权限
        method: 提升方法 ("uac", "restart", "powershell")

    Returns:
        是否具有管理员权限
    """
    admin_mgr = AdminPrivileges()
    return admin_mgr.ensure_admin_privileges(auto_elevate, method)


def is_admin() -> bool:
    """快速检查是否具有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def request_admin_uac() -> bool:
    """快速请求UAC管理员权限"""
    admin_mgr = AdminPrivileges()
    return admin_mgr.request_admin_privileges_uac()


if __name__ == "__main__":
    # 测试权限提升功能
    print("🔧 测试管理员权限提升功能")
    print("=" * 50)

    admin_mgr = AdminPrivileges()

    print(f"当前权限状态: {'✅ 管理员' if admin_mgr.is_admin else '❌ 普通用户'}")

    if not admin_mgr.is_admin:
        print("\n尝试提升权限...")
        choice = input("选择提升方法 (1=UAC弹窗, 2=重启, 3=PowerShell): ").strip()

        if choice == "1":
            admin_mgr.request_admin_privileges_uac()
        elif choice == "2":
            admin_mgr.request_admin_privileges_restart()
        elif choice == "3":
            admin_mgr.elevate_privileges_powershell()
        else:
            print("无效选择")
    else:
        print("✅ 已具有管理员权限，无需提升")
