"""
核心业务逻辑模块

提供微信群发助手的核心功能，包括连接器、消息模板、发送监控和风控管理。
"""

from .wechatferry_connector import WeChatFerryConnector
from .http_api_connector import HTTPAPIConnector
from .message_template import MessageTemplate
from .send_monitor import SendMonitor, SendTask, SendStatus
from .risk_control import RiskController
from .group_manager import group_manager
from .timing_sender import timing_sender
from .loop_sender import loop_sender

__all__ = [
    "WeChatFerryConnector",
    "HTTPAPIConnector",
    "MessageTemplate",
    "SendMonitor",
    "SendTask",
    "SendStatus",
    "RiskController",
    "group_manager",
    "timing_sender",
    "loop_sender",
]

__version__ = "1.0.0"
