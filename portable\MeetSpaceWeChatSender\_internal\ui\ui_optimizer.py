#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI响应性优化器

提供UI性能优化、响应性提升、卡顿防护等功能。
"""

import time
import gc
from typing import Dict, Any, Optional, Callable
from functools import wraps

from PyQt6.QtCore import QTimer, QObject, pyqtSignal, QThread
from PyQt6.QtWidgets import QApplication, QWidget, QTableWidget, QListWidget
from PyQt6.QtGui import QPixmap

from utils.logger import setup_logger
from utils.performance_optimizer import performance_optimizer

logger = setup_logger("ui_optimizer")


class UIOptimizer(QObject):
    """UI优化器"""

    # 信号定义
    ui_freeze_detected = pyqtSignal(str, float)  # UI冻结检测
    performance_warning = pyqtSignal(str, dict)  # 性能警告

    def __init__(self):
        super().__init__()

        # 性能监控配置
        self.freeze_threshold = 100  # UI冻结阈值(ms)
        self.memory_warning_threshold = 500 * 1024 * 1024  # 500MB

        # 监控定时器初始化为None，稍后在主线程中创建
        self.monitor_timer = None

        # 延迟初始化定时器
        QTimer.singleShot(0, self._init_timer)

        # 优化配置
        self.table_optimization_enabled = True
        self.image_cache_enabled = True
        self.lazy_loading_enabled = True

        # 缓存管理
        self.image_cache = {}
        self.max_cache_size = 50

        logger.info("UI优化器初始化完成")

    def _init_timer(self):
        """在主线程中初始化定时器"""
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app and QThread.currentThread() != app.thread():
            logger.warning("UI优化器定时器应该在主线程中初始化")
            # 重新调度到主线程
            QTimer.singleShot(100, self._init_timer)
            return

        if self.monitor_timer is None:
            # 监控定时器
            self.monitor_timer = QTimer()
            self.monitor_timer.timeout.connect(self._monitor_performance)
            self.monitor_timer.start(5000)  # 每5秒监控一次
            logger.debug("UI优化器定时器初始化完成")

    def _monitor_performance(self):
        """监控UI性能"""
        try:
            # 检查内存使用
            memory_info = performance_optimizer.memory_manager.get_memory_usage()
            current_memory = memory_info.get("rss", 0)

            if current_memory > self.memory_warning_threshold / 1024 / 1024:
                self.performance_warning.emit(
                    "memory_high",
                    {
                        "memory_mb": current_memory,
                        "threshold_mb": self.memory_warning_threshold / 1024 / 1024,
                    },
                )

                # 自动清理
                self._auto_cleanup()

            # 检查CPU使用
            cpu_info = performance_optimizer.cpu_optimizer.get_cpu_usage()
            process_cpu = cpu_info.get("process_cpu", 0)

            if process_cpu > 50:  # CPU使用率超过50%
                self.performance_warning.emit("cpu_high", {"cpu_percent": process_cpu})

        except Exception as e:
            logger.error(f"性能监控失败: {e}")

    def _auto_cleanup(self):
        """自动清理"""
        try:
            # 清理图片缓存
            if len(self.image_cache) > self.max_cache_size // 2:
                self._cleanup_image_cache()

            # 强制垃圾回收
            performance_optimizer.memory_manager.force_cleanup()

            logger.info("自动清理完成")

        except Exception as e:
            logger.error(f"自动清理失败: {e}")

    def _cleanup_image_cache(self):
        """清理图片缓存"""
        try:
            # 保留最近使用的一半
            if len(self.image_cache) > self.max_cache_size // 2:
                # 简单的LRU清理
                items = list(self.image_cache.items())
                keep_count = self.max_cache_size // 2

                # 清理旧的缓存
                for key, _ in items[:-keep_count]:
                    del self.image_cache[key]

                logger.debug(f"清理图片缓存，保留 {keep_count} 项")

        except Exception as e:
            logger.error(f"清理图片缓存失败: {e}")

    def optimize_table_widget(self, table: QTableWidget):
        """优化表格组件"""
        try:
            if not self.table_optimization_enabled:
                return

            # 启用排序但延迟更新
            table.setSortingEnabled(False)

            # 优化选择模式
            table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

            # 设置合理的行高
            table.verticalHeader().setDefaultSectionSize(25)

            # 启用交替行颜色
            table.setAlternatingRowColors(True)

            # 优化滚动
            table.setVerticalScrollMode(QTableWidget.ScrollMode.ScrollPerPixel)
            table.setHorizontalScrollMode(QTableWidget.ScrollMode.ScrollPerPixel)

            logger.debug(f"表格组件优化完成: {table.objectName()}")

        except Exception as e:
            logger.error(f"优化表格组件失败: {e}")

    def optimize_list_widget(self, list_widget: QListWidget):
        """优化列表组件"""
        try:
            # 启用批量更新
            list_widget.setUpdatesEnabled(False)

            # 优化滚动
            list_widget.setVerticalScrollMode(QListWidget.ScrollMode.ScrollPerPixel)

            # 设置合理的间距
            list_widget.setSpacing(2)

            # 重新启用更新
            list_widget.setUpdatesEnabled(True)

            logger.debug(f"列表组件优化完成: {list_widget.objectName()}")

        except Exception as e:
            logger.error(f"优化列表组件失败: {e}")

    def batch_update_table(
        self, table: QTableWidget, data: list, update_func: Callable
    ):
        """批量更新表格"""
        try:
            # 禁用更新
            table.setUpdatesEnabled(False)
            table.setSortingEnabled(False)

            # 批量处理
            batch_size = 100
            for i in range(0, len(data), batch_size):
                batch = data[i : i + batch_size]

                # 更新当前批次
                for j, item in enumerate(batch):
                    row = i + j
                    update_func(table, row, item)

                # 让出控制权
                if i + batch_size < len(data):
                    performance_optimizer.responsiveness_manager.yield_control()

            # 重新启用更新
            table.setSortingEnabled(True)
            table.setUpdatesEnabled(True)

            logger.debug(f"批量更新表格完成，数据量: {len(data)}")

        except Exception as e:
            logger.error(f"批量更新表格失败: {e}")
            # 确保重新启用更新
            table.setUpdatesEnabled(True)

    def lazy_load_images(self, image_path: str) -> Optional[QPixmap]:
        """延迟加载图片"""
        try:
            if not self.image_cache_enabled:
                return QPixmap(image_path)

            # 检查缓存
            if image_path in self.image_cache:
                return self.image_cache[image_path]

            # 加载图片
            pixmap = QPixmap(image_path)

            # 添加到缓存
            if len(self.image_cache) < self.max_cache_size:
                self.image_cache[image_path] = pixmap
            else:
                # 清理缓存后再添加
                self._cleanup_image_cache()
                self.image_cache[image_path] = pixmap

            return pixmap

        except Exception as e:
            logger.error(f"延迟加载图片失败: {image_path} - {e}")
            return None

    def optimize_widget_performance(self, widget: QWidget):
        """优化组件性能"""
        try:
            # 启用双缓冲
            widget.setAttribute(widget.WidgetAttribute.WA_OpaquePaintEvent, True)

            # 优化更新策略
            widget.setAttribute(widget.WidgetAttribute.WA_NoSystemBackground, True)

            # 启用样式表缓存
            widget.setAttribute(widget.WidgetAttribute.WA_StyleSheet, True)

            logger.debug(f"组件性能优化完成: {widget.objectName()}")

        except Exception as e:
            logger.error(f"优化组件性能失败: {e}")


def ui_performance_monitor(freeze_threshold: float = 100.0):
    """UI性能监控装饰器"""

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                return result
            finally:
                elapsed_time = (time.time() - start_time) * 1000

                if elapsed_time > freeze_threshold:
                    logger.warning(
                        f"UI操作耗时过长: {func.__name__} - {elapsed_time:.1f}ms"
                    )

                    # 发送冻结检测信号
                    if hasattr(ui_optimizer, "ui_freeze_detected"):
                        ui_optimizer.ui_freeze_detected.emit(
                            func.__name__, elapsed_time
                        )

        return wrapper

    return decorator


def batch_ui_update(batch_size: int = 50):
    """批量UI更新装饰器"""

    def decorator(func):
        @wraps(func)
        def wrapper(self, items, *args, **kwargs):
            if not items:
                return func(self, items, *args, **kwargs)

            # 如果数据量小，直接处理
            if len(items) <= batch_size:
                return func(self, items, *args, **kwargs)

            # 分批处理
            results = []
            for i in range(0, len(items), batch_size):
                batch = items[i : i + batch_size]
                result = func(self, batch, *args, **kwargs)
                results.append(result)

                # 让出控制权
                performance_optimizer.responsiveness_manager.yield_control()

            return results

        return wrapper

    return decorator


def safe_ui_operation(func):
    """安全UI操作装饰器"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # 检查应用程序是否存在
            app = QApplication.instance()
            if not app:
                logger.warning(f"应用程序不存在，跳过UI操作: {func.__name__}")
                return None

            # 执行操作
            return func(*args, **kwargs)

        except Exception as e:
            logger.error(f"UI操作失败: {func.__name__} - {e}")
            return None

    return wrapper


class LazyLoader(QThread):
    """延迟加载器"""

    data_loaded = pyqtSignal(str, object)

    def __init__(self, load_func: Callable, load_id: str):
        super().__init__()
        self.load_func = load_func
        self.load_id = load_id

    def run(self):
        """运行加载任务"""
        try:
            data = self.load_func()
            self.data_loaded.emit(self.load_id, data)
        except Exception as e:
            logger.error(f"延迟加载失败: {self.load_id} - {e}")
            self.data_loaded.emit(self.load_id, None)


# 全局UI优化器实例
ui_optimizer = UIOptimizer()
