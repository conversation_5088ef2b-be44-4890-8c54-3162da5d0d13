#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建应用程序图标

将Meet space logo转换为ICO格式图标文件
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw

def create_meetspace_icon():
    """创建Meet space风格的图标"""
    
    # 创建资源目录
    resources_dir = Path("resources")
    icons_dir = resources_dir / "icons"
    icons_dir.mkdir(parents=True, exist_ok=True)
    
    # 图标尺寸列表
    sizes = [16, 24, 32, 48, 64, 128, 256]
    
    # Meet space品牌色
    brand_color = "#A4C639"  # 绿色
    text_color = "#333333"   # 深灰色
    
    print("🎨 创建Meet space风格图标...")
    
    # 为每个尺寸创建图标
    images = []
    
    for size in sizes:
        # 创建圆形背景
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制圆形背景
        margin = 2
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=brand_color, outline=None)
        
        # 计算文字大小和位置
        if size >= 64:
            # 大图标显示 "Meet"
            try:
                from PIL import ImageFont
                # 尝试使用系统字体
                font_size = max(8, size // 8)
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    try:
                        font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
                    except:
                        font = ImageFont.load_default()
                
                text = "Meet"
                # 获取文字边界框
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                # 居中绘制文字
                x = (size - text_width) // 2
                y = (size - text_height) // 2 - 2
                draw.text((x, y), text, fill=text_color, font=font)
                
            except ImportError:
                # 如果没有PIL字体支持，绘制简单图形
                center = size // 2
                radius = size // 6
                draw.ellipse([center-radius, center-radius, 
                            center+radius, center+radius], 
                           fill=text_color)
        
        elif size >= 32:
            # 中等图标显示 "M"
            try:
                from PIL import ImageFont
                font_size = max(6, size // 4)
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    try:
                        font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
                    except:
                        font = ImageFont.load_default()
                
                text = "M"
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                x = (size - text_width) // 2
                y = (size - text_height) // 2 - 1
                draw.text((x, y), text, fill=text_color, font=font)
                
            except ImportError:
                # 简单图形
                center = size // 2
                radius = size // 8
                draw.ellipse([center-radius, center-radius, 
                            center+radius, center+radius], 
                           fill=text_color)
        else:
            # 小图标只显示一个点
            center = size // 2
            radius = max(1, size // 10)
            draw.ellipse([center-radius, center-radius, 
                        center+radius, center+radius], 
                       fill=text_color)
        
        images.append(img)
        print(f"   ✅ 创建 {size}x{size} 图标")
    
    # 保存为ICO文件
    ico_path = icons_dir / "app.ico"
    images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in images])
    
    print(f"✅ 图标已保存: {ico_path}")
    
    # 同时保存PNG版本用于其他用途
    png_path = icons_dir / "app.png"
    images[-1].save(png_path, format='PNG')
    print(f"✅ PNG版本已保存: {png_path}")
    
    return ico_path, png_path

def create_installer_icon():
    """创建安装程序专用图标"""
    
    icons_dir = Path("resources/icons")
    
    # 创建安装程序图标（带安装标识）
    sizes = [16, 24, 32, 48, 64, 128, 256]
    brand_color = "#A4C639"
    text_color = "#333333"
    install_color = "#FF6B35"  # 橙色安装标识
    
    print("📦 创建安装程序图标...")
    
    images = []
    
    for size in sizes:
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制主圆形
        margin = 2
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=brand_color, outline=None)
        
        # 绘制文字或图形
        if size >= 64:
            try:
                from PIL import ImageFont
                font_size = max(6, size // 10)
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    try:
                        font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
                    except:
                        font = ImageFont.load_default()
                
                text = "Meet"
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                x = (size - text_width) // 2
                y = (size - text_height) // 2 - 4
                draw.text((x, y), text, fill=text_color, font=font)
                
                # 添加安装标识
                install_size = size // 6
                install_x = size - install_size - 4
                install_y = size - install_size - 4
                draw.ellipse([install_x, install_y, 
                            install_x + install_size, install_y + install_size], 
                           fill=install_color)
                
            except ImportError:
                center = size // 2
                radius = size // 6
                draw.ellipse([center-radius, center-radius, 
                            center+radius, center+radius], 
                           fill=text_color)
        else:
            center = size // 2
            radius = max(1, size // 8)
            draw.ellipse([center-radius, center-radius, 
                        center+radius, center+radius], 
                       fill=text_color)
        
        images.append(img)
    
    # 保存安装程序图标
    installer_ico_path = icons_dir / "installer.ico"
    images[0].save(installer_ico_path, format='ICO', 
                  sizes=[(img.width, img.height) for img in images])
    
    print(f"✅ 安装程序图标已保存: {installer_ico_path}")
    
    return installer_ico_path

def create_additional_icons():
    """创建其他用途的图标"""
    
    icons_dir = Path("resources/icons")
    brand_color = "#A4C639"
    text_color = "#333333"
    
    print("🎯 创建其他图标...")
    
    # 创建卸载程序图标
    img = Image.new('RGBA', (48, 48), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制背景
    draw.ellipse([2, 2, 46, 46], fill="#E74C3C", outline=None)  # 红色背景
    
    # 绘制X标记
    draw.line([16, 16, 32, 32], fill="white", width=3)
    draw.line([32, 16, 16, 32], fill="white", width=3)
    
    uninstall_ico_path = icons_dir / "uninstall.ico"
    img.save(uninstall_ico_path, format='ICO')
    print(f"   ✅ 卸载程序图标: {uninstall_ico_path}")
    
    # 创建文档图标
    img = Image.new('RGBA', (48, 48), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制文档形状
    draw.rectangle([8, 6, 36, 42], fill="white", outline="#333333", width=2)
    draw.rectangle([10, 12, 34, 14], fill="#333333")
    draw.rectangle([10, 18, 30, 20], fill="#333333")
    draw.rectangle([10, 24, 32, 26], fill="#333333")
    
    doc_ico_path = icons_dir / "document.ico"
    img.save(doc_ico_path, format='ICO')
    print(f"   ✅ 文档图标: {doc_ico_path}")
    
    return uninstall_ico_path, doc_ico_path

if __name__ == "__main__":
    print("🎨 Meet space 图标创建工具")
    print("=" * 50)
    
    try:
        # 检查PIL是否可用
        from PIL import Image, ImageDraw
        print("✅ PIL库可用")
        
        # 创建主应用图标
        app_ico, app_png = create_meetspace_icon()
        
        # 创建安装程序图标
        installer_ico = create_installer_icon()
        
        # 创建其他图标
        uninstall_ico, doc_ico = create_additional_icons()
        
        print("\n" + "=" * 50)
        print("🎉 所有图标创建完成！")
        print("\n📁 创建的图标文件:")
        print(f"   • 应用程序图标: {app_ico}")
        print(f"   • 应用程序PNG: {app_png}")
        print(f"   • 安装程序图标: {installer_ico}")
        print(f"   • 卸载程序图标: {uninstall_ico}")
        print(f"   • 文档图标: {doc_ico}")
        
        print("\n💡 使用说明:")
        print("   • app.ico - 主程序图标")
        print("   • installer.ico - 安装程序图标")
        print("   • uninstall.ico - 卸载程序图标")
        print("   • document.ico - 文档图标")
        
    except ImportError:
        print("❌ 需要安装PIL库: pip install Pillow")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 创建图标时出错: {e}")
        sys.exit(1)
