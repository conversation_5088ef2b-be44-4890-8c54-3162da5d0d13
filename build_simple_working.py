#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的工作版本打包 - 不修改任何原始代码
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def simple_build():
    """最简单的打包方式"""
    print("🔧 最简单的打包方式...")
    print("完全不修改您的原始代码，只是简单打包")
    
    # 清理
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 最简单的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MeetSpaceWeChatSender_Working",
        
        # 图标
        "--icon=resources/icons/app_icon.ico",
        
        # 数据文件 - 完整复制，保持原始结构
        "--add-data=tools;tools",
        "--add-data=wxhelper_files;wxhelper_files", 
        "--add-data=resources;resources",
        "--add-data=config;config",
        "--add-data=version_info.txt;.",
        
        # 最基本的隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=psutil",
        "--hidden-import=requests",
        "--hidden-import=ctypes",
        "--hidden-import=subprocess",
        
        # 您的模块
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=ui", 
        "--hidden-import=utils",
        "--hidden-import=runtime_config_generator",
        
        # 基本选项
        "--clean",
        "--noconfirm",
        
        "main.py"
    ]
    
    print("📋 执行最简单的打包...")
    print("   保持所有原始文件结构")
    print("   不做任何代码修改")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            text=True,
            timeout=300,
            cwd=str(Path.cwd())
        )
        
        print("✅ 打包完成")
        
        # 检查结果
        exe_file = Path("dist") / "MeetSpaceWeChatSender_Working.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"✅ 输出文件: {exe_file}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            return True
        else:
            print("❌ 未找到输出文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False
    except subprocess.TimeoutExpired:
        print("❌ 打包超时")
        return False
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def main():
    print("🔧 Meet space 微信群发助手 - 简单工作版本")
    print("=" * 60)
    print("使用您原始的、已验证可工作的注入代码")
    print("=" * 60)
    
    success = simple_build()
    
    if success:
        print("\n🎉 简单工作版本打包成功!")
        print("📁 输出: dist/MeetSpaceWeChatSender_Working.exe")
        print("\n✨ 这个版本:")
        print("  ✅ 使用您原始的注入代码")
        print("  ✅ 保持所有文件结构不变")
        print("  ✅ 不做任何修改")
        print("\n📋 现在请测试:")
        print("  1. 启动微信PC版并登录")
        print("  2. 运行 MeetSpaceWeChatSender_Working.exe")
        print("  3. 点击连接微信")
        print("  4. 应该可以正常注入了")
    else:
        print("\n❌ 打包失败!")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
