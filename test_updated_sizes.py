#!/usr/bin/env python3
"""
更新后的尺寸测试工具
测试增加后的SpinBox和ComboBox尺寸
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QLabel, QGroupBox, QSpinBox, QComboBox, QPushButton)
from PyQt6.QtCore import Qt
from ui.modern_theme_manager import theme_manager
from ui.themed_dialog_base import ThemedDialogBase


class UpdatedSizeTestDialog(ThemedDialogBase):
    """更新后的尺寸测试对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("更新后的控件尺寸测试")
        self.setFixedSize(700, 500)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("更新后的控件尺寸测试 - 验证增加的宽度")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # SpinBox测试组
        spinbox_group = QGroupBox("SpinBox测试 (期望宽度≥150px)")
        spinbox_layout = QVBoxLayout(spinbox_group)
        
        # 系统设置中的实际控件
        settings_tests = [
            ("重试次数:", 0, 10, 3),
            ("重试延迟(秒):", 1, 60, 5),
            ("最大重试次数:", 0, 10, 2),
            ("批量大小:", 1, 100, 10),
        ]
        
        self.spinboxes = []
        for label_text, min_val, max_val, default_val in settings_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(label_text)
            label.setMinimumWidth(120)  # 固定标签宽度
            row_layout.addWidget(label)
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            self.spinboxes.append(spinbox)
            
            row_layout.addWidget(spinbox)
            row_layout.addStretch()
            spinbox_layout.addLayout(row_layout)
        
        layout.addWidget(spinbox_group)
        
        # ComboBox测试组
        combobox_group = QGroupBox("ComboBox测试 (期望宽度≥180px)")
        combobox_layout = QVBoxLayout(combobox_group)
        
        # 系统设置中的实际控件
        combo_tests = [
            ("选择主题:", ["默认主题", "浅色主题", "深色主题", "护眼主题", "科技主题"]),
            ("字体大小:", ["小", "中", "大", "特大"]),
            ("发送模式:", ["立即发送", "定时发送", "循环发送"]),
            ("消息类型:", ["文本消息", "图片消息", "文件消息"]),
        ]
        
        self.comboboxes = []
        for label_text, items in combo_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(label_text)
            label.setMinimumWidth(120)  # 固定标签宽度
            row_layout.addWidget(label)
            
            combobox = QComboBox()
            combobox.addItems(items)
            self.comboboxes.append(combobox)
            
            row_layout.addWidget(combobox)
            row_layout.addStretch()
            combobox_layout.addLayout(row_layout)
        
        layout.addWidget(combobox_group)
        
        # 尺寸信息显示
        info_group = QGroupBox("尺寸检查结果")
        info_layout = QVBoxLayout(info_group)
        
        self.size_info_label = QLabel("点击'检查尺寸'按钮查看控件尺寸信息")
        info_layout.addWidget(self.size_info_label)
        
        layout.addWidget(info_group)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        check_size_btn = QPushButton("检查尺寸")
        check_size_btn.clicked.connect(self.check_sizes)
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(check_size_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def check_sizes(self):
        """检查控件尺寸"""
        size_info = []
        
        # 检查SpinBox尺寸
        size_info.append("📊 SpinBox尺寸检查:")
        spinbox_pass = 0
        for i, spinbox in enumerate(self.spinboxes):
            width = spinbox.size().width()
            height = spinbox.size().height()
            status = "✅" if width >= 150 else "❌"
            size_info.append(f"  {status} SpinBox {i+1}: {width} x {height} px")
            if width >= 150:
                spinbox_pass += 1
        
        size_info.append(f"  SpinBox通过率: {spinbox_pass}/{len(self.spinboxes)}")
        size_info.append("")
        
        # 检查ComboBox尺寸
        size_info.append("📊 ComboBox尺寸检查:")
        combobox_pass = 0
        for i, combobox in enumerate(self.comboboxes):
            width = combobox.size().width()
            height = combobox.size().height()
            status = "✅" if width >= 180 else "❌"
            size_info.append(f"  {status} ComboBox {i+1}: {width} x {height} px")
            if width >= 180:
                combobox_pass += 1
        
        size_info.append(f"  ComboBox通过率: {combobox_pass}/{len(self.comboboxes)}")
        size_info.append("")
        
        # 总体评估
        total_pass = spinbox_pass + combobox_pass
        total_count = len(self.spinboxes) + len(self.comboboxes)
        
        if total_pass == total_count:
            size_info.append("🎉 所有控件尺寸检查通过！")
        else:
            size_info.append(f"⚠️  {total_count - total_pass} 个控件尺寸不达标")
        
        # 更新显示
        self.size_info_label.setText("\n".join(size_info))
        
        # 同时在控制台输出
        print("\n" + "="*60)
        for line in size_info:
            print(line)
        print("="*60)


def test_updated_sizes():
    """测试更新后的尺寸"""
    app = QApplication(sys.argv)
    
    print("🔧 更新后的控件尺寸测试")
    print("=" * 60)
    print("📏 新的尺寸标准:")
    print("  - SpinBox最小宽度: 150px (之前80px)")
    print("  - ComboBox最小宽度: 180px (之前120px)")
    print("  - 内边距: 4px 8px (之前2px 4px)")
    
    # 测试科技主题
    theme_manager.set_theme(app, "科技主题")
    print("\n✅ 已应用科技主题")
    
    dialog = UpdatedSizeTestDialog()
    dialog.setWindowTitle("科技主题 - 更新后的尺寸测试")
    dialog.show()
    
    print("\n📋 测试说明:")
    print("1. 观察SpinBox和ComboBox的宽度是否明显增加")
    print("2. 点击'检查尺寸'按钮查看具体数值")
    print("3. 确认SpinBox宽度≥150px，ComboBox宽度≥180px")
    print("4. 测试控件的交互功能是否正常")
    
    result = dialog.exec()
    if result == QDialog.DialogCode.Accepted:
        print("✅ 科技主题尺寸测试完成")
    
    print("\n🎉 尺寸更新测试完成！")
    sys.exit(0)


def main():
    """主函数"""
    print("🚀 控件尺寸更新工具")
    print("=" * 60)
    print("\n📋 更新内容:")
    print("1. SpinBox最小宽度: 80px → 150px")
    print("2. ComboBox最小宽度: 120px → 180px")
    print("3. 内边距增加: 2px 4px → 4px 8px")
    print("4. 所有主题统一应用新尺寸")
    
    print("\n✅ 更新完成！现在可以运行测试:")
    print("python test_updated_sizes.py --test")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_updated_sizes()


if __name__ == "__main__":
    main()
