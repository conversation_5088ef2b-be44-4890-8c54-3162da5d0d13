#!/usr/bin/env python3
"""
自动化打包脚本 - Meet space 微信群发助手
自动检测问题并解决，生成exe安装文件
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path
from typing import List, Optional, Tuple


class AutoBuilder:
    """自动化构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.installer_dir = self.project_root / "installer"
        self.output_dir = self.installer_dir / "Output"
        
        # 构建状态
        self.build_success = False
        self.installer_success = False
        
    def log(self, message: str, level: str = "INFO") -> None:
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌",
            "STEP": "🔄"
        }.get(level, "ℹ️")
        
        print(f"[{timestamp}] {prefix} {message}")
        
    def check_dependencies(self) -> bool:
        """检查依赖"""
        self.log("检查构建依赖...", "STEP")
        
        # 检查PyInstaller
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "show", "pyinstaller"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                self.log("PyInstaller未安装，正在安装...", "WARNING")
                subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
                self.log("PyInstaller安装完成", "SUCCESS")
        except Exception as e:
            self.log(f"安装PyInstaller失败: {e}", "ERROR")
            return False
            
        # 检查Inno Setup（可选）
        inno_paths = [
            r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
            r"C:\Program Files\Inno Setup 6\ISCC.exe",
            r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
            r"C:\Program Files\Inno Setup 5\ISCC.exe",
        ]
        
        inno_found = False
        for path in inno_paths:
            if Path(path).exists():
                self.inno_compiler = path
                inno_found = True
                self.log(f"找到Inno Setup: {path}", "SUCCESS")
                break
                
        if not inno_found:
            self.log("未找到Inno Setup，将跳过安装包生成", "WARNING")
            self.log("请从 https://jrsoftware.org/isinfo.php 下载安装", "INFO")
            self.inno_compiler = None
            
        return True
        
    def check_project_structure(self) -> bool:
        """检查项目结构"""
        self.log("检查项目结构...", "STEP")
        
        required_files = ["main.py", "requirements.txt"]
        required_dirs = ["config", "core", "ui", "utils"]
        
        missing_items = []
        
        for file in required_files:
            if not (self.project_root / file).exists():
                missing_items.append(f"文件: {file}")
                
        for dir_name in required_dirs:
            if not (self.project_root / dir_name).exists():
                missing_items.append(f"目录: {dir_name}")
                
        if missing_items:
            self.log("缺少必要的项目文件/目录:", "ERROR")
            for item in missing_items:
                self.log(f"  - {item}", "ERROR")
            return False
            
        self.log("项目结构检查通过", "SUCCESS")
        return True
        
    def create_missing_resources(self) -> None:
        """创建缺失的资源文件"""
        self.log("检查并创建缺失的资源文件...", "STEP")
        
        # 创建资源目录
        resources_dir = self.project_root / "resources"
        icons_dir = resources_dir / "icons"
        icons_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查图标文件
        app_icon = icons_dir / "app.ico"
        if not app_icon.exists():
            self.log("创建默认应用图标...", "INFO")
            self.create_default_icon(app_icon)
            
        # 检查版本信息文件
        version_file = self.project_root / "version_info.txt"
        if not version_file.exists():
            self.log("创建版本信息文件...", "INFO")
            self.create_version_info(version_file)
            
    def create_default_icon(self, icon_path: Path) -> None:
        """创建默认图标"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建32x32的图标
            img = Image.new('RGBA', (32, 32), (70, 130, 180, 255))
            draw = ImageDraw.Draw(img)
            
            # 绘制简单的"M"字母
            try:
                font = ImageFont.truetype("arial.ttf", 20)
            except:
                font = ImageFont.load_default()
                
            draw.text((8, 6), "M", fill=(255, 255, 255, 255), font=font)
            
            # 保存为ICO格式
            img.save(icon_path, format='ICO', sizes=[(32, 32)])
            self.log(f"默认图标已创建: {icon_path}", "SUCCESS")
            
        except ImportError:
            self.log("PIL未安装，跳过图标创建", "WARNING")
        except Exception as e:
            self.log(f"创建图标失败: {e}", "WARNING")
            
    def create_version_info(self, version_file: Path) -> None:
        """创建版本信息文件"""
        version_content = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404B0',
          [StringStruct(u'CompanyName', u'Meet space 会客创意空间'),
          StringStruct(u'FileDescription', u'Meet space 微信群发助手'),
          StringStruct(u'FileVersion', u'*******'),
          StringStruct(u'InternalName', u'MeetSpaceWeChatSender'),
          StringStruct(u'LegalCopyright', u'Copyright © 2024 Meet space'),
          StringStruct(u'OriginalFilename', u'MeetSpaceWeChatSender.exe'),
          StringStruct(u'ProductName', u'Meet space 微信群发助手'),
          StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)'''
        
        version_file.write_text(version_content, encoding='utf-8')
        self.log(f"版本信息文件已创建: {version_file}", "SUCCESS")
        
    def clean_build_dirs(self) -> None:
        """清理构建目录"""
        self.log("清理旧的构建文件...", "STEP")
        
        dirs_to_clean = [self.dist_dir, self.build_dir]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    self.log(f"已清理: {dir_path}", "SUCCESS")
                except Exception as e:
                    self.log(f"清理失败 {dir_path}: {e}", "WARNING")
                    
    def install_requirements(self) -> bool:
        """安装项目依赖"""
        self.log("安装项目依赖...", "STEP")
        
        requirements_file = self.project_root / "requirements.txt"
        if not requirements_file.exists():
            self.log("requirements.txt不存在，跳过依赖安装", "WARNING")
            return True
            
        try:
            # 只安装运行时必需的依赖
            runtime_deps = [
                "PyQt6>=6.4.0",
                "requests>=2.28.0",
                "aiohttp>=3.8.0",
                "Pillow>=9.0.0",
                "psutil>=5.9.0",
                "python-dateutil>=2.8.0",
                "pyyaml>=6.0",
            ]
            
            for dep in runtime_deps:
                self.log(f"安装: {dep}", "INFO")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", dep
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    self.log(f"安装失败 {dep}: {result.stderr}", "WARNING")
                    
            self.log("依赖安装完成", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"安装依赖失败: {e}", "ERROR")
            return False
            
    def build_executable(self) -> bool:
        """构建可执行文件"""
        self.log("开始构建可执行文件...", "STEP")
        
        spec_file = self.project_root / "MeetSpaceWeChatSender.spec"
        
        try:
            # 运行PyInstaller
            cmd = [sys.executable, "-m", "PyInstaller", str(spec_file), "--clean", "--noconfirm"]
            
            self.log(f"执行命令: {' '.join(cmd)}", "INFO")
            
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log("可执行文件构建成功", "SUCCESS")
                self.build_success = True
                return True
            else:
                self.log("构建失败:", "ERROR")
                self.log(result.stdout, "ERROR")
                self.log(result.stderr, "ERROR")
                return False
                
        except Exception as e:
            self.log(f"构建过程出错: {e}", "ERROR")
            return False

    def verify_build(self) -> bool:
        """验证构建结果"""
        self.log("验证构建结果...", "STEP")

        exe_path = self.dist_dir / "MeetSpaceWeChatSender" / "MeetSpaceWeChatSender.exe"

        if not exe_path.exists():
            self.log(f"可执行文件不存在: {exe_path}", "ERROR")
            return False

        # 检查文件大小
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        self.log(f"可执行文件大小: {file_size:.1f} MB", "INFO")

        if file_size < 1:
            self.log("可执行文件过小，可能构建不完整", "WARNING")

        # 检查必要的资源文件
        dist_app_dir = self.dist_dir / "MeetSpaceWeChatSender"
        required_items = ["resources", "config_templates", "tools", "wxhelper_files"]

        missing_items = []
        for item in required_items:
            if not (dist_app_dir / item).exists():
                missing_items.append(item)

        if missing_items:
            self.log("缺少必要的资源文件:", "WARNING")
            for item in missing_items:
                self.log(f"  - {item}", "WARNING")
        else:
            self.log("所有必要资源文件都已包含", "SUCCESS")

        self.log("构建验证完成", "SUCCESS")
        return True

    def create_installer(self) -> bool:
        """创建安装包"""
        if not self.inno_compiler:
            self.log("跳过安装包创建（未找到Inno Setup）", "WARNING")
            return False

        self.log("开始创建安装包...", "STEP")

        # 更新Inno Setup脚本中的路径
        setup_script = self.installer_dir / "setup.iss"
        if not setup_script.exists():
            self.log("setup.iss文件不存在", "ERROR")
            return False

        try:
            # 确保输出目录存在
            self.output_dir.mkdir(parents=True, exist_ok=True)

            # 运行Inno Setup编译器
            cmd = [self.inno_compiler, str(setup_script)]

            self.log(f"执行命令: {' '.join(cmd)}", "INFO")

            result = subprocess.run(cmd, cwd=self.installer_dir, capture_output=True, text=True)

            if result.returncode == 0:
                self.log("安装包创建成功", "SUCCESS")
                self.installer_success = True

                # 查找生成的安装包
                setup_files = list(self.output_dir.glob("*.exe"))
                if setup_files:
                    setup_file = setup_files[0]
                    file_size = setup_file.stat().st_size / (1024 * 1024)  # MB
                    self.log(f"安装包: {setup_file.name} ({file_size:.1f} MB)", "SUCCESS")

                return True
            else:
                self.log("安装包创建失败:", "ERROR")
                self.log(result.stdout, "ERROR")
                self.log(result.stderr, "ERROR")
                return False

        except Exception as e:
            self.log(f"创建安装包时出错: {e}", "ERROR")
            return False

    def fix_common_issues(self) -> None:
        """修复常见问题"""
        self.log("检查并修复常见问题...", "STEP")

        # 修复Inno Setup脚本中的路径问题
        setup_script = self.installer_dir / "setup.iss"
        if setup_script.exists():
            try:
                content = setup_script.read_text(encoding='utf-8')

                # 修复可执行文件路径
                content = content.replace(
                    'Source: "..\\dist\\WeChatGroupSender.exe"',
                    'Source: "..\\dist\\MeetSpaceWeChatSender\\MeetSpaceWeChatSender.exe"'
                )
                content = content.replace(
                    'Source: "..\\dist\\*"',
                    'Source: "..\\dist\\MeetSpaceWeChatSender\\*"'
                )

                # 修复图标引用
                content = content.replace(
                    'Filename: "{app}\\WeChatGroupSender.exe"',
                    'Filename: "{app}\\MeetSpaceWeChatSender.exe"'
                )

                setup_script.write_text(content, encoding='utf-8')
                self.log("已修复Inno Setup脚本路径问题", "SUCCESS")

            except Exception as e:
                self.log(f"修复Inno Setup脚本失败: {e}", "WARNING")

    def run_build(self) -> bool:
        """运行完整的构建流程"""
        self.log("=== 开始自动化构建流程 ===", "STEP")
        start_time = time.time()

        try:
            # 1. 检查依赖
            if not self.check_dependencies():
                return False

            # 2. 检查项目结构
            if not self.check_project_structure():
                return False

            # 3. 创建缺失的资源
            self.create_missing_resources()

            # 4. 修复常见问题
            self.fix_common_issues()

            # 5. 清理旧构建
            self.clean_build_dirs()

            # 6. 安装依赖
            if not self.install_requirements():
                self.log("依赖安装失败，但继续构建", "WARNING")

            # 7. 构建可执行文件
            if not self.build_executable():
                return False

            # 8. 验证构建
            if not self.verify_build():
                return False

            # 9. 创建安装包
            self.create_installer()

            # 构建完成
            elapsed_time = time.time() - start_time
            self.log(f"=== 构建流程完成 (耗时: {elapsed_time:.1f}秒) ===", "SUCCESS")

            # 显示结果
            self.show_build_results()

            return True

        except KeyboardInterrupt:
            self.log("构建被用户中断", "WARNING")
            return False
        except Exception as e:
            self.log(f"构建流程出错: {e}", "ERROR")
            return False

    def show_build_results(self) -> None:
        """显示构建结果"""
        self.log("=== 构建结果 ===", "INFO")

        if self.build_success:
            exe_path = self.dist_dir / "MeetSpaceWeChatSender"
            self.log(f"✅ 可执行文件: {exe_path}", "SUCCESS")

        if self.installer_success:
            setup_files = list(self.output_dir.glob("*.exe"))
            if setup_files:
                self.log(f"✅ 安装包: {setup_files[0]}", "SUCCESS")

        if not self.build_success:
            self.log("❌ 可执行文件构建失败", "ERROR")

        if self.inno_compiler and not self.installer_success:
            self.log("❌ 安装包创建失败", "ERROR")

        self.log("=== 构建完成 ===", "INFO")


def main():
    """主函数"""
    print("🚀 Meet space 微信群发助手 - 自动化打包工具")
    print("=" * 50)

    builder = AutoBuilder()
    success = builder.run_build()

    if success:
        print("\n🎉 构建成功！")
        sys.exit(0)
    else:
        print("\n💥 构建失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
