#!/usr/bin/env python3
"""
输入控件修复验证脚本
验证SpinBox和ComboBox的尺寸修复
"""

import sys
import os
from pathlib import Path

def check_input_widget_fixes():
    """检查输入控件修复状态"""
    print("🔍 检查输入控件修复状态...")
    
    theme_file = Path(__file__).parent / "ui" / "modern_theme_manager.py"
    
    if not theme_file.exists():
        print("❌ 主题文件不存在")
        return False
    
    try:
        content = theme_file.read_text(encoding='utf-8')
        
        # 检查修复项目
        checks = {
            "SpinBox最小宽度(默认主题)": "QSpinBox {\n                    background-color: #ffffff;\n                    color: #2d3748;\n                    min-width: 80px;\n                    padding: 2px 4px;",
            "SpinBox最小宽度(深色主题)": "QSpinBox {\n                    background-color: #2d3748;\n                    color: #e2e8f0;\n                    min-width: 80px;\n                    padding: 2px 4px;",
            "SpinBox最小宽度(护眼主题)": "QSpinBox {\n                    background-color: #ede5d3;\n                    color: #2f1b14;\n                    min-width: 80px;\n                    padding: 2px 4px;",
            "SpinBox最小宽度(科技主题)": "min-width: 80px;\n                    padding: 2px 4px;",
            "ComboBox最小宽度(默认主题)": "QComboBox {\n                    background-color: #ffffff;\n                    color: #2d3748;\n                    min-width: 120px;\n                    padding: 2px 4px;",
            "ComboBox最小宽度(深色主题)": "QComboBox {\n                    background-color: #2d3748;\n                    color: #e2e8f0;\n                    min-width: 120px;\n                    padding: 2px 4px;",
            "ComboBox最小宽度(护眼主题)": "QComboBox {\n                    background-color: #ede5d3;\n                    color: #2f1b14;\n                    min-width: 120px;\n                    padding: 2px 4px;",
            "ComboBox最小宽度(科技主题)": "min-width: 120px;\n                    padding: 2px 4px;",
        }
        
        results = {}
        for check_name, check_pattern in checks.items():
            if check_pattern in content:
                results[check_name] = "✅ 通过"
            else:
                results[check_name] = "❌ 缺失"
        
        # 显示结果
        print("\n📊 检查结果:")
        for check_name, result in results.items():
            print(f"  {check_name}: {result}")
        
        # 统计
        passed = sum(1 for result in results.values() if result.startswith("✅"))
        total = len(results)
        
        print(f"\n📈 总体状态: {passed}/{total} 项通过")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 检查主题文件失败: {e}")
        return False

def generate_manual_test_guide():
    """生成手动测试指南"""
    print("\n📋 手动测试指南:")
    print("=" * 60)
    
    print("\n🎯 测试目标:")
    print("  验证数字输入框(SpinBox)不再显示过短的问题")
    
    print("\n📝 测试步骤:")
    print("1. 🚀 启动程序")
    print("   python main.py")
    
    print("\n2. 🎨 切换到科技主题")
    print("   - 进入'系统设置'页面")
    print("   - 在'界面主题'下拉框中选择'科技主题'")
    
    print("\n3. 🔢 检查数字输入框")
    print("   - 观察'重试延迟(秒)'输入框")
    print("   - 确认输入框宽度足够显示数字")
    print("   - 测试输入和修改数值")
    
    print("\n4. 📋 检查下拉框")
    print("   - 观察'主题选择'下拉框")
    print("   - 观察'字体大小'下拉框")
    print("   - 确认下拉框宽度合适")
    
    print("\n5. 🔄 主题对比测试")
    print("   - 切换回'默认主题'")
    print("   - 对比输入控件尺寸是否一致")
    print("   - 再次切换到'科技主题'验证")
    
    print("\n✅ 预期结果:")
    print("  - SpinBox最小宽度≥80px，能完整显示数字")
    print("  - ComboBox最小宽度≥120px，能完整显示选项")
    print("  - 不同主题下控件尺寸保持一致")
    print("  - 所有输入控件都能正常交互")
    
    print("\n🚨 问题排查:")
    print("  如果仍然发现尺寸问题:")
    print("  1. 检查是否正确应用了主题")
    print("  2. 尝试重启程序重新加载样式")
    print("  3. 检查控制台是否有CSS错误")

def main():
    """主函数"""
    print("🚀 输入控件修复验证工具")
    print("=" * 60)
    
    # 检查文件修复状态
    file_check_passed = check_input_widget_fixes()
    
    if file_check_passed:
        print("\n🎉 所有输入控件修复项目检查通过！")
        print("\n📋 修复内容总结:")
        print("1. ✅ SpinBox添加最小宽度80px")
        print("2. ✅ SpinBox添加内边距2px 4px")
        print("3. ✅ ComboBox添加最小宽度120px")
        print("4. ✅ ComboBox添加内边距2px 4px")
        print("5. ✅ 所有主题保持一致的控件尺寸")
        
        # 生成测试指南
        generate_manual_test_guide()
        
        print(f"\n🔧 技术细节:")
        print("  修复方法: 在CSS样式中添加min-width和padding属性")
        print("  SpinBox: min-width: 80px; padding: 2px 4px;")
        print("  ComboBox: min-width: 120px; padding: 2px 4px;")
        print("  适用主题: 默认、浅色、深色、护眼、科技")
        
    else:
        print("\n❌ 发现未修复的问题，请检查上述结果")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
