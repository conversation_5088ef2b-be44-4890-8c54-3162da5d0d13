#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试循环发送按钮修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_button_removal():
    """测试按钮删除"""
    print("🔧 测试按钮删除...")
    
    try:
        import inspect
        
        # 创建QApplication以避免GUI错误
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from ui.loop_send_page import LoopSendPage
        
        # 检查setup_ui方法中是否删除了相关按钮
        init_source = inspect.getsource(LoopSendPage.setup_ui)
        
        # 检查是否删除了创建循环任务按钮
        if '创建循环任务' not in init_source:
            print("✅ 创建循环任务按钮已删除")
        else:
            print("❌ 创建循环任务按钮未删除")
            return False
        
        # 检查是否删除了暂停按钮
        if 'pause_btn' not in init_source:
            print("✅ 暂停按钮已删除")
        else:
            print("❌ 暂停按钮未删除")
            return False
        
        # 检查是否删除了停止按钮
        if 'stop_btn' not in init_source:
            print("✅ 停止按钮已删除")
        else:
            print("❌ 停止按钮未删除")
            return False
        
        # 检查是否删除了取消按钮
        if 'cancel_btn' not in init_source or 'QPushButton("❌ 取消")' not in init_source:
            print("✅ 取消按钮已删除")
        else:
            print("❌ 取消按钮未删除")
            return False
        
        # 检查是否保留了保存配置按钮
        if 'save_config_btn' in init_source and '保存配置' in init_source:
            print("✅ 保存配置按钮已保留")
        else:
            print("❌ 保存配置按钮被误删")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_removal():
    """测试方法删除"""
    print("\n🔧 测试方法删除...")
    
    try:
        from ui.loop_send_page import LoopSendPage
        
        # 检查是否删除了toggle_pause_task方法
        if not hasattr(LoopSendPage, 'toggle_pause_task'):
            print("✅ toggle_pause_task 方法已删除")
        else:
            print("❌ toggle_pause_task 方法未删除")
            return False
        
        # 检查是否删除了stop_task方法
        if not hasattr(LoopSendPage, 'stop_task'):
            print("✅ stop_task 方法已删除")
        else:
            print("❌ stop_task 方法未删除")
            return False
        
        # 检查是否删除了cancel_task方法
        if not hasattr(LoopSendPage, 'cancel_task'):
            print("✅ cancel_task 方法已删除")
        else:
            print("❌ cancel_task 方法未删除")
            return False
        
        # 检查是否保留了create_loop_task方法
        if hasattr(LoopSendPage, 'create_loop_task'):
            print("✅ create_loop_task 方法已保留")
        else:
            print("❌ create_loop_task 方法被误删")
            return False
        
        # 检查是否保留了分组任务管理方法
        if hasattr(LoopSendPage, 'start_group_task'):
            print("✅ start_group_task 方法已保留")
        else:
            print("❌ start_group_task 方法被误删")
            return False
        
        if hasattr(LoopSendPage, 'pause_group_task'):
            print("✅ pause_group_task 方法已保留")
        else:
            print("❌ pause_group_task 方法被误删")
            return False
        
        if hasattr(LoopSendPage, 'stop_group_task'):
            print("✅ stop_group_task 方法已保留")
        else:
            print("❌ stop_group_task 方法被误删")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_cleanup():
    """测试导入清理"""
    print("\n🔧 测试导入清理...")
    
    try:
        import inspect
        from ui import loop_send_page
        
        # 获取模块源码
        source = inspect.getsource(loop_send_page)
        
        # 检查是否删除了按钮状态管理器的导入
        if 'button_state_manager' not in source:
            print("✅ button_state_manager 导入已删除")
        else:
            print("❌ button_state_manager 导入未删除")
            return False
        
        if 'ButtonType' not in source:
            print("✅ ButtonType 导入已删除")
        else:
            print("❌ ButtonType 导入未删除")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 循环发送按钮修复测试")
    print("=" * 60)
    print("测试循环发送页面按钮删除和修复")
    print("=" * 60)
    
    tests = [
        ("按钮删除测试", test_button_removal),
        ("方法删除测试", test_method_removal),
        ("导入清理测试", test_import_cleanup)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 循环发送按钮修复测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 循环发送按钮修复成功！")
        print("\n✨ 修复内容:")
        print("  ❌ 删除了循环发送设置中的暂停按钮")
        print("  ❌ 删除了循环发送设置中的停止按钮")
        print("  ❌ 删除了循环发送设置中的取消按钮")
        print("  ❌ 删除了创建循环任务按钮")
        print("  ✅ 保留了保存配置按钮")
        print("  ✅ 保留了分组卡片上的所有按钮")
        print("  🧹 清理了相关的方法和导入")
        
        print("\n📋 现在的控制逻辑:")
        print("  💾 循环发送设置：只有保存配置按钮")
        print("  🎛️  分组卡片：启动、停止、删除、暂停、取消按钮")
        print("  🔄 任务管理：通过分组卡片按钮进行")
        
        print("\n🎯 循环发送页面按钮布局已优化！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
