"""
任务状态管理页面

提供定时发送任务和循环发送任务的统一管理界面，包括任务显示、删除和状态同步功能。
"""

from ui.themed_message_box import ThemedMessageBoxHelper
from datetime import datetime
from typing import Dict

from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QTableWidget,
    QTableWidgetItem,
    QMessageBox,
    QHeaderView,
    QGroupBox,
    QSplitter,
)
from PyQt6.QtGui import QFont

from core.timing_sender import timing_sender
from core.loop_sender import loop_sender
from core.group_manager import group_manager
from core.config_manager import config_manager
from core.risk_control import RiskController
from config.wechat_config import WeChatConfig
from utils.logger import setup_logger

logger = setup_logger("task_status_page")


class TaskStatusPage(QWidget):
    """任务状态管理页面"""

    # 信号定义
    task_deleted = pyqtSignal(str, str)  # task_id, task_type
    group_status_changed = pyqtSignal(str, str)  # group_id, group_type

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent

        # 数据缓存
        self.timing_tasks = {}
        self.loop_tasks = {}

        # UI组件
        self.timing_table = None
        self.loop_table = None
        self.refresh_timer = None
        self.risk_status_label = None

        # 风控控制器
        self.risk_controller = None
        self.init_risk_controller()

        self.setup_ui()
        self.setup_connections()
        self.setup_refresh_timer()
        self.refresh_all_tasks()

        logger.info("任务状态管理页面初始化完成")

    def init_risk_controller(self):
        """初始化风控控制器"""
        try:
            wechat_config = WeChatConfig()
            self.risk_controller = RiskController(wechat_config)
            logger.info("风控控制器初始化完成")
        except Exception as e:
            logger.error(f"风控控制器初始化失败: {e}")
            self.risk_controller = None

    def setup_ui(self):
        """设置用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 页面标题
        title_label = QLabel("📊 任务状态管理")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # 操作按钮区域
        self.create_action_buttons(main_layout)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        main_layout.addWidget(splitter)

        # 定时任务区域
        timing_group = self.create_timing_tasks_section()
        splitter.addWidget(timing_group)

        # 循环任务区域
        loop_group = self.create_loop_tasks_section()
        splitter.addWidget(loop_group)

        # 设置分割器比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)

    def create_action_buttons(self, layout):
        """创建操作按钮区域"""
        button_layout = QHBoxLayout()

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新任务")
        self.refresh_btn.clicked.connect(self.refresh_all_tasks)
        self.refresh_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """
        )
        button_layout.addWidget(self.refresh_btn)

        # 清理已完成任务按钮
        self.cleanup_btn = QPushButton("🧹 清理已完成")
        self.cleanup_btn.clicked.connect(self.cleanup_completed_tasks)
        self.cleanup_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """
        )
        button_layout.addWidget(self.cleanup_btn)

        button_layout.addStretch()

        # 统计信息标签
        self.stats_label = QLabel("统计信息加载中...")
        self.stats_label.setStyleSheet("color: #666; font-size: 12px;")
        button_layout.addWidget(self.stats_label)

        # 风控状态标签
        self.risk_status_label = QLabel("风控状态: 检查中...")
        self.risk_status_label.setStyleSheet(
            """
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 11px;
                color: #856404;
                font-weight: bold;
                margin-left: 10px;
            }
        """
        )
        button_layout.addWidget(self.risk_status_label)

        layout.addLayout(button_layout)

    def create_timing_tasks_section(self):
        """创建定时任务区域"""
        group_box = QGroupBox("⏰ 定时发送任务")
        group_box.setFont(QFont("Arial", 12, QFont.Weight.Bold))

        layout = QVBoxLayout(group_box)

        # 创建表格
        self.timing_table = QTableWidget()
        self.setup_timing_table()
        layout.addWidget(self.timing_table)

        return group_box

    def create_loop_tasks_section(self):
        """创建循环任务区域"""
        group_box = QGroupBox("🔄 循环发送任务")
        group_box.setFont(QFont("Arial", 12, QFont.Weight.Bold))

        layout = QVBoxLayout(group_box)

        # 创建表格
        self.loop_table = QTableWidget()
        self.setup_loop_table()
        layout.addWidget(self.loop_table)

        return group_box

    def setup_timing_table(self):
        """设置定时任务表格"""
        headers = [
            "任务ID",
            "分组名称",
            "创建时间",
            "执行时长",
            "状态消息",
            "任务类型",
            "操作",
        ]
        self.timing_table.setColumnCount(len(headers))
        self.timing_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.timing_table.setAlternatingRowColors(True)
        self.timing_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.timing_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)

        # 设置列宽
        header = self.timing_table.horizontalHeader()
        header.setSectionResizeMode(
            0, QHeaderView.ResizeMode.ResizeToContents
        )  # 任务ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 分组名称
        header.setSectionResizeMode(
            2, QHeaderView.ResizeMode.ResizeToContents
        )  # 创建时间
        header.setSectionResizeMode(
            3, QHeaderView.ResizeMode.ResizeToContents
        )  # 执行时长
        header.setSectionResizeMode(
            4, QHeaderView.ResizeMode.ResizeToContents
        )  # 状态消息
        header.setSectionResizeMode(
            5, QHeaderView.ResizeMode.ResizeToContents
        )  # 任务类型
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 操作

    def setup_loop_table(self):
        """设置循环任务表格"""
        headers = [
            "任务ID",
            "分组名称",
            "创建时间",
            "执行时长",
            "状态消息",
            "任务类型",
            "操作",
        ]
        self.loop_table.setColumnCount(len(headers))
        self.loop_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.loop_table.setAlternatingRowColors(True)
        self.loop_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.loop_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)

        # 设置列宽
        header = self.loop_table.horizontalHeader()
        header.setSectionResizeMode(
            0, QHeaderView.ResizeMode.ResizeToContents
        )  # 任务ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 分组名称
        header.setSectionResizeMode(
            2, QHeaderView.ResizeMode.ResizeToContents
        )  # 创建时间
        header.setSectionResizeMode(
            3, QHeaderView.ResizeMode.ResizeToContents
        )  # 执行时长
        header.setSectionResizeMode(
            4, QHeaderView.ResizeMode.ResizeToContents
        )  # 状态消息
        header.setSectionResizeMode(
            5, QHeaderView.ResizeMode.ResizeToContents
        )  # 任务类型
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 操作

    def setup_connections(self):
        """设置信号连接"""
        # 连接任务管理器的信号
        timing_sender.task_started.connect(self.on_timing_task_changed)
        timing_sender.task_completed.connect(self.on_timing_task_changed)
        timing_sender.task_failed.connect(self.on_timing_task_changed)

        loop_sender.task_started.connect(self.on_loop_task_changed)
        loop_sender.task_cycle_completed.connect(self.on_loop_task_changed)
        loop_sender.task_stopped.connect(self.on_loop_task_changed)
        loop_sender.task_error.connect(self.on_loop_task_error)

    def setup_refresh_timer(self):
        """设置自动刷新定时器"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_all_tasks)
        self.refresh_timer.start(5000)  # 每5秒刷新一次

    def refresh_all_tasks(self):
        """刷新所有任务"""
        try:
            self.refresh_timing_tasks()
            self.refresh_loop_tasks()
            self.update_statistics()
            self.update_risk_status()
            logger.debug("任务状态刷新完成")
        except Exception as e:
            logger.error(f"刷新任务状态失败: {e}")

    def refresh_timing_tasks(self):
        """刷新定时任务"""
        try:
            # 获取所有定时任务
            tasks = timing_sender.get_all_tasks()
            self.timing_tasks = {task.task_id: task for task in tasks}

            # 更新表格
            self.update_timing_table()

        except Exception as e:
            logger.error(f"刷新定时任务失败: {e}")

    def refresh_loop_tasks(self):
        """刷新循环任务"""
        try:
            # 获取所有循环任务
            tasks = loop_sender.get_all_tasks()
            self.loop_tasks = {task.task_id: task for task in tasks}

            # 更新表格
            self.update_loop_table()

        except Exception as e:
            logger.error(f"刷新循环任务失败: {e}")

    def update_timing_table(self):
        """更新定时任务表格"""
        try:
            self.timing_table.setRowCount(len(self.timing_tasks))

            for row, (task_id, task) in enumerate(self.timing_tasks.items()):
                # 任务ID
                task_id_display = task_id[:8] + "..." if len(task_id) > 8 else task_id
                self.timing_table.setItem(row, 0, QTableWidgetItem(task_id_display))

                # 分组名称
                group = group_manager.get_group(task.group_id, "timing")
                group_name = group.name if group else "未知分组"
                self.timing_table.setItem(row, 1, QTableWidgetItem(group_name))

                # 创建时间
                create_time = self.format_datetime(getattr(task, "created_time", None))
                self.timing_table.setItem(row, 2, QTableWidgetItem(create_time))

                # 执行时长
                duration = self.calculate_task_duration(task)
                self.timing_table.setItem(row, 3, QTableWidgetItem(duration))

                # 状态消息
                status_text = self.get_detailed_status_text(task)
                status_item = QTableWidgetItem(status_text)
                error_message = getattr(task, "error_message", None)
                status_item.setBackground(
                    self.get_status_color(task.status, error_message)
                )
                self.timing_table.setItem(row, 4, status_item)

                # 任务类型
                task_type = "定时发送"
                self.timing_table.setItem(row, 5, QTableWidgetItem(task_type))

                # 操作按钮
                self.create_timing_action_button(row, task_id)

        except Exception as e:
            logger.error(f"更新定时任务表格失败: {e}")

    def update_loop_table(self):
        """更新循环任务表格"""
        try:
            self.loop_table.setRowCount(len(self.loop_tasks))

            for row, (task_id, task) in enumerate(self.loop_tasks.items()):
                # 任务ID
                task_id_display = task_id[:8] + "..." if len(task_id) > 8 else task_id
                self.loop_table.setItem(row, 0, QTableWidgetItem(task_id_display))

                # 分组名称
                group = group_manager.get_group(task.group_id, "loop")
                group_name = group.name if group else "未知分组"
                self.loop_table.setItem(row, 1, QTableWidgetItem(group_name))

                # 创建时间
                create_time = self.format_datetime(getattr(task, "created_time", None))
                self.loop_table.setItem(row, 2, QTableWidgetItem(create_time))

                # 执行时长
                duration = self.calculate_task_duration(task)
                self.loop_table.setItem(row, 3, QTableWidgetItem(duration))

                # 状态消息
                status_text = self.get_detailed_status_text(task)
                status_item = QTableWidgetItem(status_text)
                error_message = getattr(task, "error_message", None)
                status_item.setBackground(
                    self.get_status_color(task.status, error_message)
                )
                self.loop_table.setItem(row, 4, status_item)

                # 任务类型
                task_type = "循环发送"
                self.loop_table.setItem(row, 5, QTableWidgetItem(task_type))

                # 操作按钮
                self.create_loop_action_button(row, task_id)

        except Exception as e:
            logger.error(f"更新循环任务表格失败: {e}")

    def create_timing_action_button(self, row: int, task_id: str):
        """创建定时任务操作按钮"""
        delete_btn = QPushButton("🗑️ 删除")
        delete_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """
        )
        delete_btn.clicked.connect(lambda: self.delete_timing_task(task_id))
        self.timing_table.setCellWidget(row, 6, delete_btn)

    def create_loop_action_button(self, row: int, task_id: str):
        """创建循环任务操作按钮"""
        delete_btn = QPushButton("🗑️ 删除")
        delete_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """
        )
        delete_btn.clicked.connect(lambda: self.delete_loop_task(task_id))
        self.loop_table.setCellWidget(row, 6, delete_btn)

    def get_status_text(self, status: str) -> str:
        """获取状态显示文本"""
        status_map = {
            "pending": "⏳ 等待中",
            "executing": "🔄 执行中",
            "paused": "⏸️ 已暂停",
            "completed": "✅ 已完成",
            "failed": "❌ 失败",
            "cancelled": "🚫 已取消",
            "running": "🔄 运行中",
            "stopped": "⏹️ 已停止",
            "created": "📝 已创建",
            "success": "✅ 发送成功",
            "error": "❌ 发送失败",
            "network_error": "🌐 网络连接失败",
            "risk_limited": "🛡️ 风控限制",
            "empty_group": "👥 分组成员为空",
        }
        return status_map.get(status, f"❓ {status}")

    def get_status_color(self, status: str, error_message: str = None):
        """获取状态背景颜色"""
        from PyQt6.QtGui import QColor

        # 检查是否是风控限制失败
        if status == "failed" and error_message and "超出风控限制" in error_message:
            return QColor(255, 182, 193)  # 深红色背景，突出显示风控限制

        color_map = {
            "pending": QColor(255, 248, 220),  # 浅黄色
            "executing": QColor(220, 248, 255),  # 浅蓝色
            "running": QColor(220, 248, 255),  # 浅蓝色
            "paused": QColor(255, 235, 205),  # 浅橙色
            "completed": QColor(220, 255, 220),  # 浅绿色
            "failed": QColor(255, 220, 220),  # 浅红色
            "cancelled": QColor(240, 240, 240),  # 浅灰色
            "stopped": QColor(240, 240, 240),  # 浅灰色
            "created": QColor(230, 230, 255),  # 浅紫色
            "success": QColor(220, 255, 220),  # 浅绿色
            "error": QColor(255, 220, 220),  # 浅红色
            "network_error": QColor(255, 200, 200),  # 深红色
            "risk_limited": QColor(255, 182, 193),  # 深红色，突出显示
            "empty_group": QColor(255, 235, 205),  # 浅橙色
        }
        return color_map.get(status, QColor(255, 255, 255))

    def format_datetime(self, dt_value):
        """格式化日期时间"""
        try:
            if dt_value is None:
                return "未知"

            # 如果是字符串，尝试解析
            if isinstance(dt_value, str):
                if dt_value in ["", "未知"]:
                    return "未知"
                # 尝试解析常见的时间格式
                try:
                    dt_obj = datetime.strptime(dt_value, "%Y-%m-%d %H:%M:%S")
                    return dt_obj.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    try:
                        dt_obj = datetime.strptime(dt_value, "%Y-%m-%d")
                        return dt_obj.strftime("%Y-%m-%d")
                    except ValueError:
                        return dt_value  # 返回原字符串

            # 如果是datetime对象
            elif hasattr(dt_value, "strftime"):
                return dt_value.strftime("%Y-%m-%d %H:%M:%S")

            # 其他情况
            else:
                return str(dt_value)

        except Exception as e:
            logger.error(f"格式化日期时间失败: {e}")
            return "格式错误"

    def calculate_task_duration(self, task):
        """计算任务执行时长"""
        try:
            created_time = getattr(task, "created_time", None)
            end_time = getattr(task, "end_time", None)
            status = getattr(task, "status", "unknown")

            if not created_time:
                return "未知"

            # 解析创建时间
            if isinstance(created_time, str):
                try:
                    created_dt = datetime.strptime(created_time, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    return "时间格式错误"
            elif hasattr(created_time, "strftime"):
                created_dt = created_time
            else:
                return "时间格式错误"

            # 确定结束时间
            if end_time:
                # 任务已结束，使用结束时间
                if isinstance(end_time, str):
                    try:
                        end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        end_dt = datetime.now()
                elif hasattr(end_time, "strftime"):
                    end_dt = end_time
                else:
                    end_dt = datetime.now()
            else:
                # 任务未结束，使用当前时间
                end_dt = datetime.now()

            # 计算时长
            duration = end_dt - created_dt
            total_minutes = int(duration.total_seconds() / 60)

            if total_minutes < 1:
                return "< 1分钟"
            elif total_minutes < 60:
                return f"{total_minutes}分钟"
            else:
                hours = total_minutes // 60
                minutes = total_minutes % 60
                if minutes == 0:
                    return f"{hours}小时"
                else:
                    return f"{hours}小时{minutes}分钟"

        except Exception as e:
            logger.error(f"计算任务执行时长失败: {e}")
            return "计算错误"

    def get_detailed_status_text(self, task):
        """获取详细的状态文本"""
        try:
            status = getattr(task, "status", "unknown")
            error_message = getattr(task, "error_message", None)

            # 基础状态映射
            status_map = {
                "pending": "⏳ 等待执行",
                "executing": "🔄 正在执行",
                "paused": "⏸️ 已暂停",
                "completed": "✅ 执行完成",
                "failed": "❌ 执行失败",
                "cancelled": "🚫 已取消",
                "running": "🔄 运行中",
                "stopped": "⏹️ 已停止",
                "created": "📝 任务已创建",
                "success": "✅ 发送成功",
                "error": "❌ 发送失败",
                "network_error": "🌐 网络连接失败",
                "risk_limited": "🛡️ 由于风控限制发送失败",
                "empty_group": "👥 分组成员为空",
            }

            base_text = status_map.get(status, f"❓ {status}")

            # 如果有错误信息，添加到状态文本中
            if error_message and status in ["failed", "error"]:
                # 检查错误类型并返回相应的状态消息
                if "超出风控限制" in error_message:
                    return "🛡️ 由于系统风控限制发送失败"
                elif "发送设置风控限制" in error_message:
                    return "🛡️ 由于发送设置风控限制发送失败"
                elif "网络" in error_message or "连接" in error_message:
                    return "🌐 网络连接失败"
                elif "分组成员为空" in error_message or "没有成员" in error_message:
                    return "👥 分组成员为空"
                else:
                    # 其他错误，截断过长的错误信息
                    if len(error_message) > 50:
                        error_text = error_message[:50] + "..."
                    else:
                        error_text = error_message
                    return f"{base_text} - {error_text}"

            # 添加成功状态的详细信息
            if status in ["completed", "success"]:
                # 检查是否有发送统计信息
                if hasattr(task, "total_sent_count") and task.total_sent_count > 0:
                    return f"{base_text} - 已发送 {task.total_sent_count} 条消息"
                elif (
                    hasattr(task, "current_cycle_sent") and task.current_cycle_sent > 0
                ):
                    return f"{base_text} - 本轮发送 {task.current_cycle_sent} 条消息"

            # 添加任务创建成功的信息
            if status == "created":
                return "📝 任务创建成功"

            # 添加任务启动成功的信息
            if status == "running" and hasattr(task, "started_time"):
                return "🚀 任务启动成功"

            # 检查风控状态
            risk_info = self.check_task_risk_status(task)
            if risk_info:
                return f"{base_text}\n{risk_info}"

            return base_text

        except Exception as e:
            logger.error(f"获取详细状态文本失败: {e}")
            return "状态未知"

    def check_task_risk_status(self, task) -> str:
        """检查任务的风控状态"""
        try:
            if not self.risk_controller:
                return ""

            # 获取任务相关的风控设置
            config = config_manager.get_config()
            send_settings = config.get("send_settings", {})
            risk_enabled = send_settings.get("use_system_risk_control", True)

            if not risk_enabled:
                return "风控: 已禁用"

            # 检查当前风控状态
            risk_ok = self.risk_controller.check_risk_control()

            if not risk_ok:
                return "风控: 受限制"

            # 如果任务正在运行，显示风控统计
            if hasattr(task, "status") and task.status in ["running", "executing"]:
                stats = self.risk_controller.get_send_statistics()
                hourly_count = stats.get("hourly_count", 0)
                max_hourly = send_settings.get("max_per_hour", 100)

                if hourly_count >= max_hourly * 0.8:  # 接近限制时显示警告
                    return f"风控: 接近限制 ({hourly_count}/{max_hourly})"

            return ""

        except Exception as e:
            logger.error(f"检查任务风控状态失败: {e}")
            return ""

    def delete_timing_task(self, task_id: str):
        """删除定时任务"""
        try:
            # 确认删除
            reply = ThemedMessageBoxHelper.show_question(
                self,
                "确认删除",
                f"确定要删除定时任务吗？\n\n任务ID: {task_id[:8]}...\n\n此操作不可撤销！",
            )

            if not reply:
                return

            # 获取任务信息用于后续状态更新
            task = self.timing_tasks.get(task_id)
            group_id = task.group_id if task else None

            # 删除任务
            success = timing_sender.delete_task(task_id)

            if success:
                # 发送信号通知状态变化
                self.task_deleted.emit(task_id, "timing")
                if group_id:
                    self.group_status_changed.emit(group_id, "timing")

                # 刷新显示
                self.refresh_timing_tasks()

                logger.info(f"删除定时任务成功: {task_id}")
            else:
                logger.error(f"删除定时任务失败: {task_id}")

        except Exception as e:
            logger.error(f"删除定时任务失败: {e}")

    def delete_loop_task(self, task_id: str):
        """删除循环任务"""
        try:
            # 确认删除
            reply = ThemedMessageBoxHelper.show_question(
                self,
                "确认删除",
                f"确定要删除循环任务吗？\n\n任务ID: {task_id[:8]}...\n\n此操作不可撤销！",
            )

            if not reply:
                return

            # 获取任务信息用于后续状态更新
            task = self.loop_tasks.get(task_id)
            group_id = task.group_id if task else None

            # 删除任务
            success = loop_sender.delete_task(task_id)

            if success:
                # 发送信号通知状态变化
                self.task_deleted.emit(task_id, "loop")
                if group_id:
                    self.group_status_changed.emit(group_id, "loop")

                # 刷新显示
                self.refresh_loop_tasks()

                logger.info(f"删除循环任务成功: {task_id}")
            else:
                logger.error(f"删除循环任务失败: {task_id}")

        except Exception as e:
            logger.error(f"删除循环任务失败: {e}")

    def cleanup_completed_tasks(self):
        """清理已完成的任务"""
        try:
            # 确认清理
            reply = ThemedMessageBoxHelper.show_question(
                self,
                "确认清理",
                "确定要清理所有已完成和失败的任务吗？\n\n此操作不可撤销！",
            )

            if not reply:
                return

            cleaned_count = 0
            affected_groups = set()

            # 清理定时任务
            for task_id, task in list(self.timing_tasks.items()):
                if task.status in ["completed", "failed", "cancelled"]:
                    if timing_sender.delete_task(task_id):
                        cleaned_count += 1
                        affected_groups.add((task.group_id, "timing"))

            # 清理循环任务
            for task_id, task in list(self.loop_tasks.items()):
                if task.status in ["completed", "failed", "cancelled", "stopped"]:
                    if loop_sender.delete_task(task_id):
                        cleaned_count += 1
                        affected_groups.add((task.group_id, "loop"))

            if cleaned_count > 0:
                # 通知相关分组状态变化
                for group_id, group_type in affected_groups:
                    self.group_status_changed.emit(group_id, group_type)

                # 刷新显示
                self.refresh_all_tasks()

                logger.info(f"清理已完成任务: {cleaned_count} 个")
            else:
                logger.info("没有需要清理的任务")

        except Exception as e:
            logger.error(f"清理已完成任务失败: {e}")

    def update_statistics(self):
        """更新统计信息"""
        try:
            # 统计定时任务
            timing_stats = self.calculate_timing_stats()

            # 统计循环任务
            loop_stats = self.calculate_loop_stats()

            # 更新显示
            stats_text = (
                f"定时任务: {timing_stats['total']}个 "
                f"(运行中: {timing_stats['running']}, 已完成: {timing_stats['completed']}, 失败: {timing_stats['failed']}) | "
                f"循环任务: {loop_stats['total']}个 "
                f"(运行中: {loop_stats['running']}, 已完成: {loop_stats['completed']}, 失败: {loop_stats['failed']})"
            )

            self.stats_label.setText(stats_text)

        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
            self.stats_label.setText("统计信息更新失败")

    def update_risk_status(self):
        """更新风控状态显示"""
        try:
            if not self.risk_controller:
                self.risk_status_label.setText("风控状态: 未初始化")
                self.risk_status_label.setStyleSheet(
                    """
                    QLabel {
                        background-color: #f8d7da;
                        border: 1px solid #f5c6cb;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-size: 11px;
                        color: #721c24;
                        font-weight: bold;
                        margin-left: 10px;
                    }
                """
                )
                return

            # 获取系统风控设置
            config = config_manager.get_config()
            send_settings = config.get("send_settings", {})
            risk_enabled = send_settings.get("use_system_risk_control", True)

            if not risk_enabled:
                self.risk_status_label.setText("风控状态: 已禁用")
                self.risk_status_label.setStyleSheet(
                    """
                    QLabel {
                        background-color: #d1ecf1;
                        border: 1px solid #bee5eb;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-size: 11px;
                        color: #0c5460;
                        font-weight: bold;
                        margin-left: 10px;
                    }
                """
                )
                return

            # 检查风控状态
            risk_ok = self.risk_controller.check_risk_control()

            if risk_ok:
                # 获取风控统计信息
                stats = self.risk_controller.get_send_statistics()
                hourly_count = stats.get("hourly_count", 0)
                daily_count = stats.get("daily_count", 0)
                max_hourly = send_settings.get("max_per_hour", 100)

                self.risk_status_label.setText(
                    f"风控状态: 正常 (今日: {daily_count}, 本小时: {hourly_count}/{max_hourly})"
                )
                self.risk_status_label.setStyleSheet(
                    """
                    QLabel {
                        background-color: #d4edda;
                        border: 1px solid #c3e6cb;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-size: 11px;
                        color: #155724;
                        font-weight: bold;
                        margin-left: 10px;
                    }
                """
                )
            else:
                self.risk_status_label.setText("风控状态: 受限 (发送被暂时阻止)")
                self.risk_status_label.setStyleSheet(
                    """
                    QLabel {
                        background-color: #fff3cd;
                        border: 1px solid #ffeaa7;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-size: 11px;
                        color: #856404;
                        font-weight: bold;
                        margin-left: 10px;
                    }
                """
                )

        except Exception as e:
            logger.error(f"更新风控状态失败: {e}")
            self.risk_status_label.setText("风控状态: 检查失败")
            self.risk_status_label.setStyleSheet(
                """
                QLabel {
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 11px;
                    color: #721c24;
                    font-weight: bold;
                    margin-left: 10px;
                }
            """
            )

    def calculate_timing_stats(self) -> Dict[str, int]:
        """计算定时任务统计"""
        stats = {
            "total": len(self.timing_tasks),
            "running": 0,
            "completed": 0,
            "failed": 0,
            "pending": 0,
            "paused": 0,
        }

        for task in self.timing_tasks.values():
            if task.status in ["pending", "executing"]:
                stats["running"] += 1
            elif task.status == "completed":
                stats["completed"] += 1
            elif task.status == "failed":
                stats["failed"] += 1
            elif task.status == "pending":
                stats["pending"] += 1
            elif task.status == "paused":
                stats["paused"] += 1

        return stats

    def calculate_loop_stats(self) -> Dict[str, int]:
        """计算循环任务统计"""
        stats = {
            "total": len(self.loop_tasks),
            "running": 0,
            "completed": 0,
            "failed": 0,
            "paused": 0,
            "stopped": 0,
        }

        for task in self.loop_tasks.values():
            if task.status == "running":
                stats["running"] += 1
            elif task.status == "completed":
                stats["completed"] += 1
            elif task.status == "failed":
                stats["failed"] += 1
            elif task.status == "paused":
                stats["paused"] += 1
            elif task.status == "stopped":
                stats["stopped"] += 1

        return stats

    def on_timing_task_changed(self, task_id: str):
        """定时任务状态变化处理"""
        try:
            # 延迟刷新，避免频繁更新
            QTimer.singleShot(1000, self.refresh_timing_tasks)
        except Exception as e:
            logger.error(f"处理定时任务状态变化失败: {e}")

    def on_loop_task_changed(self, task_id: str):
        """循环任务状态变化处理"""
        try:
            # 延迟刷新，避免频繁更新
            QTimer.singleShot(1000, self.refresh_loop_tasks)
        except Exception as e:
            logger.error(f"处理循环任务状态变化失败: {e}")

    def on_loop_task_error(self, task_id: str, error: str):
        """循环任务错误处理"""
        try:
            logger.error(f"循环任务错误: {task_id} - {error}")
            # 延迟刷新，避免频繁更新
            QTimer.singleShot(1000, self.refresh_loop_tasks)
        except Exception as e:
            logger.error(f"处理循环任务错误失败: {e}")

    def closeEvent(self, event):
        """页面关闭事件"""
        try:
            # 停止刷新定时器
            if self.refresh_timer:
                self.refresh_timer.stop()

            logger.info("任务状态管理页面已关闭")
        except Exception as e:
            logger.error(f"关闭任务状态页面失败: {e}")

        super().closeEvent(event)
