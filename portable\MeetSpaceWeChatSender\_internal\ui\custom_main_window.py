#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义主窗口基类

集成自定义标题栏和窗口调整功能，实现：
- 隐藏系统标题栏
- 自定义标题栏控制
- 窗口拖动和调整大小
- 最大化/最小化/关闭功能
"""

from PyQt6.QtWidgets import QMainWindow, QVBoxLayout, QWidget, QApplication
from PyQt6.QtCore import Qt, QPoint, QRect, QTimer
from PyQt6.QtGui import QResizeEvent

from ui.custom_title_bar import CustomTitleBar, ResizableWidget
from utils.logger import setup_logger

logger = setup_logger("custom_main_window")


class CustomMainWindow(QMainWindow):
    """自定义主窗口基类"""

    def __init__(self, title="微信群发助手"):
        super().__init__()

        # 窗口属性
        self.window_title = title
        self.is_maximized = False
        self.normal_geometry = QRect()

        # 调整大小相关
        self.resize_margin = 8
        self.resize_direction = None
        self.resize_start_pos = QPoint()
        self.resize_start_geometry = QRect()

        # 初始化窗口
        self.setup_window()
        self.setup_custom_title_bar()
        self.setup_main_content()

        logger.debug("自定义主窗口初始化完成")

    def setup_window(self):
        """设置窗口属性"""
        # 强制隐藏系统标题栏
        self.setWindowFlags(
            Qt.WindowType.Window
            | Qt.WindowType.FramelessWindowHint
            | Qt.WindowType.WindowSystemMenuHint
        )

        # 设置窗口属性
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)

        # 确保标题栏完全隐藏
        self.setWindowFlag(Qt.WindowType.FramelessWindowHint, True)

        # 隐藏原生标题栏
        if hasattr(self, "setTitleBarWidget"):
            # 对于某些Qt版本，设置空的标题栏组件
            empty_widget = QWidget()
            empty_widget.setFixedHeight(0)
            self.setTitleBarWidget(empty_widget)

        # 设置最小尺寸
        self.setMinimumSize(800, 600)

        # 设置初始尺寸和位置
        self.resize(1200, 800)
        self.center_window()

        # 启用鼠标跟踪
        self.setMouseTracking(True)

        # 强制隐藏标题栏
        self.force_hide_title_bar()

    def setup_custom_title_bar(self):
        """设置自定义标题栏"""
        self.title_bar = CustomTitleBar(self, self.window_title)

        # 连接信号
        self.title_bar.close_clicked.connect(self.close)
        self.title_bar.minimize_clicked.connect(self.showMinimized)
        self.title_bar.maximize_clicked.connect(self.on_maximize_clicked)

    def setup_main_content(self):
        """设置主要内容区域"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 添加标题栏
        main_layout.addWidget(self.title_bar)

        # 创建内容区域
        self.content_widget = QWidget()
        self.content_widget.setObjectName("contentWidget")
        main_layout.addWidget(self.content_widget)

        # 内容布局（子类可以使用）
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(1, 0, 1, 1)  # 留出边框空间

    def center_window(self):
        """窗口居中"""
        try:
            screen = QApplication.primaryScreen()
            if screen:
                screen_geometry = screen.availableGeometry()
                window_geometry = self.frameGeometry()
                center_point = screen_geometry.center()
                window_geometry.moveCenter(center_point)
                self.move(window_geometry.topLeft())
        except Exception as e:
            logger.error(f"窗口居中失败: {e}")

    def force_hide_title_bar(self):
        """强制隐藏标题栏"""
        try:
            # 方法1：重新设置窗口标志
            current_flags = self.windowFlags()
            new_flags = (
                current_flags | Qt.WindowType.FramelessWindowHint
            ) & ~Qt.WindowType.WindowTitleHint
            self.setWindowFlags(new_flags)

            # 方法2：设置窗口标题为空
            super().setWindowTitle("")

            # 方法3：隐藏菜单栏（如果存在）
            if self.menuBar():
                self.menuBar().hide()

            # 方法4：隐藏状态栏的标题部分
            if hasattr(self, "statusBar") and self.statusBar():
                # 保持状态栏，但确保不显示标题
                pass

            logger.debug("强制隐藏标题栏完成")

        except Exception as e:
            logger.error(f"强制隐藏标题栏失败: {e}")

    def on_maximize_clicked(self):
        """最大化按钮点击处理"""
        # 这个方法由标题栏调用，实际的最大化切换已经在标题栏中处理
        pass

    def showMaximized(self):
        """显示最大化"""
        if not self.is_maximized:
            self.normal_geometry = self.geometry()
            super().showMaximized()
            self.is_maximized = True
            self.title_bar.update_maximize_state(True)

    def showNormal(self):
        """显示正常大小"""
        if self.is_maximized:
            super().showNormal()
            if not self.normal_geometry.isEmpty():
                self.setGeometry(self.normal_geometry)
            self.is_maximized = False
            self.title_bar.update_maximize_state(False)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton and not self.is_maximized:
            self.resize_direction = self.get_resize_direction(
                event.position().toPoint()
            )
            if self.resize_direction:
                self.resize_start_pos = event.globalPosition().toPoint()
                self.resize_start_geometry = self.geometry()

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if not self.is_maximized:
            if event.buttons() == Qt.MouseButton.LeftButton and self.resize_direction:
                self.resize_window(event.globalPosition().toPoint())
            else:
                # 只在边框区域更新鼠标光标
                direction = self.get_resize_direction(event.position().toPoint())
                self.update_cursor(direction)
        else:
            # 最大化状态下确保光标为正常箭头
            self.setCursor(Qt.CursorShape.ArrowCursor)

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.resize_direction = None
            self.setCursor(Qt.CursorShape.ArrowCursor)

        super().mouseReleaseEvent(event)

    def leaveEvent(self, event):
        """鼠标离开窗口事件"""
        # 鼠标离开窗口时重置光标为正常箭头
        self.setCursor(Qt.CursorShape.ArrowCursor)
        super().leaveEvent(event)

    def get_resize_direction(self, pos: QPoint) -> str:
        """获取调整大小的方向 - 仅在边框区域返回方向"""
        if self.is_maximized:
            return None

        rect = self.rect()
        margin = self.resize_margin

        # 检查是否在边框区域内
        left_edge = pos.x() <= margin
        right_edge = pos.x() >= rect.width() - margin
        top_edge = pos.y() <= margin
        bottom_edge = pos.y() >= rect.height() - margin

        # 只有在真正的边框区域才返回调整方向
        # 角落区域（优先级最高）
        if top_edge and left_edge:
            return "top_left"
        elif top_edge and right_edge:
            return "top_right"
        elif bottom_edge and left_edge:
            return "bottom_left"
        elif bottom_edge and right_edge:
            return "bottom_right"
        # 边缘区域
        elif top_edge:
            return "top"
        elif bottom_edge:
            return "bottom"
        elif left_edge:
            return "left"
        elif right_edge:
            return "right"

        # 在窗口内容区域，不返回调整方向
        return None

    def update_cursor(self, direction: str):
        """更新鼠标光标"""
        if self.is_maximized:
            self.setCursor(Qt.CursorShape.ArrowCursor)
            return

        cursor_map = {
            "top": Qt.CursorShape.SizeVerCursor,
            "bottom": Qt.CursorShape.SizeVerCursor,
            "left": Qt.CursorShape.SizeHorCursor,
            "right": Qt.CursorShape.SizeHorCursor,
            "top_left": Qt.CursorShape.SizeFDiagCursor,
            "bottom_right": Qt.CursorShape.SizeFDiagCursor,
            "top_right": Qt.CursorShape.SizeBDiagCursor,
            "bottom_left": Qt.CursorShape.SizeBDiagCursor,
        }

        if direction in cursor_map:
            self.setCursor(cursor_map[direction])
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)

    def resize_window(self, global_pos: QPoint):
        """调整窗口大小"""
        if self.is_maximized:
            return

        delta = global_pos - self.resize_start_pos
        new_geometry = QRect(self.resize_start_geometry)

        if "left" in self.resize_direction:
            new_geometry.setLeft(new_geometry.left() + delta.x())
        if "right" in self.resize_direction:
            new_geometry.setRight(new_geometry.right() + delta.x())
        if "top" in self.resize_direction:
            new_geometry.setTop(new_geometry.top() + delta.y())
        if "bottom" in self.resize_direction:
            new_geometry.setBottom(new_geometry.bottom() + delta.y())

        # 应用最小尺寸限制
        min_size = self.minimumSize()

        if new_geometry.width() < min_size.width():
            if "left" in self.resize_direction:
                new_geometry.setLeft(new_geometry.right() - min_size.width())
            else:
                new_geometry.setWidth(min_size.width())

        if new_geometry.height() < min_size.height():
            if "top" in self.resize_direction:
                new_geometry.setTop(new_geometry.bottom() - min_size.height())
            else:
                new_geometry.setHeight(min_size.height())

        self.setGeometry(new_geometry)

    def resizeEvent(self, event: QResizeEvent):
        """窗口大小变化事件"""
        super().resizeEvent(event)

        # 检查是否是最大化状态变化
        if self.isMaximized() != self.is_maximized:
            self.is_maximized = self.isMaximized()
            self.title_bar.update_maximize_state(self.is_maximized)

    def changeEvent(self, event):
        """窗口状态变化事件"""
        if event.type() == event.Type.WindowStateChange:
            # 更新最大化状态
            is_max = self.isMaximized()
            if is_max != self.is_maximized:
                self.is_maximized = is_max
                self.title_bar.update_maximize_state(is_max)

        super().changeEvent(event)

    def set_title(self, title: str):
        """设置窗口标题"""
        self.window_title = title
        self.title_bar.set_title(title)
        # 只设置任务栏标题，不显示系统标题栏
        super().setWindowTitle(title)

    def setWindowTitle(self, title: str):
        """重写setWindowTitle方法，确保不显示系统标题栏"""
        # 只更新任务栏标题，不显示窗口标题栏
        super().setWindowTitle(title)
        # 确保标题栏仍然隐藏
        if hasattr(self, "title_bar"):
            self.title_bar.set_title(title)

    def get_content_widget(self) -> QWidget:
        """获取内容组件"""
        return self.content_widget

    def get_content_layout(self):
        """获取内容布局"""
        return self.content_layout

    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, "title_bar"):
                self.title_bar.cleanup()
        except Exception as e:
            logger.error(f"清理自定义主窗口资源失败: {e}")
