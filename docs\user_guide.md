# 用户使用指南

本指南将帮助您快速上手微信无感群发助手的各项功能。

## 快速开始

### 1. 首次启动

1. **启动程序**
   ```bash
   python main.py
   ```

2. **界面介绍**
   - 顶部：连接状态和连接按钮
   - 中间：功能标签页（发送、联系人、模板、日志、设置）
   - 底部：状态栏显示当前操作状态

3. **连接微信**
   - 确保微信客户端已启动并登录
   - 点击"连接微信"按钮
   - 等待连接成功提示

### 2. 基础操作

#### 获取联系人
1. 切换到"联系人管理"标签页
2. 点击"刷新联系人"按钮
3. 等待联系人列表加载完成
4. 可以使用搜索功能查找特定联系人

#### 发送简单消息
1. 切换到"消息发送"标签页
2. 在左侧联系人列表中选择接收人
3. 在右侧消息编辑区输入内容
4. 点击"开始发送"按钮

## 详细功能说明

### 消息发送

#### 选择接收人
- **单选**: 点击联系人名称
- **多选**: 按住Ctrl键点击多个联系人
- **全选**: 点击"全选"按钮
- **反选**: 点击"反选"按钮
- **搜索**: 在搜索框中输入关键词筛选

#### 消息类型
1. **文本消息**
   - 直接在文本框中输入内容
   - 支持变量替换（如{{name}}）
   - 最大长度1000字符

2. **图片消息**
   - 选择"图片消息"类型
   - 点击"选择文件"选择图片
   - 支持jpg、png、gif等格式

3. **文件消息**
   - 选择"文件消息"类型
   - 点击"选择文件"选择文件
   - 支持doc、pdf、excel等格式

#### 发送选项
- **发送间隔**: 设置每条消息之间的延迟时间
- **批次设置**: 设置每批发送数量和批次间隔
- **安全选项**: 启用人工模拟和随机延迟

### 联系人管理

#### 联系人列表
- 显示所有微信联系人和群聊
- 包含名称、微信ID、类型等信息
- 支持按类型筛选（好友/群聊）

#### 导入导出
1. **导出联系人**
   - 点击"导出联系人"按钮
   - 选择保存位置
   - 生成CSV格式文件

2. **导入联系人**
   - 点击"导入联系人"按钮
   - 选择CSV文件
   - 按格式要求导入数据

### 消息模板

#### 创建模板
1. 点击"新建"按钮
2. 输入模板名称
3. 选择模板类型（文本/图片/文件）
4. 编辑模板内容
5. 点击"保存模板"

#### 使用变量
支持的变量包括：
- `{{name}}`: 联系人名称
- `{{wxid}}`: 联系人微信ID
- `{{current_time}}`: 当前时间
- `{{current_date}}`: 当前日期
- `{{random_emoji}}`: 随机表情

#### 模板管理
- **编辑**: 选择模板后修改内容
- **删除**: 选择模板后点击删除
- **应用**: 在发送页面选择模板

### 运行日志

#### 日志查看
- 实时显示程序运行日志
- 包含操作记录、错误信息等
- 支持不同级别的日志显示

#### 日志管理
- **清空日志**: 清除当前显示的日志
- **导出日志**: 保存日志到文件
- **自动轮转**: 日志文件自动分割和备份

### 系统设置

#### 连接设置
- **自动启动**: 程序启动时自动连接微信
- **启动延迟**: 自动连接的延迟时间
- **登录超时**: 连接超时时间设置

#### 发送设置
- **每日限制**: 每天最大发送数量
- **每小时限制**: 每小时最大发送数量
- **风控保护**: 启用智能频率控制

#### 重试设置
- **最大重试次数**: 发送失败时的重试次数
- **重试延迟**: 重试之间的等待时间

## 高级功能

### 批量发送策略

#### 分批发送
1. 设置合适的批次大小（建议10-20个）
2. 设置批次间隔（建议60秒以上）
3. 启用随机延迟增加真实性

#### 时间控制
1. 避免在深夜或早晨发送
2. 工作日和周末采用不同策略
3. 重要节假日暂停发送

### 风控策略

#### 频率控制
- 每小时不超过50条消息
- 每天不超过200条消息
- 连续发送间隔不少于3秒

#### 内容多样化
- 使用多个消息模板
- 适当添加个性化内容
- 避免完全相同的消息

#### 行为模拟
- 启用随机延迟
- 模拟人工操作节奏
- 避免机械化发送模式

## 常见问题

### 连接问题
**Q: 无法连接到微信客户端**
A: 
1. 确保微信客户端已启动并登录
2. 检查WeChatFerry是否正确安装
3. 尝试重启微信客户端
4. 检查防火墙设置

**Q: 连接后显示未登录**
A:
1. 确认微信已完全登录
2. 等待几秒钟后重试
3. 检查微信版本是否支持

### 发送问题
**Q: 消息发送失败**
A:
1. 检查网络连接
2. 确认接收人微信ID正确
3. 检查消息内容是否合规
4. 降低发送频率

**Q: 发送速度太慢**
A:
1. 适当减少发送间隔
2. 增加批次大小
3. 关闭不必要的安全选项
4. 注意不要触发风控

### 界面问题
**Q: 界面显示异常**
A:
1. 检查屏幕分辨率设置
2. 尝试重启程序
3. 更新PyQt6版本
4. 检查系统兼容性

## 最佳实践

### 发送建议
1. **测试先行**: 先向自己发送测试消息
2. **小批量开始**: 从少量联系人开始测试
3. **内容审核**: 确保消息内容合规合法
4. **时间选择**: 选择合适的发送时间
5. **频率控制**: 严格控制发送频率

### 安全建议
1. **备份数据**: 定期备份联系人和模板
2. **更新软件**: 及时更新程序版本
3. **监控日志**: 关注错误和异常日志
4. **合规使用**: 遵守相关法律法规

### 效率建议
1. **模板复用**: 创建常用消息模板
2. **分组管理**: 对联系人进行分类管理
3. **批量操作**: 合理使用批量功能
4. **定期清理**: 清理无效联系人和模板
