
import os
import sys
import shutil
import tempfile
from pathlib import Path

def extract_injection_tools():
    """仅解压注入工具和DLL到临时目录"""
    try:
        # 获取打包资源路径
        if getattr(sys, 'frozen', False):
            bundle_dir = Path(sys._MEIPASS)
        else:
            bundle_dir = Path(__file__).parent
        
        # 创建专用临时目录
        temp_base = Path(tempfile.gettempdir()) / "MeetSpaceWeChatSender"
        temp_base.mkdir(exist_ok=True)
        
        # 解压注入工具
        tools_temp = temp_base / "tools"
        tools_temp.mkdir(exist_ok=True)
        
        injection_files = [
            ("tools/Injector.exe", "Injector.exe"),
            ("tools/x64/Injector.exe", "x64/Injector.exe"),
            ("tools/Win32/Injector.exe", "Win32/Injector.exe"),
            ("tools/ARM64/Injector.exe", "ARM64/Injector.exe")
        ]
        
        for src_path, dst_path in injection_files:
            src = bundle_dir / src_path
            dst = tools_temp / dst_path
            if src.exists():
                dst.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src, dst)
                # 确保可执行权限
                os.chmod(dst, 0o755)
        
        # 解压DLL文件
        dll_temp = temp_base / "wxhelper_files"
        dll_temp.mkdir(exist_ok=True)
        
        dll_files = [
            "wxhelper.dll",
            "wxhelper_latest.dll", 
            "wxhelper_original_backup.dll",
            "wxhelper_x64_backup.dll"
        ]
        
        for dll_file in dll_files:
            src = bundle_dir / "wxhelper_files" / dll_file
            dst = dll_temp / dll_file
            if src.exists():
                shutil.copy2(src, dst)
        
        return str(temp_base)
        
    except Exception as e:
        print(f"临时文件解压失败: {e}")
        return None

# 在模块导入时自动执行
if __name__ != "__main__":
    extract_injection_tools()
