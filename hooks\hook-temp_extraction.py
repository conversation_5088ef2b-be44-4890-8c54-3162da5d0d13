
import os
import sys
import shutil
import tempfile
from pathlib import Path

def extract_injection_tools():
    """仅解压注入工具到临时目录，DLL保持在打包内部"""
    try:
        # 获取打包资源路径
        if getattr(sys, 'frozen', False):
            bundle_dir = Path(sys._MEIPASS)
        else:
            bundle_dir = Path(__file__).parent

        # 创建专用临时目录
        temp_base = Path(tempfile.gettempdir()) / "MeetSpaceWeChatSender"
        temp_base.mkdir(exist_ok=True)

        # 仅解压注入工具（不解压DLL）
        tools_temp = temp_base / "tools"
        tools_temp.mkdir(exist_ok=True)

        injection_files = [
            ("tools/Injector.exe", "Injector.exe"),
            ("tools/x64/Injector.exe", "x64/Injector.exe"),
            ("tools/Win32/Injector.exe", "Win32/Injector.exe"),
            ("tools/ARM64/Injector.exe", "ARM64/Injector.exe")
        ]

        extracted_count = 0
        for src_path, dst_path in injection_files:
            src = bundle_dir / src_path
            dst = tools_temp / dst_path
            if src.exists():
                dst.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src, dst)
                # 确保可执行权限
                os.chmod(dst, 0o755)
                extracted_count += 1

        # DLL文件保持在打包内部，不解压到临时目录
        # 这样可以避免跨机器路径问题

        return str(temp_base)

    except Exception as e:
        print(f"临时文件解压失败: {e}")
        return None

# 在模块导入时自动执行
if __name__ != "__main__":
    extract_injection_tools()
