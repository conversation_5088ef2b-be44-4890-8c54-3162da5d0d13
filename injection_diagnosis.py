#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注入问题详细诊断工具
"""

import sys
import os
import subprocess
import psutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_wechat_info():
    """检查微信详细信息"""
    print("🔍 检查微信进程详细信息...")
    
    wechat_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'exe', 'create_time', 'cmdline']):
        try:
            if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                wechat_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if not wechat_processes:
        print("❌ 未找到微信进程")
        return None
    
    for proc in wechat_processes:
        try:
            print(f"✅ 微信进程: {proc.info['name']} (PID: {proc.info['pid']})")
            print(f"   可执行文件: {proc.info['exe']}")
            
            # 获取进程详细信息
            process = psutil.Process(proc.info['pid'])
            print(f"   内存使用: {process.memory_info().rss / 1024 / 1024:.1f} MB")
            print(f"   创建时间: {proc.info['create_time']}")
            
            # 检查进程架构
            try:
                # 尝试检查是否是32位进程
                import platform
                if platform.architecture()[0] == '64bit':
                    # 在64位系统上检查进程架构
                    print(f"   系统架构: 64位")
                    # 可以通过其他方式检查进程架构
                else:
                    print(f"   系统架构: 32位")
            except Exception:
                pass
            
            return proc.info['pid']
            
        except Exception as e:
            print(f"   获取进程信息失败: {e}")
    
    return wechat_processes[0].info['pid'] if wechat_processes else None

def test_injector_versions():
    """测试不同版本的注入器"""
    print("\n🔧 测试不同版本的注入器...")
    
    try:
        from core.injector_tool import InjectorTool
        
        # 获取微信PID
        wechat_pid = check_wechat_info()
        if not wechat_pid:
            print("❌ 无法测试，未找到微信进程")
            return False
        
        print(f"\n目标微信进程 PID: {wechat_pid}")
        
        # 创建注入器（不自动提升权限）
        injector = InjectorTool(auto_elevate=False)
        
        print(f"注入器路径: {injector.injector_path}")
        print(f"DLL路径: {injector.dll_path}")
        
        # 检查文件是否存在
        if not injector.injector_path.exists():
            print(f"❌ 注入器不存在: {injector.injector_path}")
            return False
        
        if not injector.dll_path.exists():
            print(f"❌ DLL不存在: {injector.dll_path}")
            return False
        
        print("✅ 注入器和DLL文件都存在")
        
        # 尝试注入
        print("\n开始注入测试...")
        success, message = injector.inject_dll()
        
        if success:
            print(f"✅ 注入成功: {message}")
            return True
        else:
            print(f"❌ 注入失败: {message}")
            
            # 详细分析失败原因
            print("\n🔍 分析失败原因...")
            
            if "General Error" in message:
                print("   'General Error' 可能的原因:")
                print("   1. DLL文件损坏或不兼容")
                print("   2. 微信版本与wxhelper不兼容")
                print("   3. 微信进程保护机制")
                print("   4. 杀毒软件拦截")
                print("   5. 权限不足")
            
            return False
        
    except Exception as e:
        print(f"❌ 测试注入器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dll_versions():
    """检查DLL版本"""
    print("\n📁 检查DLL版本...")
    
    try:
        from utils.path_manager import path_manager
        
        dll_files = [
            "wxhelper_files/wxhelper.dll",
            "wxhelper_files/wxhelper_latest.dll",
            "wxhelper_files/wxhelper_original_backup.dll",
            "wxhelper_files/wxhelper_x64_backup.dll"
        ]
        
        for dll_file in dll_files:
            dll_path = path_manager.get_resource_path(dll_file)
            if dll_path.exists():
                size = dll_path.stat().st_size
                print(f"✅ {dll_file}: {size} bytes")
                
                # 检查DLL文件头
                try:
                    with open(dll_path, 'rb') as f:
                        header = f.read(64)
                        if header[:2] == b'MZ':
                            print(f"   格式: PE文件 ✅")
                        else:
                            print(f"   格式: 无效 ❌")
                except Exception as e:
                    print(f"   读取失败: {e}")
            else:
                print(f"❌ {dll_file}: 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查DLL版本失败: {e}")
        return False

def test_manual_injection():
    """手动测试注入"""
    print("\n🔧 手动测试注入...")
    
    try:
        from utils.path_manager import path_manager
        
        # 获取微信PID
        wechat_pid = check_wechat_info()
        if not wechat_pid:
            return False
        
        # 尝试不同的DLL
        dll_files = [
            "wxhelper_files/wxhelper.dll",
            "wxhelper_files/wxhelper_latest.dll",
            "wxhelper_files/wxhelper_original_backup.dll"
        ]
        
        injector_path = path_manager.get_resource_path("tools/Win32/Injector.exe")
        if not injector_path.exists():
            injector_path = path_manager.get_resource_path("tools/Injector.exe")
        
        if not injector_path.exists():
            print("❌ 找不到注入器")
            return False
        
        for dll_file in dll_files:
            dll_path = path_manager.get_resource_path(dll_file)
            if not dll_path.exists():
                continue
            
            print(f"\n测试DLL: {dll_file}")
            
            # 构建命令
            cmd = [
                str(injector_path),
                "--process-id", str(wechat_pid),
                "--inject", str(dll_path)
            ]
            
            print(f"执行: {' '.join(cmd)}")
            
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                print(f"返回码: {result.returncode}")
                if result.stdout.strip():
                    print(f"输出: {result.stdout.strip()}")
                if result.stderr.strip():
                    print(f"错误: {result.stderr.strip()}")
                
                if result.returncode == 0:
                    print(f"✅ 使用 {dll_file} 注入成功！")
                    return True
                else:
                    print(f"❌ 使用 {dll_file} 注入失败")
                
            except subprocess.TimeoutExpired:
                print("❌ 注入超时")
            except Exception as e:
                print(f"❌ 执行失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        return False

def check_system_environment():
    """检查系统环境"""
    print("\n🖥️  检查系统环境...")
    
    try:
        import platform
        
        print(f"操作系统: {platform.system()} {platform.release()}")
        print(f"架构: {platform.architecture()[0]}")
        print(f"处理器: {platform.processor()}")
        
        # 检查Python版本
        print(f"Python版本: {sys.version}")
        
        # 检查权限
        from utils.simple_admin import is_admin
        print(f"管理员权限: {'是' if is_admin() else '否'}")
        
        # 检查临时目录
        import tempfile
        temp_dir = tempfile.gettempdir()
        print(f"临时目录: {temp_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查系统环境失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 注入问题详细诊断")
    print("=" * 70)
    
    tests = [
        ("系统环境检查", check_system_environment),
        ("微信进程检查", check_wechat_info),
        ("DLL版本检查", check_dll_versions),
        ("注入器测试", test_injector_versions),
        ("手动注入测试", test_manual_injection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 诊断结果:")
    
    for test_name, result in results:
        if result is not None:
            status = "✅ 成功" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
    
    print("\n🔧 解决建议:")
    print("1. 尝试重启微信")
    print("2. 检查微信版本是否过新")
    print("3. 尝试使用不同的DLL版本")
    print("4. 检查杀毒软件设置")
    print("5. 确保以管理员身份运行")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
