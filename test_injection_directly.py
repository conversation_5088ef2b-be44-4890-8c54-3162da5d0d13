#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试注入功能 - 在开发环境中
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_injection():
    """直接测试注入功能"""
    print("🔧 直接测试注入功能...")
    print("=" * 50)
    
    try:
        # 导入注入工具
        from core.injector_tool import InjectorTool
        
        print("✅ 成功导入InjectorTool")
        
        # 创建注入器实例
        print("\n📋 创建注入器实例...")
        injector = InjectorTool(auto_elevate=True)
        
        print(f"✅ 注入器创建成功")
        print(f"   DLL路径: {injector.dll_path}")
        print(f"   注入工具路径: {injector.injector_path}")
        print(f"   API端口: {injector.api_port}")
        
        # 检查文件是否存在
        print("\n📁 检查文件存在性...")
        if os.path.exists(injector.dll_path):
            dll_size = os.path.getsize(injector.dll_path)
            print(f"✅ DLL文件存在: {injector.dll_path} ({dll_size} bytes)")
        else:
            print(f"❌ DLL文件不存在: {injector.dll_path}")
            return False
        
        if os.path.exists(injector.injector_path):
            exe_size = os.path.getsize(injector.injector_path)
            print(f"✅ 注入工具存在: {injector.injector_path} ({exe_size} bytes)")
        else:
            print(f"❌ 注入工具不存在: {injector.injector_path}")
            return False
        
        # 检查微信进程
        print("\n🔍 检查微信进程...")
        wechat_process = injector.find_wechat_process()
        
        if wechat_process:
            print(f"✅ 找到微信进程: PID {wechat_process.pid}")
            print(f"   进程名: {wechat_process.name()}")
            try:
                print(f"   进程路径: {wechat_process.exe()}")
            except:
                print("   进程路径: 无法获取")
        else:
            print("❌ 未找到微信进程")
            print("请确保微信PC版已启动并登录")
            return False
        
        # 检查当前注入状态
        print("\n🔍 检查当前注入状态...")
        is_injected, status_msg = injector.check_injection_status()
        print(f"注入状态: {status_msg}")
        
        if is_injected:
            print("✅ wxhelper已注入")
            
            # 测试API服务
            print("\n🌐 测试API服务...")
            if injector.check_api_service():
                print("✅ API服务响应正常")
                return True
            else:
                print("❌ API服务未响应")
                return False
        else:
            print("⚠️  wxhelper未注入")
            
            # 尝试执行注入
            print("\n🚀 尝试执行注入...")
            success, message = injector.auto_inject()
            
            if success:
                print(f"✅ 注入成功: {message}")
                
                # 再次检查API服务
                print("\n🌐 检查API服务...")
                if injector.check_api_service():
                    print("✅ API服务响应正常")
                    return True
                else:
                    print("⚠️  注入成功但API服务未响应")
                    return False
            else:
                print(f"❌ 注入失败: {message}")
                return False
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 直接注入测试")
    print("=" * 60)
    print("在开发环境中直接测试注入功能")
    print("=" * 60)
    
    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    
    # 检查关键文件
    key_files = [
        "tools/Injector.exe",
        "wxhelper_files/wxhelper.dll",
        "core/injector_tool.py"
    ]
    
    print("\n📁 检查关键文件...")
    missing_files = []
    for file_path in key_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少关键文件: {missing_files}")
        print("请确保项目文件完整")
        input("\n按回车键退出...")
        return False
    
    # 执行注入测试
    print("\n" + "=" * 60)
    success = test_injection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 注入测试成功!")
        print("✅ 原始代码的注入功能正常工作")
        print("✅ 可以继续使用这个注入逻辑进行打包")
    else:
        print("❌ 注入测试失败!")
        print("需要检查具体问题:")
        print("  1. 微信是否已启动并登录")
        print("  2. 是否以管理员身份运行")
        print("  3. 杀毒软件是否阻止了注入")
        print("  4. 微信版本是否兼容")
    
    input("\n按回车键退出...")
    return success

if __name__ == "__main__":
    main()
