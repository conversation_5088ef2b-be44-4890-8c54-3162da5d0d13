#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试强制UAC权限提升
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_current_privileges():
    """测试当前权限"""
    print("🔐 检查当前权限...")
    
    try:
        from utils.simple_admin import is_admin
        
        current_admin = is_admin()
        print(f"当前权限状态: {'✅ 管理员' if current_admin else '❌ 普通用户'}")
        
        return current_admin
        
    except Exception as e:
        print(f"❌ 权限检查失败: {e}")
        return False

def test_force_uac():
    """测试强制UAC权限提升"""
    print("\n🚀 测试强制UAC权限提升...")
    
    try:
        from utils.simple_admin import force_admin_privileges
        
        print("⚠️  即将弹出UAC对话框...")
        print("如果您看到UAC对话框，说明修复成功！")
        print("如果没有弹出UAC对话框，说明还有问题。")
        
        input("按回车键继续测试强制UAC权限提升...")
        
        # 调用强制权限提升
        success = force_admin_privileges()
        
        # 如果到达这里，说明没有弹出UAC对话框
        print("❌ 没有弹出UAC对话框，强制权限提升失败")
        return False
        
    except SystemExit:
        # 这是正常的，说明UAC对话框弹出了，程序退出等待新进程启动
        print("✅ UAC对话框已弹出，程序正常退出")
        return True
    except Exception as e:
        print(f"❌ 强制权限提升异常: {e}")
        return False

def test_injection_with_force_uac():
    """测试带强制UAC的注入"""
    print("\n💉 测试带强制UAC的注入...")
    
    try:
        from core.injector_tool import InjectorTool
        
        # 创建注入器（启用自动权限提升）
        injector = InjectorTool(auto_elevate=True)
        
        print(f"注入器路径: {injector.injector_path}")
        print(f"DLL路径: {injector.dll_path}")
        print(f"自动权限提升: {'✅ 启用' if injector.auto_elevate else '❌ 禁用'}")
        
        # 检查微信进程
        wechat_process = injector.find_wechat_process()
        if not wechat_process:
            print("❌ 未找到微信进程，请先启动微信")
            return False
        
        print(f"✅ 找到微信进程: {wechat_process.info['name']} (PID: {wechat_process.pid})")
        
        print("\n⚠️  即将尝试注入，如果需要权限提升会弹出UAC对话框...")
        
        # 尝试注入
        success, message = injector.inject_dll()
        
        if success:
            print(f"✅ 注入成功: {message}")
            return True
        else:
            print(f"❌ 注入失败: {message}")
            return False
            
    except SystemExit:
        print("✅ UAC对话框已弹出，程序将重新启动")
        return True
    except Exception as e:
        print(f"❌ 注入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 强制UAC权限提升测试")
    print("=" * 60)
    print("测试修复后的强制UAC权限提升功能")
    print("=" * 60)
    
    # 检查当前权限
    current_admin = test_current_privileges()
    
    if current_admin:
        print("\n✅ 当前已有管理员权限")
        print("这正是之前的问题：程序认为已有权限，不弹出UAC对话框")
        print("现在测试强制权限提升功能...")
    else:
        print("\n⚠️  当前没有管理员权限")
        print("这种情况下应该会弹出UAC对话框")
    
    # 提供选择
    print("\n请选择测试项目:")
    print("1. 测试强制UAC权限提升功能")
    print("2. 测试完整的注入流程（包含UAC）")
    print("3. 退出")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            print("\n" + "="*40)
            result = test_force_uac()
            if result:
                print("✅ 强制UAC权限提升测试成功")
            else:
                print("❌ 强制UAC权限提升测试失败")
                
        elif choice == "2":
            print("\n" + "="*40)
            result = test_injection_with_force_uac()
            if result:
                print("✅ 注入流程测试成功")
            else:
                print("❌ 注入流程测试失败")
                
        elif choice == "3":
            print("退出测试")
            return
        else:
            print("无效选择")
            return
    
    except KeyboardInterrupt:
        print("\n用户取消测试")
        return
    
    print("\n" + "=" * 60)
    print("📋 测试说明:")
    print("✅ 如果看到UAC对话框弹出 = 修复成功")
    print("❌ 如果没有UAC对话框弹出 = 还有问题")
    print("🔄 如果程序重新启动 = UAC权限提升正常工作")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
