# Meet space 微信群发助手

一个功能强大的微信群发工具，支持定时发送和循环发送。

## 功能特性

- 🚀 **定时发送**：支持设定特定时间发送消息
- 🔄 **循环发送**：支持按间隔时间循环发送消息
- 👥 **分组管理**：支持创建和管理多个联系人分组
- 📝 **富文本消息**：支持文字、图片、表情等多种消息类型
- 🛡️ **风险控制**：内置发送频率控制，避免被限制
- 📊 **发送监控**：实时监控发送状态和进度
- 💾 **数据持久化**：所有设置和任务数据自动保存

## 系统要求

- Windows 10/11
- Python 3.8+ (如果从源码运行)
- 微信PC版

## 安装使用

### 方式一：使用安装程序（推荐）
1. 下载安装程序
2. 运行安装程序，按提示完成安装
3. 启动程序开始使用

### 方式二：直接运行
1. 下载程序包
2. 解压到任意目录
3. 双击 `MeetSpaceWeChatSender.exe` 启动

## 使用说明

1. **启动微信**：确保微信PC版已登录
2. **创建分组**：添加需要发送消息的联系人到分组
3. **设置消息**：编辑要发送的消息内容
4. **选择模式**：
   - 定时发送：设定具体的发送时间
   - 循环发送：设定发送间隔时间
5. **开始发送**：启动任务开始发送

## 注意事项

- 请合理使用，避免发送垃圾信息
- 建议设置合理的发送间隔，避免被微信限制
- 程序会自动保存所有设置，重启后可继续使用
- 支持单实例运行，避免重复启动

## 技术支持

如有问题或建议，请联系技术支持。

## 版权信息

Copyright © 2024 Meet space 会客创意空间
All rights reserved.
