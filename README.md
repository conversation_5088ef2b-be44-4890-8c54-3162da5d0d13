# Meet space 微信群发助手

一个功能强大、界面美观的微信群发工具，支持定时发送、循环发送和智能风控管理。

## ✨ 核心特性

### 📨 消息发送
- 🚀 **定时发送**：精确到分钟的定时发送功能
- 🔄 **循环发送**：支持按小时/天循环发送，可设置工作时间
- 📝 **富文本消息**：支持文字、图片、表情等多种消息类型
- 🎯 **批量发送**：支持批量大小控制，避免频率过高

### 👥 分组管理
- 📋 **智能分组**：支持创建和管理多个联系人分组
- 🔍 **联系人搜索**：快速查找和添加联系人
- 📊 **分组统计**：实时显示分组成员数量和状态
- 💾 **数据持久化**：分组信息自动保存，重启后保持

### 🛡️ 安全风控
- ⏱️ **智能间隔**：自动调节发送间隔，避免被限制
- 🚦 **风险控制**：内置多层风控机制，保护账号安全
- 📈 **发送监控**：实时监控发送状态、进度和成功率
- 🔄 **失败重试**：自动重试失败的发送任务

### 🎨 界面体验
- 🌈 **多主题支持**：默认、浅色、深色、护眼、科技五大主题
- 📱 **响应式设计**：适配不同屏幕尺寸，最小支持900x600
- 🎛️ **自定义控件**：精心设计的输入框、按钮和对话框
- 💫 **流畅动画**：优雅的界面过渡和状态反馈

### 🔧 技术架构
- 🏗️ **模块化设计**：清晰的代码结构，易于维护和扩展
- 🚀 **性能优化**：内存管理、UI优化、异步处理
- 📝 **完整日志**：详细的操作日志，便于问题排查
- 🔒 **配置备份**：自动备份配置文件，防止数据丢失

## 🖥️ 系统要求

- **操作系统**：Windows 10/11 (64位)
- **Python版本**：Python 3.8+ (源码运行)
- **微信版本**：微信PC版 (最新版本)
- **内存要求**：至少 4GB RAM
- **磁盘空间**：至少 500MB 可用空间

## 📦 安装使用

### 方式一：可执行文件（推荐）
```bash
# 1. 下载最新版本
# 2. 解压到任意目录
# 3. 双击 MeetSpaceWeChatSender.exe 启动
```

### 方式二：源码运行
```bash
# 1. 克隆项目
git clone [项目地址]
cd MeetSpaceWeChatSender

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动程序
python main.py
```

## 🚀 快速开始

### 1. 基础设置
1. **启动微信**：确保微信PC版已登录
2. **连接检测**：程序会自动检测微信连接状态
3. **主题选择**：在系统设置中选择喜欢的界面主题

### 2. 创建分组
1. 进入**分组管理**页面
2. 点击**新建分组**，输入分组名称
3. **添加成员**：搜索并添加联系人到分组
4. **保存分组**：确认成员列表无误后保存

### 3. 定时发送
1. 进入**定时发送**页面
2. **选择分组**：从列表中选择目标分组
3. **编辑消息**：输入要发送的消息内容
4. **设置时间**：选择执行日期和具体时间
5. **创建任务**：确认信息后创建定时任务

### 4. 循环发送
1. 进入**循环发送**页面
2. **选择分组**：选择目标联系人分组
3. **编辑消息**：输入循环发送的消息内容
4. **设置间隔**：配置发送间隔和工作时间
5. **启动循环**：开始循环发送任务

## 🎨 主题系统

### 可用主题
- **默认主题**：系统原生样式，简洁清爽
- **浅色主题**：明亮的浅色调，适合白天使用
- **深色主题**：优雅的深色调，适合夜间使用
- **护眼主题**：温和的绿色调，保护视力
- **科技主题**：未来科技感，深空蓝色渐变

### 主题特性
- 🎯 **智能适配**：自动适配不同控件和对话框
- 🔄 **实时切换**：无需重启即可切换主题
- 📐 **尺寸优化**：针对不同主题优化控件尺寸
- 💾 **记忆功能**：自动保存主题选择

## 🛡️ 安全须知

### 使用建议
- ⚠️ **合规使用**：请遵守相关法律法规，不发送违法违规内容
- 🕐 **合理间隔**：建议设置3-10秒的发送间隔
- 👥 **适量发送**：避免短时间内大量发送消息
- 🔍 **内容审查**：发送前请仔细检查消息内容

### 风险提示
- 📵 **账号安全**：频繁发送可能导致微信账号受限
- 🚫 **功能限制**：微信可能限制或禁用群发功能
- ⚖️ **法律责任**：用户需对发送内容承担法律责任

## 📊 项目结构

```
MeetSpaceWeChatSender/
├── main.py                    # 程序入口
├── requirements.txt           # 依赖包列表
├── version_info.txt          # 版本信息
├── config/                   # 配置模块
│   ├── settings.py          # 全局设置
│   └── wechat_config.py     # 微信配置
├── core/                     # 核心业务逻辑
│   ├── wechatferry_connector.py  # 微信连接器
│   ├── timing_sender.py          # 定时发送
│   ├── loop_sender.py            # 循环发送
│   ├── send_monitor.py           # 发送监控
│   ├── risk_control.py           # 风险控制
│   └── group_manager.py          # 分组管理
├── ui/                       # 用户界面
│   ├── main_window.py       # 主窗口
│   ├── timing_send_page.py  # 定时发送页面
│   ├── loop_send_page.py    # 循环发送页面
│   ├── modern_theme_manager.py   # 主题管理器
│   └── widgets/             # 自定义控件
└── utils/                    # 工具模块
    ├── logger.py            # 日志系统
    ├── performance_optimizer.py  # 性能优化
    └── path_manager.py      # 路径管理
```

## 🔧 开发信息

- **开发语言**：Python 3.8+
- **GUI框架**：PyQt6
- **微信接口**：WeChatFerry
- **版本控制**：Git
- **代码规范**：Black + Flake8

## 📞 技术支持

如有问题或建议，请通过以下方式联系：

- 📧 **邮箱支持**：[技术支持邮箱]
- 💬 **在线客服**：[客服联系方式]
- 📖 **使用文档**：[文档链接]
- 🐛 **问题反馈**：[反馈渠道]

## 📄 版权信息

```
Copyright © 2024 Meet space 会客创意空间
All rights reserved.

本软件受版权法保护，未经授权不得复制、分发或修改。
```

## 🏷️ 版本历史

### v1.0.0 (2024-08-05)
- ✨ 首个正式版本发布
- 🎨 完整的五主题系统
- 🚀 定时发送和循环发送功能
- 🛡️ 智能风控和安全机制
- 📱 响应式界面设计
- 🔧 性能优化和稳定性提升
