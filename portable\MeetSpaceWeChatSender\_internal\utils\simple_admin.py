#!/usr/bin/env python3
"""
简单可靠的管理员权限提升工具
"""

import os
import sys
import ctypes
import subprocess
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import setup_logger

logger = setup_logger("simple_admin")


def is_admin() -> bool:
    """检查是否具有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def create_admin_batch_script() -> str:
    """
    创建临时批处理脚本用于权限提升

    Returns:
        批处理脚本路径
    """
    try:
        # 获取当前程序信息
        if getattr(sys, "frozen", False):
            # 打包后的exe文件
            program_path = sys.executable
            command = f'"{program_path}"'
        else:
            # Python脚本
            python_exe = sys.executable
            script_path = os.path.abspath(sys.argv[0])
            command = f'"{python_exe}" "{script_path}"'

        # 添加命令行参数
        if len(sys.argv) > 1:
            params = " ".join(f'"{arg}"' for arg in sys.argv[1:])
            command += f" {params}"

        # 创建临时批处理文件
        temp_dir = tempfile.gettempdir()
        batch_file = os.path.join(temp_dir, "elevate_admin.bat")

        batch_content = f"""@echo off
echo 正在以管理员身份启动程序...
cd /d "{os.getcwd()}"
{command}
pause
del "%~f0"
"""

        with open(batch_file, "w", encoding="gbk") as f:
            f.write(batch_content)

        logger.info(f"创建临时批处理脚本: {batch_file}")
        return batch_file

    except Exception as e:
        logger.error(f"创建批处理脚本失败: {e}")
        return None


def request_admin_privileges_simple() -> bool:
    """
    简单可靠的权限提升方法

    Returns:
        是否成功请求权限提升
    """
    if is_admin():
        logger.info("已具有管理员权限")
        return True

    try:
        logger.info("请求管理员权限提升...")

        # 方法1: 直接使用runas命令
        try:
            # 获取当前程序信息
            if getattr(sys, "frozen", False):
                program_path = sys.executable
                target = program_path
                params = " ".join(sys.argv[1:]) if len(sys.argv) > 1 else ""
            else:
                python_exe = sys.executable
                script_path = os.path.abspath(sys.argv[0])
                target = python_exe
                params = f'"{script_path}"'
                if len(sys.argv) > 1:
                    params += " " + " ".join(f'"{arg}"' for arg in sys.argv[1:])

            # 使用ShellExecute with runas
            result = ctypes.windll.shell32.ShellExecuteW(
                None,  # hwnd
                "runas",  # lpOperation
                target,  # lpFile
                params,  # lpParameters
                None,  # lpDirectory
                1,  # nShowCmd (SW_SHOWNORMAL)
            )

            if result > 32:
                logger.info("权限提升请求已发送，当前进程将退出")
                sys.exit(0)
            else:
                logger.warning(f"ShellExecute权限提升失败，错误码: {result}")

        except Exception as e:
            logger.warning(f"ShellExecute方法失败: {e}")

        # 方法2: 使用批处理脚本
        try:
            logger.info("尝试使用批处理脚本方法...")
            batch_file = create_admin_batch_script()

            if batch_file and os.path.exists(batch_file):
                # 以管理员身份运行批处理文件
                result = ctypes.windll.shell32.ShellExecuteW(
                    None, "runas", batch_file, None, None, 1
                )

                if result > 32:
                    logger.info("批处理脚本权限提升成功，当前进程将退出")
                    sys.exit(0)
                else:
                    logger.warning(f"批处理脚本权限提升失败，错误码: {result}")

        except Exception as e:
            logger.warning(f"批处理脚本方法失败: {e}")

        # 方法3: 使用PowerShell
        try:
            logger.info("尝试使用PowerShell方法...")

            if getattr(sys, "frozen", False):
                command = f'"{sys.executable}"'
            else:
                command = f'"{sys.executable}" "{os.path.abspath(sys.argv[0])}"'

            if len(sys.argv) > 1:
                params = " ".join(f'"{arg}"' for arg in sys.argv[1:])
                command += f" {params}"

            ps_script = f"""
Start-Process -FilePath 'cmd' -ArgumentList '/c {command}' -Verb RunAs -WindowStyle Normal
"""

            result = subprocess.run(
                ["powershell", "-WindowStyle", "Hidden", "-Command", ps_script],
                capture_output=True,
                text=True,
                timeout=10,
                creationflags=(
                    subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                ),
            )

            if result.returncode == 0:
                logger.info("PowerShell权限提升成功，当前进程将退出")
                sys.exit(0)
            else:
                logger.warning(f"PowerShell权限提升失败: {result.stderr}")

        except Exception as e:
            logger.warning(f"PowerShell方法失败: {e}")

        # 所有方法都失败
        logger.error("所有权限提升方法都失败")
        return False

    except Exception as e:
        logger.error(f"权限提升过程异常: {e}")
        return False


def show_admin_help_message():
    """显示管理员权限帮助信息"""
    help_text = """
需要管理员权限才能进行DLL注入操作。

解决方法：
1. 右键点击程序图标，选择"以管理员身份运行"
2. 或者在命令行中使用管理员权限运行
3. 程序会自动尝试弹出UAC对话框请求权限

如果UAC对话框没有出现，请检查：
- Windows UAC设置是否启用
- 杀毒软件是否拦截了权限提升请求
- 用户账户是否具有管理员权限
"""
    print(help_text)
    logger.info("已显示管理员权限帮助信息")


def ensure_admin_privileges(auto_elevate: bool = True) -> bool:
    """
    确保具有管理员权限

    Args:
        auto_elevate: 是否自动尝试提升权限

    Returns:
        是否具有管理员权限
    """
    if is_admin():
        return True

    if auto_elevate:
        logger.info("检测到权限不足，尝试自动提升...")
        try:
            return request_admin_privileges_simple()
        except Exception as e:
            logger.error(f"自动权限提升失败: {e}")
            show_admin_help_message()
            return False
    else:
        logger.warning("权限不足且未启用自动提升")
        show_admin_help_message()
        return False


def create_admin_shortcut(shortcut_name: str = "微信群发助手(管理员)") -> bool:
    """
    创建以管理员身份运行的桌面快捷方式

    Args:
        shortcut_name: 快捷方式名称

    Returns:
        是否创建成功
    """
    try:
        import winreg

        # 获取桌面路径
        with winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders",
        ) as key:
            desktop_path = winreg.QueryValueEx(key, "Desktop")[0]

        # 获取程序路径
        if getattr(sys, "frozen", False):
            program_path = sys.executable
        else:
            program_path = os.path.abspath(sys.argv[0])

        # 创建VBS脚本来创建快捷方式
        vbs_script = f"""
Set WshShell = CreateObject("WScript.Shell")
Set oShellLink = WshShell.CreateShortcut("{desktop_path}\\{shortcut_name}.lnk")
oShellLink.TargetPath = "{program_path}"
oShellLink.WorkingDirectory = "{os.path.dirname(program_path)}"
oShellLink.Save
"""

        # 执行VBS脚本
        temp_vbs = os.path.join(tempfile.gettempdir(), "create_shortcut.vbs")
        with open(temp_vbs, "w", encoding="utf-8") as f:
            f.write(vbs_script)

        subprocess.run(
            ["cscript", "//NoLogo", temp_vbs], capture_output=True, text=True
        )

        # 删除临时文件
        try:
            os.remove(temp_vbs)
        except:
            pass

        logger.info(f"桌面快捷方式创建成功: {shortcut_name}")
        return True

    except Exception as e:
        logger.error(f"创建桌面快捷方式失败: {e}")
        return False


if __name__ == "__main__":
    # 测试权限提升功能
    print("🔧 测试简单权限提升功能")
    print("=" * 50)

    print(f"当前权限状态: {'✅ 管理员' if is_admin() else '❌ 普通用户'}")

    if not is_admin():
        print("\n尝试权限提升...")
        choice = input("是否尝试自动提升权限? (y/N): ").strip().lower()

        if choice == "y":
            ensure_admin_privileges(auto_elevate=True)
        else:
            print("跳过权限提升")
            show_admin_help_message()
    else:
        print("✅ 已具有管理员权限，无需提升")

        # 询问是否创建快捷方式
        choice = input("\n是否创建管理员快捷方式? (y/N): ").strip().lower()
        if choice == "y":
            create_admin_shortcut()
