#!/usr/bin/env python3
"""
SpinBox宽度测试工具
测试科技主题下SpinBox的宽度修复效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QLabel, QGroupBox, QSpinBox, QPushButton)
from PyQt6.QtCore import Qt, QTimer
from ui.modern_theme_manager import theme_manager
from ui.themed_dialog_base import ThemedDialogBase


class SpinBoxWidthTestDialog(ThemedDialogBase):
    """SpinBox宽度测试对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("SpinBox宽度测试")
        self.setFixedSize(600, 400)
        self.setup_ui()
        
        # 延迟检查宽度
        QTimer.singleShot(300, self.check_widths)
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("SpinBox宽度测试 - 科技主题修复验证")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 测试组
        test_group = QGroupBox("SpinBox测试（模拟系统设置页面）")
        test_layout = QVBoxLayout(test_group)
        
        # 各种SpinBox测试
        spinbox_configs = [
            ("启动延迟(秒):", 0, 60, 5),
            ("登录超时(秒):", 10, 300, 30),
            ("每日发送限制:", 1, 9999, 200),
            ("每小时发送限制:", 1, 999, 50),
            ("最大重试次数:", 1, 10, 5),
            ("重试延迟(秒):", 1, 60, 3),
        ]
        
        self.spinboxes = []
        for label_text, min_val, max_val, default_val in spinbox_configs:
            row_layout = QHBoxLayout()
            
            label = QLabel(label_text)
            label.setMinimumWidth(150)
            row_layout.addWidget(label)
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            self.spinboxes.append((label_text, spinbox))
            
            row_layout.addWidget(spinbox)
            row_layout.addStretch()
            test_layout.addLayout(row_layout)
        
        layout.addWidget(test_group)
        
        # 结果显示
        result_group = QGroupBox("宽度检查结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_label = QLabel("正在检查SpinBox宽度...")
        result_layout.addWidget(self.result_label)
        
        layout.addWidget(result_group)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        check_btn = QPushButton("重新检查宽度")
        check_btn.clicked.connect(self.check_widths)
        
        switch_theme_btn = QPushButton("切换主题对比")
        switch_theme_btn.clicked.connect(self.switch_theme)
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(check_btn)
        button_layout.addWidget(switch_theme_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def check_widths(self):
        """检查SpinBox宽度"""
        current_theme = theme_manager.get_current_theme()
        
        result_lines = [f"📊 当前主题: {current_theme}", ""]
        
        min_width = float('inf')
        max_width = 0
        total_width = 0
        
        for label_text, spinbox in self.spinboxes:
            width = spinbox.size().width()
            height = spinbox.size().height()
            
            min_width = min(min_width, width)
            max_width = max(max_width, width)
            total_width += width
            
            # 判断宽度是否合适
            if width >= 120:
                status = "✅"
            elif width >= 100:
                status = "⚠️"
            else:
                status = "❌"
            
            result_lines.append(f"{status} {label_text}")
            result_lines.append(f"    尺寸: {width} x {height} px")
            result_lines.append("")
        
        avg_width = total_width / len(self.spinboxes)
        
        result_lines.append("📈 统计信息:")
        result_lines.append(f"  最小宽度: {min_width} px")
        result_lines.append(f"  最大宽度: {max_width} px")
        result_lines.append(f"  平均宽度: {avg_width:.1f} px")
        result_lines.append("")
        
        # 总体评估
        if min_width >= 120:
            result_lines.append("🎉 所有SpinBox宽度都符合要求！")
            result_lines.append("✅ 修复成功，宽度足够显示数字")
        elif min_width >= 100:
            result_lines.append("⚠️  SpinBox宽度基本合适，但可以更宽一些")
        else:
            result_lines.append("❌ SpinBox宽度太窄，需要进一步调整")
        
        # 更新显示
        self.result_label.setText("\n".join(result_lines))
        
        # 同时在控制台输出
        print("\n" + "="*50)
        for line in result_lines:
            print(line)
        print("="*50)
        
    def switch_theme(self):
        """切换主题进行对比"""
        app = QApplication.instance()
        current_theme = theme_manager.get_current_theme()
        
        if current_theme == "科技主题":
            theme_manager.set_theme(app, "默认主题")
            self.setWindowTitle("SpinBox宽度测试 - 默认主题")
        else:
            theme_manager.set_theme(app, "科技主题")
            self.setWindowTitle("SpinBox宽度测试 - 科技主题")
        
        # 延迟检查，确保主题切换完成
        QTimer.singleShot(200, self.check_widths)


def test_spinbox_width():
    """测试SpinBox宽度"""
    app = QApplication(sys.argv)
    
    print("🔧 SpinBox宽度测试工具")
    print("=" * 50)
    
    print("📋 测试目标:")
    print("  验证科技主题下SpinBox的宽度是否足够")
    print("  目标最小宽度: 120px")
    
    print("\n🎨 应用科技主题...")
    theme_manager.set_theme(app, "科技主题")
    print("✅ 科技主题已应用")
    
    dialog = SpinBoxWidthTestDialog()
    dialog.show()
    
    print("\n📋 测试说明:")
    print("1. 观察各个SpinBox的宽度")
    print("2. 点击'重新检查宽度'查看详细信息")
    print("3. 点击'切换主题对比'与默认主题对比")
    print("4. 确认SpinBox能够完整显示数字")
    
    result = dialog.exec()
    if result == QDialog.DialogCode.Accepted:
        print("✅ SpinBox宽度测试完成")
    
    sys.exit(0)


def main():
    """主函数"""
    print("🚀 SpinBox宽度修复工具")
    print("=" * 50)
    print("\n📋 修复内容:")
    print("  科技主题SpinBox最小宽度: 77px → 120px")
    print("  确保数字输入框有足够的长度显示内容")
    
    print("\n✅ 修复完成！现在可以运行测试:")
    print("python test_spinbox_width.py --test")
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_spinbox_width()


if __name__ == "__main__":
    main()
