#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一错误处理装饰器

提供统一的错误处理、日志记录和用户友好的错误提示功能。
"""

import functools
import traceback
from typing import Callable, Any, Optional, Union
from PyQt6.QtWidgets import QWidget

from utils.logger import setup_logger
from ui.themed_message_box import ThemedMessageBoxHelper

logger = setup_logger("error_handler")


def safe_execute(
    default_return: Any = None,
    show_error: bool = False,
    error_title: str = "操作失败",
    log_error: bool = True,
    parent_widget: Optional[QWidget] = None
):
    """
    安全执行装饰器
    
    Args:
        default_return: 发生错误时的默认返回值
        show_error: 是否显示错误对话框
        error_title: 错误对话框标题
        log_error: 是否记录错误日志
        parent_widget: 父窗口（用于显示错误对话框）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录错误日志
                if log_error:
                    logger.error(f"执行 {func.__name__} 失败: {e}")
                    logger.debug(f"详细错误信息: {traceback.format_exc()}")
                
                # 显示错误对话框
                if show_error:
                    parent = parent_widget
                    if not parent and args:
                        # 尝试从第一个参数获取父窗口
                        if hasattr(args[0], 'parent') and callable(args[0].parent):
                            parent = args[0].parent()
                        elif isinstance(args[0], QWidget):
                            parent = args[0]
                    
                    error_msg = _format_user_friendly_error(e, func.__name__)
                    ThemedMessageBoxHelper.show_error(parent, error_title, error_msg)
                
                return default_return
        return wrapper
    return decorator


def ui_safe_execute(
    default_return: Any = None,
    error_title: str = "界面操作失败"
):
    """
    UI安全执行装饰器（自动显示错误对话框）
    
    Args:
        default_return: 发生错误时的默认返回值
        error_title: 错误对话框标题
    """
    return safe_execute(
        default_return=default_return,
        show_error=True,
        error_title=error_title,
        log_error=True
    )


def silent_execute(default_return: Any = None):
    """
    静默执行装饰器（只记录日志，不显示错误）
    
    Args:
        default_return: 发生错误时的默认返回值
    """
    return safe_execute(
        default_return=default_return,
        show_error=False,
        log_error=True
    )


def config_safe_execute(default_return: Any = None):
    """
    配置操作安全执行装饰器
    
    Args:
        default_return: 发生错误时的默认返回值
    """
    return safe_execute(
        default_return=default_return,
        show_error=True,
        error_title="配置操作失败",
        log_error=True
    )


def _format_user_friendly_error(exception: Exception, function_name: str) -> str:
    """
    格式化用户友好的错误信息
    
    Args:
        exception: 异常对象
        function_name: 函数名
        
    Returns:
        用户友好的错误信息
    """
    error_type = type(exception).__name__
    error_msg = str(exception)
    
    # 常见错误的用户友好提示
    friendly_messages = {
        "FileNotFoundError": "文件未找到，请检查文件路径是否正确",
        "PermissionError": "权限不足，请检查文件或目录权限",
        "ConnectionError": "网络连接失败，请检查网络设置",
        "TimeoutError": "操作超时，请稍后重试",
        "ValueError": "数据格式错误，请检查输入内容",
        "KeyError": "配置项缺失，请检查配置文件",
        "AttributeError": "对象属性错误，可能是程序内部问题",
        "TypeError": "数据类型错误，请检查输入格式",
        "JSONDecodeError": "JSON格式错误，请检查数据格式"
    }
    
    # 获取友好提示
    friendly_msg = friendly_messages.get(error_type, "")
    
    if friendly_msg:
        return f"{friendly_msg}\n\n技术详情：{error_msg}"
    else:
        return f"操作失败：{error_msg}\n\n如果问题持续存在，请查看日志文件获取更多信息。"


class ErrorContext:
    """错误上下文管理器"""
    
    def __init__(
        self,
        operation_name: str,
        show_error: bool = True,
        parent_widget: Optional[QWidget] = None,
        error_title: str = "操作失败"
    ):
        """
        初始化错误上下文
        
        Args:
            operation_name: 操作名称
            show_error: 是否显示错误对话框
            parent_widget: 父窗口
            error_title: 错误对话框标题
        """
        self.operation_name = operation_name
        self.show_error = show_error
        self.parent_widget = parent_widget
        self.error_title = error_title
        self.success = False
    
    def __enter__(self):
        logger.debug(f"开始执行操作: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            # 记录错误
            logger.error(f"操作失败 {self.operation_name}: {exc_val}")
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
            
            # 显示错误对话框
            if self.show_error:
                error_msg = _format_user_friendly_error(exc_val, self.operation_name)
                ThemedMessageBoxHelper.show_error(
                    self.parent_widget, 
                    self.error_title, 
                    error_msg
                )
            
            # 抑制异常传播
            return True
        else:
            self.success = True
            logger.debug(f"操作成功完成: {self.operation_name}")


def handle_rich_text_error(func: Callable) -> Callable:
    """
    富文本编辑器专用错误处理装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"富文本编辑器操作失败 {func.__name__}: {e}")
            
            # 对于富文本编辑器，通常返回空的安全值
            if "content" in func.__name__.lower():
                return {"type": "rich_text", "html": "", "plain_text": "", "images": []}
            elif "has_content" in func.__name__.lower():
                return False
            elif "count" in func.__name__.lower():
                return 0
            else:
                return None
    return wrapper


def handle_config_error(func: Callable) -> Callable:
    """
    配置操作专用错误处理装饰器
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"配置操作失败 {func.__name__}: {e}")
            
            # 配置操作失败时的默认行为
            if "save" in func.__name__.lower() or "update" in func.__name__.lower():
                return False
            elif "load" in func.__name__.lower() or "get" in func.__name__.lower():
                return None
            else:
                return False
    return wrapper


# 便捷的错误处理函数
def safe_call(func: Callable, *args, default_return: Any = None, **kwargs) -> Any:
    """
    安全调用函数
    
    Args:
        func: 要调用的函数
        *args: 函数参数
        default_return: 默认返回值
        **kwargs: 函数关键字参数
        
    Returns:
        函数返回值或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"安全调用失败 {func.__name__}: {e}")
        return default_return
