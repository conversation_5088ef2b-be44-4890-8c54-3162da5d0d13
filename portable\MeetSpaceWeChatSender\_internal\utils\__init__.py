"""
工具函数模块

提供项目中使用的各种工具函数，包括日志、文件处理、数据验证等。
"""

from .exceptions import (
    WeChatMassSenderError,
    ConnectionError,
    AuthenticationError,
    ValidationError,
    ConfigurationError,
    SendError,
    TemplateError,
    FileError,
    RiskControlError,
    APIError,
    TimeoutError,
    RetryExhaustedError,
    handle_exception,
    create_error_response,
)
from .file_handler import FileHandler
from .logger import get_logger, setup_logger
from .security import (
    SecurityManager,
    InputSanitizer,
    RateLimiter,
    SecurityAuditor,
    security_manager,
    input_sanitizer,
    rate_limiter,
    security_auditor,
    secure_compare,
    generate_token,
    validate_file_type,
)
from .validators import validate_contact, validate_message

__all__ = [
    # 日志相关
    "setup_logger",
    "get_logger",
    # 文件处理
    "FileHandler",
    # 数据验证
    "validate_contact",
    "validate_message",
    # 安全相关
    "SecurityManager",
    "InputSanitizer",
    "RateLimiter",
    "SecurityAuditor",
    "security_manager",
    "input_sanitizer",
    "rate_limiter",
    "security_auditor",
    "secure_compare",
    "generate_token",
    "validate_file_type",
    # 异常处理
    "WeChatMassSenderError",
    "ConnectionError",
    "AuthenticationError",
    "ValidationError",
    "ConfigurationError",
    "SendError",
    "TemplateError",
    "FileError",
    "RiskControlError",
    "APIError",
    "TimeoutError",
    "RetryExhaustedError",
    "handle_exception",
    "create_error_response",
]

__version__ = "1.0.0"
