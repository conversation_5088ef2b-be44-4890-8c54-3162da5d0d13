#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整构建脚本 - 包含注入工具和DLL文件
确保微信自动化功能完整可用
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def setup_environment():
    """设置构建环境"""
    print("🔧 设置构建环境...")
    
    # 设置环境变量解决编码问题
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'
    
    print("  ✅ 编码环境已设置")

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ["build", "dist"]
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"  ✅ 已清理: {dir_name}")

def verify_critical_files():
    """验证关键文件存在"""
    print("🔍 验证关键文件...")
    
    critical_files = {
        "主程序": "main.py",
        "注入工具": "tools/Injector.exe",
        "微信DLL": "wxhelper_files/wxhelper.dll",
        "应用图标": "resources/icons/app_icon.ico",
    }
    
    missing_files = []
    for name, file_path in critical_files.items():
        if Path(file_path).exists():
            print(f"  ✅ {name}: {file_path}")
        else:
            missing_files.append(f"{name} ({file_path})")
            print(f"  ❌ {name}: {file_path}")
    
    if missing_files:
        print(f"⚠️  缺少关键文件: {missing_files}")
        return False
    
    print("✅ 所有关键文件完整")
    return True

def build_complete_project():
    """构建包含所有工具的完整项目"""
    print("🔨 构建包含注入工具和DLL的完整项目...")
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 无控制台窗口
        "--name=MeetSpaceWeChatSender", # 程序名
        "--icon=resources/icons/app_icon.ico",  # 应用图标
        
        # === 关键：添加注入工具和DLL文件 ===
        # 注入工具 - 主程序
        "--add-data=tools/Injector.exe;tools",
        
        # 注入工具 - 不同架构版本
        "--add-data=tools/ARM64;tools/ARM64",
        "--add-data=tools/Win32;tools/Win32", 
        "--add-data=tools/x64;tools/x64",
        "--add-data=tools/README.md;tools",
        
        # 微信DLL文件 - 所有版本
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_original_backup.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_x64_backup.dll;wxhelper_files",
        "--add-data=wxhelper_files/README.md;wxhelper_files",
        
        # 其他资源文件
        "--add-data=resources;resources",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        "--add-data=README.md;.",
        
        # === 隐藏导入 - 完整的项目模块 ===
        # PyQt6核心
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=PyQt6.QtSvg",
        
        # 微信相关
        "--hidden-import=wcferry",
        
        # 数据处理
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=requests",
        "--hidden-import=aiohttp",
        
        # 图像处理
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        "--hidden-import=PIL.ImageDraw",
        "--hidden-import=PIL.ImageFont",
        
        # 系统和工具
        "--hidden-import=subprocess",
        "--hidden-import=ctypes",
        "--hidden-import=ctypes.wintypes",
        "--hidden-import=win32api",
        "--hidden-import=win32con",
        "--hidden-import=win32process",
        "--hidden-import=psutil",
        
        # 配置和序列化
        "--hidden-import=yaml",
        "--hidden-import=json",
        "--hidden-import=pickle",
        "--hidden-import=datetime",
        "--hidden-import=pathlib",
        
        # 您的项目模块 - config包
        "--hidden-import=config",
        "--hidden-import=config.settings",
        "--hidden-import=config.wechat_config",
        
        # 您的项目模块 - core包（包含注入相关）
        "--hidden-import=core",
        "--hidden-import=core.wechatferry_connector",
        "--hidden-import=core.http_api_connector",
        "--hidden-import=core.timing_sender",
        "--hidden-import=core.loop_sender",
        "--hidden-import=core.send_monitor",
        "--hidden-import=core.risk_control",
        "--hidden-import=core.group_manager",
        "--hidden-import=core.message_template",
        "--hidden-import=core.config_manager",
        "--hidden-import=core.message_sender_core",
        "--hidden-import=core.injector_tool",        # 注入工具
        "--hidden-import=core.auto_injector",        # 自动注入
        
        # 您的项目模块 - ui包
        "--hidden-import=ui",
        "--hidden-import=ui.main_window",
        "--hidden-import=ui.timing_send_page",
        "--hidden-import=ui.loop_send_page",
        "--hidden-import=ui.task_status_page",
        "--hidden-import=ui.modern_theme_manager",
        "--hidden-import=ui.rich_text_editor",
        "--hidden-import=ui.themed_dialog_base",
        "--hidden-import=ui.themed_message_box",
        "--hidden-import=ui.widgets",
        
        # 您的项目模块 - utils包
        "--hidden-import=utils",
        "--hidden-import=utils.logger",
        "--hidden-import=utils.path_manager",
        "--hidden-import=utils.performance_optimizer",
        "--hidden-import=utils.icon_manager",
        "--hidden-import=utils.admin_privileges",    # 管理员权限
        "--hidden-import=utils.subprocess_helper",   # 子进程帮助
        
        # 运行时配置生成器
        "--hidden-import=runtime_config_generator",
        
        # 排除不需要的模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy.testing",
        "--exclude-module=pytest",
        "--exclude-module=setuptools",
        "--exclude-module=distutils",
        
        # 优化选项
        "--clean",
        "--noconfirm",
        "--optimize=1",  # 降低优化级别，避免问题
        
        # 主程序文件
        "main.py"
    ]
    
    print(f"📋 执行PyInstaller构建...")
    print(f"   包含注入工具、DLL文件和所有项目模块")
    
    try:
        # 使用安全的环境运行
        env = os.environ.copy()
        
        result = subprocess.run(
            cmd, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env,
            timeout=900  # 15分钟超时
        )
        
        print("✅ PyInstaller执行成功")
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 构建超时（15分钟）")
        return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败，返回码: {e.returncode}")
        
        if e.stdout:
            stdout = e.stdout.replace('\x00', '').strip()
            if stdout:
                print("标准输出:")
                print(stdout[-1500:])
        
        if e.stderr:
            stderr = e.stderr.replace('\x00', '').strip()
            if stderr:
                print("错误输出:")
                print(stderr[-1500:])
        
        return False
    except Exception as e:
        print(f"❌ 构建异常: {e}")
        return False

def verify_output():
    """验证输出文件"""
    print("🔍 验证输出文件...")
    
    exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
    
    if not exe_file.exists():
        print("❌ 输出文件不存在")
        return False
    
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"✅ 输出文件: {exe_file}")
    print(f"📊 文件大小: {size_mb:.1f} MB")
    
    # 检查文件大小是否合理（包含工具后应该更大）
    if size_mb < 30:
        print("⚠️  文件大小可能偏小，检查是否包含所有工具")
        return False
    
    return True

def main():
    """主函数"""
    print("🛠️  Meet space 微信群发助手 - 完整工具包构建")
    print("=" * 70)
    print("包含：注入工具 + DLL文件 + 完整项目功能")
    print("=" * 70)
    
    try:
        # 1. 设置环境
        setup_environment()
        
        # 2. 验证关键文件
        if not verify_critical_files():
            print("❌ 关键文件缺失，无法构建")
            return False
        
        # 3. 清理
        clean_build()
        
        # 4. 构建
        if not build_complete_project():
            print("❌ 构建失败")
            return False
        
        # 5. 验证
        if not verify_output():
            print("❌ 输出验证失败")
            return False
        
        print("\n🎉 完整工具包构建成功!")
        print("📁 输出: dist/MeetSpaceWeChatSender.exe")
        print("\n✨ 包含的完整功能:")
        print("  🔧 注入工具 (Injector.exe + 多架构版本)")
        print("  📚 微信DLL文件 (wxhelper.dll + 备份版本)")
        print("  🚀 完整项目功能 (定时发送、循环发送、分组管理)")
        print("  🎨 5套主题界面")
        print("  🛡️  风险控制和监控")
        print("  ⚙️  运行时配置生成")
        print("\n📋 现在可以:")
        print("  1. 直接运行EXE文件")
        print("  2. 自动注入微信进程")
        print("  3. 使用所有群发功能")
        print("  4. 在任何Windows系统上使用")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  构建被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 构建过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
