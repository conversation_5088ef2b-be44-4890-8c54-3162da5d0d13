#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的构建脚本

使用更简单的PyInstaller命令来避免复杂的spec文件问题。
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    print("🏗️ 简化构建脚本")
    print("=" * 50)
    
    # 设置项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 清理旧文件
    print("🧹 清理旧文件...")
    if Path("dist").exists():
        shutil.rmtree("dist")
    if Path("build").exists():
        shutil.rmtree("build")
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
    
    # 构建PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onedir",  # 创建目录而不是单文件
        "--windowed",  # 无控制台窗口
        "--name=MeetSpaceWeChatSender",
        "--icon=resources/icons/app.ico",
        
        # 添加数据文件
        "--add-data=resources;resources",
        "--add-data=config;config_templates",
        "--add-data=README.md;.",
        "--add-data=LICENSE.txt;.",
        
        # 隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=PyQt6.QtNetwork",
        "--hidden-import=wcferry",
        "--hidden-import=requests",
        "--hidden-import=websocket",
        "--hidden-import=PIL",
        "--hidden-import=win32api",
        "--hidden-import=win32con",
        "--hidden-import=win32gui",
        "--hidden-import=win32process",
        "--hidden-import=psutil",
        "--hidden-import=cryptography",
        "--hidden-import=sqlite3",
        "--hidden-import=json",
        "--hidden-import=xml.etree.ElementTree",
        "--hidden-import=email.mime.text",
        "--hidden-import=email.mime.multipart",
        "--hidden-import=email.mime.base",
        "--hidden-import=urllib3",
        "--hidden-import=certifi",
        "--hidden-import=charset_normalizer",
        "--hidden-import=idna",
        "--hidden-import=dis",
        "--hidden-import=inspect",
        "--hidden-import=types",
        "--hidden-import=collections.abc",
        "--hidden-import=opcode",
        "--hidden-import=keyword",
        "--hidden-import=token",
        "--hidden-import=tokenize",
        
        # 排除不需要的模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--exclude-module=pandas",
        "--exclude-module=scipy",
        "--exclude-module=jupyter",
        "--exclude-module=IPython",
        "--exclude-module=notebook",
        "--exclude-module=pytest",
        "--exclude-module=unittest",
        
        # 主脚本
        "main.py"
    ]
    
    print("🚀 执行PyInstaller...")
    print(f"命令: {' '.join(cmd[:5])} ... (共{len(cmd)}个参数)")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        
        # 检查输出文件
        exe_path = Path("dist/MeetSpaceWeChatSender/MeetSpaceWeChatSender.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📦 可执行文件: {exe_path}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            
            # 显示目录结构
            dist_dir = Path("dist/MeetSpaceWeChatSender")
            file_count = len(list(dist_dir.rglob("*")))
            print(f"📁 总文件数: {file_count}")
            
            return True
        else:
            print("❌ 可执行文件未生成")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print("错误输出:")
        print(e.stderr)
        return False
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 构建完成！")
        print("可执行文件位置: dist/MeetSpaceWeChatSender/MeetSpaceWeChatSender.exe")
    else:
        print("\n💥 构建失败！")
        sys.exit(1)
