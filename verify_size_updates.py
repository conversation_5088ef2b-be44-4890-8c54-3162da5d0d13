#!/usr/bin/env python3
"""
尺寸更新验证脚本
验证SpinBox和ComboBox的尺寸是否已正确更新
"""

import sys
import os
from pathlib import Path

def verify_size_updates():
    """验证尺寸更新"""
    print("🔍 验证控件尺寸更新...")
    
    theme_file = Path(__file__).parent / "ui" / "modern_theme_manager.py"
    
    if not theme_file.exists():
        print("❌ 主题文件不存在")
        return False
    
    try:
        content = theme_file.read_text(encoding='utf-8')
        
        # 检查更新项目
        checks = {
            "SpinBox 150px宽度(默认主题)": "min-width: 150px;\n                    padding: 4px 8px;",
            "SpinBox 150px宽度(深色主题)": "min-width: 150px;\n                    padding: 4px 8px;",
            "SpinBox 150px宽度(护眼主题)": "min-width: 150px;\n                    padding: 4px 8px;",
            "SpinBox 150px宽度(科技主题)": "min-width: 150px;\n                    padding: 4px 8px;",
            "ComboBox 180px宽度(默认主题)": "min-width: 180px;\n                    padding: 4px 8px;",
            "ComboBox 180px宽度(深色主题)": "min-width: 180px;\n                    padding: 4px 8px;",
            "ComboBox 180px宽度(护眼主题)": "min-width: 180px;\n                    padding: 4px 8px;",
            "ComboBox 180px宽度(科技主题)": "min-width: 180px;\n                    padding: 4px 8px;",
        }
        
        results = {}
        for check_name, check_pattern in checks.items():
            if check_pattern in content:
                results[check_name] = "✅ 通过"
            else:
                results[check_name] = "❌ 缺失"
        
        # 检查是否还有旧的尺寸设置
        old_checks = {
            "无旧SpinBox 80px设置": "min-width: 80px;",
            "无旧ComboBox 120px设置": "min-width: 120px;",
            "无旧padding 2px 4px设置": "padding: 2px 4px;",
        }
        
        for check_name, old_pattern in old_checks.items():
            count = content.count(old_pattern)
            if count == 0:
                results[check_name] = "✅ 通过"
            else:
                results[check_name] = f"❌ 发现{count}个旧设置"
        
        # 显示结果
        print("\n📊 验证结果:")
        for check_name, result in results.items():
            print(f"  {check_name}: {result}")
        
        # 统计
        passed = sum(1 for result in results.values() if result.startswith("✅"))
        total = len(results)
        
        print(f"\n📈 总体状态: {passed}/{total} 项通过")
        
        if passed == total:
            print("\n🎉 所有尺寸更新验证通过！")
            print("\n📏 新的尺寸标准:")
            print("  - SpinBox: min-width: 150px, padding: 4px 8px")
            print("  - ComboBox: min-width: 180px, padding: 4px 8px")
            print("  - 适用于所有主题: 默认、浅色、深色、护眼、科技")
            
            print("\n🧪 测试建议:")
            print("1. 启动程序: python main.py")
            print("2. 进入系统设置页面")
            print("3. 切换到科技主题")
            print("4. 观察数字输入框和下拉框的宽度")
            print("5. 确认控件宽度明显增加，能完整显示内容")
            
            return True
        else:
            print(f"\n❌ 发现 {total - passed} 个问题，请检查上述结果")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 控件尺寸更新验证工具")
    print("=" * 60)
    
    success = verify_size_updates()
    
    if success:
        print("\n✅ 验证完成！控件尺寸已成功更新")
        print("\n📋 更新摘要:")
        print("  问题: 数字输入框和下拉框宽度太短")
        print("  解决: 增加最小宽度和内边距")
        print("  效果: SpinBox 150px, ComboBox 180px")
        print("  范围: 所有主题统一应用")
        
        print("\n🎯 现在您可以:")
        print("  - 正常使用科技主题")
        print("  - 数字输入框有足够宽度显示数字")
        print("  - 下拉框能完整显示选项文本")
        print("  - 所有主题下控件尺寸一致")
        
        return 0
    else:
        print("\n❌ 验证失败，请检查修复内容")
        return 1

if __name__ == "__main__":
    sys.exit(main())
