#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Meet space 微信群发助手 - 完整项目单文件打包
打包您的完整项目为单个EXE文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    dirs_to_clean = ["build", "dist"]
    
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"  ✅ 已清理: {dir_name}")

def build_complete_project():
    """构建完整项目"""
    print("🔨 构建完整项目单文件EXE...")
    
    # 构建命令 - 包含您项目的所有模块
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 无控制台窗口
        "--name=MeetSpaceWeChatSender", # 程序名
        "--icon=resources/icons/app_icon.ico",  # 应用图标
        
        # 添加数据文件 - 运行时需要的资源
        "--add-data=resources;resources",
        "--add-data=version_info.txt;.",
        "--add-data=LICENSE.txt;.",
        "--add-data=README.md;.",
        
        # 隐藏导入 - 您项目的所有核心模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=PyQt6.QtSvg",
        
        # 微信相关
        "--hidden-import=wcferry",
        
        # 数据处理
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=requests",
        "--hidden-import=aiohttp",
        
        # 图像处理
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        "--hidden-import=PIL.ImageDraw",
        "--hidden-import=PIL.ImageFont",
        
        # 配置和工具
        "--hidden-import=yaml",
        "--hidden-import=json",
        "--hidden-import=datetime",
        "--hidden-import=pathlib",
        "--hidden-import=psutil",
        
        # 您的业务模块 - config包
        "--hidden-import=config",
        "--hidden-import=config.settings",
        "--hidden-import=config.wechat_config",
        
        # 您的业务模块 - core包
        "--hidden-import=core",
        "--hidden-import=core.wechatferry_connector",
        "--hidden-import=core.http_api_connector",
        "--hidden-import=core.timing_sender",
        "--hidden-import=core.loop_sender",
        "--hidden-import=core.send_monitor",
        "--hidden-import=core.risk_control",
        "--hidden-import=core.group_manager",
        "--hidden-import=core.message_template",
        "--hidden-import=core.config_manager",
        "--hidden-import=core.message_sender_core",
        
        # 您的业务模块 - ui包
        "--hidden-import=ui",
        "--hidden-import=ui.main_window",
        "--hidden-import=ui.timing_send_page",
        "--hidden-import=ui.loop_send_page",
        "--hidden-import=ui.task_status_page",
        "--hidden-import=ui.modern_theme_manager",
        "--hidden-import=ui.rich_text_editor",
        "--hidden-import=ui.themed_dialog_base",
        "--hidden-import=ui.themed_message_box",
        
        # 您的业务模块 - ui.widgets包
        "--hidden-import=ui.widgets",
        "--hidden-import=ui.widgets.contact_selector",
        "--hidden-import=ui.widgets.group_list_widget",
        "--hidden-import=ui.widgets.loop_cycle_widget",
        "--hidden-import=ui.widgets.message_preview",
        "--hidden-import=ui.widgets.send_progress_widget",
        "--hidden-import=ui.widgets.task_item_widget",
        
        # 您的业务模块 - utils包
        "--hidden-import=utils",
        "--hidden-import=utils.logger",
        "--hidden-import=utils.path_manager",
        "--hidden-import=utils.performance_optimizer",
        "--hidden-import=utils.icon_manager",
        
        # 运行时配置生成器
        "--hidden-import=runtime_config_generator",
        
        # 排除不需要的模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy.testing",
        "--exclude-module=pytest",
        "--exclude-module=setuptools",
        "--exclude-module=distutils",
        
        # 优化选项
        "--clean",
        "--noconfirm",
        "--optimize=2",
        
        # 您的主程序文件
        "main.py"
    ]
    
    print(f"📋 执行PyInstaller构建...")
    print(f"   包含所有您的项目模块和依赖")
    
    try:
        # 运行构建
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("✅ PyInstaller执行成功")
        
        # 检查输出
        exe_file = Path("dist") / "MeetSpaceWeChatSender.exe"
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"🎉 您的项目构建成功!")
            print(f"📁 文件: {exe_file}")
            print(f"📊 大小: {size_mb:.1f} MB")
            return True
        else:
            print("❌ 未找到输出文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        if e.stdout:
            print("标准输出:")
            print(e.stdout[-2000:])  # 显示最后2000字符
        if e.stderr:
            print("错误输出:")
            print(e.stderr[-2000:])  # 显示最后2000字符
        return False

def verify_project_files():
    """验证项目文件完整性"""
    print("🔍 验证项目文件...")
    
    required_files = [
        "main.py",
        "config/__init__.py",
        "core/__init__.py", 
        "ui/__init__.py",
        "utils/__init__.py",
        "runtime_config_generator.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print(f"❌ 缺少关键文件: {missing_files}")
        return False
    
    print("✅ 项目文件完整")
    return True

def main():
    """主函数"""
    print("🚀 Meet space 微信群发助手 - 完整项目单文件打包")
    print("=" * 70)
    
    # 1. 验证项目文件
    if not verify_project_files():
        print("❌ 项目文件不完整，无法构建")
        return False
    
    # 2. 清理构建目录
    clean_build()
    
    # 3. 构建完整项目
    if not build_complete_project():
        print("❌ 项目构建失败")
        return False
    
    print("\n🎉 您的项目构建完成!")
    print("📁 输出文件: dist/MeetSpaceWeChatSender.exe")
    print("\n✨ 特性:")
    print("  ✅ 包含您的完整项目功能")
    print("  ✅ 单个EXE文件，无需安装")
    print("  ✅ 运行时自动生成加密配置文件")
    print("  ✅ 支持定时发送、循环发送、分组管理")
    print("  ✅ 包含5套主题界面")
    print("  ✅ 完整的风险控制功能")
    print("  ✅ 便携式，可在任何Windows系统运行")
    
    print("\n📋 使用说明:")
    print("  1. 双击 MeetSpaceWeChatSender.exe 运行")
    print("  2. 首次运行会自动创建配置文件")
    print("  3. 配置文件位置: %APPDATA%/MeetSpaceWeChatSender/")
    print("  4. 所有功能与原项目完全一致")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
