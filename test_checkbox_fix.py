#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试复选框点击修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_contact_selector_methods():
    """测试联系人选择器方法"""
    print("🔧 测试联系人选择器方法...")
    
    try:
        from ui.contact_selector_dialog import ContactSelectorDialog
        from core.wechatferry_connector import Contact
        
        # 创建测试联系人
        test_contacts = [
            Contact(wxid="test1", name="测试好友1", type="friend", remark="备注1"),
            Contact(wxid="test2", name="测试好友2", type="friend", remark="备注2"),
            Contact(wxid="test3@chatroom", name="测试群聊1", type="group", remark="群备注1")
        ]
        
        # 创建对话框实例（不显示）
        dialog = ContactSelectorDialog(None, [])
        dialog.set_contacts(test_contacts)
        
        # 检查是否有复选框事件处理方法
        if hasattr(dialog, 'on_friend_checkbox_changed'):
            print("✅ on_friend_checkbox_changed 方法存在")
        else:
            print("❌ on_friend_checkbox_changed 方法不存在")
            return False
        
        if hasattr(dialog, 'on_group_checkbox_changed'):
            print("✅ on_group_checkbox_changed 方法存在")
        else:
            print("❌ on_group_checkbox_changed 方法不存在")
            return False
        
        # 测试方法调用
        from PyQt6.QtCore import Qt
        
        # 测试添加好友
        dialog.on_friend_checkbox_changed(test_contacts[0], Qt.CheckState.Checked.value)
        if len(dialog.selected_contacts) == 1:
            print("✅ 好友复选框选中功能正常")
        else:
            print("❌ 好友复选框选中功能异常")
            return False
        
        # 测试取消选择好友
        dialog.on_friend_checkbox_changed(test_contacts[0], Qt.CheckState.Unchecked.value)
        if len(dialog.selected_contacts) == 0:
            print("✅ 好友复选框取消选中功能正常")
        else:
            print("❌ 好友复选框取消选中功能异常")
            return False
        
        # 测试添加群聊
        dialog.on_group_checkbox_changed(test_contacts[2], Qt.CheckState.Checked.value)
        if len(dialog.selected_contacts) == 1:
            print("✅ 群聊复选框选中功能正常")
        else:
            print("❌ 群聊复选框选中功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_member_selection_methods():
    """测试成员选择器方法"""
    print("\n🔧 测试成员选择器方法...")
    
    try:
        from ui.member_selection_dialog import MemberSelectionDialog
        
        # 创建对话框实例（不显示）
        dialog = MemberSelectionDialog(None, "test_group", "timing")
        
        # 检查是否有复选框事件处理方法
        if hasattr(dialog, 'on_friend_selection_changed'):
            print("✅ on_friend_selection_changed 方法存在")
        else:
            print("❌ on_friend_selection_changed 方法不存在")
            return False
        
        if hasattr(dialog, 'on_group_selection_changed'):
            print("✅ on_group_selection_changed 方法存在")
        else:
            print("❌ on_group_selection_changed 方法不存在")
            return False
        
        # 测试方法调用
        from PyQt6.QtCore import Qt
        
        test_friend = {
            "wxid": "test_friend",
            "name": "测试好友",
            "type": "contact",
            "remark": "测试备注"
        }
        
        # 测试添加好友
        dialog.on_friend_selection_changed(test_friend, Qt.CheckState.Checked.value)
        if len(dialog.selected_members) == 1:
            print("✅ 成员选择器好友复选框功能正常")
        else:
            print("❌ 成员选择器好友复选框功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_checkbox_logic():
    """测试复选框逻辑"""
    print("\n🔧 测试复选框逻辑...")
    
    try:
        from PyQt6.QtCore import Qt
        
        # 测试Qt复选框状态值
        checked_value = Qt.CheckState.Checked.value
        unchecked_value = Qt.CheckState.Unchecked.value
        
        print(f"✅ Qt.CheckState.Checked.value = {checked_value}")
        print(f"✅ Qt.CheckState.Unchecked.value = {unchecked_value}")
        
        # 验证状态值
        if checked_value == 2 and unchecked_value == 0:
            print("✅ Qt复选框状态值正确")
        else:
            print("❌ Qt复选框状态值异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 复选框点击修复测试")
    print("=" * 60)
    print("测试修复后的复选框点击功能")
    print("=" * 60)
    
    tests = [
        ("复选框逻辑测试", test_checkbox_logic),
        ("联系人选择器方法测试", test_contact_selector_methods),
        ("成员选择器方法测试", test_member_selection_methods)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 复选框点击修复测试结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 复选框点击修复成功！")
        print("\n✨ 修复内容:")
        print("  ☑️  ContactSelectorDialog 添加了复选框点击事件")
        print("  🔗 连接了 stateChanged 信号到处理方法")
        print("  📝 添加了 on_friend_checkbox_changed 方法")
        print("  📝 添加了 on_group_checkbox_changed 方法")
        print("  ✅ MemberSelectionDialog 已有完整的复选框逻辑")
        
        print("\n📋 现在所有主题的成员选择复选框都能正常工作了！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
