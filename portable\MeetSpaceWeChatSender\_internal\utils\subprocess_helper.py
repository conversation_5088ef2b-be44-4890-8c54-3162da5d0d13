#!/usr/bin/env python3
"""
subprocess辅助工具
解决Windows下的编码问题
"""

import subprocess
import sys
from typing import List, Optional, Union


def safe_subprocess_run(
    cmd: Union[str, List[str]],
    capture_output: bool = True,
    text: bool = True,
    timeout: Optional[float] = None,
    cwd: Optional[str] = None,
    hide_window: bool = True,
    **kwargs,
) -> subprocess.CompletedProcess:
    """
    安全的subprocess.run包装器，处理Windows编码问题

    Args:
        cmd: 要执行的命令
        capture_output: 是否捕获输出
        text: 是否以文本模式处理
        timeout: 超时时间
        cwd: 工作目录
        **kwargs: 其他参数

    Returns:
        subprocess.CompletedProcess对象
    """
    # 在Windows上设置合适的编码和窗口隐藏
    if sys.platform == "win32":
        # 设置窗口隐藏标志
        if hide_window and "creationflags" not in kwargs:
            kwargs["creationflags"] = subprocess.CREATE_NO_WINDOW

        # 优先使用UTF-8，如果失败则使用GBK，最后使用ignore错误处理
        encodings = ["utf-8", "gbk", "cp936"]

        for encoding in encodings:
            try:
                return subprocess.run(
                    cmd,
                    capture_output=capture_output,
                    text=text,
                    timeout=timeout,
                    cwd=cwd,
                    encoding=encoding,
                    errors="ignore",  # 忽略无法解码的字符
                    **kwargs,
                )
            except UnicodeDecodeError:
                continue
            except Exception as e:
                # 如果不是编码错误，直接抛出
                if "codec" not in str(e).lower():
                    raise
                continue

        # 如果所有编码都失败，使用bytes模式
        try:
            result = subprocess.run(
                cmd,
                capture_output=capture_output,
                text=False,  # 使用bytes模式
                timeout=timeout,
                cwd=cwd,
                **kwargs,
            )

            # 手动解码输出
            if result.stdout:
                try:
                    result.stdout = result.stdout.decode("utf-8", errors="ignore")
                except:
                    try:
                        result.stdout = result.stdout.decode("gbk", errors="ignore")
                    except:
                        result.stdout = str(result.stdout)

            if result.stderr:
                try:
                    result.stderr = result.stderr.decode("utf-8", errors="ignore")
                except:
                    try:
                        result.stderr = result.stderr.decode("gbk", errors="ignore")
                    except:
                        result.stderr = str(result.stderr)

            return result

        except Exception as e:
            raise RuntimeError(f"subprocess执行失败: {e}")

    else:
        # 非Windows系统，使用默认设置
        return subprocess.run(
            cmd,
            capture_output=capture_output,
            text=text,
            timeout=timeout,
            cwd=cwd,
            **kwargs,
        )


def safe_powershell_run(
    command: str, timeout: Optional[float] = 10, **kwargs
) -> subprocess.CompletedProcess:
    """
    安全的PowerShell命令执行

    Args:
        command: PowerShell命令
        timeout: 超时时间
        **kwargs: 其他参数

    Returns:
        subprocess.CompletedProcess对象
    """
    return safe_subprocess_run(
        ["powershell", "-Command", command], timeout=timeout, **kwargs
    )


def safe_cmd_run(
    command: str, timeout: Optional[float] = 10, **kwargs
) -> subprocess.CompletedProcess:
    """
    安全的CMD命令执行

    Args:
        command: CMD命令
        timeout: 超时时间
        **kwargs: 其他参数

    Returns:
        subprocess.CompletedProcess对象
    """
    return safe_subprocess_run(command, shell=True, timeout=timeout, **kwargs)
