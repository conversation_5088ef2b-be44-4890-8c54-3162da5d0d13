"""
微信连接器模块

负责与微信客户端的连接和通信。
"""

import asyncio
import os
import winreg
from typing import Dict, List, Optional, Tuple, Any
from PyQt6.QtCore import QObject, pyqtSignal, QMetaObject, Qt
from dataclasses import dataclass

from utils.logger import setup_logger

logger = setup_logger("wechatferry_connector")

# 尝试导入WeChatFerry库
try:
    from wcferry import Wcf, WxMsg

    WCFERRY_AVAILABLE = True
    logger.info("WeChatFerry库已导入，将使用真实微信接口")
except ImportError:
    WCFERRY_AVAILABLE = False
    logger.warning("WeChatFerry库未安装，将使用模拟数据。请运行: pip install wcferry")
except Exception as e:
    WCFERRY_AVAILABLE = False
    logger.error(f"WeChatFerry库导入失败: {e}，将使用模拟数据")


@dataclass
class Contact:
    """联系人信息"""

    wxid: str
    name: str
    remark: str = ""
    type: str = "friend"  # friend, group
    avatar: str = ""


class WeChatFerryConnector(QObject):
    """微信Ferry连接器"""

    # 信号定义
    login_success = pyqtSignal(dict)  # 登录成功
    login_failed = pyqtSignal(str)  # 登录失败
    logout = pyqtSignal()  # 登出
    contact_updated = pyqtSignal(list)  # 联系人更新
    connection_lost = pyqtSignal()  # 连接丢失
    message_received = pyqtSignal(dict)  # 收到消息

    def __init__(self):
        super().__init__()
        self.is_connected = False
        self.is_logged_in = False
        self.user_info = {}
        self.contacts = []
        self._wcf = None  # WeChatFerry实例
        self.use_real_wechat = WCFERRY_AVAILABLE  # 是否尝试使用真实微信

    def check_wcferry_available(self) -> Tuple[bool, str]:
        """检查WeChatFerry是否可用"""
        if WCFERRY_AVAILABLE:
            return True, "WeChatFerry模块已安装"
        else:
            return False, "WeChatFerry模块未安装，请运行: pip install wcferry"

    def _emit_signal_safe(self, signal, *args):
        """安全地发射信号，确保在主线程中执行"""
        QMetaObject.invokeMethod(
            self, "_emit_signal_impl", Qt.ConnectionType.QueuedConnection, signal, *args
        )

    def _emit_signal_impl(self, signal, *args):
        """实际发射信号的方法"""
        signal.emit(*args)

    async def connect(self) -> Tuple[bool, str]:
        """
        连接到微信客户端

        Returns:
            (是否成功, 消息)
        """
        try:
            logger.info("正在连接微信客户端...")

            if self.use_real_wechat:
                # 使用真实的WeChatFerry连接
                logger.info("使用WeChatFerry连接真实微信...")
                try:
                    # 跳过复杂的环境检测，直接尝试连接

                    logger.info("正在初始化WeChatFerry...")

                    # 直接创建WeChatFerry实例，不进行复杂的错误处理
                    # 参考成功项目的简单做法
                    self._wcf = Wcf(debug=False)
                    logger.info("WeChatFerry实例创建成功")

                    # 检查微信是否已启动
                    logger.info("检查微信登录状态...")
                    if not self._wcf.is_login():
                        logger.error("微信未登录，请先启动并登录微信客户端")
                        return False, "微信未登录，请先启动并登录微信客户端"

                    self.is_connected = True
                    self.is_logged_in = True  # WeChatFerry连接成功即表示已登录
                    logger.info("真实微信客户端连接成功")
                    return True, "连接真实微信成功"

                except Exception as e:
                    logger.error(f"WeChatFerry初始化失败: {e}")

                    # 检查是否是微信未安装的问题
                    error_str = str(e)
                    if (
                        "无法读取注册表" in error_str
                        or "获取 WeChat 安装路径失败" in error_str
                        or "初始化失败" in error_str
                    ):
                        return (
                            False,
                            "微信客户端未安装或未正确配置。请先下载并安装官方微信客户端，然后启动并登录。",
                        )
                    else:
                        error_msg = self._parse_wechat_error(error_str)
                        return False, error_msg
            else:
                logger.error("WeChatFerry库不可用，无法连接微信")
                return False, "WeChatFerry库不可用，请安装wcferry库"

        except Exception as e:
            logger.error(f"连接微信客户端失败: {e}")
            return False, str(e)

    def _check_wechat_installation(self) -> bool:
        """检查微信是否安装"""
        logger.info("正在检测微信安装状态...")

        # 检查注册表
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER, r"Software\Tencent\WeChat"
            ) as key:
                install_path = winreg.QueryValueEx(key, "InstallPath")[0]
                wechat_exe = os.path.join(install_path, "WeChat.exe")
                if os.path.exists(wechat_exe):
                    logger.info(f"从注册表检测到微信: {wechat_exe}")
                    return True
        except Exception as e:
            logger.debug(f"注册表检测失败: {e}")

        # 检查HKEY_LOCAL_MACHINE注册表
        try:
            with winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Tencent\WeChat"
            ) as key:
                install_path = winreg.QueryValueEx(key, "InstallPath")[0]
                wechat_exe = os.path.join(install_path, "WeChat.exe")
                if os.path.exists(wechat_exe):
                    logger.info(f"从系统注册表检测到微信: {wechat_exe}")
                    return True
        except Exception as e:
            logger.debug(f"系统注册表检测失败: {e}")

        # 检查常见安装路径
        common_paths = [
            r"C:\Program Files\Tencent\WeChat\WeChat.exe",
            r"C:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
            r"D:\Program Files\Tencent\WeChat\WeChat.exe",
            r"D:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
            r"E:\Program Files\Tencent\WeChat\WeChat.exe",
            r"F:\Program Files\Tencent\WeChat\WeChat.exe",
            # 用户目录下的安装
            os.path.expanduser(r"~\AppData\Local\Tencent\WeChat\WeChat.exe"),
            os.path.expanduser(r"~\Documents\Tencent\WeChat\WeChat.exe"),
        ]

        for path in common_paths:
            if os.path.exists(path):
                logger.info(f"在路径检测到微信: {path}")
                return True

        # 检查进程中是否有微信运行
        try:
            import subprocess

            result = subprocess.run(
                ["tasklist", "/FI", "IMAGENAME eq WeChat.exe"],
                capture_output=True,
                text=True,
            )
            if "WeChat.exe" in result.stdout:
                logger.info("检测到微信进程正在运行")
                return True
        except Exception as e:
            logger.debug(f"进程检测失败: {e}")

        logger.warning("未检测到微信安装，但程序将尝试连接")
        return True  # 改为True，让WeChatFerry自己处理检测

    def _parse_wechat_error(self, error_msg: str) -> str:
        """解析微信错误信息"""
        if "无法读取注册表" in error_msg or "获取 WeChat 安装路径失败" in error_msg:
            return "无法找到微信安装路径，请确保微信已正确安装"
        elif "初始化失败" in error_msg:
            return "WeChatFerry初始化失败，请检查微信版本兼容性"
        elif "连接失败" in error_msg:
            return "无法连接到微信，请确保微信已启动并登录"
        else:
            return f"微信连接错误: {error_msg}"

    async def disconnect(self) -> Tuple[bool, str]:
        """
        断开与微信客户端的连接

        Returns:
            (是否成功, 消息)
        """
        try:
            logger.info("正在断开微信客户端连接...")

            if self.use_real_wechat and self._wcf:
                # 断开真实微信连接
                try:
                    # WeChatFerry通常不需要显式断开，但我们可以清理资源
                    self._wcf = None
                    logger.info("真实微信连接已清理")
                except Exception as e:
                    logger.warning(f"清理WeChatFerry资源时出错: {e}")

            self.is_connected = False
            self.is_logged_in = False
            self.user_info = {}
            self.contacts = []

            QMetaObject.invokeMethod(self, "logout", Qt.ConnectionType.QueuedConnection)
            logger.info("微信客户端连接已断开")
            return True, "断开成功"

        except Exception as e:
            logger.error(f"断开微信客户端连接失败: {e}")
            return False, str(e)

    async def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        获取当前登录用户信息

        Returns:
            用户信息字典
        """
        try:
            if not self.is_connected:
                return None

            if self.use_real_wechat and self._wcf:
                # 获取真实用户信息
                try:
                    user_info = self._wcf.get_user_info()
                    if user_info:
                        # 转换为标准格式
                        formatted_info = {
                            "wxid": user_info.get("wxid", ""),
                            "name": user_info.get("name", ""),
                            "mobile": user_info.get("mobile", ""),
                            "avatar": user_info.get("avatar", ""),
                        }
                        self.user_info = formatted_info
                        self.is_logged_in = True
                        QMetaObject.invokeMethod(
                            self,
                            "login_success",
                            Qt.ConnectionType.QueuedConnection,
                            formatted_info,
                        )
                        logger.info(
                            f"获取真实用户信息成功: {formatted_info.get('name', '未知')}"
                        )
                        return formatted_info
                    else:
                        logger.error("获取用户信息返回空")
                        return None
                except Exception as e:
                    logger.error(f"调用WeChatFerry获取用户信息失败: {e}")
                    return None
            else:
                logger.error("WeChatFerry不可用，无法获取用户信息")
                return None

        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            QMetaObject.invokeMethod(
                self, "login_failed", Qt.ConnectionType.QueuedConnection, str(e)
            )
            return None

    async def get_contacts(self) -> List[Contact]:
        """
        获取联系人列表

        Returns:
            联系人列表
        """
        try:
            if not self.is_logged_in:
                logger.warning("未登录，无法获取联系人")
                return []

            logger.info("开始获取微信联系人列表...")

            if self.use_real_wechat and self._wcf:
                # 获取真实联系人
                try:
                    logger.info("正在从真实微信获取联系人...")

                    # 获取联系人列表
                    contacts_data = self._wcf.get_contacts()

                    if not contacts_data:
                        logger.warning("未获取到任何联系人数据")
                        return []

                    contacts = []
                    for contact_info in contacts_data:
                        try:
                            # 解析联系人信息
                            wxid = contact_info.get("wxid", "")
                            name = contact_info.get("name", "")
                            remark = contact_info.get("remark", "")

                            # 判断联系人类型
                            if wxid.endswith("@chatroom"):
                                contact_type = "group"
                            else:
                                contact_type = "friend"

                            # 过滤掉一些系统联系人
                            if wxid in ["filehelper", "fmessage", "newsapp", "weixin"]:
                                continue

                            contact = Contact(
                                wxid=wxid,
                                name=name or wxid,
                                remark=remark,
                                type=contact_type,
                                avatar=contact_info.get("avatar", ""),
                            )
                            contacts.append(contact)

                        except Exception as e:
                            logger.warning(f"解析联系人信息失败: {e}")
                            continue

                    self.contacts = contacts

                    # 统计联系人类型
                    friend_count = len([c for c in contacts if c.type == "friend"])
                    group_count = len([c for c in contacts if c.type == "group"])

                    logger.info(
                        f"成功获取真实联系人列表: 好友 {friend_count} 个, 群聊 {group_count} 个, 总计 {len(contacts)} 个"
                    )

                    # 发送联系人更新信号
                    print(
                        f"🔍 DEBUG: 准备发送 contact_updated 信号，联系人数量: {len(contacts)}"
                    )
                    # 使用安全的方式发射信号
                    self._emit_signal_safe(self.contact_updated, contacts)
                    print(f"🔍 DEBUG: contact_updated 信号已发送")

                    return contacts

                except Exception as e:
                    logger.error(f"获取真实联系人失败: {e}")
                    return []
            else:
                logger.error("WeChatFerry不可用，无法获取联系人")
                return []

        except Exception as e:
            logger.error(f"获取联系人失败: {e}")
            return []

    async def send_text_message(self, wxid: str, content: str) -> bool:
        """
        发送文本消息

        Args:
            wxid: 接收者微信ID
            content: 消息内容

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            if self.use_real_wechat and self._wcf:
                # 发送真实消息
                try:
                    result = self._wcf.send_text(wxid, content)
                    if result == 0:  # WeChatFerry返回0表示成功
                        logger.info(f"成功发送真实文本消息到 {wxid}: {content[:50]}...")
                        return True
                    else:
                        logger.error(f"发送真实文本消息失败，错误码: {result}")
                        return False
                except Exception as e:
                    logger.error(f"调用WeChatFerry发送文本消息失败: {e}")
                    return False
            else:
                logger.error("WeChatFerry不可用，无法发送消息")
                return False

        except Exception as e:
            logger.error(f"发送文本消息失败: {e}")
            return False

    async def send_file_message(self, wxid: str, file_path: str) -> bool:
        """
        发送文件消息

        Args:
            wxid: 接收者微信ID
            file_path: 文件路径

        Returns:
            是否发送成功
        """
        try:
            if not self.is_logged_in:
                logger.error("未登录，无法发送消息")
                return False

            if self.use_real_wechat and self._wcf:
                # 发送真实文件
                try:
                    # 检查文件是否存在
                    if not os.path.exists(file_path):
                        logger.error(f"文件不存在: {file_path}")
                        return False

                    # 根据文件类型选择发送方法
                    file_ext = os.path.splitext(file_path)[1].lower()
                    if file_ext in [".jpg", ".jpeg", ".png", ".gif", ".bmp"]:
                        # 发送图片
                        result = self._wcf.send_image(wxid, file_path)
                    else:
                        # 发送文件
                        result = self._wcf.send_file(wxid, file_path)

                    if result == 0:  # WeChatFerry返回0表示成功
                        logger.info(f"成功发送真实文件到 {wxid}: {file_path}")
                        return True
                    else:
                        logger.error(f"发送真实文件失败，错误码: {result}")
                        return False
                except Exception as e:
                    logger.error(f"调用WeChatFerry发送文件失败: {e}")
                    return False
            else:
                logger.error("WeChatFerry不可用，无法发送文件")
                return False

        except Exception as e:
            logger.error(f"发送文件消息失败: {e}")
            return False
