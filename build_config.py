#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包配置

用于生成Windows安装程序的打包配置。
"""

import os
import sys
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
DIST_DIR = PROJECT_ROOT / "dist"
BUILD_DIR = PROJECT_ROOT / "build"

# 应用信息
APP_NAME = "MeetSpaceWeChatSender"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Meet space 微信群发助手"
APP_AUTHOR = "Meet space 会客创意空间"

# PyInstaller配置
PYINSTALLER_CONFIG = {
    # 基本配置
    "name": APP_NAME,
    "script": str(PROJECT_ROOT / "main.py"),
    "icon": str(PROJECT_ROOT / "resources" / "icons" / "app.ico"),
    
    # 输出配置
    "distpath": str(DIST_DIR),
    "workpath": str(BUILD_DIR),
    "specpath": str(PROJECT_ROOT),
    
    # 打包选项
    "onefile": False,  # 使用目录模式，便于资源管理
    "windowed": True,  # Windows GUI应用
    "console": False,  # 不显示控制台
    "clean": True,     # 清理临时文件
    
    # 优化选项
    "optimize": 2,     # Python字节码优化
    "strip": False,    # 不删除符号信息（便于调试）
    "upx": False,      # 不使用UPX压缩（避免杀毒软件误报）
    
    # 隐藏导入
    "hiddenimports": [
        "PyQt6.QtCore",
        "PyQt6.QtGui", 
        "PyQt6.QtWidgets",
        "PyQt6.QtNetwork",
        "wcferry",
        "requests",
        "websocket",
        "PIL",
        "win32api",
        "win32con",
        "win32gui",
        "win32process",
        "psutil",
        "cryptography",
        "sqlite3",
        "json",
        "xml.etree.ElementTree",
        "email.mime.text",
        "email.mime.multipart",
        "email.mime.base",
        "urllib3",
        "certifi",
        "charset_normalizer",
        "idna",
        "dis",
        "inspect",
        "types",
        "collections.abc",
        "opcode",
        "keyword",
        "token",
        "tokenize"
    ],
    
    # 数据文件
    "datas": [
        (str(PROJECT_ROOT / "resources"), "resources"),
        (str(PROJECT_ROOT / "config" / "*.json"), "config_templates"),
        (str(PROJECT_ROOT / "README.md"), "."),
        (str(PROJECT_ROOT / "LICENSE.txt"), "."),
    ],
    
    # 二进制文件
    "binaries": [],
    
    # 排除模块
    "excludes": [
        "tkinter",
        "matplotlib",
        "numpy",
        "pandas",
        "scipy",
        "IPython",
        "jupyter",
        "notebook",
        "pytest",
        "unittest",
        "doctest",
        "pdb",
        "profile",
        "pstats",
        "cProfile",
        "trace",
        "dis",
        "pickletools",
        "turtle",
        "turtledemo",
        "lib2to3",
        "distutils",
        "setuptools",
        "pip",
        "wheel",
        "pkg_resources"
    ],
    
    # 运行时钩子
    "runtime_hooks": [str(PROJECT_ROOT / "hooks" / "rthook_fix_dis.py")],
    
    # 版本信息
    "version_file": None,  # 将在构建时生成
}

# 版本信息模板
VERSION_INFO_TEMPLATE = """
# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({major}, {minor}, {patch}, 0),
    prodvers=({major}, {minor}, {patch}, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'{author}'),
        StringStruct(u'FileDescription', u'{description}'),
        StringStruct(u'FileVersion', u'{version}'),
        StringStruct(u'InternalName', u'{name}'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024 {author}'),
        StringStruct(u'OriginalFilename', u'{name}.exe'),
        StringStruct(u'ProductName', u'{description}'),
        StringStruct(u'ProductVersion', u'{version}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
"""

def create_version_file():
    """创建版本信息文件"""
    version_parts = APP_VERSION.split('.')
    major, minor, patch = (int(v) for v in version_parts[:3])
    
    version_content = VERSION_INFO_TEMPLATE.format(
        major=major,
        minor=minor,
        patch=patch,
        name=APP_NAME,
        description=APP_DESCRIPTION,
        version=APP_VERSION,
        author=APP_AUTHOR
    )
    
    version_file = PROJECT_ROOT / "version_info.txt"
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    return str(version_file)

def create_spec_file():
    """创建PyInstaller spec文件"""
    
    # 创建版本文件
    version_file = create_version_file()
    PYINSTALLER_CONFIG["version_file"] = version_file
    
    # 处理路径转义
    script_path = str(PYINSTALLER_CONFIG["script"]).replace('\\', '\\\\')
    project_root = str(PROJECT_ROOT).replace('\\', '\\\\')
    version_file_path = str(version_file).replace('\\', '\\\\')
    icon_path = str(PYINSTALLER_CONFIG["icon"]).replace('\\', '\\\\')

    spec_content = f"""# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    [r'{script_path}'],
    pathex=[r'{project_root}'],
    binaries={PYINSTALLER_CONFIG["binaries"]},
    datas={PYINSTALLER_CONFIG["datas"]},
    hiddenimports={PYINSTALLER_CONFIG["hiddenimports"]},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks={PYINSTALLER_CONFIG["runtime_hooks"]},
    excludes={PYINSTALLER_CONFIG["excludes"]},
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{PYINSTALLER_CONFIG["name"]}',
    debug=False,
    bootloader_ignore_signals=False,
    strip={PYINSTALLER_CONFIG["strip"]},
    upx={PYINSTALLER_CONFIG["upx"]},
    console={PYINSTALLER_CONFIG["console"]},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version=r'{version_file_path}',
    icon=r'{icon_path}',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip={PYINSTALLER_CONFIG["strip"]},
    upx={PYINSTALLER_CONFIG["upx"]},
    upx_exclude=[],
    name='{PYINSTALLER_CONFIG["name"]}',
)
"""
    
    spec_file = PROJECT_ROOT / f"{APP_NAME}.spec"
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    return str(spec_file)

def build_executable():
    """构建可执行文件"""
    import subprocess
    
    print("🔨 开始构建可执行文件...")
    
    # 创建spec文件
    spec_file = create_spec_file()
    print(f"✅ 创建spec文件: {spec_file}")
    
    # 清理旧的构建文件
    if BUILD_DIR.exists():
        import shutil
        shutil.rmtree(BUILD_DIR)
        print("🧹 清理旧的构建文件")
    
    if DIST_DIR.exists():
        import shutil
        shutil.rmtree(DIST_DIR)
        print("🧹 清理旧的分发文件")
    
    # 运行PyInstaller
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",
        "--noconfirm",
        spec_file
    ]
    
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功!")
        print(result.stdout)
        
        # 检查输出文件
        exe_path = DIST_DIR / APP_NAME / f"{APP_NAME}.exe"
        if exe_path.exists():
            print(f"📦 可执行文件: {exe_path}")
            print(f"📁 分发目录: {DIST_DIR / APP_NAME}")
        else:
            print("❌ 未找到可执行文件")
            return False
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_installer():
    """创建安装程序"""
    print("📦 创建安装程序...")
    
    # 检查Inno Setup是否安装
    inno_setup_path = None
    possible_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            inno_setup_path = path
            break
    
    if not inno_setup_path:
        print("❌ 未找到Inno Setup，请先安装Inno Setup")
        print("下载地址: https://jrsoftware.org/isdl.php")
        return False
    
    # 运行Inno Setup
    iss_file = PROJECT_ROOT / "installer" / "setup.iss"
    if not iss_file.exists():
        print(f"❌ 未找到安装脚本: {iss_file}")
        return False
    
    import subprocess
    cmd = [inno_setup_path, str(iss_file)]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 安装程序创建成功!")
        
        # 查找生成的安装程序
        installer_dir = PROJECT_ROOT / "installer" / "Output"
        if installer_dir.exists():
            for file in installer_dir.glob("*.exe"):
                print(f"📦 安装程序: {file}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 创建安装程序失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

if __name__ == "__main__":
    print("🏗️ 微信无感群发助手 - 构建脚本")
    print("=" * 50)
    
    # 构建可执行文件
    if build_executable():
        print("\n" + "=" * 50)
        
        # 询问是否创建安装程序
        create_installer_choice = input("是否创建安装程序? (y/N): ").lower().strip()
        if create_installer_choice in ['y', 'yes']:
            create_installer()
    
    print("\n🎉 构建完成!")
