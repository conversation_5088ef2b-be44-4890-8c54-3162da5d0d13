# Meet space 微信群发助手 - 构建指南

本文档详细说明如何将项目构建为可执行文件和MSI安装包。

## 📋 构建前准备

### 系统要求
- **操作系统**: Windows 10/11 (64位)
- **Python版本**: Python 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **磁盘空间**: 至少 2GB 可用空间

### 必需工具

#### 1. Python环境
```bash
# 检查Python版本
python --version

# 应该显示 Python 3.8.x 或更高版本
```

#### 2. 项目依赖
```bash
# 安装项目依赖
pip install -r requirements.txt

# 或者安装开发依赖
pip install -r requirements.txt
pip install pyinstaller
```

#### 3. WiX Toolset (MSI构建)
```bash
# 使用winget安装 (推荐)
winget install WiX.Toolset

# 或从官网下载安装
# https://wixtoolset.org/releases/
```

## 🚀 快速构建

### 一键构建 (推荐)
```bash
# 运行完整构建脚本
python build_all.py
```

这个脚本会自动完成：
1. ✅ 检查构建环境
2. 🔨 构建可执行文件
3. 📦 创建MSI安装包
4. 📝 生成构建信息

### 分步构建

#### 步骤1: 构建可执行文件
```bash
python build_exe.py
```

输出位置: `dist/MeetSpaceWeChatSender/`

#### 步骤2: 构建MSI安装包
```bash
python build_msi.py
```

输出位置: `output/MeetSpaceWeChatSender_v1.0.0.msi`

## 📁 项目结构

构建完成后的目录结构：

```
MeetSpaceWeChatSender/
├── 📁 dist/                          # 可执行文件输出
│   └── 📁 MeetSpaceWeChatSender/
│       ├── 🚀 MeetSpaceWeChatSender.exe  # 主程序
│       ├── 📚 各种依赖库文件
│       ├── 📄 README.md
│       └── 📄 LICENSE.txt
├── 📁 output/                         # MSI安装包输出
│   └── 📦 MeetSpaceWeChatSender_v1.0.0.msi
├── 📁 installer/                      # 安装程序配置
│   ├── 📝 setup.wxs                   # WiX配置文件
│   ├── 📄 license.rtf                 # 许可协议
│   └── 🖼️ 安装程序图片资源
├── 📁 build/                          # 构建临时文件
└── 📄 BUILD_INFO.md                   # 构建信息
```

## 🔧 构建配置

### PyInstaller配置

主要配置文件: `MeetSpaceWeChatSender.spec`

```python
# 关键配置项
datas = [
    ('resources', 'resources'),        # 资源文件
    ('config/*.json', 'config'),       # 配置文件
    ('tools', 'tools'),                # 工具文件
]

hiddenimports = [
    'PyQt6.QtCore',                    # PyQt6核心
    'wcferry',                         # 微信接口
    'core',                            # 业务逻辑
    'ui',                              # 用户界面
    'utils',                           # 工具模块
]

excludes = [
    'tkinter',                         # 排除不需要的模块
    'matplotlib',
    'numpy',
]
```

### WiX配置

主要配置文件: `installer/setup.wxs`

```xml
<!-- 产品信息 -->
<Product Id="*" 
         Name="Meet space 微信群发助手" 
         Version="1.0.0" 
         Manufacturer="Meet space 会客创意空间">

<!-- 功能特性 -->
<Feature Id="ProductFeature" Title="主程序" Level="1">
  <ComponentRef Id="MainExecutable" />
  <ComponentRef Id="ProgramMenuShortcut" />
  <ComponentRef Id="DesktopShortcut" />
</Feature>
```

## 🧪 测试构建结果

### 测试可执行文件
```bash
# 进入输出目录
cd dist/MeetSpaceWeChatSender/

# 运行程序
MeetSpaceWeChatSender.exe
```

### 测试MSI安装包
```bash
# 以管理员身份运行
# 双击 output/MeetSpaceWeChatSender_v1.0.0.msi

# 或使用命令行
msiexec /i output/MeetSpaceWeChatSender_v1.0.0.msi
```

## 🐛 常见问题

### 构建失败

#### 问题1: PyInstaller找不到模块
```bash
# 解决方案: 添加到hiddenimports
hiddenimports = [
    'missing_module_name',
]
```

#### 问题2: WiX编译失败
```bash
# 检查WiX安装
candle -?

# 重新安装WiX Toolset
winget install WiX.Toolset
```

#### 问题3: 文件路径错误
```bash
# 确保使用正确的路径分隔符
# Windows: 使用 \\ 或 /
# 示例: "dist\\MeetSpaceWeChatSender\\app.exe"
```

### 运行时错误

#### 问题1: 缺少DLL文件
```bash
# 解决方案: 添加到binaries
binaries = [
    ('path/to/missing.dll', '.'),
]
```

#### 问题2: 资源文件找不到
```bash
# 解决方案: 添加到datas
datas = [
    ('resources/icons', 'resources/icons'),
]
```

## 📊 性能优化

### 减小文件大小
```python
# 在.spec文件中添加
excludes = [
    'tkinter',      # 如果不使用tkinter
    'matplotlib',   # 如果不使用matplotlib
    'numpy',        # 如果不使用numpy
]

# 启用UPX压缩
upx=True
```

### 提高启动速度
```python
# 禁用不必要的优化
strip=False,           # 保留调试信息
upx=False,            # 禁用UPX压缩
```

## 📦 分发准备

### 文件清单
构建完成后，准备分发的文件：

1. **可执行文件版本**
   - `dist/MeetSpaceWeChatSender/` 整个目录
   - 压缩为ZIP文件分发

2. **安装包版本**
   - `output/MeetSpaceWeChatSender_v1.0.0.msi`
   - 可直接分发

### 版本信息
- **版本号**: 1.0.0
- **构建日期**: 自动生成
- **文件大小**: 约50-100MB (取决于依赖)
- **支持系统**: Windows 10/11 (64位)

## 🔄 持续集成

### GitHub Actions示例
```yaml
name: Build Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    - name: Install dependencies
      run: pip install -r requirements.txt
    - name: Build application
      run: python build_all.py
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: MeetSpaceWeChatSender
        path: |
          dist/
          output/
```

## 📞 技术支持

如果在构建过程中遇到问题：

1. **检查构建日志**: 查看详细的错误信息
2. **查看文档**: 参考PyInstaller和WiX官方文档
3. **联系支持**: 发送构建日志到技术支持邮箱

---

**构建愉快！** 🎉
