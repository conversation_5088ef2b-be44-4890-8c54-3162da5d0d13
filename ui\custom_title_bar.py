#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义标题栏组件

实现隐藏系统标题栏后的窗口控制功能，包括：
- 关闭、最大化、最小化按钮
- 窗口拖动
- 窗口大小调整
- 双击最大化/还原
"""

from PyQt6.QtWidgets import (
    QWidget,
    QHBoxLayout,
    QVBoxLayout,
    QPushButton,
    QLabel,
    QSizePolicy,
    QApplication,
)
from PyQt6.QtCore import Qt, QPoint, QRect, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPainter, QColor

from ui.modern_theme_manager import theme_manager as modern_theme_manager
from utils.logger import setup_logger

logger = setup_logger("custom_title_bar")


class CustomTitleBar(QWidget):
    """自定义标题栏"""

    # 信号定义
    close_clicked = pyqtSignal()
    minimize_clicked = pyqtSignal()
    maximize_clicked = pyqtSignal()

    def __init__(self, parent=None, title="微信群发助手"):
        super().__init__(parent)
        self.parent_window = parent
        self.title_text = title
        self.is_maximized = False

        # 拖动相关变量
        self.drag_position = QPoint()
        self.is_dragging = False

        # 双击检测
        self.last_click_time = 0
        self.double_click_threshold = 300  # 毫秒

        self.setup_ui()
        self.setup_style()

        # 注册到现代主题管理器
        modern_theme_manager.register_widget(self, "main")
        modern_theme_manager.theme_changed.connect(self.on_theme_changed)

        # 注册窗口控制按钮到主题管理器
        self.register_control_buttons()

        logger.debug("自定义标题栏初始化完成")

    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(40)
        self.setObjectName("customTitleBar")

        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 0, 5, 0)
        layout.setSpacing(10)

        # 应用图标 - 修复：支持高DPI显示
        self.icon_label = QLabel()
        # 根据系统DPI调整图标尺寸
        icon_size = (
            int(24 * self.devicePixelRatio())
            if hasattr(self, "devicePixelRatio")
            else 24
        )
        self.icon_label.setFixedSize(icon_size, icon_size)
        self.icon_label.setScaledContents(True)
        self.set_app_icon()
        layout.addWidget(self.icon_label)

        # 标题
        self.title_label = QLabel(self.title_text)
        self.title_label.setObjectName("titleLabel")
        font = QFont()
        font.setPointSize(10)
        font.setBold(True)
        self.title_label.setFont(font)
        layout.addWidget(self.title_label)

        # 弹性空间
        layout.addStretch()

        # 控制按钮
        self.create_control_buttons(layout)

        # 设置鼠标跟踪
        self.setMouseTracking(True)

    def create_control_buttons(self, layout):
        """创建苹果样式的控制按钮"""
        # 苹果样式按钮尺寸
        button_size = 12  # 苹果样式的小圆点

        # 创建按钮容器，苹果样式按钮在左侧
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(8, 0, 0, 0)  # 左侧留出空间
        button_layout.setSpacing(8)  # 按钮间距

        # 关闭按钮（红色圆点）
        self.close_btn = QPushButton()
        self.close_btn.setFixedSize(button_size, button_size)
        self.close_btn.setObjectName("macCloseButton")
        self.close_btn.clicked.connect(self.close_clicked.emit)
        self.close_btn.setToolTip("关闭")
        self.setup_mac_button_style(self.close_btn, "#ff5f57", "×")
        button_layout.addWidget(self.close_btn)

        # 最小化按钮（黄色圆点）
        self.minimize_btn = QPushButton()
        self.minimize_btn.setFixedSize(button_size, button_size)
        self.minimize_btn.setObjectName("macMinimizeButton")
        self.minimize_btn.clicked.connect(self.minimize_clicked.emit)
        self.minimize_btn.setToolTip("最小化")
        self.setup_mac_button_style(self.minimize_btn, "#ffbd2e", "−")
        button_layout.addWidget(self.minimize_btn)

        # 最大化/还原按钮（绿色圆点）
        self.maximize_btn = QPushButton()
        self.maximize_btn.setFixedSize(button_size, button_size)
        self.maximize_btn.setObjectName("macMaximizeButton")
        self.maximize_btn.clicked.connect(self.on_maximize_clicked)
        self.maximize_btn.setToolTip("最大化")
        self.setup_mac_button_style(self.maximize_btn, "#28ca42", "□")
        button_layout.addWidget(self.maximize_btn)

        button_layout.addStretch()  # 推到左侧

        # 将按钮容器添加到主布局的开始位置
        layout.insertWidget(0, button_container)

    def setup_mac_button_style(self, button, color, symbol):
        """设置苹果样式按钮的样式"""
        # 基础样式：圆形按钮
        base_style = f"""
            QPushButton {{
                background-color: {color};
                border: none;
                border-radius: 6px;
                color: transparent;
                font-size: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                color: rgba(0, 0, 0, 0.6);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color)};
            }}
        """
        button.setStyleSheet(base_style)

        # 悬停时显示符号
        button.enterEvent = lambda event: self.on_mac_button_enter(button, symbol)
        button.leaveEvent = lambda event: self.on_mac_button_leave(button)

    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            "#ff5f57": "#e04b42",  # 红色变暗
            "#ffbd2e": "#e6a82a",  # 黄色变暗
            "#28ca42": "#24b83c",  # 绿色变暗
        }
        return color_map.get(color, color)

    def on_mac_button_enter(self, button, symbol):
        """鼠标进入苹果按钮"""
        button.setText(symbol)

    def on_mac_button_leave(self, button):
        """鼠标离开苹果按钮"""
        button.setText("")

    def register_control_buttons(self):
        """注册窗口控制按钮到主题管理器"""
        try:
            # 为每个控制按钮单独注册主题
            modern_theme_manager.register_widget(self.minimize_btn, "window_control")
            modern_theme_manager.register_widget(self.maximize_btn, "window_control")
            modern_theme_manager.register_widget(self.close_btn, "window_control")
            logger.debug("窗口控制按钮已注册到主题管理器")
        except Exception as e:
            logger.error(f"注册窗口控制按钮失败: {e}")

    def set_app_icon(self):
        """设置应用图标 - Meet space风格"""
        try:
            # 创建Meet space风格的圆形图标
            pixmap = QPixmap(24, 24)
            pixmap.fill(Qt.GlobalColor.transparent)  # 透明背景

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)  # 抗锯齿

            # 绘制绿色圆形背景 (Meet space的亮绿色)
            painter.setBrush(QColor(165, 214, 67))  # 更接近原图的绿色 #A5D643
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawEllipse(0, 0, 24, 24)  # 填满整个区域

            # 绘制"Meet"文字 - 使用深灰色
            painter.setPen(QColor(68, 68, 68))  # 深灰色 #444444
            painter.setFont(QFont("Arial", 7, QFont.Weight.Bold))

            # 计算"Meet"的位置（上半部分）
            meet_rect = painter.fontMetrics().boundingRect("Meet")
            x_meet = (24 - meet_rect.width()) // 2
            y_meet = 10  # 上半部分

            painter.drawText(x_meet, y_meet, "Meet")

            # 绘制"space"文字 - 使用较细的字体
            painter.setFont(QFont("Arial", 5, QFont.Weight.Normal))
            space_rect = painter.fontMetrics().boundingRect("space")
            x_space = (24 - space_rect.width()) // 2
            y_space = 18  # 下半部分

            painter.drawText(x_space, y_space, "space")

            painter.end()

            self.icon_label.setPixmap(pixmap)
        except Exception as e:
            logger.error(f"设置应用图标失败: {e}")

    def setup_style(self):
        """设置样式"""
        # 样式将由主题管理器管理
        pass

    def on_theme_changed(self, theme_name: str):
        """主题变更处理"""
        try:
            # 重新应用样式
            self.style().unpolish(self)
            self.style().polish(self)
            logger.debug(f"标题栏主题已更新: {theme_name}")
        except Exception as e:
            logger.error(f"标题栏主题更新失败: {e}")

    def on_maximize_clicked(self):
        """最大化按钮点击处理"""
        self.toggle_maximize()

    def toggle_maximize(self):
        """切换最大化状态"""
        if self.parent_window:
            if self.is_maximized:
                self.parent_window.showNormal()
                self.maximize_btn.setToolTip("最大化")
                self.is_maximized = False
                # 苹果样式按钮不需要改变文字，只改变工具提示
            else:
                self.parent_window.showMaximized()
                self.maximize_btn.setToolTip("还原")
                self.is_maximized = True
                # 苹果样式按钮不需要改变文字，只改变工具提示

            self.maximize_clicked.emit()

    def set_title(self, title: str):
        """设置标题"""
        self.title_text = title
        self.title_label.setText(title)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = (
                event.globalPosition().toPoint()
                - self.parent_window.frameGeometry().topLeft()
            )
            self.is_dragging = True

            # 检测双击
            current_time = (
                QTimer().remainingTime() if hasattr(QTimer(), "remainingTime") else 0
            )
            import time

            current_time = int(time.time() * 1000)

            if current_time - self.last_click_time < self.double_click_threshold:
                # 双击事件
                self.toggle_maximize()

            self.last_click_time = current_time

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.is_dragging:
            if self.parent_window and not self.is_maximized:
                # 拖动窗口
                new_pos = event.globalPosition().toPoint() - self.drag_position
                self.parent_window.move(new_pos)

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = False

        super().mouseReleaseEvent(event)

    def update_maximize_state(self, is_maximized: bool):
        """更新最大化状态"""
        self.is_maximized = is_maximized
        if is_maximized:
            self.maximize_btn.setToolTip("还原")
            # 苹果样式按钮不需要改变文字，保持绿色圆点
        else:
            self.maximize_btn.setToolTip("最大化")
            # 苹果样式按钮不需要改变文字，保持绿色圆点

    def cleanup(self):
        """清理资源"""
        try:
            # 取消注册现代主题管理器
            modern_theme_manager.unregister_widget(self)
            # 取消注册窗口控制按钮
            if hasattr(self, "minimize_btn"):
                modern_theme_manager.unregister_widget(self.minimize_btn)
            if hasattr(self, "maximize_btn"):
                modern_theme_manager.unregister_widget(self.maximize_btn)
            if hasattr(self, "close_btn"):
                modern_theme_manager.unregister_widget(self.close_btn)
        except Exception as e:
            logger.error(f"清理标题栏资源失败: {e}")


class ResizableWidget(QWidget):
    """可调整大小的窗口组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.resize_margin = 5  # 调整大小的边距
        self.resize_direction = None
        self.resize_start_pos = QPoint()
        self.resize_start_geometry = QRect()

        # 设置鼠标跟踪
        self.setMouseTracking(True)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.resize_direction = self.get_resize_direction(
                event.position().toPoint()
            )
            if self.resize_direction:
                self.resize_start_pos = event.globalPosition().toPoint()
                self.resize_start_geometry = self.geometry()

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.resize_direction:
            self.resize_window(event.globalPosition().toPoint())
        else:
            # 更新鼠标光标
            direction = self.get_resize_direction(event.position().toPoint())
            self.update_cursor(direction)

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.resize_direction = None

        super().mouseReleaseEvent(event)

    def get_resize_direction(self, pos: QPoint) -> str:
        """获取调整大小的方向"""
        rect = self.rect()
        margin = self.resize_margin

        left = pos.x() <= margin
        right = pos.x() >= rect.width() - margin
        top = pos.y() <= margin
        bottom = pos.y() >= rect.height() - margin

        if top and left:
            return "top_left"
        elif top and right:
            return "top_right"
        elif bottom and left:
            return "bottom_left"
        elif bottom and right:
            return "bottom_right"
        elif top:
            return "top"
        elif bottom:
            return "bottom"
        elif left:
            return "left"
        elif right:
            return "right"

        return None

    def update_cursor(self, direction: str):
        """更新鼠标光标"""
        cursor_map = {
            "top": Qt.CursorShape.SizeVerCursor,
            "bottom": Qt.CursorShape.SizeVerCursor,
            "left": Qt.CursorShape.SizeHorCursor,
            "right": Qt.CursorShape.SizeHorCursor,
            "top_left": Qt.CursorShape.SizeFDiagCursor,
            "bottom_right": Qt.CursorShape.SizeFDiagCursor,
            "top_right": Qt.CursorShape.SizeBDiagCursor,
            "bottom_left": Qt.CursorShape.SizeBDiagCursor,
        }

        if direction in cursor_map:
            self.setCursor(cursor_map[direction])
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)

    def resize_window(self, global_pos: QPoint):
        """调整窗口大小"""
        delta = global_pos - self.resize_start_pos
        new_geometry = QRect(self.resize_start_geometry)

        if "left" in self.resize_direction:
            new_geometry.setLeft(new_geometry.left() + delta.x())
        if "right" in self.resize_direction:
            new_geometry.setRight(new_geometry.right() + delta.x())
        if "top" in self.resize_direction:
            new_geometry.setTop(new_geometry.top() + delta.y())
        if "bottom" in self.resize_direction:
            new_geometry.setBottom(new_geometry.bottom() + delta.y())

        # 设置最小尺寸限制 - 修复：与主窗口设置保持一致
        min_width = 900  # 与main_window.py中的setMinimumSize保持一致
        min_height = 600  # 与main_window.py中的setMinimumSize保持一致

        if new_geometry.width() < min_width:
            if "left" in self.resize_direction:
                new_geometry.setLeft(new_geometry.right() - min_width)
            else:
                new_geometry.setWidth(min_width)

        if new_geometry.height() < min_height:
            if "top" in self.resize_direction:
                new_geometry.setTop(new_geometry.bottom() - min_height)
            else:
                new_geometry.setHeight(min_height)

        self.setGeometry(new_geometry)
