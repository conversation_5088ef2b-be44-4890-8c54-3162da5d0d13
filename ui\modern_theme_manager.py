#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化主题管理器

仅提供PyQt6原生样式，移除所有自定义主题和样式系统。
保持界面简洁，使用系统默认外观。
"""

import os
import json
from typing import Dict, List, Optional, Any
from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QPalette, QColor, QFont
import platform

from utils.logger import setup_logger

logger = setup_logger("theme_manager")


class ModernThemeManager(QObject):
    """简化主题管理器 - 仅支持PyQt6原生样式"""

    # 主题切换信号
    theme_changed = pyqtSignal(str)
    theme_switching_started = pyqtSignal(str)
    theme_switching_finished = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.current_theme = "默认主题"
        self.theme_styles = self._load_all_themes()
        logger.info("简化主题管理器初始化完成")

    def get_available_themes(self) -> List[str]:
        """获取可用主题列表 - 包含所有五个主题"""
        return ["默认主题", "浅色主题", "深色主题", "护眼主题", "科技主题"]

    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme

    def _load_all_themes(self) -> Dict[str, Dict[str, str]]:
        """加载所有主题样式 - 包含所有五个主题"""
        return {
            "默认主题": self._get_default_theme(),
            "浅色主题": self._get_light_theme(),
            "深色主题": self._get_dark_theme(),
            "护眼主题": self._get_eye_care_theme(),
            "科技主题": self._get_tech_theme(),
        }

    def _get_default_theme(self) -> Dict[str, str]:
        """默认主题 - PyQt6原生样式

        设计理念：
        - 使用PyQt6的原生控件样式
        - 保持系统主题适配
        - 简洁清爽的界面
        - 适配900x600最小窗口尺寸
        """
        return {
            "main": """
                /* PyQt6原生样式 - 无自定义样式 */
            """,
            "button": """
                /* PyQt6原生按钮样式 - 无自定义样式 */
            """,
            "window_control": """
                /* PyQt6原生窗口控制样式 - 无自定义样式 */
            """,
            "input": """
                /* PyQt6原生输入框样式 - 无自定义样式 */
            """,
            "groupbox": """
                /* PyQt6原生分组框样式 - 无自定义样式 */
            """,
            "combobox": """
                /* PyQt6原生下拉框样式 - 无自定义样式 */
            """,
            "dialog": """
                /* PyQt6原生对话框样式 - 无自定义样式 */
            """,
            "table": """
                /* PyQt6原生表格样式 - 无自定义样式 */
            """,
            "scrollbar": """
                /* PyQt6原生滚动条样式 - 无自定义样式 */
            """,
            "frame": """
                /* PyQt6原生框架样式 - 无自定义样式 */
            """,
            "window_control": """
                /* 默认主题苹果样式窗口控制按钮 */

                /* 苹果样式关闭按钮（红色圆点） */
                QPushButton[objectName="macCloseButton"] {
                    background-color: #ff5f57;
                    color: transparent;
                }

                QPushButton[objectName="macCloseButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macCloseButton"]:pressed {
                    background-color: #e04b42;
                }

                /* 苹果样式最小化按钮（黄色圆点） */
                QPushButton[objectName="macMinimizeButton"] {
                    background-color: #ffbd2e;
                    color: transparent;
                }

                QPushButton[objectName="macMinimizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMinimizeButton"]:pressed {
                    background-color: #e6a82a;
                }

                /* 苹果样式最大化按钮（绿色圆点） */
                QPushButton[objectName="macMaximizeButton"] {
                    background-color: #28ca42;
                    color: transparent;
                }

                QPushButton[objectName="macMaximizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMaximizeButton"]:pressed {
                    background-color: #24b83c;
                }
            """,
        }

    def _get_light_theme(self) -> Dict[str, str]:
        """浅色主题 - 严格按照PyQt6 UI优化技术规范

        设计理念：
        - 仅添加颜色，绝对不修改任何尺寸
        - 不修改边框、内边距、外边距、宽度、高度
        - 保持与默认主题完全相同的布局和尺寸
        - 仅进行字体颜色反相调整
        """
        return {
            "main": """
                /* 浅色主题 - 严格仅颜色，绝对不修改尺寸 */
                QMainWindow, QWidget {
                    background-color: #fafbfc;
                    color: #2d3748;
                }

                /* 修复标题栏样式 - 确保标题栏组件背景透明 */
                QWidget[objectName="customTitleBar"] {
                    background-color: transparent;
                }

                QWidget[objectName="customTitleBar"] QLabel {
                    background-color: transparent;
                    color: #2d3748;
                }

                QLabel[objectName="titleLabel"] {
                    background-color: transparent;
                    color: #2d3748;
                }

                QTabWidget::pane {
                    background-color: #ffffff;
                }

                QTabBar::tab {
                    background-color: #f7fafc;
                    color: #718096;
                }

                QTabBar::tab:selected {
                    background-color: #4299e1;
                    color: white;
                }

                QTabBar::tab:hover {
                    background-color: #edf2f7;
                    color: #4a5568;
                }
            """,
            "button": """
                /* 浅色主题按钮 - 严格仅颜色，不修改边框和尺寸 */
                QPushButton {
                    background-color: #4299e1;
                    color: white;
                }

                QPushButton:hover {
                    background-color: #3182ce;
                }

                QPushButton:pressed {
                    background-color: #2c5282;
                }

                /* 大型主要按钮 - 仅颜色 */
                QPushButton[class="primary-large"] {
                    background-color: #4299e1;
                    color: white;
                }

                QPushButton[class="primary-large"]:hover {
                    background-color: #3182ce;
                }

                /* 炫酷紫色连接按钮 */
                QPushButton[class="connect-wechat"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f4b1fd, stop:1 #8e26e2);
                    color: #ffe7ff;
                    border: none;
                    border-radius: 18px;
                    font-weight: 600;
                    font-size: 14px;
                    padding: 8px 16px;
                    text-shadow: -1px 1px 2px #5e2b83;
                    box-shadow: 0 4px 15px rgba(142, 38, 226, 0.3);
                }

                QPushButton[class="connect-wechat"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #d190ff, stop:1 #5e2b83);
                    box-shadow: 0 6px 20px rgba(142, 38, 226, 0.4);
                    transform: translateY(-2px);
                }

                QPushButton[class="connect-wechat"]:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #c389f2, stop:1 #8e26e2);
                    box-shadow: 0 2px 10px rgba(142, 38, 226, 0.5);
                    transform: translateY(0px);
                }

                /* 次要按钮 - 仅颜色 */
                QPushButton[class="secondary"] {
                    background-color: #718096;
                    color: white;
                }

                QPushButton[class="secondary"]:hover {
                    background-color: #4a5568;
                }

                /* 小型按钮 - 仅颜色 */
                QPushButton[class="small"] {
                    background-color: #f7fafc;
                    color: #718096;
                }

                QPushButton[class="small"]:hover {
                    background-color: #edf2f7;
                    color: #4a5568;
                }

                /* 成功按钮 - 仅颜色 */
                QPushButton[class="success"] {
                    background-color: #48bb78;
                    color: white;
                }

                QPushButton[class="success"]:hover {
                    background-color: #38a169;
                }

                /* 危险按钮 - 仅颜色 */
                QPushButton[class="danger"] {
                    background-color: #f56565;
                    color: white;
                }

                QPushButton[class="danger"]:hover {
                    background-color: #e53e3e;
                }
            """,
            "window_control": """
                /* 浅色主题苹果样式窗口控制按钮 - 严格仅颜色 */

                /* 苹果样式关闭按钮（红色圆点） */
                QPushButton[objectName="macCloseButton"] {
                    background-color: #ff5f57;
                    color: transparent;
                }

                QPushButton[objectName="macCloseButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macCloseButton"]:pressed {
                    background-color: #e04b42;
                }

                /* 苹果样式最小化按钮（黄色圆点） */
                QPushButton[objectName="macMinimizeButton"] {
                    background-color: #ffbd2e;
                    color: transparent;
                }

                QPushButton[objectName="macMinimizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMinimizeButton"]:pressed {
                    background-color: #e6a82a;
                }

                /* 苹果样式最大化按钮（绿色圆点） */
                QPushButton[objectName="macMaximizeButton"] {
                    background-color: #28ca42;
                    color: transparent;
                }

                QPushButton[objectName="macMaximizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMaximizeButton"]:pressed {
                    background-color: #24b83c;
                }

                /* 兼容旧样式按钮 */
                QPushButton[objectName="closeButton"] {
                    color: #f56565;
                }

                QPushButton[objectName="maximizeButton"] {
                    color: #718096;
                }

                QPushButton[objectName="minimizeButton"] {
                    color: #718096;
                }
            """,
            "input": """
                /* 浅色主题输入框 - 严格仅颜色，不修改边框和尺寸 */
                QLineEdit, QTextEdit, QPlainTextEdit {
                    background-color: #ffffff;
                    color: #2d3748;
                    selection-background-color: #4299e1;
                    selection-color: white;
                }

                QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                    background-color: #ffffff;
                }

                QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
                    background-color: #ffffff;
                }

                QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
                    background-color: #f7fafc;
                    color: #a0aec0;
                }

                QComboBox {
                    background-color: #ffffff;
                    color: #2d3748;
                }

                QComboBox:hover {
                    background-color: #ffffff;
                }

                QComboBox:focus {
                    background-color: #ffffff;
                }

                QComboBox::down-arrow {
                    background-color: #718096;
                }

                QComboBox QAbstractItemView {
                    background-color: #ffffff;
                    selection-background-color: #4299e1;
                    selection-color: white;
                }

                QSpinBox {
                    background-color: #ffffff;
                    color: #2d3748;
                }

                QSpinBox:hover {
                    background-color: #ffffff;
                }

                QSpinBox:focus {
                    background-color: #ffffff;
                }

                QCheckBox {
                    color: #2d3748;
                }

                QCheckBox::indicator {
                    background-color: #ffffff;
                }

                QCheckBox::indicator:hover {
                    background-color: #ffffff;
                }

                QCheckBox::indicator:checked {
                    background-color: #4299e1;
                }
            """,
            "groupbox": """
                /* 浅色主题分组框 - 严格仅颜色，不修改边框和尺寸 */
                QGroupBox {
                    background-color: #ffffff;
                    color: #2d3748;
                }

                QGroupBox::title {
                    background-color: #ffffff;
                    color: #2d3748;
                }
            """,
            "combobox": """
                /* 浅色主题下拉框 - 严格仅颜色，不修改边框和尺寸 */
                QComboBox {
                    background-color: #ffffff;
                    color: #2d3748;
                }

                QComboBox:hover {
                    background-color: #ffffff;
                }

                QComboBox:focus {
                    background-color: #ffffff;
                }

                QComboBox::down-arrow {
                    background-color: #718096;
                }

                QComboBox QAbstractItemView {
                    background-color: #ffffff;
                    selection-background-color: #4299e1;
                    selection-color: white;
                }
            """,
            "dialog": """
                /* 浅色主题对话框 - 严格仅颜色，不修改边框和尺寸 */
                QDialog {
                    background-color: #ffffff;
                    color: #2d3748;
                }

                QDialog QLabel {
                    color: #2d3748;
                    background-color: transparent;
                }

                QDialog QPushButton {
                    background-color: #4299e1;
                    color: white;
                }

                QDialog QPushButton:hover {
                    background-color: #3182ce;
                }

                QDialog QPushButton:pressed {
                    background-color: #2c5282;
                }

                /* 对话框中的标签栏样式 */
                QDialog QTabWidget::pane {
                    background-color: #ffffff;
                }

                QDialog QTabBar::tab {
                    background-color: #f7fafc;
                    color: #718096;
                }

                QDialog QTabBar::tab:selected {
                    background-color: #4299e1;
                    color: white;
                }

                QDialog QTabBar::tab:hover {
                    background-color: #edf2f7;
                    color: #4a5568;
                }

                /* 对话框中的其他组件 */
                QDialog QGroupBox {
                    background-color: #ffffff;
                    color: #2d3748;
                }

                QDialog QLineEdit, QDialog QTextEdit, QDialog QPlainTextEdit {
                    background-color: #ffffff;
                    color: #2d3748;
                    selection-background-color: #4299e1;
                    selection-color: white;
                }

                QDialog QTableWidget {
                    background-color: #ffffff;
                    gridline-color: #e2e8f0;
                    selection-background-color: #4299e1;
                    selection-color: white;
                    color: #2d3748;
                }

                QDialog QHeaderView::section {
                    background-color: #f7fafc;
                    color: #2d3748;
                }
            """,
            "table": """
                /* 浅色主题表格 - 严格仅颜色，不修改边框和尺寸 */
                QTableWidget {
                    background-color: #ffffff;
                    gridline-color: #f7fafc;
                    selection-background-color: #bee3f8;
                    selection-color: #2c5282;
                    color: #2d3748;
                }

                QTableWidget::item:selected {
                    background-color: #bee3f8;
                    color: #2c5282;
                }

                QTableWidget::item:hover {
                    background-color: #f7fafc;
                }

                QHeaderView::section {
                    background-color: #f7fafc;
                    color: #718096;
                }

                QHeaderView::section:hover {
                    background-color: #edf2f7;
                }
            """,
            "scrollbar": """
                /* 浅色主题滚动条 - 严格仅颜色，不修改尺寸 */
                QScrollBar:vertical {
                    background-color: #f7fafc;
                }

                QScrollBar::handle:vertical {
                    background-color: #cbd5e0;
                }

                QScrollBar::handle:vertical:hover {
                    background-color: #a0aec0;
                }

                QScrollBar:horizontal {
                    background-color: #f7fafc;
                }

                QScrollBar::handle:horizontal {
                    background-color: #cbd5e0;
                }

                QScrollBar::handle:horizontal:hover {
                    background-color: #a0aec0;
                }
            """,
            "frame": """
                /* 浅色主题框架 - 严格仅颜色，不修改边框和尺寸 */
                QFrame {
                    background-color: #ffffff;
                }

                QFrame:hover {
                    background-color: #f7fafc;
                }

                QFrame[selected="true"] {
                    background-color: #bee3f8;
                }
            """,
        }

    def _get_dark_theme(self) -> Dict[str, str]:
        """深色主题 - 严格按照PyQt6 UI优化技术规范

        设计理念：
        - 仅添加颜色，绝对不修改任何尺寸
        - 不修改边框、内边距、外边距、宽度、高度
        - 保持与默认主题完全相同的布局和尺寸
        - 现代深色风格，护眼舒适
        - 支持苹果样式窗口控制按钮
        """
        return {
            "main": """
                /* 深色主题 - 严格仅颜色，绝对不修改尺寸 */
                QMainWindow, QWidget {
                    background-color: #1a202c;
                    color: #e2e8f0;
                }

                /* 修复标题栏样式 - 确保标题栏组件背景透明 */
                QWidget[objectName="customTitleBar"] {
                    background-color: transparent;
                }

                QWidget[objectName="customTitleBar"] QLabel {
                    background-color: transparent;
                    color: #e2e8f0;
                }

                QLabel[objectName="titleLabel"] {
                    background-color: transparent;
                    color: #e2e8f0;
                }

                QTabWidget::pane {
                    background-color: #2d3748;
                }

                QTabBar::tab {
                    background-color: #4a5568;
                    color: #a0aec0;
                }

                QTabBar::tab:selected {
                    background-color: #4299e1;
                    color: white;
                }

                QTabBar::tab:hover {
                    background-color: #718096;
                    color: #f7fafc;
                }
            """,
            "button": """
                /* 深色主题按钮 - 严格仅颜色，不修改边框和尺寸 */
                QPushButton {
                    background-color: #4299e1;
                    color: white;
                }

                QPushButton:hover {
                    background-color: #3182ce;
                }

                QPushButton:pressed {
                    background-color: #2c5282;
                }

                /* 大型主要按钮 - 仅颜色 */
                QPushButton[class="primary-large"] {
                    background-color: #4299e1;
                    color: white;
                }

                QPushButton[class="primary-large"]:hover {
                    background-color: #3182ce;
                }

                /* 炫酷紫色连接按钮 */
                QPushButton[class="connect-wechat"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f4b1fd, stop:1 #8e26e2);
                    color: #ffe7ff;
                    border: none;
                    border-radius: 18px;
                    font-weight: 600;
                    font-size: 14px;
                    padding: 8px 16px;
                    text-shadow: -1px 1px 2px #5e2b83;
                    box-shadow: 0 4px 15px rgba(142, 38, 226, 0.3);
                }

                QPushButton[class="connect-wechat"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #d190ff, stop:1 #5e2b83);
                    box-shadow: 0 6px 20px rgba(142, 38, 226, 0.4);
                    transform: translateY(-2px);
                }

                QPushButton[class="connect-wechat"]:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #c389f2, stop:1 #8e26e2);
                    box-shadow: 0 2px 10px rgba(142, 38, 226, 0.5);
                    transform: translateY(0px);
                }

                /* 次要按钮 - 仅颜色 */
                QPushButton[class="secondary"] {
                    background-color: #718096;
                    color: white;
                }

                QPushButton[class="secondary"]:hover {
                    background-color: #4a5568;
                }

                /* 小型按钮 - 仅颜色 */
                QPushButton[class="small"] {
                    background-color: #4a5568;
                    color: #e2e8f0;
                }

                QPushButton[class="small"]:hover {
                    background-color: #718096;
                    color: white;
                }

                /* 成功按钮 - 仅颜色 */
                QPushButton[class="success"] {
                    background-color: #48bb78;
                    color: white;
                }

                QPushButton[class="success"]:hover {
                    background-color: #38a169;
                }

                /* 危险按钮 - 仅颜色 */
                QPushButton[class="danger"] {
                    background-color: #f56565;
                    color: white;
                }

                QPushButton[class="danger"]:hover {
                    background-color: #e53e3e;
                }
            """,
            "window_control": """
                /* 深色主题苹果样式窗口控制按钮 - 严格仅颜色 */

                /* 苹果样式关闭按钮（红色圆点） */
                QPushButton[objectName="macCloseButton"] {
                    background-color: #ff5f57;
                    color: transparent;
                }

                QPushButton[objectName="macCloseButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macCloseButton"]:pressed {
                    background-color: #e04b42;
                }

                /* 苹果样式最小化按钮（黄色圆点） */
                QPushButton[objectName="macMinimizeButton"] {
                    background-color: #ffbd2e;
                    color: transparent;
                }

                QPushButton[objectName="macMinimizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMinimizeButton"]:pressed {
                    background-color: #e6a82a;
                }

                /* 苹果样式最大化按钮（绿色圆点） */
                QPushButton[objectName="macMaximizeButton"] {
                    background-color: #28ca42;
                    color: transparent;
                }

                QPushButton[objectName="macMaximizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMaximizeButton"]:pressed {
                    background-color: #24b83c;
                }

                /* 兼容旧样式按钮 */
                QPushButton[objectName="closeButton"] {
                    color: #f56565;
                }

                QPushButton[objectName="maximizeButton"] {
                    color: #e2e8f0;
                }

                QPushButton[objectName="minimizeButton"] {
                    color: #e2e8f0;
                }
            """,
            "input": """
                /* 深色主题输入框 - 严格仅颜色，不修改边框和尺寸 */
                QLineEdit, QTextEdit, QPlainTextEdit {
                    background-color: #2d3748;
                    color: #e2e8f0;
                    selection-background-color: #4299e1;
                    selection-color: white;
                }

                QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                    background-color: #4a5568;
                }

                QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
                    background-color: #4a5568;
                }

                QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
                    background-color: #1a202c;
                    color: #718096;
                }

                QComboBox {
                    background-color: #2d3748;
                    color: #e2e8f0;
                }

                QComboBox:hover {
                    background-color: #4a5568;
                }

                QComboBox:focus {
                    background-color: #4a5568;
                }

                QComboBox::down-arrow {
                    background-color: #e2e8f0;
                }

                QComboBox QAbstractItemView {
                    background-color: #2d3748;
                    selection-background-color: #4299e1;
                    selection-color: white;
                }

                QSpinBox {
                    background-color: #2d3748;
                    color: #e2e8f0;
                }

                QSpinBox:hover {
                    background-color: #4a5568;
                }

                QSpinBox:focus {
                    background-color: #4a5568;
                }

                QCheckBox {
                    color: #e2e8f0;
                }

                QCheckBox::indicator {
                    background-color: #2d3748;
                }

                QCheckBox::indicator:hover {
                    background-color: #4a5568;
                }

                QCheckBox::indicator:checked {
                    background-color: #4299e1;
                }
            """,
            "groupbox": """
                /* 深色主题分组框 - 严格仅颜色，不修改边框和尺寸 */
                QGroupBox {
                    background-color: #2d3748;
                    color: #e2e8f0;
                }

                QGroupBox::title {
                    background-color: #2d3748;
                    color: #e2e8f0;
                }
            """,
            "combobox": """
                /* 深色主题下拉框 - 严格仅颜色，不修改边框和尺寸 */
                QComboBox {
                    background-color: #2d3748;
                    color: #e2e8f0;
                }

                QComboBox:hover {
                    background-color: #4a5568;
                }

                QComboBox:focus {
                    background-color: #4a5568;
                }

                QComboBox::down-arrow {
                    background-color: #e2e8f0;
                }

                QComboBox QAbstractItemView {
                    background-color: #2d3748;
                    selection-background-color: #4299e1;
                    selection-color: white;
                }
            """,
            "dialog": """
                /* 深色主题对话框 - 严格仅颜色，不修改边框和尺寸 */
                QDialog {
                    background-color: #1a202c;
                    color: #e2e8f0;
                }

                /* 自定义对话框标题栏 */
                QWidget[objectName="dialogTitleBar"] {
                    background-color: #2d3748;
                    color: #e2e8f0;
                }

                QLabel[objectName="dialogTitleLabel"] {
                    background-color: transparent;
                    color: #e2e8f0;
                }

                QPushButton[objectName="dialogCloseButton"] {
                    background-color: #e53e3e;
                    color: white;
                }

                QPushButton[objectName="dialogCloseButton"]:hover {
                    background-color: #c53030;
                }

                QPushButton[objectName="dialogCloseButton"]:pressed {
                    background-color: #9c1c1c;
                }

                /* 对话框内容区域 */
                QWidget[objectName="dialogContent"] {
                    background-color: #1a202c;
                    color: #e2e8f0;
                }

                QDialog QLabel {
                    color: #e2e8f0;
                    background-color: transparent;
                }

                QDialog QPushButton {
                    background-color: #4299e1;
                    color: white;
                }

                QDialog QPushButton:hover {
                    background-color: #3182ce;
                }

                QDialog QPushButton:pressed {
                    background-color: #2c5282;
                }

                /* 对话框中的标签栏样式 */
                QDialog QTabWidget::pane {
                    background-color: #2d3748;
                }

                QDialog QTabBar::tab {
                    background-color: #4a5568;
                    color: #a0aec0;
                }

                QDialog QTabBar::tab:selected {
                    background-color: #4299e1;
                    color: white;
                }

                QDialog QTabBar::tab:hover {
                    background-color: #718096;
                    color: #f7fafc;
                }

                /* 对话框中的其他组件 */
                QDialog QGroupBox {
                    background-color: #2d3748;
                    color: #e2e8f0;
                }

                QDialog QLineEdit, QDialog QTextEdit, QDialog QPlainTextEdit {
                    background-color: #2d3748;
                    color: #e2e8f0;
                    selection-background-color: #4299e1;
                    selection-color: white;
                }

                QDialog QTableWidget {
                    background-color: #2d3748;
                    gridline-color: #4a5568;
                    selection-background-color: #4299e1;
                    selection-color: white;
                    color: #e2e8f0;
                }

                QDialog QHeaderView::section {
                    background-color: #4a5568;
                    color: #e2e8f0;
                }
            """,
            "table": """
                /* 深色主题表格 - 严格仅颜色，不修改边框和尺寸 */
                QTableWidget {
                    background-color: #2d3748;
                    gridline-color: #4a5568;
                    selection-background-color: #4299e1;
                    selection-color: white;
                    color: #e2e8f0;
                }

                QTableWidget::item:selected {
                    background-color: #4299e1;
                    color: white;
                }

                QTableWidget::item:hover {
                    background-color: #4a5568;
                }

                QHeaderView::section {
                    background-color: #4a5568;
                    color: #e2e8f0;
                }

                QHeaderView::section:hover {
                    background-color: #718096;
                }
            """,
            "scrollbar": """
                /* 深色主题滚动条 - 严格仅颜色，不修改尺寸 */
                QScrollBar:vertical {
                    background-color: #1a202c;
                }

                QScrollBar::handle:vertical {
                    background-color: #4a5568;
                }

                QScrollBar::handle:vertical:hover {
                    background-color: #718096;
                }

                QScrollBar:horizontal {
                    background-color: #1a202c;
                }

                QScrollBar::handle:horizontal {
                    background-color: #4a5568;
                }

                QScrollBar::handle:horizontal:hover {
                    background-color: #718096;
                }
            """,
            "frame": """
                /* 深色主题框架 - 严格仅颜色，不修改边框和尺寸 */
                QFrame {
                    background-color: #2d3748;
                }

                QFrame:hover {
                    background-color: #4a5568;
                }

                QFrame[selected="true"] {
                    background-color: #4299e1;
                }
            """,
        }

    def _get_eye_care_theme(self) -> Dict[str, str]:
        """护眼主题 - 严格按照PyQt6 UI优化技术规范

        设计理念：
        - 仅添加颜色，绝对不修改任何尺寸
        - 不修改边框、内边距、外边距、宽度、高度
        - 保持与默认主题完全相同的布局和尺寸
        - 护眼色调，减少眼部疲劳
        - 支持苹果样式窗口控制按钮
        """
        return {
            "main": """
                /* 护眼主题 - 严格仅颜色，绝对不修改尺寸 */
                QMainWindow, QWidget {
                    background-color: #f7f3e9;
                    color: #2f1b14;
                }

                /* 修复标题栏样式 - 确保标题栏组件背景透明 */
                QWidget[objectName="customTitleBar"] {
                    background-color: transparent;
                }

                QWidget[objectName="customTitleBar"] QLabel {
                    background-color: transparent;
                    color: #2f1b14;
                }

                QLabel[objectName="titleLabel"] {
                    background-color: transparent;
                    color: #2f1b14;
                }

                QTabWidget::pane {
                    background-color: #ede5d3;
                }

                QTabBar::tab {
                    background-color: #e6dcc6;
                    color: #5d4e37;
                }

                QTabBar::tab:selected {
                    background-color: #8fbc8f;
                    color: white;
                }

                QTabBar::tab:hover {
                    background-color: #ddd4c0;
                    color: #2f1b14;
                }
            """,
            "button": """
                /* 护眼主题按钮 - 严格仅颜色，不修改边框和尺寸 */
                QPushButton {
                    background-color: #8fbc8f;
                    color: white;
                }

                QPushButton:hover {
                    background-color: #7aa67a;
                }

                QPushButton:pressed {
                    background-color: #6b8e6b;
                }

                /* 大型主要按钮 - 仅颜色 */
                QPushButton[class="primary-large"] {
                    background-color: #8fbc8f;
                    color: white;
                }

                QPushButton[class="primary-large"]:hover {
                    background-color: #7aa67a;
                }

                /* 炫酷紫色连接按钮 */
                QPushButton[class="connect-wechat"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f4b1fd, stop:1 #8e26e2);
                    color: #ffe7ff;
                    border: none;
                    border-radius: 18px;
                    font-weight: 600;
                    font-size: 14px;
                    padding: 8px 16px;
                    text-shadow: -1px 1px 2px #5e2b83;
                    box-shadow: 0 4px 15px rgba(142, 38, 226, 0.3);
                }

                QPushButton[class="connect-wechat"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #d190ff, stop:1 #5e2b83);
                    box-shadow: 0 6px 20px rgba(142, 38, 226, 0.4);
                    transform: translateY(-2px);
                }

                QPushButton[class="connect-wechat"]:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #c389f2, stop:1 #8e26e2);
                    box-shadow: 0 2px 10px rgba(142, 38, 226, 0.5);
                    transform: translateY(0px);
                }

                /* 次要按钮 - 仅颜色 */
                QPushButton[class="secondary"] {
                    background-color: #d4c4a8;
                    color: #2f1b14;
                }

                QPushButton[class="secondary"]:hover {
                    background-color: #c7b299;
                }

                /* 小型按钮 - 仅颜色 */
                QPushButton[class="small"] {
                    background-color: #e6dcc6;
                    color: #2f1b14;
                }

                QPushButton[class="small"]:hover {
                    background-color: #ddd4c0;
                    color: #2f1b14;
                }

                /* 成功按钮 - 仅颜色 */
                QPushButton[class="success"] {
                    background-color: #9acd32;
                    color: white;
                }

                QPushButton[class="success"]:hover {
                    background-color: #8bb82d;
                }

                /* 危险按钮 - 仅颜色 */
                QPushButton[class="danger"] {
                    background-color: #cd853f;
                    color: white;
                }

                QPushButton[class="danger"]:hover {
                    background-color: #b8763a;
                }
            """,
            "window_control": """
                /* 护眼主题苹果样式窗口控制按钮 - 严格仅颜色 */

                /* 苹果样式关闭按钮（红色圆点） */
                QPushButton[objectName="macCloseButton"] {
                    background-color: #ff5f57;
                    color: transparent;
                }

                QPushButton[objectName="macCloseButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macCloseButton"]:pressed {
                    background-color: #e04b42;
                }

                /* 苹果样式最小化按钮（黄色圆点） */
                QPushButton[objectName="macMinimizeButton"] {
                    background-color: #ffbd2e;
                    color: transparent;
                }

                QPushButton[objectName="macMinimizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMinimizeButton"]:pressed {
                    background-color: #e6a82a;
                }

                /* 苹果样式最大化按钮（绿色圆点） */
                QPushButton[objectName="macMaximizeButton"] {
                    background-color: #28ca42;
                    color: transparent;
                }

                QPushButton[objectName="macMaximizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMaximizeButton"]:pressed {
                    background-color: #24b83c;
                }

                /* 兼容旧样式按钮 */
                QPushButton[objectName="closeButton"] {
                    color: #cd853f;
                }

                QPushButton[objectName="maximizeButton"] {
                    color: #2f1b14;
                }

                QPushButton[objectName="minimizeButton"] {
                    color: #2f1b14;
                }
            """,
            "input": """
                /* 护眼主题输入框 - 严格仅颜色，不修改边框和尺寸 */
                QLineEdit, QTextEdit, QPlainTextEdit {
                    background-color: #ede5d3;
                    color: #2f1b14;
                    selection-background-color: #8fbc8f;
                    selection-color: white;
                }

                QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                    background-color: #e6dcc6;
                }

                QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
                    background-color: #e6dcc6;
                }

                QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
                    background-color: #f7f3e9;
                    color: #8b7355;
                }

                QComboBox {
                    background-color: #ede5d3;
                    color: #2f1b14;
                }

                QComboBox:hover {
                    background-color: #e6dcc6;
                }

                QComboBox:focus {
                    background-color: #e6dcc6;
                }

                QComboBox::down-arrow {
                    background-color: #2f1b14;
                }

                QComboBox QAbstractItemView {
                    background-color: #ede5d3;
                    selection-background-color: #8fbc8f;
                    selection-color: white;
                }

                QSpinBox {
                    background-color: #ede5d3;
                    color: #2f1b14;
                }

                QSpinBox:hover {
                    background-color: #e6dcc6;
                }

                QSpinBox:focus {
                    background-color: #e6dcc6;
                }

                QCheckBox {
                    color: #2f1b14;
                }

                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                    background-color: #ede5d3;
                    border: 1px solid #8fbc8f;
                    border-radius: 3px;
                }

                QCheckBox::indicator:hover {
                    background-color: #e6dcc6;
                    border: 1px solid #7aa87a;
                }

                QCheckBox::indicator:checked {
                    background-color: #8fbc8f;
                    border: 1px solid #7aa87a;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0iIzJmMWIxNCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
                }

                QCheckBox::indicator:disabled {
                    background-color: #d0d0d0;
                    border: 1px solid #a0a0a0;
                }

                QRadioButton::indicator {
                    width: 16px;
                    height: 16px;
                    background-color: #ede5d3;
                    border: 1px solid #8fbc8f;
                    border-radius: 8px;
                }

                QRadioButton::indicator:hover {
                    background-color: #e6dcc6;
                    border: 1px solid #7aa87a;
                }

                QRadioButton::indicator:checked {
                    background-color: #8fbc8f;
                    border: 1px solid #7aa87a;
                }

                QRadioButton::indicator:checked {
                    background-color: #8fbc8f;
                    border: 1px solid #7aa87a;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjMiIGZpbGw9IiMyZjFiMTQiLz4KPC9zdmc+Cg==);
                }
            """,
            "groupbox": """
                /* 护眼主题分组框 - 严格仅颜色，不修改边框和尺寸 */
                QGroupBox {
                    background-color: #ede5d3;
                    color: #2f1b14;
                }

                QGroupBox::title {
                    background-color: #ede5d3;
                    color: #2f1b14;
                }
            """,
            "combobox": """
                /* 护眼主题下拉框 - 严格仅颜色，不修改边框和尺寸 */
                QComboBox {
                    background-color: #ede5d3;
                    color: #2f1b14;
                }

                QComboBox:hover {
                    background-color: #e6dcc6;
                }

                QComboBox:focus {
                    background-color: #e6dcc6;
                }

                QComboBox::down-arrow {
                    background-color: #2f1b14;
                }

                QComboBox QAbstractItemView {
                    background-color: #ede5d3;
                    selection-background-color: #8fbc8f;
                    selection-color: white;
                }
            """,
            "dialog": """
                /* 护眼主题对话框 - 严格仅颜色，不修改边框和尺寸 */
                QDialog {
                    background-color: #f7f3e9;
                    color: #2f1b14;
                }

                QDialog QLabel {
                    color: #2f1b14;
                    background-color: transparent;
                }

                QDialog QPushButton {
                    background-color: #8fbc8f;
                    color: white;
                }

                QDialog QPushButton:hover {
                    background-color: #7aa67a;
                }

                QDialog QPushButton:pressed {
                    background-color: #6b8e6b;
                }

                /* 对话框中的标签栏样式 */
                QDialog QTabWidget::pane {
                    background-color: #ede5d3;
                }

                QDialog QTabBar::tab {
                    background-color: #e6dcc6;
                    color: #5d4e37;
                }

                QDialog QTabBar::tab:selected {
                    background-color: #8fbc8f;
                    color: white;
                }

                QDialog QTabBar::tab:hover {
                    background-color: #ddd4c0;
                    color: #2f1b14;
                }

                /* 对话框中的其他组件 */
                QDialog QGroupBox {
                    background-color: #ede5d3;
                    color: #2f1b14;
                }

                QDialog QLineEdit, QDialog QTextEdit, QDialog QPlainTextEdit {
                    background-color: #ede5d3;
                    color: #2f1b14;
                    selection-background-color: #8fbc8f;
                    selection-color: white;
                }

                QDialog QTableWidget {
                    background-color: #ede5d3;
                    gridline-color: #d4c4a8;
                    selection-background-color: #8fbc8f;
                    selection-color: white;
                    color: #2f1b14;
                }

                QDialog QHeaderView::section {
                    background-color: #e6dcc6;
                    color: #2f1b14;
                }
            """,
            "table": """
                /* 护眼主题表格 - 严格仅颜色，不修改边框和尺寸 */
                QTableWidget {
                    background-color: #ede5d3;
                    gridline-color: #d4c4a8;
                    selection-background-color: #8fbc8f;
                    selection-color: white;
                    color: #2f1b14;
                }

                QTableWidget::item:selected {
                    background-color: #8fbc8f;
                    color: white;
                }

                QTableWidget::item:hover {
                    background-color: #e6dcc6;
                }

                QHeaderView::section {
                    background-color: #e6dcc6;
                    color: #2f1b14;
                }

                QHeaderView::section:hover {
                    background-color: #ddd4c0;
                }
            """,
            "scrollbar": """
                /* 护眼主题滚动条 - 严格仅颜色，不修改尺寸 */
                QScrollBar:vertical {
                    background-color: #f7f3e9;
                }

                QScrollBar::handle:vertical {
                    background-color: #d4c4a8;
                }

                QScrollBar::handle:vertical:hover {
                    background-color: #c7b299;
                }

                QScrollBar:horizontal {
                    background-color: #f7f3e9;
                }

                QScrollBar::handle:horizontal {
                    background-color: #d4c4a8;
                }

                QScrollBar::handle:horizontal:hover {
                    background-color: #c7b299;
                }
            """,
            "frame": """
                /* 护眼主题框架 - 严格仅颜色，不修改边框和尺寸 */
                QFrame {
                    background-color: #ede5d3;
                }

                QFrame:hover {
                    background-color: #e6dcc6;
                }

                QFrame[selected="true"] {
                    background-color: #8fbc8f;
                }
            """,
        }

    def _get_tech_theme(self) -> Dict[str, str]:
        """科技主题 - 全新设计，未来科技感

        设计理念：
        - 深空蓝黑渐变背景，营造科技感
        - 霓虹蓝/青色作为强调色
        - 仅修改颜色，绝对不改变布局和尺寸
        - 现代扁平化设计
        - 支持光晕效果和科技感渐变
        """
        return {
            "title_bar": """
                /* 科技主题标题栏 - 对话框和主窗口通用 */
                QWidget[objectName="customTitleBar"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1a1a2e, stop:0.5 #0f1419, stop:1 #0a0a0f);
                    border-bottom: 2px solid #00d9ff;
                }

                QWidget[objectName="dialogTitleBar"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1a1a2e, stop:0.5 #0f1419, stop:1 #0a0a0f);
                    border-bottom: 2px solid #00d9ff;
                }

                /* 标题文字样式 */
                QLabel[objectName="titleLabel"] {
                    color: #00d9ff;
                    font-weight: 600;
                    background-color: transparent;
                }

                QLabel[objectName="dialogTitleLabel"] {
                    color: #00d9ff;
                    font-weight: 600;
                    background-color: transparent;
                }

                /* 标题栏按钮通用样式 */
                QWidget[objectName="customTitleBar"] QPushButton {
                    background-color: transparent;
                    border: none;
                    color: #b8d4e3;
                }

                QWidget[objectName="customTitleBar"] QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #00d9ff;
                }

                QWidget[objectName="dialogTitleBar"] QPushButton {
                    background-color: transparent;
                    border: none;
                    color: #b8d4e3;
                }

                QWidget[objectName="dialogTitleBar"] QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #00d9ff;
                }

                /* 普通关闭按钮 */
                QPushButton[objectName="closeButton"] {
                    color: #ff6677;
                    background-color: transparent;
                    border: none;
                }

                QPushButton[objectName="closeButton"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ff6677, stop:1 #ff3355);
                    color: #ffffff;
                }

                QPushButton[objectName="minimizeButton"] {
                    color: #00d9ff;
                    background-color: transparent;
                    border: none;
                }

                QPushButton[objectName="minimizeButton"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #33e5ff;
                }

                QPushButton[objectName="maximizeButton"] {
                    color: #00d9ff;
                    background-color: transparent;
                    border: none;
                }

                QPushButton[objectName="maximizeButton"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #33e5ff;
                }
            """,
            "main": """
                /* 科技主题主体 - 深空科技感 */
                QMainWindow, QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #0a0a0f, stop:0.3 #1a1a2e, stop:0.7 #0f1419, stop:1 #000814);
                    color: #e8f4fd;
                }

                /* 标题栏 - 保持透明但增加科技感文字 */
                QWidget[objectName="customTitleBar"] {
                    background-color: transparent;
                }

                QWidget[objectName="customTitleBar"] QLabel {
                    background-color: transparent;
                    color: #00d9ff;
                }

                QLabel[objectName="titleLabel"] {
                    background-color: transparent;
                    color: #00d9ff;
                    font-weight: 600;
                }

                /* 选项卡区域 - 科技蓝调 */
                QTabWidget::pane {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1a1a2e, stop:0.5 #0f1419, stop:1 #0a0a0f);
                    border: 1px solid #00d9ff;
                }

                QTabBar::tab {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    color: #b8d4e3;
                    border: 1px solid #004d5c;
                }

                QTabBar::tab:selected {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00d9ff, stop:0.3 #0099cc, stop:0.7 #006699, stop:1 #003d66);
                    color: #ffffff;
                    border: 1px solid #00d9ff;
                }

                QTabBar::tab:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:0.5 #2a2a3e, stop:1 #1a1a2e);
                    color: #00d9ff;
                    border: 1px solid #0099cc;
                }

                /* 菜单栏 */
                QMenuBar {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1a1a2e, stop:1 #0a0a0f);
                    color: #e8f4fd;
                    border-bottom: 2px solid #00d9ff;
                }

                QMenuBar::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00d9ff, stop:1 #0099cc);
                    color: #ffffff;
                }

                /* 状态栏 */
                QStatusBar {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    color: #e8f4fd;
                    border-top: 2px solid #00d9ff;
                }
            """,
            "button": """
                /* 科技主题按钮 - 未来科技感设计 */
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00d9ff, stop:0.5 #0099cc, stop:1 #006699);
                    color: #ffffff;
                    border: 1px solid #00d9ff;
                }

                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #33e5ff, stop:0.5 #00b3e6, stop:1 #0080b3);
                    border: 1px solid #33e5ff;
                    color: #ffffff;
                }

                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #0099cc, stop:0.5 #006699, stop:1 #004d66);
                    border: 1px solid #0099cc;
                }

                QPushButton:disabled {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #404040, stop:1 #2a2a2a);
                    color: #808080;
                    border: 1px solid #404040;
                }

                /* 大型主要按钮 - 仅颜色 */
                QPushButton[class="primary-large"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00d9ff, stop:0.5 #0099cc, stop:1 #006699);
                    color: #ffffff;
                    border: 2px solid #00d9ff;
                }

                QPushButton[class="primary-large"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #33e5ff, stop:0.5 #00b3e6, stop:1 #0080b3);
                    border: 2px solid #33e5ff;
                }

                /* 连接微信按钮 - 科技感霓虹效果 */
                QPushButton[class="connect-wechat"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00ffcc, stop:0.3 #00d9ff, stop:0.7 #0099cc, stop:1 #006699);
                    color: #ffffff;
                    border: 2px solid #00ffcc;
                    font-weight: 700;
                    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
                }

                QPushButton[class="connect-wechat"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #33ffdd, stop:0.3 #33e5ff, stop:0.7 #00b3e6, stop:1 #0080b3);
                    border: 2px solid #33ffdd;
                }

                QPushButton[class="connect-wechat"]:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00e6bb, stop:0.3 #00ccee, stop:0.7 #0088bb, stop:1 #005588);
                    border: 2px solid #00e6bb;
                }

                QPushButton[class="connect-wechat"]:disabled {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #666666, stop:0.5 #555555, stop:1 #444444);
                    border: 2px solid #666666;
                    color: #999999;
                    font-weight: 400;
                }

                /* 次要按钮 - 深色科技感 */
                QPushButton[class="secondary"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    color: #b8d4e3;
                    border: 1px solid #004d5c;
                }

                QPushButton[class="secondary"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:0.5 #2a2a3e, stop:1 #1a1a2e);
                    color: #00d9ff;
                    border: 1px solid #0099cc;
                }

                /* 小型按钮 - 科技感 */
                QPushButton[class="small"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    color: #b8d4e3;
                    border: 1px solid #004d5c;
                }

                QPushButton[class="small"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #00d9ff;
                    border: 1px solid #0099cc;
                }

                /* 成功按钮 - 科技绿 */
                QPushButton[class="success"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00ff88, stop:0.5 #00cc66, stop:1 #009944);
                    color: #ffffff;
                    border: 1px solid #00ff88;
                }

                QPushButton[class="success"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #33ff99, stop:0.5 #00dd77, stop:1 #00aa55);
                    border: 1px solid #33ff99;
                }

                /* 危险按钮 - 科技红 */
                QPushButton[class="danger"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ff4466, stop:0.5 #ee2244, stop:1 #cc1122);
                    color: #ffffff;
                    border: 1px solid #ff4466;
                }

                QPushButton[class="danger"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ff6677, stop:0.5 #ff3355, stop:1 #dd2233);
                    border: 1px solid #ff6677;
                }
            """,
            "window_control": """
                /* 科技主题苹果样式窗口控制按钮 - 严格仅颜色 */

                /* 苹果样式关闭按钮（红色圆点） */
                QPushButton[objectName="macCloseButton"] {
                    background-color: #ff5f57;
                    color: transparent;
                }

                QPushButton[objectName="macCloseButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macCloseButton"]:pressed {
                    background-color: #e04b42;
                }

                /* 苹果样式最小化按钮（黄色圆点） */
                QPushButton[objectName="macMinimizeButton"] {
                    background-color: #ffbd2e;
                    color: transparent;
                }

                QPushButton[objectName="macMinimizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMinimizeButton"]:pressed {
                    background-color: #e6a82a;
                }

                /* 苹果样式最大化按钮（绿色圆点） */
                QPushButton[objectName="macMaximizeButton"] {
                    background-color: #28ca42;
                    color: transparent;
                }

                QPushButton[objectName="macMaximizeButton"]:hover {
                    color: rgba(0, 0, 0, 0.6);
                }

                QPushButton[objectName="macMaximizeButton"]:pressed {
                    background-color: #24b83c;
                }

                /* 兼容旧样式按钮 */
                QPushButton[objectName="closeButton"] {
                    color: #ff6b6b;
                }

                QPushButton[objectName="maximizeButton"] {
                    color: #e0e6ed;
                }

                QPushButton[objectName="minimizeButton"] {
                    color: #e0e6ed;
                }
            """,
            "input": """
                /* 科技主题输入框 - 未来科技感设计 */
                QLineEdit, QTextEdit, QPlainTextEdit {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1a1a2e, stop:0.5 #0f1419, stop:1 #0a0a0f);
                    color: #e8f4fd;
                    border: 1px solid #004d5c;
                    selection-background-color: #00d9ff;
                    selection-color: #000000;
                }

                QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    border: 1px solid #00d9ff;
                    color: #ffffff;
                    selection-background-color: #33e5ff;
                }

                QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    border: 1px solid #0099cc;
                }

                QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #404040, stop:1 #2a2a2a);
                    color: #666666;
                    border: 1px solid #404040;
                }

                QComboBox {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    color: #e8f4fd;
                    border: 1px solid #004d5c;
                }

                QComboBox:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:0.5 #2a2a3e, stop:1 #1a1a2e);
                    border: 1px solid #0099cc;
                }

                QComboBox:focus {
                    border: 1px solid #00d9ff;
                }

                QComboBox:focus {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                }

                QComboBox::down-arrow {
                    background-color: #00d9ff;
                }

                QComboBox QAbstractItemView {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    border: 1px solid #004d5c;
                    selection-background-color: #00d9ff;
                    selection-color: #000000;
                }

                QSpinBox {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    color: #e8f4fd;
                    border: 1px solid #004d5c;
                }

                QSpinBox:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:0.5 #2a2a3e, stop:1 #1a1a2e);
                    border: 1px solid #0099cc;
                }

                QSpinBox:focus {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:0.5 #2a2a3e, stop:1 #1a1a2e);
                    border: 1px solid #00d9ff;
                }

                QCheckBox {
                    color: #e8f4fd;
                }

                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    border: 1px solid #004d5c;
                    border-radius: 3px;
                }

                QCheckBox::indicator:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:0.5 #2a2a3e, stop:1 #1a1a2e);
                    border: 1px solid #0099cc;
                }

                QCheckBox::indicator:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00d9ff, stop:0.5 #0099cc, stop:1 #006699);
                    border: 1px solid #00d9ff;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
                }

                QCheckBox::indicator:disabled {
                    background: #404040;
                    border: 1px solid #666666;
                }

                QRadioButton::indicator {
                    width: 16px;
                    height: 16px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:0.5 #1a1a2e, stop:1 #0f1419);
                    border: 1px solid #004d5c;
                    border-radius: 8px;
                }

                QRadioButton::indicator:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:0.5 #2a2a3e, stop:1 #1a1a2e);
                    border: 1px solid #0099cc;
                }

                QRadioButton::indicator:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00d9ff, stop:0.5 #0099cc, stop:1 #006699);
                    border: 1px solid #00d9ff;
                }

                QRadioButton::indicator:checked {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00d9ff, stop:0.5 #0099cc, stop:1 #006699);
                    border: 1px solid #00d9ff;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjMiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=);
                }
            """,
            "groupbox": """
                /* 科技主题分组框 - 严格仅颜色，不修改边框和尺寸 */
                QGroupBox {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    color: #e0e6ed;
                }

                QGroupBox::title {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    color: #e0e6ed;
                }
            """,
            "combobox": """
                /* 科技主题下拉框 - 严格仅颜色，不修改边框和尺寸 */
                QComboBox {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    color: #e0e6ed;
                }

                QComboBox:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                }

                QComboBox:focus {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                }

                QComboBox::down-arrow {
                    background-color: #e0e6ed;
                }

                QComboBox QAbstractItemView {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    selection-background-color: #4a90e2;
                    selection-color: #ffffff;
                }
            """,
            "dialog": """
                /* 科技主题对话框 - 未来科技感 */
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #0a0a0f, stop:0.3 #1a1a2e, stop:0.7 #0f1419, stop:1 #000814);
                    color: #e8f4fd;
                    border: 2px solid #00d9ff;
                }

                /* 对话框标题栏 - 科技感设计 */
                QDialog QWidget[objectName="customTitleBar"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1a1a2e, stop:0.5 #0f1419, stop:1 #0a0a0f);
                    border-bottom: 2px solid #00d9ff;
                }

                QDialog QWidget[objectName="dialogTitleBar"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1a1a2e, stop:0.5 #0f1419, stop:1 #0a0a0f);
                    border-bottom: 2px solid #00d9ff;
                }

                /* 对话框标题文字 */
                QDialog QLabel[objectName="titleLabel"] {
                    color: #00d9ff;
                    font-weight: 600;
                    background-color: transparent;
                }

                QDialog QLabel[objectName="dialogTitleLabel"] {
                    color: #00d9ff;
                    font-weight: 600;
                    background-color: transparent;
                }

                /* 对话框标题栏按钮 */
                QDialog QWidget[objectName="customTitleBar"] QPushButton {
                    background-color: transparent;
                    border: none;
                    color: #b8d4e3;
                }

                QDialog QWidget[objectName="customTitleBar"] QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #00d9ff;
                }

                /* 关闭按钮特殊样式 */
                QDialog QPushButton[objectName="closeButton"] {
                    color: #ff6677;
                    background-color: transparent;
                    border: none;
                }

                QDialog QPushButton[objectName="closeButton"]:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ff6677, stop:1 #ff3355);
                    color: #ffffff;
                }

                /* 普通对话框标签 */
                QDialog QLabel {
                    color: #e8f4fd;
                    background-color: transparent;
                }

                /* 对话框按钮 */
                QDialog QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00d9ff, stop:0.5 #0099cc, stop:1 #006699);
                    color: #ffffff;
                    border: 1px solid #00d9ff;
                }

                QDialog QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #33e5ff, stop:0.5 #00b3e6, stop:1 #0080b3);
                    border: 1px solid #33e5ff;
                }

                QDialog QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #0099cc, stop:0.5 #006699, stop:1 #004d66);
                    border: 1px solid #0099cc;
                }

                /* 对话框中的标签栏样式 */
                QDialog QTabWidget::pane {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1a1a2e, stop:1 #0f0f23);
                }

                QDialog QTabBar::tab {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    color: #e0e6ed;
                }

                QDialog QTabBar::tab:selected {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4a90e2, stop:1 #357abd);
                    color: #ffffff;
                }

                QDialog QTabBar::tab:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #f0f6fd;
                }

                /* 对话框中的其他组件 */
                QDialog QGroupBox {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    color: #e0e6ed;
                }

                QDialog QLineEdit, QDialog QTextEdit, QDialog QPlainTextEdit {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    color: #e0e6ed;
                    selection-background-color: #4a90e2;
                    selection-color: #ffffff;
                }

                QDialog QTableWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    gridline-color: #4a4a5e;
                    selection-background-color: #4a90e2;
                    selection-color: #ffffff;
                    color: #e0e6ed;
                }

                QDialog QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #e0e6ed;
                }
            """,
            "table": """
                /* 科技主题表格 - 严格仅颜色，不修改边框和尺寸 */
                QTableWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                    gridline-color: #4a4a5e;
                    selection-background-color: #4a90e2;
                    selection-color: #ffffff;
                    color: #e0e6ed;
                }

                QTableWidget::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4a90e2, stop:1 #357abd);
                    color: #ffffff;
                }

                QTableWidget::item:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                }

                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                    color: #e0e6ed;
                }

                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4a4a5e, stop:1 #3a3a4e);
                }
            """,
            "scrollbar": """
                /* 科技主题滚动条 - 严格仅颜色，不修改尺寸 */
                QScrollBar:vertical {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0f0f23, stop:1 #1a1a2e);
                }

                QScrollBar::handle:vertical {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #4a90e2, stop:1 #357abd);
                }

                QScrollBar::handle:vertical:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #5ba0f2, stop:1 #468acd);
                }

                QScrollBar:horizontal {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #0f0f23, stop:1 #1a1a2e);
                }

                QScrollBar::handle:horizontal {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4a90e2, stop:1 #357abd);
                }

                QScrollBar::handle:horizontal:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5ba0f2, stop:1 #468acd);
                }
            """,
            "frame": """
                /* 科技主题框架 - 严格仅颜色，不修改边框和尺寸 */
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2a2a3e, stop:1 #1a1a2e);
                }

                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3a3a4e, stop:1 #2a2a3e);
                }

                QFrame[selected="true"] {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4a90e2, stop:1 #357abd);
                }
            """,
        }

    def get_theme_styles(self, theme_name: str = None) -> Dict[str, str]:
        """获取指定主题的样式定义"""
        if theme_name is None:
            theme_name = self.current_theme

        if theme_name in self.theme_styles:
            return self.theme_styles[theme_name]
        else:
            logger.warning(f"主题 '{theme_name}' 不存在，返回默认主题样式")
            return self.theme_styles["默认主题"]

    def apply_component_style(
        self, widget, component_type: str, theme_name: str = None
    ):
        """为指定组件应用样式 - 原生样式下不应用任何自定义样式"""
        try:
            # 在原生样式模式下，清除所有自定义样式
            widget.setStyleSheet("")
            logger.debug(f"已清除组件 {component_type} 的自定义样式，使用PyQt6原生样式")

        except Exception as e:
            logger.error(f"应用组件样式失败: {e}")

    def save_theme_preference(
        self, theme_name: str, config_path: str = "config/settings.json"
    ):
        """保存主题偏好设置"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            # 读取现有配置
            config = {}
            if os.path.exists(config_path):
                try:
                    with open(config_path, "r", encoding="utf-8") as f:
                        config = json.load(f)
                except (json.JSONDecodeError, FileNotFoundError):
                    config = {}

            # 更新主题设置
            config["theme"] = theme_name

            # 保存配置
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            logger.info(f"主题偏好已保存: {theme_name}")

        except Exception as e:
            logger.error(f"保存主题偏好失败: {e}")

    def load_theme_preference(self, config_path: str = "config/settings.json") -> str:
        """加载主题偏好设置"""
        try:
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                    theme_name = config.get("theme", "默认主题")

                    # 验证主题是否存在
                    if theme_name in self.get_available_themes():
                        logger.info(f"加载主题偏好: {theme_name}")
                        return theme_name
                    else:
                        logger.warning(
                            f"配置中的主题 '{theme_name}' 不存在，使用默认主题"
                        )
                        return "默认主题"
            else:
                logger.info("配置文件不存在，使用默认主题")
                return "默认主题"

        except Exception as e:
            logger.error(f"加载主题偏好失败: {e}")
            return "默认主题"

    def get_theme_info(self, theme_name: str = None) -> Dict[str, Any]:
        """获取主题信息"""
        if theme_name is None:
            theme_name = self.current_theme

        theme_info = {
            "name": theme_name,
            "description": "PyQt6原生样式，简洁清爽",
            "style_count": 0,  # 原生样式无自定义样式
            "is_current": theme_name == self.current_theme,
            "supports_dark_mode": False,  # 跟随系统主题
            "custom_styles": False,  # 无自定义样式
        }

        return theme_info

    def reset_to_default(self, app: QApplication):
        """重置为默认主题"""
        self.set_theme(app, "默认主题")
        logger.info("已重置为默认主题")

    def setup_dynamic_font(self, app: QApplication, font_size_name: str = "中"):
        """按照PyQt6 UI优化技术规范设置动态字体"""
        try:
            # 动态字体设置示例
            font = QFont()

            # 根据操作系统自动调整字体族
            is_mac = platform.system() == "Darwin"
            is_windows = platform.system() == "Windows"

            # 字体大小映射（与界面设置保持一致）
            size_map = {"小": 9, "中": 11, "大": 13, "特大": 15}
            base_size = size_map.get(font_size_name, 11)

            if is_mac:
                font.setFamilies(["SF Pro Display", "Helvetica Neue", "Arial"])
                font.setPointSize(base_size + 1)  # Mac稍大一点
            elif is_windows:
                font.setFamilies(["Microsoft YaHei", "Segoe UI", "Arial"])
                font.setPointSize(base_size)
            else:  # Linux
                font.setFamilies(["Noto Sans CJK SC", "DejaVu Sans", "Arial"])
                font.setPointSize(base_size)

            # 应用到整个应用程序
            app.setFont(font)
            logger.info(
                f"已设置动态字体: {font.families()}, 大小: {font.pointSize()} (用户设置: {font_size_name})"
            )

        except Exception as e:
            logger.error(f"设置动态字体失败: {e}")

    def update_font_size(self, app: QApplication, font_size_name: str):
        """更新字体大小（保持当前主题）- 改进版本，确保所有主题下都立即生效"""
        try:
            # 重新设置动态字体
            self.setup_dynamic_font(app, font_size_name)

            # 强制刷新所有组件的字体，无论当前是什么主题
            self._refresh_all_widgets_font(app)

            # 如果当前是自定义主题，重新应用主题以确保样式和字体协调
            if self.current_theme in ["浅色主题", "深色主题"]:
                self.set_theme(app, self.current_theme)

            # 发出字体大小变化信号，通知所有对话框
            self.theme_changed.emit(self.current_theme)

            logger.info(
                f"已更新字体大小: {font_size_name} (当前主题: {self.current_theme})"
            )

        except Exception as e:
            logger.error(f"更新字体大小失败: {e}")

    def _refresh_all_widgets_font(self, app: QApplication):
        """刷新所有组件的字体"""
        try:
            # 获取新字体
            new_font = app.font()

            # 刷新所有顶级窗口
            for widget in app.topLevelWidgets():
                try:
                    # 设置字体
                    widget.setFont(new_font)

                    # 递归刷新子组件
                    self._refresh_widget_children_font(widget, new_font)

                    # 强制重新绘制
                    widget.update()

                except Exception as widget_error:
                    logger.debug(
                        f"刷新组件字体失败: {widget.__class__.__name__} - {widget_error}"
                    )

            logger.debug(
                f"已刷新所有组件字体: {new_font.families()}, 大小: {new_font.pointSize()}"
            )

        except Exception as e:
            logger.debug(f"刷新组件字体过程失败: {e}")

    def _refresh_widget_children_font(self, widget, font):
        """递归刷新组件子元素的字体"""
        try:
            for child in widget.findChildren(QWidget):
                try:
                    # 设置字体
                    child.setFont(font)

                    # 强制重新绘制
                    child.update()

                except Exception as child_error:
                    # 某些组件可能不支持字体设置，这是正常的
                    pass

        except Exception as e:
            logger.debug(f"刷新子组件字体失败: {e}")

    def register_widget(self, widget, component_type: str = "default"):
        """注册组件 - 简化版本，不执行任何操作"""
        logger.debug(f"注册组件 {component_type} (简化模式，无操作)")
        pass

    def set_theme(self, theme_name_or_app, theme_name: str = None):
        """设置主题 - 兼容旧版本API"""
        if isinstance(theme_name_or_app, QApplication):
            # 新版本API: set_theme(app, theme_name)
            app = theme_name_or_app
            if theme_name is None:
                theme_name = "默认主题"
        else:
            # 旧版本API: set_theme(theme_name)
            app = QApplication.instance()
            theme_name = theme_name_or_app

        return self._set_theme_internal(app, theme_name)

    def _set_theme_internal(self, app: QApplication, theme_name: str) -> bool:
        """内部主题设置方法"""
        try:
            if theme_name not in self.get_available_themes():
                logger.warning(f"主题 '{theme_name}' 不存在，使用默认主题")
                theme_name = "默认主题"

            if theme_name == "默认主题":
                # 默认主题：清除所有自定义样式，恢复PyQt6原生外观
                app.setStyleSheet("")
                logger.info(f"已切换到主题: {theme_name} (PyQt6原生样式)")
            else:
                # 其他主题：应用自定义样式
                theme_styles = self.get_theme_styles(theme_name)
                combined_style = ""

                # 合并所有样式组件
                for component_name, style_content in theme_styles.items():
                    if style_content.strip():
                        combined_style += style_content + "\n"

                app.setStyleSheet(combined_style)
                logger.info(f"已切换到主题: {theme_name} (自定义样式)")

            self.current_theme = theme_name

            # 发出主题变化信号，确保所有对话框都能收到通知
            self.theme_changed.emit(theme_name)

            # 强制刷新所有现有的对话框
            self._refresh_all_dialogs(app, theme_name)

            return True

        except Exception as e:
            logger.error(f"设置主题失败: {e}")
            return False

    def _refresh_all_dialogs(self, app: QApplication, theme_name: str):
        """刷新所有现有对话框的主题和字体"""
        try:
            from PyQt6.QtWidgets import QDialog

            # 查找所有对话框
            dialogs = []
            for widget in app.allWidgets():
                if isinstance(widget, QDialog) and widget.isVisible():
                    dialogs.append(widget)

            if not dialogs:
                return

            logger.debug(f"发现 {len(dialogs)} 个活动对话框，正在刷新主题和字体")

            # 获取当前应用字体
            current_font = app.font()

            for dialog in dialogs:
                try:
                    if theme_name == "默认主题":
                        # 默认主题：清除对话框的自定义样式
                        dialog.setStyleSheet("")
                    else:
                        # 其他主题：对话框样式会通过全局样式表自动应用
                        # 但我们需要强制刷新
                        pass

                    # 确保对话框使用正确的字体
                    dialog.setFont(current_font)

                    # 递归设置子组件字体
                    self._refresh_widget_children_font(dialog, current_font)

                    # 强制重新绘制对话框
                    dialog.style().unpolish(dialog)
                    dialog.style().polish(dialog)
                    dialog.update()

                    logger.debug(f"已刷新对话框主题和字体: {dialog.__class__.__name__}")

                except Exception as dialog_error:
                    logger.debug(
                        f"刷新对话框主题失败: {dialog.__class__.__name__} - {dialog_error}"
                    )

        except Exception as e:
            logger.debug(f"刷新对话框主题过程失败: {e}")


# 全局主题管理器实例
theme_manager = ModernThemeManager()


def get_theme_manager() -> ModernThemeManager:
    """获取全局主题管理器实例"""
    return theme_manager


def apply_theme(app: QApplication, theme_name: str = "默认主题") -> bool:
    """应用主题的便捷函数"""
    return theme_manager.set_theme(app, theme_name)


def get_available_themes() -> List[str]:
    """获取可用主题列表的便捷函数"""
    return theme_manager.get_available_themes()


def get_current_theme() -> str:
    """获取当前主题的便捷函数"""
    return theme_manager.get_current_theme()
