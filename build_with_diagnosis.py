#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建包含诊断功能的版本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_diagnostic_version():
    """构建包含诊断功能的版本"""
    print("🔧 构建诊断版本...")
    
    # 清理
    for dir_name in ["build", "dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name, ignore_errors=True)
            print(f"  ✅ 已清理: {dir_name}")
    
    # 设置环境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 构建主程序
    main_cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MeetSpaceWeChatSender_Diagnostic",
        "--icon=resources/icons/app_icon.ico",
        
        # 工具文件
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64/Injector.exe;tools/x64",
        "--add-data=tools/Win32/Injector.exe;tools/Win32",
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        "--add-data=wxhelper_files/wxhelper_latest.dll;wxhelper_files",
        
        # 诊断脚本
        "--add-data=diagnose_injection.py;.",
        
        # 资源
        "--add-data=resources/icons;resources/icons",
        "--add-data=version_info.txt;.",
        
        # 核心模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=wcferry",
        "--hidden-import=ctypes",
        "--hidden-import=subprocess",
        "--hidden-import=psutil",
        "--hidden-import=tempfile",
        "--hidden-import=shutil",
        "--hidden-import=requests",
        
        # 项目模块
        "--hidden-import=config",
        "--hidden-import=core",
        "--hidden-import=core.injector_tool",
        "--hidden-import=core.auto_injector",
        "--hidden-import=core.http_api_connector",
        "--hidden-import=ui",
        "--hidden-import=utils",
        "--hidden-import=runtime_config_generator",
        
        # 排除
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        
        # 选项
        "--clean",
        "--noconfirm",
        "--noupx",
        
        "main.py"
    ]
    
    # 构建诊断工具
    diag_cmd = [
        "pyinstaller",
        "--onefile",
        "--console",  # 控制台版本
        "--name=DiagnoseInjection",
        "--icon=resources/icons/app_icon.ico",
        
        # 工具文件
        "--add-data=tools/Injector.exe;tools",
        "--add-data=tools/x64/Injector.exe;tools/x64",
        "--add-data=tools/Win32/Injector.exe;tools/Win32",
        "--add-data=wxhelper_files/wxhelper.dll;wxhelper_files",
        
        # 隐藏导入
        "--hidden-import=psutil",
        "--hidden-import=requests",
        "--hidden-import=tempfile",
        "--hidden-import=shutil",
        
        # 选项
        "--clean",
        "--noconfirm",
        
        "diagnose_injection.py"
    ]
    
    print("📋 构建主程序...")
    try:
        result = subprocess.run(
            main_cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=300
        )
        print("✅ 主程序构建完成")
    except Exception as e:
        print(f"❌ 主程序构建失败: {e}")
        return False
    
    print("📋 构建诊断工具...")
    try:
        result = subprocess.run(
            diag_cmd,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=300
        )
        print("✅ 诊断工具构建完成")
    except Exception as e:
        print(f"❌ 诊断工具构建失败: {e}")
        return False
    
    # 检查结果
    main_exe = Path("dist") / "MeetSpaceWeChatSender_Diagnostic.exe"
    diag_exe = Path("dist") / "DiagnoseInjection.exe"
    
    if main_exe.exists() and diag_exe.exists():
        main_size = main_exe.stat().st_size / (1024 * 1024)
        diag_size = diag_exe.stat().st_size / (1024 * 1024)
        
        print(f"✅ 主程序: {main_exe} ({main_size:.1f} MB)")
        print(f"✅ 诊断工具: {diag_exe} ({diag_size:.1f} MB)")
        
        # 创建使用说明
        instructions = """# Meet space 微信群发助手 - 诊断版本

## 🔧 使用步骤

### 1. 诊断问题
运行 `DiagnoseInjection.exe` 来诊断注入问题：
- 检查资源文件提取
- 测试注入器执行
- 检测微信进程
- 测试API服务

### 2. 运行主程序
运行 `MeetSpaceWeChatSender_Diagnostic.exe` 使用完整功能

## 🚨 故障排除

如果诊断工具显示问题：

1. **微信进程检测失败**
   - 启动微信PC版并登录
   - 确保微信完全启动

2. **注入器执行失败**
   - 以管理员身份运行
   - 关闭杀毒软件
   - 检查Windows Defender

3. **API服务失败**
   - 先运行诊断工具
   - 检查防火墙设置
   - 重启微信后再试

## 📞 支持
如果问题仍然存在，请提供诊断工具的完整输出。
"""
        
        with open("dist/诊断版本说明.txt", "w", encoding="utf-8") as f:
            f.write(instructions)
        
        print("✅ 使用说明已创建")
        return True
    else:
        print("❌ 构建文件不完整")
        return False

def main():
    print("🔧 Meet space 微信群发助手 - 诊断版本构建")
    print("=" * 60)
    
    if build_diagnostic_version():
        print("\n🎉 诊断版本构建成功!")
        print("📁 输出文件:")
        print("  - MeetSpaceWeChatSender_Diagnostic.exe (主程序)")
        print("  - DiagnoseInjection.exe (诊断工具)")
        print("  - 诊断版本说明.txt (使用说明)")
        print("\n📋 建议测试步骤:")
        print("  1. 先运行 DiagnoseInjection.exe 诊断问题")
        print("  2. 根据诊断结果解决问题")
        print("  3. 再运行主程序测试功能")
    else:
        print("\n❌ 构建失败!")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
