#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试架构检测功能
"""

import sys
import os
import platform
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_system_info():
    """测试系统信息"""
    print("🖥️  系统信息检测")
    print("=" * 50)
    
    print(f"操作系统: {platform.system()}")
    print(f"系统版本: {platform.version()}")
    print(f"架构: {platform.architecture()}")
    print(f"机器类型: {platform.machine()}")
    print(f"处理器: {platform.processor()}")
    print(f"Python位数: {platform.architecture()[0]}")
    
    # 检查是否为64位系统
    is_64bit = platform.machine().endswith('64') or '64' in platform.architecture()[0]
    print(f"64位系统: {is_64bit}")

def test_wechat_process_detection():
    """测试微信进程检测"""
    print("\n🔍 微信进程检测")
    print("=" * 50)
    
    try:
        import psutil
        
        # 查找微信进程
        wechat_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'create_time']):
            try:
                if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                    wechat_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if wechat_processes:
            print(f"✅ 找到 {len(wechat_processes)} 个微信进程:")
            for i, proc in enumerate(wechat_processes, 1):
                try:
                    info = proc.as_dict(['pid', 'name', 'exe', 'create_time'])
                    print(f"  {i}. PID: {info['pid']}")
                    print(f"     名称: {info['name']}")
                    print(f"     路径: {info.get('exe', 'N/A')}")
                    
                    # 尝试检测进程架构
                    if info.get('exe'):
                        arch = detect_file_architecture(info['exe'])
                        print(f"     架构: {arch}")
                    
                    print()
                except Exception as e:
                    print(f"     获取进程信息失败: {e}")
            
            return wechat_processes[0]  # 返回第一个进程用于测试
        else:
            print("❌ 未找到微信进程")
            print("请确保微信PC版已启动并登录")
            return None
            
    except ImportError:
        print("❌ psutil库未安装")
        return None
    except Exception as e:
        print(f"❌ 进程检测失败: {e}")
        return None

def detect_file_architecture(file_path):
    """检测文件架构"""
    try:
        import subprocess
        
        # 方法1: 使用PowerShell检查PE文件头
        cmd = f'''powershell -Command "& {{
            try {{
                $bytes = [System.IO.File]::ReadAllBytes('{file_path}')
                $peOffset = [System.BitConverter]::ToInt32($bytes, 60)
                $machineType = [System.BitConverter]::ToUInt16($bytes, $peOffset + 4)
                switch ($machineType) {{
                    0x014c {{ '32-bit (x86)' }}
                    0x8664 {{ '64-bit (x64)' }}
                    0xAA64 {{ '64-bit (ARM64)' }}
                    default {{ 'Unknown (0x{0:X})' -f $machineType }}
                }}
            }} catch {{
                'Error: ' + $_.Exception.Message
            }}
        }}"'''
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10, shell=True)
        
        if result.returncode == 0 and result.stdout.strip():
            return result.stdout.strip()
        else:
            return "检测失败"
            
    except Exception as e:
        return f"检测异常: {e}"

def test_injector_selection():
    """测试注入器选择逻辑"""
    print("\n🔧 注入器选择测试")
    print("=" * 50)
    
    try:
        from core.injector_tool import InjectorTool
        
        # 创建注入器实例
        injector = InjectorTool(auto_elevate=False)
        
        print(f"选择的注入器: {injector.injector_path}")
        print(f"DLL路径: {injector.dll_path}")
        
        # 检查文件是否存在
        if os.path.exists(injector.injector_path):
            size = os.path.getsize(injector.injector_path)
            print(f"注入器文件大小: {size} bytes")
            
            # 检测注入器架构
            arch = detect_file_architecture(injector.injector_path)
            print(f"注入器架构: {arch}")
        else:
            print("❌ 注入器文件不存在")
        
        if os.path.exists(injector.dll_path):
            size = os.path.getsize(injector.dll_path)
            print(f"DLL文件大小: {size} bytes")
            
            # 检测DLL架构
            arch = detect_file_architecture(injector.dll_path)
            print(f"DLL架构: {arch}")
        else:
            print("❌ DLL文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 注入器选择测试失败: {e}")
        return False

def test_available_injectors():
    """测试可用的注入器"""
    print("\n📁 可用注入器检测")
    print("=" * 50)
    
    injector_paths = [
        ("默认版本", "tools/Injector.exe"),
        ("64位版本", "tools/x64/Injector.exe"),
        ("32位版本", "tools/Win32/Injector.exe"),
        ("ARM64版本", "tools/ARM64/Injector.exe"),
    ]
    
    available_injectors = []
    
    for name, path in injector_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            arch = detect_file_architecture(path)
            print(f"✅ {name}: {path}")
            print(f"   大小: {size} bytes")
            print(f"   架构: {arch}")
            available_injectors.append((name, path, arch))
        else:
            print(f"❌ {name}: {path} (不存在)")
    
    print(f"\n总共找到 {len(available_injectors)} 个可用注入器")
    return available_injectors

def test_dll_files():
    """测试DLL文件"""
    print("\n📚 DLL文件检测")
    print("=" * 50)
    
    dll_paths = [
        ("主DLL", "wxhelper_files/wxhelper.dll"),
        ("最新DLL", "wxhelper_files/wxhelper_latest.dll"),
        ("原始备份", "wxhelper_files/wxhelper_original_backup.dll"),
        ("x64备份", "wxhelper_files/wxhelper_x64_backup.dll"),
    ]
    
    available_dlls = []
    
    for name, path in dll_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            arch = detect_file_architecture(path)
            print(f"✅ {name}: {path}")
            print(f"   大小: {size} bytes")
            print(f"   架构: {arch}")
            available_dlls.append((name, path, arch))
        else:
            print(f"❌ {name}: {path} (不存在)")
    
    print(f"\n总共找到 {len(available_dlls)} 个可用DLL文件")
    return available_dlls

def main():
    """主函数"""
    print("🔧 Meet space 微信群发助手 - 架构检测测试")
    print("=" * 70)
    
    # 1. 系统信息
    test_system_info()
    
    # 2. 微信进程检测
    wechat_process = test_wechat_process_detection()
    
    # 3. 可用注入器检测
    available_injectors = test_available_injectors()
    
    # 4. DLL文件检测
    available_dlls = test_dll_files()
    
    # 5. 注入器选择测试
    injector_ok = test_injector_selection()
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 50)
    print(f"微信进程: {'✅ 已找到' if wechat_process else '❌ 未找到'}")
    print(f"可用注入器: {len(available_injectors)} 个")
    print(f"可用DLL: {len(available_dlls)} 个")
    print(f"注入器选择: {'✅ 正常' if injector_ok else '❌ 异常'}")
    
    if wechat_process and available_injectors and available_dlls and injector_ok:
        print("\n🎉 架构检测功能完整，可以正常进行注入！")
    else:
        print("\n⚠️  存在问题，请检查:")
        if not wechat_process:
            print("  - 启动微信PC版并登录")
        if not available_injectors:
            print("  - 检查tools目录是否包含注入器文件")
        if not available_dlls:
            print("  - 检查wxhelper_files目录是否包含DLL文件")
        if not injector_ok:
            print("  - 检查注入器选择逻辑")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
