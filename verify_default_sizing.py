#!/usr/bin/env python3
"""
默认尺寸验证脚本
验证所有主题都使用默认主题的尺寸，只改变颜色
"""

import sys
import os
from pathlib import Path

def verify_default_sizing():
    """验证默认尺寸设置"""
    print("🔍 验证所有主题使用默认尺寸...")
    
    theme_file = Path(__file__).parent / "ui" / "modern_theme_manager.py"
    
    if not theme_file.exists():
        print("❌ 主题文件不存在")
        return False
    
    try:
        content = theme_file.read_text(encoding='utf-8')
        
        # 检查不应该存在的尺寸相关属性
        size_properties = [
            "width:",
            "height:", 
            "min-width:",
            "max-width:",
            "min-height:",
            "max-height:",
            "padding:",
            "margin:",
            "border-radius:",
            "border:",
            "image:"
        ]
        
        # 默认主题应该没有这些属性（除了窗口控制按钮）
        default_theme_section = content.split('def get_light_theme_styles(self):')[0]
        default_issues = []
        
        for prop in size_properties:
            if prop in default_theme_section and "macCloseButton" not in default_theme_section:
                # 检查是否在窗口控制按钮部分
                lines = default_theme_section.split('\n')
                for i, line in enumerate(lines):
                    if prop in line and "mac" not in line.lower() and "window" not in line.lower():
                        default_issues.append(f"默认主题中发现{prop} (第{i+1}行)")
        
        # 其他主题应该只有颜色相关属性
        other_themes_issues = []
        
        # 检查浅色主题
        light_section = content.split('def get_light_theme_styles(self):')[1].split('def get_dark_theme_styles(self):')[0]
        for prop in ["width:", "height:", "min-width:", "padding:", "border-radius:", "border:", "image:"]:
            count = light_section.count(prop)
            if count > 0:
                other_themes_issues.append(f"浅色主题中发现{count}个{prop}")
        
        # 检查深色主题
        dark_section = content.split('def get_dark_theme_styles(self):')[1].split('def get_eye_care_theme_styles(self):')[0]
        for prop in ["width:", "height:", "min-width:", "padding:", "border-radius:", "border:", "image:"]:
            count = dark_section.count(prop)
            if count > 0:
                other_themes_issues.append(f"深色主题中发现{count}个{prop}")
        
        # 检查护眼主题
        eye_care_section = content.split('def get_eye_care_theme_styles(self):')[1].split('def get_tech_theme_styles(self):')[0]
        for prop in ["width:", "height:", "min-width:", "padding:", "border-radius:", "border:", "image:"]:
            count = eye_care_section.count(prop)
            if count > 0:
                other_themes_issues.append(f"护眼主题中发现{count}个{prop}")
        
        # 检查科技主题
        tech_section = content.split('def get_tech_theme_styles(self):')[1]
        for prop in ["width:", "height:", "min-width:", "padding:", "border-radius:", "border:", "image:"]:
            count = tech_section.count(prop)
            if count > 0:
                other_themes_issues.append(f"科技主题中发现{count}个{prop}")
        
        # 显示结果
        print("\n📊 验证结果:")
        
        if not default_issues:
            print("  ✅ 默认主题: 无尺寸相关属性 (正确)")
        else:
            print("  ❌ 默认主题问题:")
            for issue in default_issues:
                print(f"    - {issue}")
        
        if not other_themes_issues:
            print("  ✅ 其他主题: 已移除所有尺寸相关属性 (正确)")
        else:
            print("  ❌ 其他主题问题:")
            for issue in other_themes_issues:
                print(f"    - {issue}")
        
        # 检查颜色属性是否保留
        color_properties = ["background-color:", "color:", "background:"]
        color_checks = {}
        
        for theme_name, section in [
            ("浅色主题", light_section),
            ("深色主题", dark_section), 
            ("护眼主题", eye_care_section),
            ("科技主题", tech_section)
        ]:
            color_count = sum(section.count(prop) for prop in color_properties)
            color_checks[theme_name] = color_count
        
        print("\n🎨 颜色属性检查:")
        for theme_name, count in color_checks.items():
            if count > 0:
                print(f"  ✅ {theme_name}: {count}个颜色属性")
            else:
                print(f"  ❌ {theme_name}: 无颜色属性")
        
        # 总体评估
        all_good = not default_issues and not other_themes_issues and all(count > 0 for count in color_checks.values())
        
        if all_good:
            print("\n🎉 验证通过！所有主题都使用默认尺寸，只改变颜色")
            print("\n📋 修复效果:")
            print("  - 默认主题: 使用PyQt6原生尺寸")
            print("  - 其他主题: 移除所有尺寸属性，保留颜色属性")
            print("  - 结果: 所有主题的控件尺寸完全一致")
            
            return True
        else:
            print(f"\n❌ 验证失败，发现问题需要修复")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def generate_test_guide():
    """生成测试指南"""
    print("\n📋 测试指南:")
    print("=" * 60)
    
    print("\n🎯 测试目标:")
    print("  验证所有主题的控件尺寸与默认主题完全一致")
    
    print("\n📝 测试步骤:")
    print("1. 🚀 启动程序: python main.py")
    print("2. 📏 记录默认主题的控件尺寸")
    print("   - 进入系统设置页面")
    print("   - 观察数字输入框、下拉框、复选框的尺寸")
    print("3. 🎨 切换到其他主题并对比:")
    print("   - 浅色主题: 控件尺寸应与默认主题相同")
    print("   - 深色主题: 控件尺寸应与默认主题相同")
    print("   - 护眼主题: 控件尺寸应与默认主题相同")
    print("   - 科技主题: 控件尺寸应与默认主题相同")
    
    print("\n✅ 预期结果:")
    print("  - 所有主题下控件的宽度、高度、位置完全一致")
    print("  - 只有颜色和背景效果不同")
    print("  - 布局和间距保持一致")
    print("  - 用户体验在所有主题下都相同")
    
    print("\n🔍 重点检查:")
    print("  - SpinBox (数字输入框)")
    print("  - ComboBox (下拉框)")
    print("  - QCheckBox (复选框)")
    print("  - QRadioButton (单选按钮)")
    print("  - QPushButton (按钮)")

def main():
    """主函数"""
    print("🚀 默认尺寸验证工具")
    print("=" * 60)
    print("\n📋 验证目标:")
    print("  确保所有主题都使用默认主题的控件尺寸")
    print("  只改变颜色，不影响布局和尺寸")
    
    success = verify_default_sizing()
    
    if success:
        print("\n✅ 验证完成！所有主题现在使用统一的默认尺寸")
        generate_test_guide()
        
        print(f"\n🎯 现在您可以:")
        print("  - 在任何主题下都有一致的控件尺寸")
        print("  - 享受纯粹的颜色主题变化")
        print("  - 不再担心布局问题")
        print("  - 获得最佳的用户体验")
        
        return 0
    else:
        print("\n❌ 验证失败，请检查修复内容")
        return 1

if __name__ == "__main__":
    sys.exit(main())
