[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "meetspace-wechat-sender"
version = "1.0.0"
description = "Meet space 微信群发助手 - 功能强大的微信群发工具"
readme = "README.md"
license = {file = "LICENSE.txt"}
authors = [
    {name = "Meet space 会客创意空间", email = "<EMAIL>"}
]
maintainers = [
    {name = "Meet space 会客创意空间", email = "<EMAIL>"}
]
keywords = [
    "wechat", "群发", "定时发送", "循环发送", "微信自动化", 
    "PyQt6", "GUI", "桌面应用", "消息发送"
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: End Users/Desktop",
    "License :: Other/Proprietary License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Communications :: Chat",
    "Topic :: Desktop Environment",
    "Topic :: Office/Business",
    "Environment :: Win32 (MS Windows)",
    "Natural Language :: Chinese (Simplified)",
]
requires-python = ">=3.8"
dependencies = [
    "PyQt6>=6.4.0,<7.0.0",
    "wcferry>=39.0.0",
    "pandas>=1.5.0,<3.0.0",
    "openpyxl>=3.0.0,<4.0.0",
    "requests>=2.28.0,<3.0.0",
    "aiohttp>=3.8.0,<4.0.0",
    "Pillow>=9.0.0,<11.0.0",
    "pyyaml>=6.0,<7.0.0",
    "python-dateutil>=2.8.0,<3.0.0",
    "psutil>=5.9.0,<6.0.0",
]

[project.optional-dependencies]
dev = [
    "black>=22.0.0,<25.0.0",
    "flake8>=5.0.0,<8.0.0",
    "pytest>=7.0.0,<9.0.0",
    "pytest-qt>=4.0.0,<5.0.0",
    "pytest-asyncio>=0.21.0,<1.0.0",
    "pytest-cov>=4.0.0,<6.0.0",
    "mypy>=1.0.0,<2.0.0",
]
build = [
    "pyinstaller>=5.0.0",
    "setuptools>=61.0",
    "wheel",
]

[project.urls]
Homepage = "https://meetspace.cn"
Documentation = "https://docs.meetspace.cn"
Repository = "https://github.com/meetspace/wechat-sender"
"Bug Tracker" = "https://github.com/meetspace/wechat-sender/issues"
Changelog = "https://github.com/meetspace/wechat-sender/blob/main/CHANGELOG.md"

[project.scripts]
meetspace-wechat-sender = "main:main"

[project.gui-scripts]
MeetSpaceWeChatSender = "main:main"

[tool.setuptools]
packages = ["config", "core", "ui", "utils"]
include-package-data = true

[tool.setuptools.package-data]
"*" = ["*.json", "*.yaml", "*.yml", "*.txt", "*.md", "*.ico", "*.png", "*.jpg"]

[tool.setuptools.exclude-package-data]
"*" = ["*.pyc", "__pycache__", "*.log", "*.tmp"]

# Black代码格式化配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | portable
  | __pycache__
)/
'''

# Flake8代码检查配置
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "portable",
    "*.egg-info",
]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "build/",
    "dist/",
    "portable/",
]

# Pytest测试配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "ui: marks tests as UI tests",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

# Coverage配置
[tool.coverage.run]
source = ["config", "core", "ui", "utils"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "build/*",
    "dist/*",
    "portable/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
