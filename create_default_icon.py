#!/usr/bin/env python3
"""
创建默认图标文件
"""

import os
from pathlib import Path

def create_default_icon():
    """创建默认应用图标"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 确保图标目录存在
        icons_dir = Path("resources/icons")
        icons_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建不同尺寸的图标
        sizes = [16, 32, 48, 64, 128, 256]
        images = []
        
        for size in sizes:
            # 创建图像
            img = Image.new('RGBA', (size, size), (70, 130, 180, 255))  # 钢蓝色背景
            draw = ImageDraw.Draw(img)
            
            # 计算字体大小
            font_size = max(8, size // 2)
            
            try:
                # 尝试使用系统字体
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                try:
                    font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", font_size)  # 微软雅黑
                except:
                    font = ImageFont.load_default()
            
            # 绘制"M"字母
            text = "M"
            
            # 计算文本位置（居中）
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (size - text_width) // 2
            y = (size - text_height) // 2
            
            # 绘制白色文字
            draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
            
            images.append(img)
        
        # 保存为ICO文件
        app_icon_path = icons_dir / "app.ico"
        images[0].save(
            app_icon_path, 
            format='ICO', 
            sizes=[(img.width, img.height) for img in images],
            append_images=images[1:]
        )
        
        print(f"✅ 应用图标已创建: {app_icon_path}")
        
        # 创建安装程序图标（复制应用图标）
        installer_icon_path = icons_dir / "installer.ico"
        images[0].save(installer_icon_path, format='ICO', sizes=[(32, 32)])
        print(f"✅ 安装程序图标已创建: {installer_icon_path}")
        
        # 创建卸载图标
        uninstall_img = Image.new('RGBA', (32, 32), (180, 70, 70, 255))  # 红色背景
        draw = ImageDraw.Draw(uninstall_img)
        
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
            
        # 绘制"X"
        text = "X"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (32 - text_width) // 2
        y = (32 - text_height) // 2
        
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        uninstall_icon_path = icons_dir / "uninstall.ico"
        uninstall_img.save(uninstall_icon_path, format='ICO', sizes=[(32, 32)])
        print(f"✅ 卸载图标已创建: {uninstall_icon_path}")
        
        return True
        
    except ImportError:
        print("❌ PIL库未安装，无法创建图标")
        print("请运行: pip install Pillow")
        return False
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")
        return False

if __name__ == "__main__":
    print("🎨 创建默认图标...")
    success = create_default_icon()
    if success:
        print("🎉 图标创建完成！")
    else:
        print("💥 图标创建失败！")
